import json
import re
import ast
import importlib.util
import sys
import io
import os
import autoflake
from blitzy_utils.logger import logger
from pyflakes.api import checkPath
from pathlib import Path

from local_config import FOLDER_PATH


def create_result_structure():
    """Create the base result structure for error tracking."""
    return {
        "errors": {},
        "summary": {
            "total_errors": 0,
            "total_files": 0
        }
    }


def check_import(module_name, file_path):
    """
    Enhanced check if a module can be imported, handling both package and direct file imports.
    Returns (True, None) if successful, (False, error_message) if failed.
    """
    try:
        # First try standard module import
        spec = importlib.util.find_spec(module_name)
        if spec is not None:
            try:
                if spec.origin and os.path.isfile(spec.origin):
                    with open(spec.origin, 'r', encoding='utf-8') as f:
                        source = f.read()
                    compile(source, spec.origin, 'exec')
                return True, None
            except SyntaxError as se:
                return False, f"Syntax error in imported module: {se}"
            except Exception as e:
                return False, f"Error compiling module: {str(e)}"

        # Handle local imports with improved file path checking
        parts = module_name.split('.')
        current_path = os.path.dirname(os.path.abspath(file_path))
        base_path = current_path

        # Try all possible file paths
        for i in range(len(parts)):
            # Check direct .py file
            if i > 0:
                partial_path = os.path.join(base_path, *parts[:i+1])
            else:
                partial_path = os.path.join(base_path, parts[i])

            py_file = f"{partial_path}.py"
            init_file = os.path.join(partial_path, '__init__.py')

            if os.path.isfile(py_file):
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        source = f.read()
                    compile(source, py_file, 'exec')
                    return True, None
                except SyntaxError as se:
                    return False, f"Syntax error in local module: {se}"
                except Exception as e:
                    return False, f"Error compiling local module: {str(e)}"

            # Check for package with __init__.py
            if os.path.isfile(init_file):
                try:
                    with open(init_file, 'r', encoding='utf-8') as f:
                        source = f.read()
                    compile(source, init_file, 'exec')
                    continue  # Continue checking next part of the path
                except SyntaxError as se:
                    return False, f"Syntax error in package __init__: {se}"
                except Exception as e:
                    return False, f"Error compiling package __init__: {str(e)}"

            # Also check one level up for common project structures
            parent_path = os.path.dirname(base_path)
            parent_py_file = os.path.join(parent_path, *parts[:i+1]) + '.py'
            if os.path.isfile(parent_py_file):
                try:
                    with open(parent_py_file, 'r', encoding='utf-8') as f:
                        source = f.read()
                    compile(source, parent_py_file, 'exec')
                    return True, None
                except SyntaxError as se:
                    return False, f"Syntax error in parent module: {se}"
                except Exception as e:
                    return False, f"Error compiling parent module: {str(e)}"

        return False, f"Module '{module_name}' not found"

    except Exception as e:
        if "Environment file not found" in str(e):
            return True, None  # Skip environment file checks
        return False, f"Import check error: {str(e)}"


def get_module_contents(module_name, file_path):
    """
    Try to import a module and get its contents safely, with enhanced local file handling.

    Args:
        module_name: Name of the module to import (e.g., 'db.models.formulary')
        file_path: Path of the file doing the importing, used for resolving relative paths

    Returns:
        The module's dictionary of contents or None if import fails
    """
    try:
        # First try to import the module directly
        try:
            __import__(module_name)
            module = sys.modules[module_name]
            return vars(module)
        except ImportError as e:
            if "Environment file not found" in str(e):
                # If it's just an env file issue, consider this a successful import
                if module_name in sys.modules:
                    return vars(sys.modules[module_name])
            pass

        try:
            # If direct import fails, try to find and load the spec
            spec = importlib.util.find_spec(module_name)
            if spec is not None:
                module = importlib.util.module_from_spec(spec)
                try:
                    spec.loader.exec_module(module)
                except Exception as e:
                    if "Environment file not found" in str(e):
                        # Return what we have even if env file is missing
                        return vars(module)
                    raise
                return vars(module)
        except Exception as e:
            if "Environment file not found" in str(e):
                # If it's just an env file issue, return empty dict
                return {}
            pass

        return None

    except Exception as e:
        if "Environment file not found" in str(e):
            return {}  # Return empty dict for env file errors
        return None


def check_imported_items(module_name, names, file_path):
    """
    Enhanced check for imported items with better file path handling.
    Returns (True, None) if all items exist, (False, error_message) if any are missing.
    """
    try:
        # Try standard module import first
        module_contents = get_module_contents(module_name, file_path)
        if module_contents is not None:
            # Check each imported name
            defined_names = set(module_contents.keys())
            missing_items = [name for name in names if name not in defined_names]

            if not missing_items:
                return True, None

            # Check submodules for missing items
            remaining_missing = []
            for name in missing_items:
                submodule_name = f"{module_name}.{name}"
                if get_module_contents(submodule_name, file_path) is not None:
                    continue
                remaining_missing.append(name)

            if not remaining_missing:
                return True, None

            # If we still have missing items, try checking the file directly
            success, error_msg = check_file_for_items(file_path, remaining_missing, module_name)
            if success:
                return True, None

            return False, f"Items not found in module '{module_name}': {', '.join(remaining_missing)}"

        # Enhanced local file checking
        parts = module_name.split('.')
        current_path = os.path.dirname(os.path.abspath(file_path))
        base_path = current_path

        # Try all possible file paths
        for i in range(len(parts)):
            if i > 0:
                partial_path = os.path.join(base_path, *parts[:i+1])
            else:
                partial_path = os.path.join(base_path, parts[i])

            # Check direct .py file
            py_file = f"{partial_path}.py"
            if os.path.isfile(py_file):
                success, error_msg = check_file_for_items(py_file, names, module_name)
                if success:
                    return True, None

            # Check package __init__.py
            init_file = os.path.join(partial_path, '__init__.py')
            if os.path.isfile(init_file):
                success, error_msg = check_file_for_items(init_file, names, module_name)
                if success:
                    return True, None

            # Check one level up (common in project structures)
            parent_path = os.path.dirname(base_path)
            parent_py_file = os.path.join(parent_path, *parts[:i+1]) + '.py'
            if os.path.isfile(parent_py_file):
                success, error_msg = check_file_for_items(parent_py_file, names, module_name)
                if success:
                    return True, None

        return False, f"Items not found in module '{module_name}': {', '.join(remaining_missing if 'remaining_missing' in locals() else names)}"

    except Exception as e:
        if "Environment file not found" in str(e):
            return True, None
        return False, f"Import check error: {str(e)}"


def check_file_for_items(file_path, names, module_name):
    """Helper function to check if items exist in a specific file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source = f.read()
        tree = ast.parse(source)

        defined_names = set()
        imported_names = set()
        class_methods = set()  # Track methods defined in classes

        # Track the last seen class for method association
        current_class = None

        for node in ast.walk(tree):
            # Direct definitions (variables, functions, classes)
            if isinstance(node, ast.Name) and isinstance(node.ctx, ast.Store):
                defined_names.add(node.id)
            elif isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                if current_class:
                    # This is a method in a class
                    class_methods.add(node.name)
                else:
                    # This is a standalone function
                    defined_names.add(node.name)
            elif isinstance(node, ast.ClassDef):
                defined_names.add(node.name)
                current_class = node.name
                # Add class name to defined names
                defined_names.add(node.name)
                # Process class methods
                for item in node.body:
                    if isinstance(item, (ast.FunctionDef, ast.AsyncFunctionDef)):
                        class_methods.add(item.name)
                        # Also add ClassMethod.method_name pattern
                        defined_names.add(f"{node.name}.{item.name}")
                current_class = None

            # Assignments
            elif isinstance(node, ast.Assign):
                for target in node.targets:
                    if isinstance(target, ast.Name):
                        defined_names.add(target.id)
                    elif isinstance(target, ast.Tuple):
                        for elt in target.elts:
                            if isinstance(elt, ast.Name):
                                defined_names.add(elt.id)

            # AnnAssign (type-annotated assignments)
            elif isinstance(node, ast.AnnAssign) and isinstance(node.target, ast.Name):
                defined_names.add(node.target.id)

            # Import statements
            elif isinstance(node, ast.Import):
                for alias in node.names:
                    asname = alias.asname or alias.name
                    imported_names.add(asname)
                    # Also add the original name
                    imported_names.add(alias.name)
            elif isinstance(node, ast.ImportFrom):
                for alias in node.names:
                    if alias.name == '*':
                        # Handle "from module import *"
                        return True, None  # Assume all items exist with wildcard imports
                    asname = alias.asname or alias.name
                    imported_names.add(asname)
                    # Also add the original name
                    imported_names.add(alias.name)

            # __all__ definitions
            elif (isinstance(node, ast.Assign) and
                  len(node.targets) == 1 and
                  isinstance(node.targets[0], ast.Name) and
                  node.targets[0].id == '__all__'):
                if isinstance(node.value, ast.List):
                    for elt in node.value.elts:
                        if isinstance(elt, ast.Str):
                            defined_names.add(elt.s)

        # Check all available names (both defined and imported)
        available_names = defined_names.union(imported_names)

        # Debug output
        # logger.info(f"\nDebug - File: {file_path}")
        # logger.info(f"Looking for: {names}")
        # logger.info(f"Defined names: {sorted(defined_names)}")
        # logger.info(f"Class methods: {sorted(class_methods)}")
        # logger.info(f"Imported names: {sorted(imported_names)}")

        # Special handling for class methods
        missing_items = []
        for name in names:
            if name not in available_names:
                # Check if it might be a class method
                if any(f"{class_name}.{name}" in defined_names for class_name in defined_names):
                    continue
                # Check if it's directly in class_methods
                if name in class_methods:
                    continue
                missing_items.append(name)

        if missing_items:
            return False, f"Items not found in module '{module_name}': {', '.join(missing_items)}"

        return True, None
    except Exception as e:
        return False, f"Error checking file {file_path}: {str(e)}"


def parse_python_errors(filename):
    result = create_result_structure()
    result["summary"]["total_files"] = 1

    def add_error(file_path, line_num, col_num, error_msg, context=""):
        if file_path not in result["errors"]:
            result["errors"][file_path] = []

        error = {
            "location": f"{line_num}:{col_num}",
            "error": error_msg,
            "context": context
        }

        result["errors"][file_path].append(error)
        result["summary"]["total_errors"] += 1

    try:
        with open(filename, 'r', encoding='utf-8') as file:
            source = file.read()
            source_lines = source.split('\n')
            tree = ast.parse(source)

        # Import check with detailed error reporting
        for node in ast.walk(tree):
            if isinstance(node, (ast.Import, ast.ImportFrom)):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        module_name = alias.name
                        success, error_msg = check_import(module_name, filename)
                        if not success:
                            context = source_lines[node.lineno - 1].strip()
                            add_error(filename, node.lineno, node.col_offset + 1,
                                      f"Import Error: {error_msg}",
                                      context)

                elif isinstance(node, ast.ImportFrom):
                    if node.level > 0:  # Relative import
                        continue  # Skip relative imports as they're handled differently
                    module_name = node.module if node.module else ''
                    if module_name:
                        # First check if the module itself exists
                        success, error_msg = check_import(module_name, filename)
                        if not success:
                            context = source_lines[node.lineno - 1].strip()
                            add_error(filename, node.lineno, node.col_offset + 1,
                                      f"Import Error: {error_msg}",
                                      context)
                        else:
                            # Then check if the specific imported items exist
                            imported_names = [alias.name for alias in node.names]
                            success, error_msg = check_imported_items(module_name, imported_names, filename)
                            if not success:
                                context = source_lines[node.lineno - 1].strip()
                                add_error(filename, node.lineno, node.col_offset + 1,
                                          f"Import Error: {error_msg}",
                                          context)

        # Logic check (pyflakes) with focus on current file
        stdout = sys.stdout
        stderr = sys.stderr
        sys.stdout = io.StringIO()
        sys.stderr = io.StringIO()
        try:
            checkPath(filename)
            logic_errors = sys.stdout.getvalue()
        finally:
            sys.stdout = stdout
            sys.stderr = stderr

        for line in logic_errors.splitlines():
            if filename in line and ':' in line:
                try:
                    parts = line.split(':')
                    if len(parts) >= 4:
                        file_path = parts[0]
                        # Only process errors from the current file
                        if os.path.abspath(file_path) != os.path.abspath(filename):
                            continue

                        line_num = int(parts[1])
                        col_num = int(parts[2])
                        error_msg = ':'.join(parts[3:]).strip()

                        # Skip import-related errors from pyflakes as we handle them above
                        if (not any(skip in error_msg.lower() for skip in
                                    ['undefined name', 'unable to detect',
                                     'imported but unused', 'unable to import'])):
                            context = source_lines[line_num - 1].strip() if line_num <= len(source_lines) else ""
                            add_error(filename, line_num, col_num,
                                      f"Logic Error: {error_msg}",
                                      context)
                except (ValueError, IndexError):
                    continue

    except SyntaxError as e:
        context = source_lines[e.lineno - 1].strip() if e.lineno <= len(source_lines) else ""
        add_error(filename, e.lineno, e.offset or 1, f"Syntax Error: {e.msg}", context)
    except Exception as e:
        logger.info(f'File Error: Could not read or parse file {filename}: {str(e)}')

    return result


def analyze_folder(folder_path, remove_unused=False):
    """
    Recursively analyze all Python files in the given folder and its subfolders.

    Args:
        folder_path: Path to the folder to analyze
        remove_unused: If True, remove unused imports from files

    Returns:
        dict: Combined result dictionary
    """
    combined_result = create_result_structure()
    folder_path = Path(folder_path)
    added_paths = add_package_paths(folder_path)

    try:
        for python_file in folder_path.rglob("*.py"):
            try:
                if "venv" in str(python_file) or ".env" in str(python_file):
                    continue

                logger.info(f"Analyzing: {python_file}")

                # Remove unused imports if requested
                if remove_unused:
                    changes_made, error = remove_unused_imports(
                        str(python_file),
                        dry_run=False  # Set to True for testing first
                    )
                    if changes_made:
                        logger.info(f"Removed unused imports from: {python_file}")
                    if error:
                        logger.info(f"Error removing unused imports from {python_file}: {error}")

                result = parse_python_errors(str(python_file))

                combined_result["errors"].update(result["errors"])
                combined_result["summary"]["total_errors"] += result["summary"]["total_errors"]
                combined_result["summary"]["total_files"] += 1

            except Exception as e:
                logger.error(f"Error processing {python_file}: {str(e)}")
                continue

    finally:
        for path in added_paths:
            if path in sys.path:
                sys.path.remove(path)

    return combined_result


def add_package_paths(folder_path):
    """
    Recursively find all directories containing __init__.py files
    and add them to sys.path. Handles nested src directories.
    Returns a list of added paths.
    """
    added_paths = []
    folder_path = Path(folder_path)

    # First, find the innermost src directory
    inner_src = folder_path
    while (inner_src / 'src').exists():
        inner_src = inner_src / 'src'

    # Add the innermost src directory first
    if str(inner_src.absolute()) not in sys.path:
        sys.path.insert(0, str(inner_src.absolute()))
        added_paths.append(str(inner_src.absolute()))

    # Add all parent directories up to the original folder_path
    current = inner_src
    while current != folder_path.parent:
        if str(current.absolute()) not in sys.path:
            sys.path.insert(0, str(current.absolute()))
            added_paths.append(str(current.absolute()))
        current = current.parent

    # Find all directories containing __init__.py
    for init_file in inner_src.rglob("__init__.py"):
        package_dir = str(init_file.parent.absolute())
        if (package_dir not in sys.path and
            "venv" not in package_dir and
                ".env" not in package_dir):

            sys.path.insert(0, package_dir)
            added_paths.append(package_dir)

            # Add parent directories up to the inner_src
            current_dir = init_file.parent
            while str(current_dir) != str(inner_src):
                parent_dir = str(current_dir.parent.absolute())
                if parent_dir not in sys.path:
                    sys.path.insert(0, parent_dir)
                    added_paths.append(parent_dir)
                current_dir = current_dir.parent

    # logger.info the paths for debugging
    logger.info("\nAdded Python paths:")
    for path in added_paths:
        logger.info(f"  - {path}")

    return added_paths


def remove_unused_imports(file_path, dry_run=True):
    """
    Remove unused imports from a Python file using autoflake.

    Args:
        file_path (str): Path to the Python file
        dry_run (bool): If True, only show what would be changed without making changes

    Returns:
        tuple: (bool, str) - (Whether changes were made/would be made, Error message if any)
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            source = file.read()

        # Configure autoflake options
        modified_source = autoflake.fix_code(
            source,
            remove_all_unused_imports=True,
            remove_unused_variables=True,
            remove_duplicate_keys=True
        )

        if source != modified_source:
            if not dry_run:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(modified_source)
            return True, None
        return False, None

    except Exception as e:
        return False, str(e)


def write_errors_to_file(result, output_file):
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2)


if __name__ == "__main__":
    logger.info(f"Analyzing folder: {FOLDER_PATH}")

    # Add a flag for removing unused imports
    REMOVE_UNUSED_IMPORTS = True  # Set to True to remove unused imports

    result = analyze_folder(FOLDER_PATH, remove_unused=REMOVE_UNUSED_IMPORTS)

    output_file = f'Code Structure - compiler output - {os.path.basename(FOLDER_PATH)}.json'
    write_errors_to_file(result, output_file)

    logger.info(f"\nAnalysis complete!")
    logger.info(f"Total files analyzed: {result['summary']['total_files']}")
    logger.info(f"Total errors found: {result['summary']['total_errors']}")
    logger.info(f"Results written to: {output_file}")
