import os

from blitzy_utils.logger import logger
from git import <PERSON><PERSON>

from blitzy_utils.github import get_user_secret_info
from local_config import USER_ID, SECRET_SERVER, REPO_NAME, LOCAL_PATH


def push_changes_to_branch():
    # Get user access token (reusing your existing authentication)
    access_token, _ = get_user_secret_info(user_id=USER_ID, server=SECRET_SERVER)
    if not access_token:
        raise Exception("Failed to get GitHub access token")

    try:
        # Initialize the local repository
        repo_path = os.path.join(LOCAL_PATH, REPO_NAME)
        repo = Repo(repo_path)

        # Stage all changes, including new files
        repo.git.add(A=True)  # 'A' flag ensures new files are included

        # Create commit with all staged changes
        repo.index.commit("Initial Setup")

        # Create and checkout new branch if it doesn't exist
        new_branch = f"blitzy-updates-{USER_ID}"

        if new_branch not in repo.refs:
            # Create new branch from current HEAD
            current = repo.active_branch
            new_ref = repo.create_head(new_branch, current.commit)
            repo.head.reference = new_ref

        # Checkout the branch
        repo.git.checkout(new_branch)

        try:
            # Pull with rebase from remote
            repo.git.pull('--rebase', 'origin', new_branch)
        except:
            # If branch doesn't exist remotely yet, this will fail - that's ok
            pass

        # Push to remote, setting upstream if it's a new branch
        repo.git.push('--set-upstream', 'origin', new_branch)

        logger.info(f"Successfully pushed changes to {new_branch}")

    except Exception as e:
        logger.info(f"An error occurred: {e}")
        raise


if __name__ == "__main__":
    push_changes_to_branch()
