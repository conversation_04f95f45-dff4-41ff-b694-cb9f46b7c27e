# Configure these values
PUBLIC_SAMPLES_USER_ID = "4428a4b5-0dd0-4734-8b95-f5dd81b702a4"
USER_ID = "b388177a-cc79-46eb-bf6f-6b7e1c63fde6"  # Replace with the user ID
REPO_NAME = "hao-backprop-test"  # Replace with the repository name
LOCAL_PATH = "./downloads"  # Local directory where repos will be cloned
PROJECT_ID = 'blitzy-os-dev'  # Dev: blitzy-os-dev, Prod: blitzy-platform-prod

DEV_GITHUB_SECRET_SERVER = "https://archie-secret-manager-464705070478.us-central1.run.app"
PROD_GITHUB_SECRET_SERVER = "https://archie-secret-manager-648803317587.us-east1.run.app"
SECRET_SERVER = DEV_GITHUB_SECRET_SERVER

FOLDER_NAME = 'backend'
FOLDER_PATH = f'{LOCAL_PATH}/{REPO_NAME}/src/{FOLDER_NAME}'
