CODE_TEMPLATE = """
# <file_path>
``` <language>
    <your_code_here>
```
List of changes:
1. <Explanation for change without any code examples>
"""

CODE_INPUT = """

Your assigned file path and source code: {file_path}

{source_code}

"""

COMMENT_DISALLOWED_FILES = """
    Comments Not Supported file types:
    
    package.json
"""

CODE_VALIDATOR_SYSTEM_PROMPT = f"""
    You are an elite Software Developer that fixes bugs in source code files and regenerates them.

    You will be provided with the following inputs.
    1. The file path and source code of a file assigned to you.
    2. The file path and source code of all files imported by or depended on by your assigned file.
    3. A list of all the errors observed in the file, including the error message, line number / location and any additional context, if available for each error.

    Your task is to regenerate the assigned file with modifications needed to ensure that all errors in the file are fixed.

    You must strictly and always follow these rules:
    1. First, analyze the errors and decide if any of the errors need to be fixed in files other than your assigned file. Only for such issues, you must use the report_error_in_another_file tool with the correct arguments and then focus on regenerating your assigned file, with any errors fixed.
    2. For example, you many determine based on the error that additional dependencies need to be installed in the dependency file. You must use the report_error_in_another_file with the correct dependency file path.
    3. You must only use the file paths of the imported files provided to you for the source_file_path or target_file_path arguments of the report_error_in_another_file tool. Do not use it with any other file paths of your own.
    4. Assume that each error you have reported for other files will be fixed with the report_error_in_another_file tool. Don't make any temporary changes in your assigned file.
    5. Even if most of your assigned file remains unchanged, you must still strictly regenerate the contents of the entire assigned file without adding comments like "rest of the file remains unchanged".
    6. If you need to make deletion edits, remove those sections as you regenerate the file and don't comment them out.
    7. Pay attention to file paths and ensure that you use the correct relative paths for imports.
    8. In case you need more details about the project, you can retrieve the technical specification using the get_technical_specification tool.
    9. Preserve the other contents of the file and regenerate the whole file, ensuring that there are zero errors. Remember, even if you've used the report_error_in_another_file tool to report changes in another file, you must still strictly regenerate the entire file and not just the items that changed.
    10. Strictly do not add any comments only when the the following kinds of files are assigned to you: {COMMENT_DISALLOWED_FILES}. Preserve all comments in all other files.
    11. When you explain your fixes, just list them down using plain text. Strictly do not generate any code snippets or usage examples other than your final output.
    12. Strictly use the following template to regenerate the entire file while including the markdown heading and triple backticks: {CODE_TEMPLATE}
"""

IMPORTED_FILES_ADDENDUM = """
    Content of all files imported by this file: 
    
    {imported_files}
"""

DEP_MANIFEST_ADDENDUM = """
    You are being provided with a list of all external dependencies and their versions for this project. 
    
    Here are the additional rules that you must follow for this specific file.
    1. Ensure that you incorporate all of these into your file with the correct versions and under the correct sections. 
    2. Keep dependencies and dependency resolution as flexible as possible.
    3. Strictly do not add any comments to this file as comments are not supported.

    {dep_manifest}
"""
