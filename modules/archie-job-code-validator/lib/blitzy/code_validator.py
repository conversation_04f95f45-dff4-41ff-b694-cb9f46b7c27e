import json
from typing import TypedDict, Dict, Any, Literal, List

from blitzy_utils.logger import logger
from langgraph.graph import START, StateGraph, END
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.tools import tool
from langgraph.prebuilt import ToolNode
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage, ChatMessage
from ssl import SSLError
from tenacity import retry, retry_if_exception_type, stop_after_attempt, wait_fixed
from anthropic import InternalServerError as AnthropicInternalServerError
from openai import InternalServerError as OpenAIInternalServerError
from github import InputGitTreeElement
from github.GitCommit import GitCommit

from lib.blitzy.prompts import CODE_VALIDATOR_SYSTEM_PROMPT, IMPORTED_FILES_ADDENDUM, CODE_INPUT, DEP_MANIFEST_ADDENDUM
from blitzy_utils.consts import DEFAULT_SLEEP_TIME, DEFAULT_MAX_RETRIES, DEP<PERSON>DENCY_FILES
from blitzy_utils.common import get_json_content, upload_to_gcs, DictFileHelper, FileContentHelper, download_from_gcs
from blitzy_utils.github import get_github_repo as get_repo


class CodeValidatorState(TypedDict):
    files_list: List[str]
    folder_files_list: List[str]
    file_dict: Dict[str, Dict[str, Any]]
    file_spec_dict: Dict[str, Dict[str, Any]]
    index: int
    is_retry: bool
    src_folder_name: str
    error_dict: Dict[str, Dict[str, Any]]
    dep_dict: Dict[str, Dict[str, str]]


code_files_dict_helper = DictFileHelper(files={})
code_spec_dict_helper = DictFileHelper(files={})
error_dict_helper = DictFileHelper(files={})
tech_spec_helper = FileContentHelper(file="")


def reset_dict(dict: Dict[Any, Any]):
    dict = {}
    return dict


@tool("get_technical_specification", parse_docstring=True)
def get_technical_specification():
    """
        Retrieves the contents of the technical specification document.
        Useful for retrieving additional context about the project like design, architecture, technologies, requirements, and other specifications.
    """
    logger.info('retrieved technical specification')
    content = tech_spec_helper.get_file()
    return content if content else ""


@tool("report_error_in_another_file", parse_docstring=True)
def report_error_in_another_file(source_file_path: str, target_file_path: str, error_message: str, location: str,
                                 context: str):
    """
        Records an error in target file that was detected in source_file.
        Useful for tracking errors that cannot be fixed in the file at source_file_path but must be fixed in the file at target_file_path.

        Args:
            source_file_path: Your assigned file path, where the error was found, must always be a valid file path
            target_file_path: Imported file path that actually contains the error, must always be a valid file path and different from source_file_path
            error_message: Error message seen in the file
            location: Location of the error message within the file
            context: Any additional information about the error that would help the agent responsible for fixing it
    """
    error_info = {
        "target_file_path": target_file_path,
        "error": error_message,
        "location": location,
        "context": context
    }
    error_dict = error_dict_helper.get_dict()
    errors_list = error_dict.get(target_file_path, [])
    errors_list.append(error_info)
    error_dict[target_file_path] = errors_list
    logger.info(f'error info added for target {target_file_path} error: {json.dumps(error_info)}')
    error_dict_helper.add_pending_info(source_file_path, errors_list.copy())


def get_file_implementation(imported_file_path: str):
    """
        Attempts to retrieve the implementation of a potential dependency file within a GitHub repo. 
        Useful for understanding the implementation of potential dependency files, when those file paths are queried using imported_file_path.

        Args:
            imported_file_path: A file path in the GitHub repo which might be imported inside the assigned_file_path file.
            assigned_file_path: The file path from the GitHub repo that you are currently investigating, which might import file_path file.
    """
    implementation_files_dict = code_files_dict_helper.get_dict()
    file = implementation_files_dict.get(imported_file_path)
    if file:
        logger.info(f'retrieved existing file implementation: {imported_file_path}')
        # logger.info(file)
        return file
    logger.info(f'invalid file path: {imported_file_path}')
    return ""


def get_file_implementation_internal(imported_file_path: str):
    """
        Attempts to retrieve the implementation of a potential dependency file within a GitHub repo. 
        Useful for understanding the implementation of potential dependency files, when those file paths are queried using imported_file_path.

        Args:
            imported_file_path: A file path in the GitHub repo which might be imported inside the assigned_file_path file.
            assigned_file_path: The file path from the GitHub repo that you are currently investigating, which might import file_path file.
    """
    implementation_files_dict = code_files_dict_helper.get_dict()
    file = implementation_files_dict.get(imported_file_path)
    if file:
        logger.info(f'retrieved existing file implementation internally: {imported_file_path}')
        # logger.info(file)
        return file
    logger.info(f'file implementation does not exist yet: {imported_file_path}, returning specification')
    spec_files_dict = code_spec_dict_helper.get_dict()
    # logger.info(imported_file_path)
    return spec_files_dict.get(imported_file_path)


code_tools = [get_technical_specification, report_error_in_another_file]
code_tools_node = ToolNode(code_tools)


class CodeValidatorHelper:
    def __init__(
            self,
            validator_llm: BaseChatModel,
            repo_name: str,
            blob_name: str,
            bucket_name: str,
            storage_client,
            file_spec_dict: Dict[str, Dict[str, Any]],
            error_dict: Dict[str, Dict[str, Any]],
            tech_spec: str,
            source_mode: str,
            github_secret_server: str,
            user_id: str):
        self.validator_llm = validator_llm
        self.repo_name = repo_name
        self.blob_name = blob_name
        self.bucket_name = bucket_name
        self.storage_client = storage_client
        self.source_mode = source_mode
        self.github_branch_name = f"blitzy-updates-{user_id}"
        self.github_commits: List[GitCommit] = []  # To track commits for PR
        self.github_secret_server = github_secret_server
        self.user_id = user_id

        self.code_spec_dict_helper = code_spec_dict_helper
        self.code_spec_dict_helper.set_dict(file_spec_dict)
        self.code_files_dict_helper = code_files_dict_helper
        self.tech_spec_helper = tech_spec_helper
        self.tech_spec_helper.set_file(file=tech_spec)
        self.error_dict_helper = error_dict_helper
        self.error_dict_helper.set_dict(error_dict)

    def create_graph(self) -> StateGraph:
        # Define the graph
        code_validator = StateGraph(CodeValidatorState)

        # Add nodes
        code_validator.add_node("process_file", self.process_file)
        code_validator.add_node("prepare_files_list", self.prepare_files_list)

        # Set the entry point
        code_validator.add_edge(START, "prepare_files_list")
        code_validator.add_edge("prepare_files_list", "process_file")

        # Add edges
        code_validator.add_conditional_edges(
            "process_file",
            self.router,
            {
                "continue": "process_file",
                "end": END
            }
        )

        return code_validator

    def router(self, state: CodeValidatorState) -> Literal["continue", "end"]:
        files = state["files_list"]
        if state["index"] >= len(files):
            self.create_pull_request()
            return "end"
        return "continue"

    def prepare_files_list(self, state: CodeValidatorState) -> Dict[str, Any]:
        state["files_list"] = list(filter(lambda f: f.startswith(
            f'src/{state["src_folder_name"]}/'), state["files_list"]))

        state["folder_files_list"] = state["files_list"].copy()

        combined_files = []
        error_dict_list = list(state["error_dict"].keys())
        # logger.info(error_dict_list)
        for file_path in state["files_list"]:
            filename = file_path.split('/')[-1]
            if file_path in error_dict_list or filename in DEPENDENCY_FILES:
                combined_files.append(file_path)

        state["files_list"] = combined_files

        if self.source_mode == "gcp":
            self.handle_gcp_files(state)
        else:
            self.handle_github_files(state)

        return {
            "index": state["index"],
            "file_dict": state["file_dict"],
            "file_spec_dict": state["file_spec_dict"],
            "dep_dict": state["dep_dict"],
            "is_retry": state["is_retry"],
            "files_list": state["files_list"],
            "folder_files_list": state["folder_files_list"],
            "src_folder_name": state["src_folder_name"],
            "error_dict": state["error_dict"]
        }

    def handle_gcp_files(self, state: CodeValidatorState):
        for file_path in state["files_list"]:
            content = download_from_gcs(
                self.storage_client,
                self.bucket_name,
                file_path,
                blob_name=self.blob_name
            )
            state["file_dict"][file_path] = content
            self.code_files_dict_helper.files_dict[file_path] = content

            self.handle_dependencies(state, file_path)

    def handle_dependencies(self, state: CodeValidatorState, file_path: str, processed_deps=None):
        """
        Recursively handle dependencies for a given file.

        Args:
            state: CodeValidatorState object containing the validation state
            file_path: Path of the file whose dependencies need to be processed
            processed_deps: Set to track processed dependencies to avoid circular dependencies
        """
        if processed_deps is None:
            processed_deps = set()

        # Skip if this dependency has already been processed
        if file_path in processed_deps:
            return

        processed_deps.add(file_path)

        file_spec = state["file_spec_dict"].get(file_path)
        if not file_spec:
            logger.info(f"Warning: No file specification found for {file_path}")
            return

        if 'imports' in file_spec and 'internal' in file_spec['imports']:
            for dep in file_spec['imports']['internal']:
                dep_path = dep['path']

                # Download dependency if not already in file_dict
                if dep_path not in state["file_dict"]:
                    try:
                        dep_content = self.download_dependency(dep_path)
                        if dep_content:
                            state["file_dict"][dep_path] = dep_content
                            self.code_files_dict_helper.files_dict[dep_path] = dep_content

                            # Get spec for the dependency
                            if dep_path in state["file_spec_dict"]:
                                # Recursively handle dependencies of this dependency
                                self.handle_dependencies(state, dep_path, processed_deps)
                    except Exception as e:
                        logger.warning(f"Error processing dependency {dep_path}: {e}")
                        continue
                else:
                    logger.info(f'dependency {dep_path} has already been downloaded')

    def download_dependency(self, dep_path: str) -> str:
        """
        Download a dependency file from either GCP or GitHub.

        Args:
            dep_path: Path of the dependency file

        Returns:
            str: Content of the dependency file

        Raises:
            Exception: If download fails
        """
        try:
            if self.source_mode == "gcp":
                return download_from_gcs(
                    self.storage_client,
                    self.bucket_name,
                    dep_path,
                    blob_name=self.blob_name
                )
            else:
                repo = self.get_github_repo()
                logger.info(f'downloading dependency file from github: {dep_path}')
                return repo.get_contents(dep_path).decoded_content.decode('utf-8')

        except Exception as e:
            logger.error(f"Error downloading dependency {dep_path}: {e}")
            raise

    def handle_github_files(self, state: CodeValidatorState):
        repo = self.get_github_repo()

        for file_path in state["files_list"]:
            try:
                github_path = file_path
                logger.info(f'dowloading file from github: {github_path}')
                file_content = repo.get_contents(path=github_path).decoded_content.decode('utf-8')

                state["file_dict"][file_path] = file_content
                self.code_files_dict_helper.files_dict[file_path] = file_content

                self.handle_dependencies(state, file_path)

            except Exception as e:
                logger.error(f"Error downloading file {file_path} from GitHub: {str(e)}")

    def get_github_repo(self):
        repo, _ = get_repo(self.repo_name, self.user_id, self.github_secret_server, False)
        return repo

    @retry(
        retry=retry_if_exception_type(
            (
                    AnthropicInternalServerError,
                    OpenAIInternalServerError,
                    SSLError
            )
        ),
        stop=stop_after_attempt(DEFAULT_MAX_RETRIES),
        wait=wait_fixed(DEFAULT_SLEEP_TIME)
    )
    def process_file(self, state: CodeValidatorState) -> Dict[str, Any]:
        file_path = state["files_list"][state["index"]]

        logger.info(f"validating code for file: {file_path}")

        file_spec = state["file_spec_dict"][file_path]

        imported_files, _ = self.get_imported_files(file_spec=file_spec, state=state)

        if not state["file_dict"].get(file_path, None):
            dep_content = self.download_dependency(file_path)
            if dep_content:
                state["file_dict"][file_path] = dep_content
                self.code_files_dict_helper.files_dict[file_path] = dep_content

        # logger.info(f'imported files: {imported_files}')
        if file_path in state["error_dict"]:
            errors = json.dumps(state["error_dict"][file_path])

            # logger.info(f'errors: {errors}')

            messages = [
                SystemMessage(
                    content=[
                        {
                            "type": "text",
                            "text": CODE_VALIDATOR_SYSTEM_PROMPT
                        }
                    ]
                )
            ]

            file_parts = file_path.split('/')
            filename = file_parts[-1]
            if filename in DEPENDENCY_FILES:
                subfolder_name = file_parts[1]
                dep_manifest = state["dep_dict"].get(subfolder_name)
                logger.info(f'including addendum for dep file {filename} in subfolder {subfolder_name}')
                messages.append(
                    HumanMessage(
                        content=[
                            {
                                "type": "text",
                                "text": CODE_INPUT.format(file_path=file_path,
                                                          source_code=state["file_dict"][file_path])
                            },
                            {
                                "type": "text",
                                "text": "Errors in your assigned file: {errors}".format(errors=errors)
                            },
                            {
                                "type": "text",
                                "text": IMPORTED_FILES_ADDENDUM.format(imported_files=imported_files)
                            },
                            {
                                "type": "text",
                                "text": DEP_MANIFEST_ADDENDUM.format(dep_manifest=json.dumps(dep_manifest)),
                                "cache_control": {"type": "ephemeral"}
                            }
                        ]
                    )
                )
            else:
                messages.append(
                    HumanMessage(
                        content=[
                            {
                                "type": "text",
                                "text": CODE_INPUT.format(file_path=file_path,
                                                          source_code=state["file_dict"][file_path])
                            },
                            {
                                "type": "text",
                                "text": "Errors in your assigned file: {errors}".format(errors=errors)
                            },
                            {
                                "type": "text",
                                "text": IMPORTED_FILES_ADDENDUM.format(imported_files=imported_files)
                            }
                        ]
                    )
                )
            response: AIMessage = self.validator_llm.invoke(messages)

            # logger.info(response)

            while len(response.tool_calls):
                # fill in the value of local variables
                messages.append(response)
                tools_response: List[ChatMessage] = code_tools_node.invoke([response])
                # logger.info(tools_response)
                messages += tools_response

                # logger.info(f'tools reponse: {tools_response}')
                # logger.info(f'sending tool response back to llm for file: {file_path}')

                # logger.info(f'messages: {messages}')
                response: AIMessage = self.validator_llm.invoke(messages)

            # logger.info(response.content)

            content = ""

            try:
                content = get_json_content(response.content)
                if content == "":
                    content = state["file_dict"][file_path]

                state["file_dict"][file_path] = content
                self.code_files_dict_helper.files_dict[file_path] = content

                if self.error_dict_helper.pending_info.get(file_path, None):
                    new_error_list = self.error_dict_helper.pending_info.pop(file_path)
                    for new_error_info in new_error_list:
                        target_file_path = new_error_info["target_file_path"]
                        # last index
                        target_file_in_list = target_file_path in state["files_list"]
                        file_added = False
                        if target_file_in_list:
                            reversed_index = list(reversed(state["files_list"])).index(target_file_path)
                            last_index = len(state["files_list"]) - reversed_index - 1
                        if not target_file_in_list or state["index"] > last_index:
                            if target_file_path in state["folder_files_list"]:
                                file_added = True
                                state["files_list"].append(target_file_path)
                                logger.info(f'added {target_file_path} to validation files list')
                            else:
                                logger.info(
                                    f'file path f{target_file_path} does not exist in original files list, ignoring')
                        if file_added:
                            errors_list = state["error_dict"].get(target_file_path, [])
                            errors_list.append(new_error_info)
                            self.error_dict_helper.files_dict[target_file_path] = errors_list.copy()
                            logger.info(f'added new error info for file {target_file_path}')

                # Assume previously reported errors are fixed
                state["error_dict"][file_path] = []
                self.error_dict_helper.files_dict[file_path] = []

                # logger.info(content)
                # logger.info(state["error_dict"])

                logger.info(f'validated code for file: {file_path}')

            except Exception as e:
                if not state["is_retry"]:
                    logger.info(f'failed to validate code {file_path} due to error {e}, retrying once.')
                    state["is_retry"] = True
                    return {
                        "index": state["index"],
                        "file_dict": state["file_dict"],
                        "file_spec_dict": state["file_spec_dict"],
                        "dep_dict": state["dep_dict"],
                        "is_retry": state["is_retry"],
                        "files_list": state["files_list"],
                        "folder_files_list": state["folder_files_list"],
                        "src_folder_name": state["src_folder_name"],
                        "error_dict": state["error_dict"]
                    }
                else:
                    logger.info(f'failed to validate code {file_path} due to error {e}, skipping.')

            if not content:
                content = state["file_dict"][file_path]
                self.code_files_dict_helper.files_dict[file_path] = content

            if self.source_mode == "gcp":
                blob_name = f"{self.blob_name}"
                upload_to_gcs(
                    storage_client=self.storage_client,
                    bucket_name=self.bucket_name,
                    blob_name=blob_name,
                    filename=file_path,
                    data=content
                )
            else:
                # Create commit for GitHub
                self.create_github_commit(file_path, content)
        else:
            logger.info(f'file {file_path} not found in error dict')

        # Update the state
        next_index = state["index"] + 1
        state["index"] = next_index
        state["is_retry"] = False

        return {
            "index": state["index"],
            "file_dict": state["file_dict"],
            "file_spec_dict": state["file_spec_dict"],
            "dep_dict": state["dep_dict"],
            "is_retry": state["is_retry"],
            "files_list": state["files_list"],
            "folder_files_list": state["folder_files_list"],
            "src_folder_name": state["src_folder_name"],
            "error_dict": state["error_dict"]
        }

    def get_imported_files(self, file_spec, state: CodeValidatorState, processed_files=None):
        """
        Recursively get all imported files and their dependencies.

        Args:
            file_spec: File specification dictionary
            processed_files: Set to track processed files to avoid circular dependencies

        Returns:
            tuple: (imported_files_string, imported_files_list)
        """
        if processed_files is None:
            processed_files = set()

        imported_files = ""
        imported_files_list = []

        if 'imports' in file_spec and 'internal' in file_spec['imports']:
            for dep in file_spec['imports']['internal']:
                dep_path = dep['path']

                # Skip if already processed
                if dep_path in processed_files:
                    continue

                processed_files.add(dep_path)

                if dep_path not in imported_files_list:
                    imported_files_list.append(dep_path)
                    file_implementation = get_file_implementation_internal(dep_path)
                    imported_files += f"file path: {dep_path} \n\n{json.dumps(file_implementation)}\n\n"

                    # Get dependency's file spec
                    dep_file_spec = state["file_spec_dict"].get(dep_path)
                    if dep_file_spec:
                        # Recursively get dependencies of this dependency
                        recursive_imports, recursive_list = self.get_imported_files(
                            dep_file_spec,
                            state,
                            processed_files
                        )
                        imported_files += recursive_imports
                        imported_files_list.extend([f for f in recursive_list if f not in imported_files_list])

        return imported_files, imported_files_list

    def create_github_commit(self, file_path: str, content: str):
        """Create a commit for a single file change and update branch."""
        try:
            repo = self.get_github_repo()

            # Get the default branch
            default_branch = repo.default_branch

            try:
                # Try to get the branch reference
                branch_ref = repo.get_git_ref(f"heads/{self.github_branch_name}")
            except Exception:
                # Branch doesn't exist, create it from default branch
                default_ref = repo.get_git_ref(f"heads/{default_branch}")
                repo.create_git_ref(
                    ref=f"refs/heads/{self.github_branch_name}",
                    sha=default_ref.object.sha
                )
                branch_ref = repo.get_git_ref(f"heads/{self.github_branch_name}")

            # Get the latest commit on the branch
            base_tree = repo.get_git_tree(branch_ref.object.sha)

            # Create blob
            blob = repo.create_git_blob(content, "utf-8")

            # Create tree
            element = InputGitTreeElement(
                path=file_path,
                mode='100644',
                type='blob',
                sha=blob.sha
            )
            tree = repo.create_git_tree([element], base_tree)

            # Create commit
            parent = repo.get_git_commit(branch_ref.object.sha)
            commit = repo.create_git_commit(
                f"BugFix: Automated bug fixes for {file_path}",
                tree,
                [parent]
            )

            # Update branch reference to point to the new commit
            branch_ref.edit(commit.sha)

            self.github_commits.append(commit)
            logger.info(f"Created commit for {file_path}: {commit.sha}")

        except Exception as e:
            logger.error(f"Error creating GitHub commit for {file_path}: {str(e)}")

    def create_pull_request(self):
        if not self.github_commits:
            return

        try:
            repo = self.get_github_repo()

            # Check if PR already exists
            existing_prs = repo.get_pulls(
                state='open',
                head=f"{repo.owner.login}:{self.github_branch_name}"
            )

            # Update existing PR if it exists
            for pr in existing_prs:
                logger.info(f"Using existing PR #{pr.number}: {pr.html_url}")
                return

            # Create new PR if none exists
            pr_body = "This PR contains automated fixes created by Blitzy for the following files:\n\n"
            for commit in self.github_commits:
                pr_body += f"* {commit.message}\n"

            pr = repo.create_pull(
                title=f"Automated Code Fixes by Blitzy",
                body=pr_body,
                head=self.github_branch_name,
                base=repo.default_branch
            )

            logger.info(f"Created new PR #{pr.number}: {pr.html_url}")

        except Exception as e:
            logger.error(f"Error handling pull request: {str(e)}")
