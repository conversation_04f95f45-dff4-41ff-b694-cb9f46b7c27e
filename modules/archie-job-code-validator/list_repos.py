from github import Github

from blitzy_utils.github import get_user_secret_info, get_github_installations
from local_config import USER_ID, SECRET_SERVER


def list_repos():
    # Get user access token
    access_token, installation_id = get_user_secret_info(user_id=USER_ID, server=SECRET_SERVER)
    if not access_token:
        raise Exception("Failed to get GitHub access token")

    # Initialize GitHub client
    g = Github(access_token)
    installations = get_github_installations(access_token=access_token)

    # Find the correct installation
    login_name = ''
    for installation in installations:
        if str(installation['id']) == installation_id:
            login_name = installation['account']['login']
            break

    if not login_name:
        raise Exception("Could not find matching GitHub installation")

    for repo in g.get_user().get_repos():
        print(repo.name)


if __name__ == "__main__":
    list_repos()
