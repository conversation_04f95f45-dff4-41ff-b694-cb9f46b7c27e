from blitzy_utils.common import publish_notification
from google.cloud import pubsub_v1

from local_config import PROJECT_ID, USER_ID, REPO_NAME, FOLDER_NAME

VALIDATE_CODE_TOPIC = 'validate-code'

publisher = pubsub_v1.PublisherClient()

notification_data = {
    'repo_name': REPO_NAME,
    'folder_name': FOLDER_NAME,
    'user_id': USER_ID,
    'source_mode': 'github'  # do not edit unless you know what you are doing
}
publish_notification(publisher, notification_data, PROJECT_ID, VALIDATE_CODE_TOPIC)
