# archie-job-code-validator

## Prerequisites
### 1. Install Anaconda
https://docs.anaconda.com/anaconda/install/

### 2. Activate a new environment
Refer to google or chatbots for instructions

conda create --name validator
conda activate validator

### 3. Install dependencies
Ensure that you have python 3.11 or higher installed: https://www.python.org/downloads/macos/

Then, sync this GitHub repo locally and run the following command
pip install -r requirements.txt

### 4. Setup authentication with Blitzy

#### 4.0 Login with GCloud (must have certain access rights)
Install the gcloud cli, then run the following command
gcloud auth application-default login

Gcloud cli installation instructions: https://cloud.google.com/sdk/docs/install#mac

#### 4.1 Create service account key (IMP: run this within the folder of this README.md)
For Dev (if working with Blitzy Public Samples):
gcloud iam service-accounts keys create service-key-dev.json \
--iam-account=<EMAIL>

For Prod (if working with a Real user's repo):
gcloud iam service-accounts keys create service-key-prod.json \
--iam-account=<EMAIL>

#### 4.2 Point ADC to the correct set of credentils
Run the following command in Terminal

Depending on whether you are pushing to Blitzy Public Samples (dev) or a real user's repo (prod), run the correct command below.

Blitzy Public Samples:
export GOOGLE_APPLICATION_CREDENTIALS="$(pwd)/service-key-dev.json"

Real User:
export GOOGLE_APPLICATION_CREDENTIALS="$(pwd)/service-key-prod.json"

### 5. Compile the code manually and save the errors to a local file

Only TS is supported at the moment.

#### 5.1: Sync the code locally.

We need the repo_name and user_id. This is typically available from the Blitzy CLI or daily email reports. Ensure that the state of the CODE_GENRATION column for this project is "DONE" and that GitHub is still connected.

Then, update the repo_name and user_id in sync_repo.py

Finally, run it. Be sure to use the correct github server (dev vs prod) based on whether you are running the job on dev (Blitzy Public Samples) or prod (For a real user).
You can do this by importing the correct constant from local_config.py

python3 sync_repo.py

This will create a downloads/ folder and add the user's repo. From here on, whatever changes you make can be pushed to the user's repo directly if we authenticate correctly, so be very careful.

#### 5.2: Fix package.json

** This step only applies to jobs where package.json is not yet validated, I am writing this sub-step assuming developer-level intuition **

(Node must be installed on your mac)

Open the src/web folder within the synced repository and run "npm install" on package.json

Feed each error to Claude and fix package.json, such that "npm install" starts running correctly.

Do NOT use --legacy-peer-deps to short circuit the errors. Fix them cleanly.

#### 5.3: Ensure that tsconfig.json is configured correctly

** This step only applies to jobs where tsconfig.json is not yet validated, I am writing this sub-step assuming developer-level intuition **

Open tsconfig.json and ensure that all test files have been excluded from the compilation, and all source files are included correctly.
Next, ensure that noEmit is set to true.
Finally, ensure that all types are included correctly.

#### 5.4 Push your local changes to the target repo

** This step only applies to jobs where compiler is not yet automatically integrated, I am writing this sub-step assuming developer-level intuition **

IMPORTANT: First, be sure to update the correct user id and repo_name in local_config.py. It should be the same config that you used to sync the repo.

Next, run the following command
python3 push_local_changes.py

This should push your local changes to the repo so that agents can pick them up.

#### 5.5: Compile the code

** This step only applies to jobs where compiler is not yet automatically integrated, I am writing this sub-step assuming developer-level intuition **

Within the "src/web" folder containing package.json, ensure that package.json has a script "tsc" that simply runs "tsc"/

Then, execute the following command

"npm run tsc"

If the code has errors, these will now print to the console.

#### 5.5: Save the errors

** This step only applies to jobs where compiler is not yet automatically integrated, I am writing this sub-step assuming developer-level intuition **

Copy and paste the errors into output.txt, within the same folder as this README.md

IMPORTANT: Do not pipe the output from tsc, that will break the flow. You must copy and paste the output manually.

### 6. Format and upload the errors

#### 6.1: Transform errors into JSON

Once output.txt is available in the current folder (same as this README.md), then edit the tsc_result.py to set the correct folder_name (default is "web"). No change needed if you are working on the web folder.

Run tsc_result.py using "python3 tsc_result.py"

This will transform the errors into JSON and a format that agents understand.
It will generate a file "Code Structure - compiler output - <folder_name>.json"

#### 6.2: Upload the JSON file to Cloud Storage
 
Find and open your user's repository folder here: https://console.cloud.google.com/storage/browser/blitzy-platform-prod/public-samples?inv=1&invt=AbljUQ&project=blitzy-platform-prod&pageState=(%22StorageObjectListTable%22:(%22f%22:%22%255B%255D%22))

Then, upload the "Code Structure - compiler output - <folder_name>.json" inside the "documentation" folder within that repository folder

You are now ready to execute agent-driven validation. You will need to repeat step 5.4 to 6.2 for each run.

# include comment for rebase

### 7. Trigger the validation
Set the correct repo_name, folder_name (web or backend), and user_id within retry.test.py

Then, execute the following command to trigger a validation run
python3 retry.test.py

Always remember to use the correct environment (Dev vs Prod) till we implement proper environment variables.