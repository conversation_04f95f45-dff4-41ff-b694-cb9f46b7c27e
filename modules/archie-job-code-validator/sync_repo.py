import os
import shutil

from blitzy_utils.logger import logger
from git import Repo
from github import Github, GithubException

from blitzy_utils.github import get_user_secret_info, get_github_installations
from local_config import USER_ID, SECRET_SERVER, REPO_NAME, LOCAL_PATH


def sync_repo():
    # Get user access token
    access_token, installation_id = get_user_secret_info(user_id=USER_ID, server=SECRET_SERVER)
    if not access_token:
        raise Exception("Failed to get GitHub access token")

    # Initialize GitHub client
    g = Github(access_token)
    installations = get_github_installations(access_token=access_token)

    # Find the correct installation
    login_name = ''
    for installation in installations:
        if str(installation['id']) == installation_id:
            login_name = installation['account']['login']
            break

    if not login_name:
        raise Exception("Could not find matching GitHub installation")

    # Get the full repository name
    full_repo_name = f"{login_name}/{REPO_NAME}"

    try:
        # Get the repository
        repo = g.get_repo(full_repo_name)
        clone_url = repo.clone_url

        # Create local directory if it doesn't exist
        os.makedirs(LOCAL_PATH, exist_ok=True)
        repo_path = os.path.join(LOCAL_PATH, REPO_NAME)

        # Delete existing repository folder if it exists
        if os.path.exists(repo_path):
            logger.info(f"Removing existing repository folder at {repo_path}")
            shutil.rmtree(repo_path)

        # Clone the repository without submodules first
        print(f"Cloning {full_repo_name}")
        git_repo = Repo.clone_from(
            clone_url.replace('https://', f'https://{access_token}@'),
            repo_path,
            recurse_submodules=False  # Don't fetch submodules yet
        )

        # Check if .gitmodules exists
        gitmodules_path = os.path.join(repo_path, '.gitmodules')
        if os.path.exists(gitmodules_path):
            # Update submodule URLs to use HTTPS instead of SSH
            with open(gitmodules_path, 'r') as f:
                gitmodules_content = f.read()

            # Replace SSH URLs with HTTPS URLs
            updated_content = gitmodules_content.replace(
                '**************:',
                f'https://{access_token}@github.com/'
            )

            with open(gitmodules_path, 'w') as f:
                f.write(updated_content)

            # Now update submodules
            git_repo.git.submodule('update', '--init', '--recursive')

        print(f"Repository synchronized successfully at {repo_path}")

    except GithubException as e:
        if e.status == 404:
            print(f"Repository not found. User has likely renamed it. Please retry with one of the following names.")
            for repo in g.get_user().get_repos():
                logger.info(repo.name)
        else:
            logger.info(f"An error occurred: {e}")
            raise
    except Exception as e:
        logger.error(f"An error occurred: {e}")
        raise


if __name__ == "__main__":
    sync_repo()
