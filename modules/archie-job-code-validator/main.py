import os
import json5
from typing import Dict, List
from google.cloud import storage, pubsub_v1
from langgraph.graph import StateGraph
from langchain_anthropic import Chat<PERSON>nthropic
from langchain_openai import ChatOpenAI

from lib.blitzy.code_validator import CodeValidatorHelper, CodeValidatorState, code_tools

from blitzy_utils.consts import CODE_STRUCTURE_NAME, TECH_SPECIFICATION_NAME, REPO_STRUCTURE_NAME, \
    DEPENDENCY_MANIFEST_NAME, COMPILER_OUTPUT_BASE
from blitzy_utils.common import publish_notification, download_from_gcs, upload_to_gcs
from blitzy_utils.enums import ProjectPhase, JobStatus

EVENT_DATA = os.environ["EVENT_DATA"]
PROJECT_ID = os.environ["PROJECT_ID"]
GCS_BUCKET_NAME = os.environ["GCS_BUCKET_NAME"]
BLOB_NAME = os.environ["BLOB_NAME"]
PLATFORM_EVENTS_TOPIC = os.environ["PLATFORM_EVENTS_TOPIC"]
ANTHROPIC_API_KEY = os.environ["ANTHROPIC_API_KEY"]
OPENAI_API_KEY = os.environ["OPENAI_API_KEY"]
LANGCHAIN_TRACING_V2 = os.environ["LANGCHAIN_TRACING_V2"]
LANGCHAIN_ENDPOINT = os.environ["LANGCHAIN_ENDPOINT"]
LANGCHAIN_API_KEY = os.environ["LANGCHAIN_API_KEY"]
LANGCHAIN_PROJECT = os.environ["LANGCHAIN_PROJECT"]
GITHUB_SECRET_SERVER = os.environ["GITHUB_SECRET_SERVER"]

storage_client = storage.Client()
publisher = pubsub_v1.PublisherClient()
llm_claude_3_5_sonnet = ChatAnthropic(
    model="claude-3-5-sonnet-20241022",
    max_tokens=8192,
    temperature=0,
    default_headers={
        "anthropic-beta": "prompt-caching-2024-07-31"
    },
    api_key=ANTHROPIC_API_KEY
).bind_tools(code_tools)

llm_gpt4o = ChatOpenAI(
    model="gpt-4o-2024-11-20",
    max_tokens=None,
    timeout=None,
    temperature=0.1,
    api_key=OPENAI_API_KEY
).bind_tools(code_tools)

llm_o1 = ChatOpenAI(
    model="o1-preview",
    max_tokens=None,
    timeout=None,
    temperature=1,
    api_key=OPENAI_API_KEY
)


def validate_code(event_data_str: str):
    event_data = json5.loads(event_data_str)
    repo_name = event_data.get('repo_name')
    src_subfolder_name = event_data.get('folder_name')
    source_mode = event_data.get('source_mode', 'github')
    project_id = event_data.get('project_id', '')
    job_id = event_data.get('job_id', '')
    user_id = event_data.get('user_id', '')
    propagate = event_data.get('propagate', True)

    notification_data = {
        "projectId": project_id,
        "jobId": job_id,
        "phase": ProjectPhase.VALIDATION.value,
        "status": JobStatus.IN_PROGRESS.value,
        "user_id": user_id,
        "metadata": {
            "propagate": propagate,
            "repo_name": repo_name
        }
    }
    publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)

    blob_name = f"{BLOB_NAME}/{repo_name}/blitzy/documentation"
    cs_filename = f"{CODE_STRUCTURE_NAME}.json"
    code_structure = download_from_gcs(
        storage_client=storage_client,
        bucket_name=GCS_BUCKET_NAME,
        filename=cs_filename,
        blob_name=blob_name
    )
    files: Dict = json5.loads(code_structure)

    repo_structure_filename = f'{REPO_STRUCTURE_NAME}.md'
    repo_files_str = download_from_gcs(
        storage_client,
        GCS_BUCKET_NAME,
        repo_structure_filename,
        blob_name=blob_name
    )
    repo_files_list: List[str] = json5.loads(repo_files_str)
    files_list = repo_files_list.copy()

    dep_dict_filename = f'{DEPENDENCY_MANIFEST_NAME}.json'
    dep_dict = json5.loads(download_from_gcs(
        storage_client,
        GCS_BUCKET_NAME,
        dep_dict_filename,
        blob_name=blob_name
    ))

    tech_spec_filename = f'{TECH_SPECIFICATION_NAME}.md'
    tech_spec = download_from_gcs(
        storage_client,
        GCS_BUCKET_NAME,
        tech_spec_filename,
        blob_name=blob_name
    )

    error_dict_filename = f'{COMPILER_OUTPUT_BASE} - {src_subfolder_name}.json'
    error_dict = json5.loads(download_from_gcs(
        storage_client,
        GCS_BUCKET_NAME,
        error_dict_filename,
        blob_name=blob_name
    ))

    code_helper = CodeValidatorHelper(
        validator_llm=llm_claude_3_5_sonnet,
        repo_name=repo_name,
        blob_name=f"{BLOB_NAME}/{repo_name}",
        bucket_name=GCS_BUCKET_NAME,
        storage_client=storage_client,
        file_spec_dict=files.copy(),
        tech_spec=tech_spec,
        error_dict=error_dict["errors"],
        source_mode=source_mode,
        github_secret_server=GITHUB_SECRET_SERVER,
        user_id=user_id
    )

    code_validator: StateGraph = code_helper.create_graph()
    app = code_validator.compile()

    index = 0

    initial_state = CodeValidatorState(
        file_spec_dict=files.copy(),
        file_dict={},
        dep_dict=dep_dict,
        files_list=files_list.copy(),
        folder_files_list=files_list.copy(),
        index=index,
        is_retry=False,
        src_folder_name=src_subfolder_name,
        error_dict=error_dict["errors"]
    )
    result = app.invoke(initial_state, {"recursion_limit": 10000})

    notification_data = {
        "projectId": project_id,
        "jobId": job_id,
        "phase": ProjectPhase.VALIDATION.value,
        "status": JobStatus.DONE.value,
        "user_id": user_id,
        "metadata": {
            "propagate": propagate,
            "repo_name": repo_name
        }
    }
    publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)

    return event_data


if __name__ == "__main__":
    print(f"Validating code for notification data: {EVENT_DATA}")
    validate_code(event_data_str=EVENT_DATA)
