import json
import re

from local_config import FOLDER_NAME


def parse_tsc_output(output, src_subfolder_name: str):
    result = {
        "errors": {},
        "summary": {
            "total_errors": 0,
            "total_files": 0
        }
    }

    error_pattern = r'^(.+?):(\d+):(\d+) - (error TS\d+:.+)$'
    tilde_pattern = r'^~+$'
    package_info_pattern = r'^> .+$'

    lines = output.split('\n')
    i = 0

    while i < len(lines):
        line = lines[i].strip()

        if not line or re.match(package_info_pattern, line):
            i += 1
            continue

        # Check for both summary formats
        if "Found" in line and ("errors in" in line or "in the same file" in line):
            if "in the same file" in line:
                # Handle the special case summary
                summary_match = re.match(r'Found (\d+) errors? in the same file', line)
                if summary_match:
                    result["summary"]["total_errors"] = int(summary_match.group(1))
                    result["summary"]["total_files"] = 1
            else:
                # Handle the standard summary
                summary_match = re.match(r'Found (\d+) errors? in (\d+) files?\.', line)
                if summary_match:
                    result["summary"]["total_errors"] = int(summary_match.group(1))
                    result["summary"]["total_files"] = int(summary_match.group(2))
            break

        match = re.match(error_pattern, line)
        if match:
            file_path, line_num, col_num, error_msg = match.groups()

            file_path = f'src/{src_subfolder_name}/' + file_path

            if file_path not in result["errors"]:
                result["errors"][file_path] = []

            # Collect all lines until the next error or summary
            context_lines = []
            j = i + 1
            while j < len(lines):
                next_line = lines[j].strip()
                if not next_line or re.match(package_info_pattern, next_line):
                    j += 1
                    continue
                if re.match(error_pattern, next_line) or "Found" in next_line:
                    break
                if not re.match(tilde_pattern, next_line):
                    context_lines.append(next_line)
                j += 1

            error = {
                "location": f"{line_num}:{col_num}",
                "error": error_msg,
                "context": "\n".join(context_lines) if context_lines else ""
            }

            result["errors"][file_path].append(error)
            i = j
        else:
            i += 1

    return result


def write_errors_to_file(result, output_file):
    with open(output_file, 'w') as f:
        f.write(json.dumps(result, indent=2))


# Example usage:
tsc_output = open('output.txt').read()
result = parse_tsc_output(tsc_output, FOLDER_NAME)
write_errors_to_file(result, f'Code Structure - compiler output - {FOLDER_NAME}.json')
