openapi: 3.0.0
info:
  title: Flask Microservice API
  version: 1.0.0
  description: Template API for Flask Microservices
  contact:
    email: <EMAIL>
x-google-endpoints:
  - name: <<your-service-name.endpoints.your-project.cloud.goog>>
    allowCors: true

x-google-backend:
  address: <<https://your-service-name-id.region.run.app>>
  path_translation: APPEND_PATH_TO_ADDRESS
  deadline: 30.0

paths:
  /health-check:
    get:
      tags:
        - System
      summary: Health check endpoint
      description: Checks if the service is running
      operationId: healthCheck
      responses:
        "200":
          description: Service is healthy
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Status200"
        "500":
          description: Service unhealthy
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Status500"

  /uptime-check:
    get:
      tags:
        - System
      summary: Uptime check endpoint
      description: Returns current timestamp for uptime monitoring
      operationId: uptimeCheck
      security:
        - jwt_auth: []
      responses:
        "200":
          description: Current timestamp
          content:
            application/json:
              schema:
                type: object
                properties:
                  accessed_ts:
                    type: string
                    format: date-time
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Status401"
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Status500"

components:
  securitySchemes:
    jwt_auth:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: ""
          scopes: {}
      x-google-issuer: <<<EMAIL>>>
      x-google-jwks_uri: <<https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>>>
      x-google-audiences: <<https://api.your-domain.com/v1>>

  schemas:
    Status200:
      type: object
      properties:
        message:
          type: string
          example: "OK"

    Status201:
      type: object
      properties:
        id:
          type: string
          example: "123e4567-e89b-12d3-a456-426614174000"
        message:
          type: string
          example: "Created Successfully"

    Status400:
      type: object
      properties:
        message:
          type: string
          example: "Bad Request"

    Status401:
      type: object
      properties:
        message:
          type: string
          example: "Unauthorized"

    Status403:
      type: object
      properties:
        message:
          type: string
          example: "Forbidden"

    Status404:
      type: object
      properties:
        message:
          type: string
          example: "Not Found"

    Status409:
      type: object
      properties:
        message:
          type: string
          example: "Conflict"

    Status500:
      type: object
      properties:
        message:
          type: string
          example: "Internal Server Error"

    ErrorResponse:
      type: object
      properties:
        code:
          type: string
          description: Error code
        message:
          type: string
          description: Error message
        details:
          type: object
          description: Additional error details

    SuccessResponse:
      type: object
      properties:
        data:
          type: object
          description: Response data
        message:
          type: string
          description: Success message
