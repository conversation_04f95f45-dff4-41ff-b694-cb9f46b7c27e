# Flask Microservice Template

A production-ready Flask microservice template with the following features:
- Structured API routing
- Swagger documentation
- Logging configuration
- Error handling
- Testing setup
- CI/CD configuration
- Docker support
- Environment configuration

## Using this template

1. Click "Use this template" button at the top of this repository
2. Clone your new repository
3. Update the following files with your service-specific configuration:
   - `swagger.yaml`
   - `env_config/env-dev.yaml`
   - `service.yaml`
   - `requirements.txt`
4. Update package names in `src/` directory
5. Remove example routes and services you don't need

## Please replace the values enclosed with <<<value>>> with your value.
