import signal
import sys
from types import FrameType

from flask import Flask
from flask_cors import CORS
from blitzy_utils.logger import logger
from flask_utils.middleware.request_context import RequestContextMiddleware


from src.api.routes.utils import utils_bp
from src.consts import (
    PORT,
    AUTHORIZATION_HEADER_NAME,
    ID_TOKEN_HEADER_NAME,
    BASIC_AUTH_HEADER_NAME,
)

# Initialize Flask app
app = Flask(__name__)

# Configure CORS
CORS(app, resources={r"/*": {"origins": "*", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
                             "allow_headers": ["Content-Type", AUTHORIZATION_HEADER_NAME, ID_TOKEN_HEADER_NAME, BASIC_AUTH_HEADER_NAME]}})

# Register blueprints
app.register_blueprint(utils_bp)  # Basic health check routes

# Add your additional blueprints here
# Example:
# from src.api.routes.your_route import your_bp
# app.register_blueprint(your_bp)

middleware = RequestContextMiddleware(app, logger=logger)


def shutdown_handler(signal_int: int, frame: FrameType) -> None:
    """Handle shutdown signals gracefully."""
    logger.info(f"Caught Signal {signal.strsignal(signal_int)}")
    sys.exit(0)


def initialize_app() -> None:
    """Initialize application dependencies."""
    # Add your initialization code here
    # Examples:
    # - Database connections
    # - Cache clients
    # - External service clients
    logger.info("Initializing application dependencies...")
    pass


def configure_error_handlers(app: Flask) -> None:
    """Configure application error handlers."""
    @app.errorhandler(400)
    def bad_request(error):
        return {"message": "Bad Request"}, 400

    @app.errorhandler(401)
    def unauthorized(error):
        return {"message": "Unauthorized"}, 401

    @app.errorhandler(403)
    def forbidden(error):
        return {"message": "Forbidden"}, 403

    @app.errorhandler(404)
    def not_found(error):
        return {"message": "Not Found"}, 404

    @app.errorhandler(500)
    def internal_server_error(error):
        logger.error(f"Internal Server Error: {error}")
        return {"message": "Internal Server Error"}, 500


if __name__ == "__main__":
    # Running application locally
    logger.info("Starting service in local development mode...")
    
    # Initialize dependencies
    initialize_app()
    
    # Configure error handlers
    configure_error_handlers(app)
    
    # Handle Ctrl-C termination
    signal.signal(signal.SIGINT, shutdown_handler)
    
    # Run the application
    app.run(
        host="localhost",
        port=PORT,
        debug=True
    )
else:
    # Running in production (e.g., Cloud Run)
    logger.info("Starting service in production mode...")
    
    # Initialize dependencies
    initialize_app()
    
    # Configure error handlers
    configure_error_handlers(app)
    
    # Handle Cloud Run container termination
    signal.signal(signal.SIGTERM, shutdown_handler)