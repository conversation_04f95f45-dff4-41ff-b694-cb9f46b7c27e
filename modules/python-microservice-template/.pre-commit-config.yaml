repos:
- repo: https://github.com/pre-commit/pre-commit-hooks
  rev: v4.5.0
  hooks:
  - id: trailing-whitespace
  - id: end-of-file-fixer
  - id: check-yaml
    exclude: |
      (?x)^(
        service\.yaml|
        service\.yml
      )$
  - id: check-added-large-files
  - id: debug-statements
  - id: requirements-txt-fixer

- repo: https://github.com/PyCQA/flake8
  rev: 7.0.0
  hooks:
  - id: flake8
    args: [--max-line-length=120]
    additional_dependencies: [flake8-bugbear]

- repo: https://github.com/pycqa/isort
  rev: 5.13.2
  hooks:
  - id: isort

- repo: https://github.com/macisamuele/language-formatters-pre-commit-hooks
  rev: v2.12.0
  hooks:
  - id: pretty-format-yaml
    args: [--autofix, --indent, '2']
    exclude: ^service\.yaml$
