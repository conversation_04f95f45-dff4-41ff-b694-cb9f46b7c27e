import os
from google.cloud import storage, secretmanager, pubsub_v1

# Environment Variables
SERVICE_NAME = os.environ.get("SERVICE_NAME", "flask-microservice")
PORT = int(os.environ.get("PORT", 8080))
PROJECT_ID = os.environ.get("PROJECT_ID")
LOG_LEVEL = os.environ.get("LOG_LEVEL", "INFO")

# Authentication Constants
AUTHORIZATION_HEADER_NAME = "Authorization"
ID_TOKEN_HEADER_NAME = os.environ.get("ID_TOKEN_HEADER_NAME", "id-token")
BASIC_AUTH_HEADER_NAME = os.environ.get("BASIC_AUTH_HEADER_NAME", "basic-auth")

JWT_EXPIRY_LENGTH = 3600  # 1 hour in seconds

# Google Cloud Clients
storage_client = storage.Client()
secrets_client = secretmanager.SecretManagerServiceClient()
publisher = pubsub_v1.PublisherClient()
