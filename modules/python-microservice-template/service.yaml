apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: {{SERVICE_NAME}}
  labels:
    managed-by: github-actions
    cloud.googleapis.com/location: {{DEPLOYMENT_REGION}}
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/minScale: '1' # This keeps the service running
    spec:
      containerConcurrency: 80
      timeoutSeconds: 300
      serviceAccountName: {{SERVICE_ACCOUNT_NAME}}
      containers:
      - name: {{SERVICE_NAME}}
        image: {{ARTIFACTORY_REGION}}-docker.pkg.dev/{{PROJECT_ID}}/{{REPOSITORY}}/{{IMAGE_NAME}}:{{IMAGE_TAG}}
        ports:
        - name: http1
          containerPort: {{PORT}}
        env:
        - name: SERVICE_NAME
          value: {{SERVICE_NAME}}
        - name: PROJECT_ID
          value: {{PROJECT_ID}}
        - name: LOG_LEVEL
          value: {{LOG_LEVEL}}
        resources:
          limits:
            cpu: 1000m
            memory: 512Mi
        startupProbe:
          httpGet:
            path: /health
            port: {{PORT}}
          initialDelaySeconds: 10
          periodSeconds: 5
          failureThreshold: 3
      timeoutSeconds: 300