from blitzy_utils.logger import logger

import set_env
import os
import json
import asyncio
from typing import List, Dict, Any
from google.cloud import storage, pubsub_v1
from langgraph.graph import StateGraph
from langchain_anthropic import Cha<PERSON><PERSON><PERSON>hropic
from langchain_openai import Chat<PERSON><PERSON>A<PERSON>

from blitzy_utils.consts import REPO_STRUCTURE_NAME, DOWNLOADED_CODE_STRUCTURE_NAME, \
    BLITZY_FOLDER_PATH, HEAD_COMMIT_HASH_NAME, DOCUMENTATION_FOLDER_PATH
from blitzy_utils.common import publish_notification, download_from_gcs, upload_to_gcs
from blitzy_utils.enums import ProjectPhase, JobStatus
from blitzy_platform_shared.code_graph.builder import CodeGraphBuilder, Visibility
from blitzy_platform_shared.common.llms import llm_o3_mini, llm_claude_3_5_sonnet

from lib.blitzy.code_graph.helper import Code<PERSON>raph<PERSON>el<PERSON>, CodeGraphState

EVENT_DATA = json.dumps({
    'repo_name': 'mfe-toolbox',
    'repo_id': 'msci-mfe-toolbox-main-repo-id',
    'branch_id': 'mfe-toolbox-main',
    'branch_name': 'main',
    'company_id': 'msci',
    'user_id': '4b1797f6-5098-40c4-8e59-9773ed5aeaef',
    'project_id': 'msci-project-id',
    'team_id': 'msci-team',
    'start_file_path': 'realized/realized_kernel.m',
    'head_commit_hash': '9622b6c546bc6d649fd9bf0a36a7fcd53872e04a'
})
PROJECT_ID = 'blitzy-platform-prod'
GCS_BUCKET_NAME = 'blitzy-platform-prod'
PRIVATE_BLOB_NAME = 'private-src'
GENERATE_CODE_TOPIC = 'generate-code'
NEO4J_SERVER = "neo4j://*************/:7687"
NEO4J_USERNAME = "neo4j"
NEO4J_PASSWORD = "sygpYc-cuhcec-6tyfke"

DEV_GITHUB_SECRET_SERVER = "https://archie-secret-manager-464705070478.us-central1.run.app"
PROD_GITHUB_SECRET_SERVER = "https://archie-secret-manager-648803317587.us-east1.run.app"
GITHUB_SECRET_SERVER = PROD_GITHUB_SECRET_SERVER

TECH_SPECIFICATION_NAME = "Technical Specifications"
CODE_STRUCTURE_NAME = "Code Structure"
CODE_DESCRIPTION_NAME = "Code Description"
REPO_STRUCTURE_NAME = "GitHub Repo Files List"

storage_client = storage.Client()
publisher = pubsub_v1.PublisherClient()


async def generate_code_graph(event_data: Dict[str, Any], graph_builder: CodeGraphBuilder):
    repo_name = event_data.get('repo_name')
    project_id = event_data.get('project_id', '')
    job_id = event_data.get('job_id', '')
    propagate = event_data.get('propagate', True)
    user_id = event_data.get('user_id', 'default')
    team_id = event_data.get('team_id', 'default')
    company_id = event_data.get('company_id', 'default')
    repo_id = event_data.get('repo_id', 'repo_id')
    branch_id = event_data.get('branch_id', 'main')
    start_file_path = event_data.get('start_file_path', '')

    job_metadata = {
        'is_org_repo': True,
        'repo_name': repo_name,
        'repo_id': repo_id,
        'branch_id': branch_id,
        'company_id': company_id,
        'user_id': user_id,
        'team_id': team_id,
        'project_id': project_id,
        'visibility': Visibility.SHARED
    }

    blob_name = \
        f"{PRIVATE_BLOB_NAME}/company_{company_id}/team_{team_id}/user_{user_id}/{repo_name}_{repo_id}/branch_{branch_id}"
    document_blob_name = f"{blob_name}/{DOCUMENTATION_FOLDER_PATH}"

    repo_structure_filename = f'{REPO_STRUCTURE_NAME}.md'
    repo_files_str = download_from_gcs(
        storage_client,
        GCS_BUCKET_NAME,
        repo_structure_filename,
        blob_name=document_blob_name
    )
    repo_files_list: List[str] = json.loads(repo_files_str)

    start_file_index = 0
    if start_file_path:
        try:
            start_file_index = repo_files_list.index(start_file_path)
            print(f"Found path '{start_file_path}' at index {start_file_index}, overriding start index")
        except ValueError:
            print(f"{start_file_path} not found in files list, starting from the beginning")
            start_file_index = 0

    prev_file_spec_dict_filename = f"{DOWNLOADED_CODE_STRUCTURE_NAME}.json"
    file_spec_dict = {}
    try:
        file_spec_dict_str = download_from_gcs(
            storage_client,
            GCS_BUCKET_NAME,
            prev_file_spec_dict_filename,
            blob_name=document_blob_name
        )
        file_spec_dict = json.loads(file_spec_dict_str)
    except Exception as e:
        print('could not download or parse existing spec dict', e)
        file_spec_dict = {}

    head_commit_hash_filename = f"{HEAD_COMMIT_HASH_NAME}.txt"
    commit_hash_blob_name = f"{blob_name}/{BLITZY_FOLDER_PATH}"
    head_commit_hash = download_from_gcs(
        storage_client,
        GCS_BUCKET_NAME,
        head_commit_hash_filename,
        blob_name=commit_hash_blob_name
    )

    cg_helper = CodeGraphHelper(
        coder_llm=llm_o3_mini,
        summarizer_llm=llm_claude_3_5_sonnet,
        fallback_llm=llm_claude_3_5_sonnet,
        job_metadata=job_metadata,
        blob_name=blob_name,
        document_blob_name=document_blob_name,
        bucket_name=GCS_BUCKET_NAME,
        storage_client=storage_client,
        graph_builder=graph_builder,
        head_commit_hash=head_commit_hash,
        github_server=GITHUB_SECRET_SERVER
    )
    cg_generator: StateGraph = cg_helper.create_graph()
    app = cg_generator.compile()
    initial_state = CodeGraphState(
        acc_file_spec={},
        dep_dict={},
        file_index=start_file_index,
        file_spec_dict=file_spec_dict,
        file_type="",
        files_dict={},
        files_list=repo_files_list,
        root_folder_summarized=False
    )
    result = await app.ainvoke(initial_state, {"recursion_limit": 100000})
    # upload code spec
    # filename = f"{DOWNLOADED_CODE_STRUCTURE_NAME}.json"
    # upload_to_gcs(storage_client=storage_client, bucket_name=GCS_BUCKET_NAME, blob_name=blob_name, filename=filename,
    #               content_type='application/json', data=json.dumps(result["file_spec_dict"]))
    # # upload code spec
    # filename = f"{CODE_STRUCTURE_NAME}.json"
    # upload_to_gcs(storage_client=storage_client, bucket_name=GCS_BUCKET_NAME, blob_name=blob_name, filename=filename,
    #               content_type='application/json', data=json.dumps(result["file_spec_dict"]))

    # # upload thinking dict
    # filename = f"{CODE_THINKING_NAME}.json"
    # upload_to_gcs(storage_client=storage_client, bucket_name=GCS_BUCKET_NAME, blob_name=blob_name, filename=filename,
    #               content_type='application/json', data=json.dumps(result["thinking_dict"]))

    # # re-upload repo structure
    # filename = f"{REPO_STRUCTURE_NAME}.md"
    # upload_to_gcs(storage_client=storage_client, bucket_name=GCS_BUCKET_NAME, blob_name=blob_name, filename=filename,
    #               content_type='text/markdown', data=json.dumps(result["files_list"]))

    # # upload dependency manifest
    # filename = f"{DEPENDENCY_MANIFEST_NAME}.json"
    # upload_to_gcs(storage_client=storage_client, bucket_name=GCS_BUCKET_NAME, blob_name=blob_name, filename=filename,
    #               content_type='application/json', data=json.dumps(result["dep_dict"]))

    # notification_data = {
    #     "projectId": project_id,
    #     "jobId": job_id,
    #     "phase": ProjectPhase.THINKING.value,
    #     "status": JobStatus.DONE.value,
    #     "user_id": user_id,
    #     "metadata": {
    #         "propagate": propagate,
    #         "repo_name": repo_name
    #     }
    # }
    # publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)

    # notification_data = {
    #     'repo_name': repo_name,
    #     'project_id': project_id,
    #     'job_id': job_id,
    #     'propagate': propagate,
    #     "user_id": user_id
    # }
    # publish_notification(publisher, notification_data, PROJECT_ID, GENERATE_CODE_TOPIC)
    return event_data


if __name__ == "__main__":
    logger.info(f"Generating code graph for notification data: {EVENT_DATA}")
    event_data = json.loads(EVENT_DATA)
    repo_id = event_data.get('repo_id', 'repo_id')
    branch_id = event_data.get('branch_id', 'main')
    head_commit_hash = event_data.get('head_commit_hash', '')
    graph_builder = CodeGraphBuilder(
        uri=NEO4J_SERVER,
        username=NEO4J_USERNAME,
        password=NEO4J_PASSWORD,
        repo_id=repo_id,
        branch_id=branch_id,
        head_commit_hash=head_commit_hash
    )
    try:
        asyncio.run(generate_code_graph(event_data=event_data, graph_builder=graph_builder))
    except Exception as e:
        logger.error(f'Failed to generate code graph: {e}')
    finally:
        graph_builder.close()
