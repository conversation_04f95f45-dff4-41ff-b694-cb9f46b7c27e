from neo4j import GraphDatabase


NEO4J_URI = "neo4j://34.134.29.39:7687"
NEO4J_USERNAME = "neo4j"
NEO4J_PASSWORD = "wySsik-cisboq-fizve1"


class RepoDocumenter:
    def __init__(self, uri, user, password):
        self.driver = GraphDatabase.driver(uri, auth=(user, password))

    def filter_properties(self, props):
        """
        Remove embeddings from properties dictionary
        """
        if not props:
            return props
        return {k: v for k, v in props.items() if k != "embedding"}

    def get_file_info(self, file_path):
        """
        Get file properties (excluding embeddings) and its parent folder given a file path.
        """
        with self.driver.session() as session:
            result = session.run("""
                MATCH (f:FILE {path: $file_path})
                OPTIONAL MATCH (parent:FOLDER)-[r:HAS_FILE]->(f)
                RETURN 
                    properties(f) as file_props,
                    {
                        name: parent.name,
                        path: parent.path,
                        properties: properties(parent)
                    } as parent_folder
                """, file_path=file_path)
            record = result.single()
            if not record:
                return None, None

            # Filter embeddings from both file and parent folder properties
            file_props = self.filter_properties(record["file_props"])
            parent_folder = record["parent_folder"]
            if parent_folder:
                parent_folder["properties"] = self.filter_properties(parent_folder["properties"])

            return file_props, parent_folder

    def get_folder_info(self, folder_name):
        """
        Get folder properties and direct children (files and folders), excluding embeddings
        """
        with self.driver.session() as session:
            result = session.run("""
                MATCH (f:FOLDER {name: $folder_name})
                OPTIONAL MATCH (f)-[r:HAS_FILE]->(file:FILE)
                OPTIONAL MATCH (f)-[r:HAS_FOLDER]->(subfolder:FOLDER)
                RETURN 
                    properties(f) as folder_props,
                    COLLECT(DISTINCT {
                        type: 'FILE',
                        path: file.path,
                        properties: properties(file)
                    }) as files,
                    COLLECT(DISTINCT {
                        type: 'FOLDER',
                        name: subfolder.name,
                        path: subfolder.path,
                        properties: properties(subfolder)
                    }) as subfolders
                """, folder_name=folder_name)
            record = result.single()
            if not record:
                return None, [], []

            # Filter embeddings from all properties
            folder_props = self.filter_properties(record["folder_props"])
            files = [{
                **file,
                "properties": self.filter_properties(file["properties"])
            } for file in record["files"]]
            subfolders = [{
                **subfolder,
                "properties": self.filter_properties(subfolder["properties"])
            } for subfolder in record["subfolders"]]

            return folder_props, files, subfolders

    def get_folder_children_paths(self, folder_name):
        """
        Get just the paths of direct children (files and folders)
        Using COALESCE to handle nulls explicitly
        """
        with self.driver.session() as session:
            result = session.run("""
                MATCH (parent:FOLDER {name: $folder_name})
                WITH parent,
                    [(parent)-[:HAS_FILE]->(file:FILE) | file.path] as file_paths,
                    [(parent)-[:HAS_FOLDER]->(subfolder:FOLDER) | subfolder.path] as folder_paths
                RETURN 
                    COALESCE(file_paths, []) as file_paths,
                    COALESCE(folder_paths, []) as folder_paths
                """, folder_name=folder_name)
            record = result.single()
            if not record:
                return [], []
            return record["file_paths"], record["folder_paths"]

    def close(self):
        self.driver.close()


if __name__ == "__main__":
    documenter = RepoDocumenter(NEO4J_URI, NEO4J_USERNAME, NEO4J_PASSWORD)

    # Get folder information with children
    folder_name = "controllers"
    # Get just the paths
    file_paths, folder_paths = documenter.get_folder_children_paths(folder_name)
    print("\nChild paths:")
    print(f"Files: {file_paths}")
    print(f"Folders: {folder_paths}")

    # Get file information
    file_path = "app/controllers/application_controller.rb"
    file_props, parent_folder = documenter.get_file_info(file_path)

    if file_props:
        print(f"\nFile properties: {file_props}")
        print(f"Parent folder: {parent_folder['path']}")

    # Clean up
    documenter.close()
