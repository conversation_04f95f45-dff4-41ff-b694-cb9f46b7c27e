from google.cloud import pubsub_v1

from blitzy_utils.common import publish_notification

PROJECT_ID = 'blitzy-os-dev'
GRAPH_CODE_TOPIC = 'graph-code'

publisher = pubsub_v1.PublisherClient()

notification_data = {"repo_name": "NewProducRefactorCode1-7May", "repo_id": "*********", "branch_id": "c9c6b2eb-1067-4c05-8e89-8d1279f92675", "branch_name": "main", "company_id": "company-1", "user_id": "20ce50b9-8a3b-4209-b91b-377d3849d861", "team_id": "default",
                     "job_id": "71a0ec9e-c8b8-4143-b8c5-f27969e7657c", "project_id": "0c6c8395-8231-4a83-9103-dc162c3fdf45", "head_commit_hash": "145190decf58f5f399ac70869700818bbd380d82", "prev_head_commit_hash": "", "propagate": True, "batch_index": 0, "total_batches": 1, "tech_spec_id": "1b45d9f5-f6bb-4a46-8bf4-10ef544e9ea0"}


publish_notification(publisher, notification_data, PROJECT_ID, GRAPH_CODE_TOPIC)
