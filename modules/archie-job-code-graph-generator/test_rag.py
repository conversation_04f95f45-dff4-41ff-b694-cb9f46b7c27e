from langchain_voyageai import VoyageAIEmbeddings
from langchain_community.vectorstores.neo4j_vector import Neo4j<PERSON><PERSON>
from langchain.chains.retrieval_qa.base import RetrievalQ<PERSON>
from langchain_anthropic import Chat<PERSON>nthropic
from langchain_openai import ChatOpenAI
from langchain_core.prompts import PromptTemplate
from langchain_community.chains.graph_qa.prompts import CYPHER_GENERATION_PROMPT
from langchain_neo4j import GraphCypherQAChain, Neo4jGraph
from langchain.agents import AgentExecutor, create_tool_calling_agent, Tool
from langchain_core.prompts import ChatPromptTemplate

NEO4J_URI = "neo4j://34.134.29.39:7687"
NEO4J_USERNAME = "neo4j"
NEO4J_PASSWORD = "wySsik-cisboq-fizve1"

FILE_PROPERTIES = [
    "name",
    "purpose",
    "path",
    "usage_context"
]

ANTHROPIC_API_KEY = "************************************************************************************************************"
OPENAI_API_KEY = "********************************************************************************************************************************************************************"

llm_claude_3_5_sonnet = ChatAnthropic(
    model="claude-3-5-sonnet-20241022",
    max_tokens=8192,
    temperature=0,
    api_key=ANTHROPIC_API_KEY
)

llm_o1 = ChatOpenAI(
    model="o1",
    max_tokens=None,
    timeout=None,
    temperature=1,
    reasoning_effort="high",
    api_key=OPENAI_API_KEY
)

llm_deepseek = ChatOpenAI(
    model="deepseek-chat",
    max_tokens=None,
    temperature=0,
    api_key="sk-d56bd6f5930146d3b1a82920ffc36f19",
    base_url="https://api.deepseek.com"
)

files_index = Neo4jVector.from_existing_graph(
    VoyageAIEmbeddings(
        voyage_api_key="pa-5WI8Vwyw7YUpoWeX4ckHbDd7dN4w74S61zXHQKD0wTE", model="voyage-3-large", batch_size=64
    ),
    index_name='file',
    node_label="FILE",
    text_node_properties=FILE_PROPERTIES,
    embedding_node_property='embedding',
    url=NEO4J_URI,
    username=NEO4J_USERNAME,
    password=NEO4J_PASSWORD
)

# response = functions_index.similarity_search(
#     query="render"
# )
# print(response)

vector_files = RetrievalQA.from_chain_type(
    llm=llm_claude_3_5_sonnet,
    chain_type="stuff",
    retriever=files_index.as_retriever()
)

# result = vector_files("What functions can I use to render?")
# print(result)

graph = Neo4jGraph(
    url=NEO4J_URI,
    username=NEO4J_USERNAME,
    password=NEO4J_PASSWORD
)

CYPHER_GENERATION_TEMPLATE = """Task:Generate Cypher statement to query a graph database.
Instructions:
Use only the provided relationship types and properties in the schema.
Do not use any other relationship types or properties that are not provided.

Schema:
{schema}

Note: Do not include any explanations or apologies in your responses.
Do not respond to any questions that might ask anything else than for you to construct a Cypher statement.
Do not include any text except the generated Cypher statement.

The question is:
{question}"""
CYPHER_GENERATION_PROMPT = PromptTemplate(
    input_variables=["schema", "question"], template=CYPHER_GENERATION_TEMPLATE
)

cypher_chain = GraphCypherQAChain.from_llm(
    graph=graph,
    cypher_llm=llm_o1,
    qa_llm=llm_claude_3_5_sonnet,
    verbose=True,
    cypher_prompt=CYPHER_GENERATION_PROMPT,
    allow_dangerous_requests=True
)

tools = [
    Tool(
        name="file_details",
        func=vector_files.invoke,
        description="""
            Useful for querying name, purpose, path, and usage context details about files.
            Not useful for identifying dependencies or relationships.
            Only send one argument at a time. Input argument must be a string and not a list.
            Use full question as input.
            Read the description key for details.
        """,
    ),
    Tool(
        name="code_structure",
        func=cypher_chain.invoke,
        description="""
            Useful when you need to answer questions about code based on structure or for making aggregations.
            Use full question as input.
        """,
    ),
]

prompt = ChatPromptTemplate.from_messages(
    [
        (
            "system",
            "You are a world class software architect agent. Always use the right tool to answer the question.",
        ),
        ("placeholder", "{chat_history}"),
        ("human", "{input}"),
        ("placeholder", "{agent_scratchpad}"),
    ]
)

agent = create_tool_calling_agent(llm_deepseek, tools, prompt)
agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)
# response = agent_executor.invoke(
#     {"input": "Train an ML model from an excel data file for loan underwriting based on previous lending decisions and outcomes in gcp."})
# response = agent_executor.invoke(
#     {"input": "How can I generate a signed URL for a Google Cloud Storage object that expires after 10 minutes using the Python client library, and what are the required parameters?"})
response = agent_executor.invoke(
    {"input": "If I wanted to use firebase authentication, which files should I refactor?"})
# response = agent_executor.invoke(
#     {"input": "Implement a secure method to rotate Google Cloud Secret Manager secrets automatically, and update references to these secrets in a Firestore database."})
# response = agent_executor.invoke(
#     {"input": "Create a Python script that uses Google Cloud Firestore's query cursors and limits to implement efficient pagination for a large dataset, with both forward and backward navigation."})
output_text = response['output']

# Writing the output to a Markdown file
markdown_content = f"""
{output_text}
"""

with open("output.md", "w") as file:
    file.write(markdown_content)

print("Markdown file has been created: output.md")
