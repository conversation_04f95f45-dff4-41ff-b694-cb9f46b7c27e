import json
from lib.blitzy.code_graph.builder import CodeGraphBuilder

NEO4J_SERVER = "neo4j://34.134.29.39:7687"
NEO4J_USERNAME = "neo4j"
NEO4J_PASSWORD = "wySsik-cisboq-fizve1"


if __name__ == "__main__":
    event_data_str = json.dumps({
        'repo_name': 'hamlet',
        'repo_id': 'hamlet',
        'branch_id': 'main',
        'company_id': 'Symphony42'
    })
    event_data = json.loads(event_data_str)
    repo_name = event_data.get('repo_name')
    project_id = event_data.get('project_id', '')
    job_id = event_data.get('job_id', '')
    propagate = event_data.get('propagate', True)
    company_id = event_data.get('company_id', '')
    team_id = event_data.get('team_id', '')
    user_id = event_data.get('user_id', '')
    repo_id = event_data.get('repo_id', '')
    branch_id = event_data.get('branch_id', '')
    start_file_index = event_data.get('start_file_index', 0)
    graph_builder = CodeGraphBuilder(uri=NEO4J_SERVER, username=NEO4J_USERNAME, password=NEO4J_PASSWORD)

    # print(graph_builder.mark_empty_folders(
    #     repo_id=repo_id,
    #     branch_id=branch_id
    # ))

    # graph_builder.set_folder_summary(
    #     repo_id=repo_id,
    #     branch_id=branch_id,
    #     folder_path="app/assets/config",
    #     summary="Hello world"
    # )

    print(graph_builder.get_folder_children_summaries(
        repo_id=repo_id,
        branch_id=branch_id
    ))
