import asyncio
import json
import os
from typing import List, Dict, Any

from google.cloud import storage, pubsub_v1
from langgraph.graph import StateGraph

from blitzy_platform_shared.code_graph.builder import CodeGraphBuilder
from blitzy_platform_shared.common.llms import llm_o4_mini, llm_gpt4_1_nomax, llm_gemini_2_5_pro

from blitzy_utils.common import publish_notification, download_from_gcs, upload_to_gcs, \
    setup_maintenance_signal_handlers, get_repo_blitzy_folder_path, get_existing_product_files_list_name, \
    get_existing_product_updated_files_list_name, get_batched_files_list_filename
from blitzy_utils.consts import REPO_METADATA_NAME, REPO_MAPPING_NAME
from blitzy_utils.logger import logger
from blitzy_utils.enums import BackpropChangeMode, ProjectPhase, JobStatus

from lib.blitzy.code_graph.helper import CodeGraphHelper, CodeGraphState

EVENT_DATA = os.environ["EVENT_DATA"]
PROJECT_ID = os.environ["PROJECT_ID"]
GCS_BUCKET_NAME = os.environ["GCS_BUCKET_NAME"]
PRIVATE_BLOB_NAME = os.environ["PRIVATE_BLOB_NAME"]
PLATFORM_EVENTS_TOPIC = os.environ["PLATFORM_EVENTS_TOPIC"]
GENERATE_REVERSE_DOCUMENT_TOPIC = os.environ["GENERATE_REVERSE_DOCUMENT_TOPIC"]
GRAPH_CODE_TOPIC = os.environ["GRAPH_CODE_TOPIC"]
NEO4J_SERVER = os.environ["NEO4J_SERVER"]
NEO4J_USERNAME = os.environ["NEO4J_USERNAME"]
NEO4J_PASSWORD = os.environ["NEO4J_PASSWORD"]
GITHUB_SECRET_SERVER = os.environ["GITHUB_SECRET_SERVER"]

ANTHROPIC_API_KEY = os.environ["ANTHROPIC_API_KEY"]
OPENAI_API_KEY = os.environ["OPENAI_API_KEY"]
VOYAGE_API_KEY = os.environ["VOYAGE_API_KEY"]

LANGCHAIN_TRACING_V2 = os.environ["LANGCHAIN_TRACING_V2"]
LANGCHAIN_ENDPOINT = os.environ["LANGCHAIN_ENDPOINT"]
LANGCHAIN_API_KEY = os.environ["LANGCHAIN_API_KEY"]
LANGCHAIN_PROJECT = os.environ["LANGCHAIN_PROJECT"]

storage_client = storage.Client()
publisher = pubsub_v1.PublisherClient()


async def generate_code_graph(event_data: Dict[str, Any], graph_builder: CodeGraphBuilder):
    global publisher

    setup_maintenance_signal_handlers(
        publisher=publisher,
        notification_data=event_data,
        project_id=PROJECT_ID,
        topic_id=GRAPH_CODE_TOPIC
    )

    repo_name = event_data.get('repo_name')
    project_id = event_data.get('project_id', '')
    job_id = event_data.get('job_id', '')
    propagate = event_data.get('propagate', True)
    user_id = event_data.get('user_id', 'default')
    team_id = event_data.get('team_id', 'default')
    company_id = event_data.get('company_id', 'default')
    repo_id = event_data.get('repo_id', 'repo_id')
    branch_id = event_data.get('branch_id', 'main')
    branch_name = event_data.get('branch_name', 'main')
    head_commit_hash = event_data.get('head_commit_hash', "")
    prev_head_commit_hash = event_data.get('prev_head_commit_hash', "")
    batch_index = event_data.get('batch_index', 0)
    total_batches = event_data.get('total_batches', 1)
    tech_spec_id = event_data.get('tech_spec_id', "")
    org_name = event_data.get('org_name', '')
    single_file_mode = event_data.get('single_file_mode', False)
    resume = event_data.get('resume', False)

    if batch_index == 0:
        notification_data = {
            "projectId": project_id,
            "jobId": job_id,
            "tech_spec_id": tech_spec_id,
            "org_name": org_name,
            "repo_id": repo_id,
            "branch_name": branch_name,
            "phase": ProjectPhase.CODE_GRAPH.value,
            "status": JobStatus.IN_PROGRESS.value,
            "user_id": user_id,
            "branch_id": branch_id,
            "metadata": {
                "propagate": propagate,
                "repo_name": repo_name,
                "head_commit_hash": head_commit_hash
            }
        }
        publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)

    job_metadata = {
        'repo_name': repo_name,
        'repo_id': repo_id,
        'branch_id': branch_id,
        'branch_name': branch_name,
        'company_id': company_id,
        'user_id': user_id,
        'team_id': team_id,
        'project_id': project_id,
        'head_commit_hash': head_commit_hash,
        'prev_head_commit_hash': prev_head_commit_hash,
        'batch_index': batch_index,
        'total_batches': total_batches
    }

    blitzy_folder_path = get_repo_blitzy_folder_path(
        company_id=company_id,
        team_id=team_id,
        user_id=user_id,
        repo_name=repo_name,
        repo_id=repo_id,
        branch_id=branch_id,
        blob_name=PRIVATE_BLOB_NAME
    )

    repo_metadata_filename = f"{REPO_METADATA_NAME}.json"
    repo_metadata = json.loads(download_from_gcs(
        storage_client=storage_client,
        bucket_name=GCS_BUCKET_NAME,
        filename=repo_metadata_filename,
        blob_name=blitzy_folder_path
    ))

    change_mode = BackpropChangeMode.GENERATE
    repo_structure_base_filename = get_existing_product_files_list_name(
        head_commit_hash=head_commit_hash
    )
    if head_commit_hash and prev_head_commit_hash:
        change_mode = BackpropChangeMode.UPDATE
        repo_structure_base_filename = get_existing_product_updated_files_list_name(
            head_commit_hash=head_commit_hash,
            prev_head_commit_hash=prev_head_commit_hash
        )
    repo_structure_filename = f"{get_batched_files_list_filename(base_filename=repo_structure_base_filename, batch_index=batch_index)}.json"
    repo_files_str = download_from_gcs(
        storage_client,
        GCS_BUCKET_NAME,
        repo_structure_filename,
        blob_name=blitzy_folder_path
    )
    repo_files_list: List[str] = json.loads(repo_files_str)

    state_metadata_filename = f"{REPO_MAPPING_NAME} - Graph State Metadata - {batch_index}.json"

    cg_helper = CodeGraphHelper(
        coder_llm=llm_o4_mini,
        summarizer_llm=llm_o4_mini,
        fallback_llm_1=llm_gpt4_1_nomax,
        fallback_llm_2=llm_gemini_2_5_pro,
        job_metadata=job_metadata,
        blob_name=blitzy_folder_path,
        bucket_name=GCS_BUCKET_NAME,
        storage_client=storage_client,
        graph_builder=graph_builder,
        head_commit_hash=head_commit_hash,
        github_server=GITHUB_SECRET_SERVER,
        state_metadata_filename=state_metadata_filename
    )
    cg_generator: StateGraph = cg_helper.create_graph()
    app = cg_generator.compile()
    initial_state = CodeGraphState(
        acc_file_spec={},
        change_mode=change_mode,
        dep_dict={},
        file_index=0,
        file_type="",
        files_dict={},
        files_list=repo_files_list,
        resume=resume,
        root_folder_summarized=False,
        single_file_mode=single_file_mode,
        unsummarized_files_exist=False
    )
    result = await app.ainvoke(initial_state, {"recursion_limit": 100000})

    if graph_builder.are_other_batches_complete(
        company_id=company_id,
        repo_id=repo_id,
        branch_id=branch_id,
        current_batch_index=batch_index,
        total_batches=total_batches
    ):
        repo_metadata = {
            "repo_name": repo_name,
            "repo_id": repo_id,
            "branch_name": branch_name,
            "branch_id": branch_id,
            "prev_head_commit_hash": prev_head_commit_hash,
            "head_commit_hash": head_commit_hash
        }
        upload_to_gcs(
            storage_client=storage_client,
            bucket_name=GCS_BUCKET_NAME,
            blob_name=blitzy_folder_path,
            filename=repo_metadata_filename,
            content_type='application/json',
            data=json.dumps(repo_metadata)
        )

        notification_data = {
            "projectId": project_id,
            "jobId": job_id,
            "tech_spec_id": tech_spec_id,
            "org_name": org_name,
            "repo_id": repo_id,
            "branch_name": branch_name,
            "branch_id": branch_id,
            "phase": ProjectPhase.CODE_GRAPH.value,
            "status": JobStatus.DONE.value,
            "user_id": user_id,
            "metadata": {
                "propagate": propagate,
                "repo_name": repo_name,
                "head_commit_hash": head_commit_hash
            }
        }
        publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)

        if propagate:
            notification_data = {
                'repo_name': repo_name,
                'repo_id': repo_id,
                'branch_id': branch_id,
                'project_id': project_id,
                'job_id': job_id,
                'propagate': propagate,
                "user_id": user_id,
                'company_id': company_id,
                'user_id': user_id,
                'team_id': team_id,
                'head_commit_hash': head_commit_hash,
                'tech_spec_id': tech_spec_id
            }
            publish_notification(publisher, notification_data, PROJECT_ID, GENERATE_REVERSE_DOCUMENT_TOPIC)
    return event_data


if __name__ == "__main__":
    logger.info(f"Generating code graph for notification data: {EVENT_DATA}")
    event_data = json.loads(EVENT_DATA)
    company_id = event_data.get('repo_id', 'repo_id')
    repo_id = event_data.get('company_id', 'company_id')
    branch_id = event_data.get('branch_id', 'main')
    head_commit_hash = event_data.get('head_commit_hash', '')
    graph_builder = CodeGraphBuilder(
        uri=NEO4J_SERVER,
        username=NEO4J_USERNAME,
        password=NEO4J_PASSWORD,
        company_id=company_id,
        repo_id=repo_id,
        branch_id=branch_id,
        head_commit_hash=head_commit_hash
    )
    try:
        asyncio.run(generate_code_graph(event_data=event_data, graph_builder=graph_builder))
    except Exception as e:
        logger.error(f'Failed to generate code graph: {e}')
        raise
    finally:
        graph_builder.close()
