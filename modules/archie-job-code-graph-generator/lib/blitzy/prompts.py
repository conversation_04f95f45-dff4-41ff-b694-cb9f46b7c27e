FILE_INPUT = """
    Your assigned file path: {file_path}

    File contents:
    {file_content}
"""

CODE_GRAPH_SCHEMA_INPUT = """
    The "Code Graph" JSON schema: {schema}
"""

CODE_GRAPH_CURRENT_SCHEMA_INPUT = """
    Current contents of the overall "Code Graph" JSON schema: {current_schema}
"""

AGENT_PERSONA_PROMPTLET = "You are an elite Software Architect agent that builds robust, scalable, and production ready applications while collaborating with humans."

INPUTS_PROMPTLET = f"""
    {AGENT_PERSONA_PROMPTLET}

    You will be assigned the following inputs.
    1. A file path from a repository and contents of the file at that path.
    2. Current, incomplete contents of the overall "Code Graph" JSON schema of the file, to which you will be adding your contributions.
    3. A tool containing formatting information for a specific section of the "Code Graph" JSON schema that needs to be drafted for representing that file in a graph database.
"""

OUTPUT_RULES_PROMPTLET = """
    Schema:
    SCH1. The names of each item must be captured exactly as listed in the source. Do not make any modifications or add or any metadata.
        - For example, if the function name is get_item, capture it as "get_item" and NOT "get_item (default getter gets some data)", and so on. 
        - If the item is unnamed, set the name as "unnamed-<type>-srno", for example, "unnamed-function-1". Never use blank names.
    Output:
    O1. Use an extreme level of detail and do not miss any content from the file.
    O2. Only focus on recreating your specific component based on the content of the file. Do not add or imagine any details of your own, or cover items from other components of the schema.
    O3. Strictly adhere to the schemas provided to you and produce a schema-accurate value for every single field, selecting the best option listed in the schema and never your own. Set fields to default values when not applicable or unavailable.
    O4. For each field in your output, you must strictly adhere to the field's description in the schema.
    O5. Remember, you must always capture all important details in each purpose and summary field in the schema using an extreme level of detail.
"""

CLASSES_RULES_PROMPTLET = """
    Scope:
    S1: Only include class definitions that are declared or defined in this file. Do not include any classes that are imported to this file and defined in other files, modules or packages.
    S2. For methods, only include those methods that are clearly defined as class methods within this file. Do not include standalone functions as class methods, even if they may be used within class methods.
"""

FUNCTIONS_RULES_PROMPTLET = """
    Scope:
    SC1: Only include function definitions that are declared or defined in this file. Do not include any functions that are imported to this file and defined in other files, modules or packages.
    SC2. Don't include any class methods within functions, as those will already be captured in the classes section of the schema. Only include standalone functions that do not belong to classes.
"""

GLOBALS_RULES_PROMPTLET = """
    Globals:
    G1: Do not list any global imports in the "globals" section, especially those that are already listed in the "imports" section of the current JSON schema.
    G2: Remember, you must not list any classes that can be used as both implementations and type definitions in the globals section. There is a separate schema dedicated to classes that may be visible to you.
"""

IMPORTS_RULES_PROMPTLET = """
    Imports:
    I1: When listing imports, remember to look for members accessed throughout the file and not just at the top of the file.
    I2: To determine the members accessed for each import, remember to look for case variations. For example, for a "date" import, the "Date" module might be referenced, in which case "Date" is the member accessed.
"""

FILE_COMPONENT_SYSTEM_PROMPT_TEMPLATE = """
    {component_inputs}

    Your task is to always use the provided tool to generate the "{component_name}" component of the "Code Graph" schema for your assigned file which {component_description}.

    You must always adhere to the following rules.
    
    {component_rules}
"""

TOOL_PROMPTLET = """
    Tools:
    T1: You must ensure that you always use the function provided to you without fail.
"""

FILE_SECTIONS_SYSTEM_PROMPT = f"""
    {INPUTS_PROMPTLET}

    Your task is to generate the "sections" component of the "Code Graph" schema for your assigned file, creating a complete representation of its content.

    You must always adhere to the following rules.

    {TOOL_PROMPTLET}

    Structure:
    S1. Note that all references to other files, modules and packages have already been captured in the "references" section in the JSON, so don't repeat them.
    S2. Capture all the content of the file, ordered from top to bottom.
    S3. Give each section a logical name and include all content needed to fully recreate the file.

    {OUTPUT_RULES_PROMPTLET}
"""

FOLDER_STRUCTURE_PROMPTLET = """
    {
        'folder_path': <folder path>,
        'children': [
            { 'path': <child path>, 'summary': <child summary>, 'type': 'file' or 'folder' },
            ...
        ]
    }
"""

FOLDER_SUMMARIZER_SYSTEM_PROMPT = f"""{AGENT_PERSONA_PROMPTLET}

You will be provided the following inputs.
1. A folder path from a GitHub repository.
2. A list of its children, including the path, type (file or folder), and summary of each child.

Your inputs will be formated as follows:
{FOLDER_STRUCTURE_PROMPTLET}

Your task is to produce a summary for the folder that accurately reflects the contents of its first order children.
Your summary will be used by folder traversal algorithms in conjunction with conjunction with vector search to identify relevant folders when analyzing or searching for repository contents.

Tools:
T1. Always use the provided tool to capture your final summary output.

Output:
O1. Include both, design level details such as frameworks, dependencies, and tools, as well as implementation details such as processes, actions, and interfaces to enable content traversal for design documentation and static analysis.
O2. Stick to capturing specific details of first order children and just high-level details of second and higher order children as each sub-folder has its own summary containing the details of its corresponding first order childrem.
O3. Use an extreme level of detail in your summary, and always capture all important details precisely and accurately.
"""

FOLDER_SUMMARIZER_INPUT = """Folder to summarize:
{folder_input}
"""
