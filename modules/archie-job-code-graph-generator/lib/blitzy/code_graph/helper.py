import json
import gc
from typing import Dict, Any, Literal, List

from blitzy_utils.logger import logger
from langgraph.graph import START, StateGraph, END
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import SystemMessage, HumanMessage
from openai import BadRequestError as OpenAIBadRequestError, LengthFinishReasonError
from pydantic import BaseModel, ValidationError

from blitzy_utils.github import download_single_file
from blitzy_utils.enums import BackpropChangeMode
from blitzy_utils.common import download_from_gcs, upload_to_gcs

from blitzy_platform_shared.code_graph.builder import CodeGraphBuilder
from blitzy_platform_shared.code_graph.schema.src import CodeGraphSourceSchema
from blitzy_platform_shared.code_graph.schema.src_adjacent import CodeGraphSourceAdjacentSchema
from blitzy_platform_shared.code_graph.schema.summary import FolderSummary, FileSummary
from blitzy_platform_shared.code_graph.common import CodeGraphFileType
from blitzy_platform_shared.code_graph.consts import BatchStatus
from blitzy_platform_shared.code_graph.utils import create_single_field_model, is_source_adjacent_file, is_source_file
from blitzy_platform_shared.common.utils import archie_exponential_retry
from blitzy_platform_shared.common.consts import RETRYABLE_EXCEPTIONS, SUPPLEMENTARY_RETRYABLE_EXCEPTIONS

from ..prompts import FILE_COMPONENT_SYSTEM_PROMPT_TEMPLATE, FILE_INPUT, CODE_GRAPH_CURRENT_SCHEMA_INPUT, \
    FILE_SECTIONS_SYSTEM_PROMPT, OUTPUT_RULES_PROMPTLET, INPUTS_PROMPTLET, CLASSES_RULES_PROMPTLET, FUNCTIONS_RULES_PROMPTLET, \
    GLOBALS_RULES_PROMPTLET, IMPORTS_RULES_PROMPTLET, FOLDER_SUMMARIZER_SYSTEM_PROMPT, FOLDER_SUMMARIZER_INPUT
from .state import CodeGraphState, get_state


class CodeGraphHelper:
    def __init__(
        self,
        coder_llm: BaseChatModel,
        summarizer_llm: BaseChatModel,
        fallback_llm_1: BaseChatModel,
        fallback_llm_2: BaseChatModel,
        job_metadata: Dict[str, Any],
        blob_name: str,
        bucket_name: str,
        storage_client,
        graph_builder: CodeGraphBuilder,
        head_commit_hash: str,
        github_server: str,
        state_metadata_filename: str
    ):
        self.coder_llm = coder_llm
        self.fallback_llm_1 = fallback_llm_1
        self.fallback_llm_2 = fallback_llm_2
        self.summarizer_llm = summarizer_llm
        self.job_metadata = job_metadata
        self.blob_name = blob_name
        self.bucket_name = bucket_name
        self.storage_client = storage_client
        self.graph_builder = graph_builder
        self.head_commit_hash = head_commit_hash
        self.github_server = github_server
        self.state_metadata_filename = state_metadata_filename

        self.generator = self.create_graph()

        self.srca_steps = list(CodeGraphSourceAdjacentSchema.model_fields.keys())
        self.src_steps = list(CodeGraphSourceSchema.model_fields.keys())

        self.company_id = self.job_metadata["company_id"]
        self.team_id = self.job_metadata["team_id"]
        self.user_id = self.job_metadata["user_id"]
        self.repo_id = self.job_metadata["repo_id"]
        self.project_id = self.job_metadata["project_id"]
        self.repo_name = self.job_metadata["repo_name"]
        self.repo_id = self.job_metadata["repo_id"]
        self.branch_id = self.job_metadata["branch_id"]
        self.batch_index = self.job_metadata["batch_index"]
        self.total_batches = self.job_metadata["total_batches"]

        self.graph_builder.setup_branch(
            company_id=self.company_id,
            repo_id=self.repo_id,
            repo_name=self.repo_name,
            branch_id=self.branch_id,
            head_commit_hash=self.head_commit_hash
        )

    def create_graph(self) -> StateGraph:
        # Define the graph
        generator = StateGraph(CodeGraphState)

        # Add nodes
        generator.add_node("setup", self.setup)
        generator.add_node("prepare_file", self.prepare_file)
        generator.add_node("write_srca_references", self.write_srca_references)
        generator.add_node("write_srca_sections", self.write_srca_sections)
        generator.add_node("write_file_summary", self.write_file_summary)
        generator.add_node("write_src_imports", self.write_src_imports)
        generator.add_node("write_src_globals", self.write_src_globals)
        generator.add_node("write_src_classes", self.write_src_classes)
        generator.add_node("write_src_functions", self.write_src_functions)
        generator.add_node("write_src_exports", self.write_src_exports)
        generator.add_node("summarize_folders", self.summarize_folders)
        generator.add_node("teardown", self.teardown)

        generator.add_conditional_edges(
            "setup",
            self.setup_router,
            {
                "prepare_file": "prepare_file",
                "summarize": "summarize_folders"
            }
        )

        generator.add_conditional_edges(
            "prepare_file",
            self.classify_router,
            {
                "source_adjacent": "write_srca_references",
                "source": "write_src_imports",
                "other": "prepare_file",
                "summarize": "summarize_folders",
                "end": "teardown"
            }
        )

        generator.add_conditional_edges(
            "write_file_summary",
            self.file_router,
            {
                "continue": "prepare_file",
                "summarize": "summarize_folders",
                "end": "teardown"
            }
        )

        generator.add_conditional_edges(
            "summarize_folders",
            self.folder_summary_router,
            {
                "continue": "summarize_folders",
                "retry_files": "prepare_file",
                "end": "teardown"
            }
        )

        # Set the entry point
        generator.add_edge(START, "setup")
        generator.add_edge("write_srca_references", "write_srca_sections")
        generator.add_edge("write_srca_sections", "write_file_summary")
        generator.add_edge("write_src_imports", "write_src_globals")
        generator.add_edge("write_src_globals", "write_src_classes")
        generator.add_edge("write_src_classes", "write_src_functions")
        generator.add_edge("write_src_functions", "write_src_exports")
        generator.add_edge("write_src_exports", "write_file_summary")
        generator.add_edge("teardown", END)

        return generator

    def setup(self, state: CodeGraphState) -> Dict[str, Any]:
        self.graph_builder.update_batch_status(
            company_id=self.company_id,
            repo_id=self.repo_id,
            branch_id=self.branch_id,
            batch_index=self.batch_index,
            status=BatchStatus.IN_PROGRESS
        )
        if state["resume"]:
            # Restore state
            logger.info('Attempting to resume state')
            try:
                state_metadata: Dict[str, str] = json.loads(download_from_gcs(
                    storage_client=self.storage_client,
                    bucket_name=self.bucket_name,
                    blob_name=self.blob_name,
                    filename=self.state_metadata_filename
                ))
                state["file_index"] = state_metadata["file_index"]
            except Exception as e:
                logger.warning(f'Failed to resume job due to exception: {e}')
                state["file_index"] = 0
        elif state["change_mode"] == BackpropChangeMode.UPDATE:
            logger.info('Deleting changed files from graph')
            self.graph_builder.delete_files(
                file_paths=state["files_list"],
                company_id=self.company_id,
                repo_id=self.repo_id,
                branch_id=self.branch_id
            )
            logger.info('Changed files deleted successfully')
        return get_state(state=state)

    def setup_router(self, state: CodeGraphState) -> Literal["prepare_file", "summarize"]:
        if state["file_index"] >= len(state["files_list"]):
            return "summarize"
        return "prepare_file"

    def prepare_file(self, state: CodeGraphState) -> Dict[str, Any]:
        # Run manual garbage collection
        gc.collect()

        index = state["file_index"]
        file_path = state["files_list"][index]

        if is_source_adjacent_file(file_path=file_path):
            state["file_type"] = CodeGraphFileType.SOURCE_ADJACENT
        elif is_source_file(file_path=file_path):
            state["file_type"] = CodeGraphFileType.SOURCE
        else:
            state["file_index"] += 1
            state["file_type"] = CodeGraphFileType.OTHER

        if state["file_type"] != CodeGraphFileType.OTHER:
            try:
                raw_content = download_single_file(
                    repo_name=self.repo_name,
                    user_id=self.user_id,
                    server=self.github_server,
                    commit_hash=self.head_commit_hash,
                    file_path=file_path,
                    repo_id=self.repo_id
                )

                content = self.decode_github_content(raw_content)

                state["files_dict"][file_path] = content

                self.graph_builder.setup_file(
                    company_id=self.company_id,
                    repo_id=self.repo_id,
                    branch_id=self.branch_id,
                    head_commit_hash=self.head_commit_hash,
                    file_path=file_path
                )
            except Exception as e:
                logger.warning(f"Failed to decode, download or process file, skipping: {str(e)}")
                state["file_index"] += 1
                state["file_type"] = CodeGraphFileType.OTHER

        return get_state(state=state)

    def decode_github_content(self, content_bytes: bytes):
        if content_bytes is None:
            raise ValueError("Received empty content from GitHub API")

        # Try UTF-8 first as it's most common
        try:
            return content_bytes.decode('utf-8')
        except UnicodeDecodeError:
            # Fallback encodings
            try:
                return content_bytes.decode('latin-1')  # This rarely fails as it can handle all byte sequences
            except UnicodeDecodeError:
                raise ValueError("Could not decode content with any supported encoding")

    def classify_router(self, state: CodeGraphState) -> Literal["source_adjacent", "source", "other", "summarize", "end"]:
        if state["file_index"] >= len(state["files_list"]):
            if self.graph_builder.are_other_batches_complete(
                company_id=self.company_id,
                repo_id=self.repo_id,
                branch_id=self.branch_id,
                current_batch_index=self.batch_index,
                total_batches=self.total_batches
            ):
                return "summarize"
            return "end"
        if state["file_type"] == CodeGraphFileType.SOURCE_ADJACENT:
            return "source_adjacent"
        elif state["file_type"] == CodeGraphFileType.SOURCE:
            return "source"
        return "other"

    @archie_exponential_retry(
        exceptions=RETRYABLE_EXCEPTIONS + SUPPLEMENTARY_RETRYABLE_EXCEPTIONS
    )
    async def write_srca_references(self, state: CodeGraphState) -> Dict[str, Any]:
        index = state["file_index"]
        file_path = state["files_list"][index]

        logger.info(f'writing references for new srca file: {file_path}')

        state["acc_file_spec"] = {
            "path": file_path
        }

        # json_model = CodeGraphSourceAdjacentSchema.model_json_schema()
        # logger.info('json model', json_model)

        messages = [
            SystemMessage(content=[
                {
                    "type": "text",
                    "text": FILE_COMPONENT_SYSTEM_PROMPT_TEMPLATE.format(
                        component_name="references",
                        component_description="describes local and external items referenced by this file",
                        component_inputs=INPUTS_PROMPTLET,
                        component_rules=f"{OUTPUT_RULES_PROMPTLET}"
                    )
                }
            ]),
            HumanMessage(content=[
                {
                    "type": "text",
                    "text": FILE_INPUT.format(
                        file_path=file_path,
                        file_content=state["files_dict"][file_path]
                    )
                },
                {
                    "type": "text",
                    "text": CODE_GRAPH_CURRENT_SCHEMA_INPUT.format(
                        current_schema=state["acc_file_spec"]
                    )
                }
            ])
        ]

        ReferencesSchema = create_single_field_model(
            base_model=CodeGraphSourceAdjacentSchema, new_model_name="ReferencesSchema", field_to_keep="references")
        structured_llm = self.coder_llm.with_structured_output(ReferencesSchema)

        references = {}
        try:
            try:
                response: BaseModel = await structured_llm.ainvoke(messages)

                response_dict = response.model_dump()
                references = response_dict["references"]
            except (OpenAIBadRequestError, LengthFinishReasonError) as e:
                logger.warning(f'encountered error, trying with alternate openai model: {e}')
                structured_llm = self.fallback_llm_1.with_structured_output(ReferencesSchema)
                response: BaseModel = await structured_llm.ainvoke(messages)

                response_dict = response.model_dump()
                references = response_dict["references"]
        except (OpenAIBadRequestError, LengthFinishReasonError, ValidationError) as e:
            logger.warning(f'encountered error, trying with alternate google model: {e}')
            structured_llm = self.fallback_llm_2.with_structured_output(ReferencesSchema)
            response: BaseModel = await structured_llm.ainvoke(messages)

            if response:
                response_dict = response.model_dump()
                references = response_dict["references"]
            else:
                logger.warning(f'Encountered error, skipping section for file: {file_path}')

        # logger.info(references)

        self.graph_builder.graph_file_references(
            file_path=file_path,
            company_id=self.company_id,
            repo_id=self.repo_id,
            branch_id=self.branch_id,
            references=references
        )

        state["acc_file_spec"]["references"] = references

        logger.info(f'references added to schema for srca file: {file_path}')

        return get_state(state=state)

    @archie_exponential_retry(
        exceptions=RETRYABLE_EXCEPTIONS + SUPPLEMENTARY_RETRYABLE_EXCEPTIONS
    )
    async def write_srca_sections(self, state: CodeGraphState) -> Dict[str, Any]:
        index = state["file_index"]
        file_path = state["files_list"][index]

        logger.info(f'writing sections for srca file: {file_path}')

        # json_model = CodeGraphSourceAdjacentSchema.model_json_schema()
        # logger.info('json model', json_model)

        messages = [
            SystemMessage(content=[
                {
                    "type": "text",
                    "text": FILE_SECTIONS_SYSTEM_PROMPT
                }
            ]),
            HumanMessage(content=[
                {
                    "type": "text",
                    "text": FILE_INPUT.format(
                        file_path=file_path,
                        file_content=state["files_dict"][file_path]
                    )
                },
                {
                    "type": "text",
                    "text": CODE_GRAPH_CURRENT_SCHEMA_INPUT.format(
                        current_schema=state["acc_file_spec"]
                    )
                }
            ])
        ]

        SectionsSchema = create_single_field_model(
            base_model=CodeGraphSourceAdjacentSchema, new_model_name="SectionsSchema", field_to_keep="sections")
        structured_llm = self.coder_llm.with_structured_output(SectionsSchema, strict=True)

        sections = []
        try:
            try:
                response: BaseModel = await structured_llm.ainvoke(messages)

                response_dict = response.model_dump()
                sections = response_dict["sections"]
            except (OpenAIBadRequestError, LengthFinishReasonError) as e:
                logger.warning(f'encountered error, trying with alternate openai model: {e}')
                structured_llm = self.fallback_llm_1.with_structured_output(SectionsSchema)
                response: BaseModel = await structured_llm.ainvoke(messages)

                response_dict = response.model_dump()
                sections = response_dict["sections"]
        except (OpenAIBadRequestError, LengthFinishReasonError, ValidationError) as e:
            logger.warning(f'encountered error, trying with alternate google model: {e}')
            structured_llm = self.fallback_llm_2.with_structured_output(SectionsSchema)
            response: BaseModel = await structured_llm.ainvoke(messages)

            if response:
                response_dict = response.model_dump()
                sections = response_dict["sections"]
            else:
                logger.warning(f'Encountered error, skipping section for file: {file_path}')

        # logger.info(sections)

        self.graph_builder.graph_file_sections(
            file_path=file_path,
            company_id=self.company_id,
            repo_id=self.repo_id,
            branch_id=self.branch_id,
            sections=sections
        )

        state["acc_file_spec"]["sections"] = sections

        logger.info(f'sections added to schema for srca file: {file_path}')

        # logger.info(state["acc_file_spec"])

        return get_state(state=state)

    @archie_exponential_retry(
        exceptions=RETRYABLE_EXCEPTIONS + SUPPLEMENTARY_RETRYABLE_EXCEPTIONS
    )
    async def write_file_summary(self, state: CodeGraphState) -> Dict[str, Any]:
        index = state["file_index"]
        file_path = state["files_list"][index]

        logger.info(f'writing summary for srca file: {file_path}')

        # json_model = CodeGraphSourceAdjacentSchema.model_json_schema()
        # logger.info('json model', json_model)

        messages = [
            SystemMessage(content=[
                {
                    "type": "text",
                    "text": FILE_COMPONENT_SYSTEM_PROMPT_TEMPLATE.format(
                        component_name="summary",
                        component_description="summarizes the content in this file.",
                        component_inputs=INPUTS_PROMPTLET,
                        component_rules=f"{OUTPUT_RULES_PROMPTLET}"
                    )
                }
            ]),
            HumanMessage(content=[
                {
                    "type": "text",
                    "text": FILE_INPUT.format(
                        file_path=file_path,
                        file_content=state["files_dict"][file_path]
                    )
                },
                {
                    "type": "text",
                    "text": CODE_GRAPH_CURRENT_SCHEMA_INPUT.format(
                        current_schema=state["acc_file_spec"]
                    )
                }
            ])
        ]

        structured_llm = self.summarizer_llm.with_structured_output(FileSummary, strict=True)

        summary = {}
        try:
            try:
                response: BaseModel = await structured_llm.ainvoke(messages)

                response_dict = response.model_dump()
                summary = response_dict
            except (OpenAIBadRequestError, LengthFinishReasonError) as e:
                logger.warning(f'encountered error, trying with alternate openai model: {e}')
                structured_llm = self.fallback_llm_1.with_structured_output(FileSummary)
                response: BaseModel = await structured_llm.ainvoke(messages)

                response_dict = response.model_dump()
                summary = response_dict
        except (OpenAIBadRequestError, LengthFinishReasonError, ValidationError) as e:
            logger.warning(f'encountered error, trying with alternate google model: {e}')
            structured_llm = self.fallback_llm_2.with_structured_output(FileSummary)
            response: BaseModel = await structured_llm.ainvoke(messages)

            if response:
                response_dict = response.model_dump()
                summary = response_dict
            else:
                logger.warning(f'Encountered error, skipping section for file: {file_path}')

        # logger.info(summary)

        self.graph_builder.graph_file_summary(
            file_path=file_path,
            company_id=self.company_id,
            repo_id=self.repo_id,
            branch_id=self.branch_id,
            summary=summary
        )

        state["acc_file_spec"]["summary"] = summary

        logger.info(f'summary added to schema for srca file: {file_path}')

        state["file_index"] += 1

        self.upload_state(state=state)

        # logger.info(state["acc_file_spec"])
        return get_state(state=state)

    def file_router(self, state: CodeGraphState) -> Literal["continue", "summarize", "end"]:
        if state["single_file_mode"]:
            return "end"
        if state["file_index"] >= len(state["files_list"]):
            if self.graph_builder.are_other_batches_complete(
                company_id=self.company_id,
                repo_id=self.repo_id,
                branch_id=self.branch_id,
                current_batch_index=self.batch_index,
                total_batches=self.total_batches
            ):
                return "summarize"
            return "end"
        return "continue"

    def upload_state(self, state: CodeGraphState):
        state_metadata = {
            "file_index": state["file_index"]
        }
        upload_to_gcs(storage_client=self.storage_client, bucket_name=self.bucket_name, blob_name=self.blob_name,
                      filename=self.state_metadata_filename, content_type='application/json', data=json.dumps(state_metadata))

    @archie_exponential_retry(
        exceptions=RETRYABLE_EXCEPTIONS + SUPPLEMENTARY_RETRYABLE_EXCEPTIONS
    )
    async def write_src_imports(self, state: CodeGraphState) -> Dict[str, Any]:
        index = state["file_index"]
        file_path = state["files_list"][index]

        logger.info(f'writing imports for new src file: {file_path}')

        state["acc_file_spec"] = {
            "path": file_path
        }

        # json_model = CodeGraphSourceSchema.model_json_schema()
        # logger.info('json model', json_model)

        messages = [
            SystemMessage(content=[
                {
                    "type": "text",
                    "text": FILE_COMPONENT_SYSTEM_PROMPT_TEMPLATE.format(
                        component_name="imports",
                        component_description="describes the imports of this file.",
                        component_inputs=INPUTS_PROMPTLET,
                        component_rules=f"{IMPORTS_RULES_PROMPTLET}\n\n{OUTPUT_RULES_PROMPTLET}"
                    )
                }
            ]),
            HumanMessage(content=[
                {
                    "type": "text",
                    "text": FILE_INPUT.format(
                        file_path=file_path,
                        file_content=state["files_dict"][file_path]
                    )
                },
                {
                    "type": "text",
                    "text": CODE_GRAPH_CURRENT_SCHEMA_INPUT.format(
                        current_schema=state["acc_file_spec"]
                    )
                }
            ])
        ]

        ImportsSchema = create_single_field_model(
            base_model=CodeGraphSourceSchema, new_model_name="ImportsSchema", field_to_keep="imports")
        structured_llm = self.coder_llm.with_structured_output(ImportsSchema)

        imports = {}
        try:
            try:
                response: BaseModel = await structured_llm.ainvoke(messages)

                response_dict = response.model_dump()
                imports = response_dict["imports"]
            except (OpenAIBadRequestError, LengthFinishReasonError) as e:
                logger.warning(f'encountered error, trying with alternate openai model: {e}')
                structured_llm = self.fallback_llm_1.with_structured_output(ImportsSchema)
                response: BaseModel = await structured_llm.ainvoke(messages)

                response_dict = response.model_dump()
                imports = response_dict["imports"]
        except (OpenAIBadRequestError, LengthFinishReasonError, ValidationError) as e:
            logger.warning(f'encountered error, trying with alternate google model: {e}')
            structured_llm = self.fallback_llm_2.with_structured_output(ImportsSchema)
            response: BaseModel = await structured_llm.ainvoke(messages)

            if response:
                response_dict = response.model_dump()
                imports = response_dict["imports"]
            else:
                logger.warning(f'Encountered error, skipping section for file: {file_path}')

        # logger.info(imports)

        self.graph_builder.graph_imports(
            file_path=file_path,
            company_id=self.company_id,
            repo_id=self.repo_id,
            branch_id=self.branch_id,
            imports=imports
        )

        state["acc_file_spec"]["imports"] = imports

        logger.info(f'imports added to schema for src file: {file_path}')

        return get_state(state=state)

    @archie_exponential_retry(
        exceptions=RETRYABLE_EXCEPTIONS + SUPPLEMENTARY_RETRYABLE_EXCEPTIONS
    )
    async def write_src_globals(self, state: CodeGraphState) -> Dict[str, Any]:
        index = state["file_index"]
        file_path = state["files_list"][index]

        logger.info(f'writing globals for new src file: {file_path}')

        # json_model = CodeGraphSourceSchema.model_json_schema()
        # logger.info('json model', json_model)

        messages = [
            SystemMessage(content=[
                {
                    "type": "text",
                    "text": FILE_COMPONENT_SYSTEM_PROMPT_TEMPLATE.format(
                        component_name="globals",
                        component_description="describes the global type definitions, declarations, variables, and external entities in this file.",
                        component_inputs=INPUTS_PROMPTLET,
                        component_rules=f"{GLOBALS_RULES_PROMPTLET}\n\n{OUTPUT_RULES_PROMPTLET}"
                    )
                }
            ]),
            HumanMessage(content=[
                {
                    "type": "text",
                    "text": FILE_INPUT.format(
                        file_path=file_path,
                        file_content=state["files_dict"][file_path]
                    )
                },
                {
                    "type": "text",
                    "text": CODE_GRAPH_CURRENT_SCHEMA_INPUT.format(
                        current_schema=state["acc_file_spec"]
                    )
                }
            ])
        ]

        GlobalsSchema = create_single_field_model(
            base_model=CodeGraphSourceSchema, new_model_name="GlobalsSchema", field_to_keep="globals")
        structured_llm = self.coder_llm.with_structured_output(GlobalsSchema, strict=True)

        globals = {}
        try:
            try:
                response: BaseModel = await structured_llm.ainvoke(messages)

                response_dict = response.model_dump()
                globals = response_dict["globals"]
            except (OpenAIBadRequestError, LengthFinishReasonError) as e:
                logger.warning(f'encountered error, trying with alternate openai model: {e}')
                structured_llm = self.fallback_llm_1.with_structured_output(GlobalsSchema)
                response: BaseModel = await structured_llm.ainvoke(messages)

                response_dict = response.model_dump()
                globals = response_dict["globals"]
        except (OpenAIBadRequestError, LengthFinishReasonError, ValidationError) as e:
            logger.warning(f'encountered error, trying with alternate google model: {e}')
            structured_llm = self.fallback_llm_2.with_structured_output(GlobalsSchema)
            response: BaseModel = await structured_llm.ainvoke(messages)

            if response:
                response_dict = response.model_dump()
                globals = response_dict["globals"]
            else:
                logger.warning(f'Encountered error, skipping section for file: {file_path}')

        # logger.info(globals)

        self.graph_builder.graph_globals(
            file_path=file_path,
            company_id=self.company_id,
            repo_id=self.repo_id,
            branch_id=self.branch_id,
            globals=globals
        )

        state["acc_file_spec"]["globals"] = globals

        logger.info(f'globals added to schema for src file: {file_path}')

        return get_state(state=state)

    @archie_exponential_retry(
        exceptions=RETRYABLE_EXCEPTIONS + SUPPLEMENTARY_RETRYABLE_EXCEPTIONS
    )
    async def write_src_classes(self, state: CodeGraphState) -> Dict[str, Any]:
        index = state["file_index"]
        file_path = state["files_list"][index]

        logger.info(f'writing classes for new src file: {file_path}')

        # json_model = CodeGraphSourceSchema.model_json_schema()
        # logger.info('json model', json_model)

        messages = [
            SystemMessage(content=[
                {
                    "type": "text",
                    "text": FILE_COMPONENT_SYSTEM_PROMPT_TEMPLATE.format(
                        component_name="classes",
                        component_description="describes all class definitions in this file.",
                        component_inputs=INPUTS_PROMPTLET,
                        component_rules=f"{OUTPUT_RULES_PROMPTLET}\n\n{CLASSES_RULES_PROMPTLET}"
                    )
                }
            ]),
            HumanMessage(content=[
                {
                    "type": "text",
                    "text": FILE_INPUT.format(
                        file_path=file_path,
                        file_content=state["files_dict"][file_path]
                    )
                },
                {
                    "type": "text",
                    "text": CODE_GRAPH_CURRENT_SCHEMA_INPUT.format(
                        current_schema=state["acc_file_spec"]
                    )
                }
            ])
        ]

        ClassesSchema = create_single_field_model(
            base_model=CodeGraphSourceSchema, new_model_name="ClassesSchema", field_to_keep="classes")
        structured_llm = self.coder_llm.with_structured_output(ClassesSchema, strict=True)

        classes_list = []
        try:
            try:
                response: BaseModel = await structured_llm.ainvoke(messages)

                response_dict = response.model_dump()
                classes_list: List = response_dict["classes"]
            except (OpenAIBadRequestError, LengthFinishReasonError) as e:
                logger.warning(f'encountered error, trying with alternate openai model: {e}')
                structured_llm = self.fallback_llm_1.with_structured_output(ClassesSchema)
                response: BaseModel = await structured_llm.ainvoke(messages)

                response_dict = response.model_dump()
                classes_list: List = response_dict["classes"]
        except (OpenAIBadRequestError, LengthFinishReasonError, ValidationError) as e:
            logger.warning(f'encountered error, trying with alternate google model: {e}')
            structured_llm = self.fallback_llm_2.with_structured_output(ClassesSchema)
            response: BaseModel = await structured_llm.ainvoke(messages)

            if response:
                response_dict = response.model_dump()
                classes_list = response_dict["classes"]
            else:
                logger.warning(f'Encountered error, skipping section for file: {file_path}')

        # logger.info(classes_list)

        self.graph_builder.graph_classes(
            file_path=file_path,
            company_id=self.company_id,
            repo_id=self.repo_id,
            branch_id=self.branch_id,
            classes_list=classes_list.copy()
        )

        state["acc_file_spec"]["classes"] = classes_list

        logger.info(f'classes added to schema for src file: {file_path}')

        return get_state(state=state)

    @archie_exponential_retry(
        exceptions=RETRYABLE_EXCEPTIONS + SUPPLEMENTARY_RETRYABLE_EXCEPTIONS
    )
    async def write_src_functions(self, state: CodeGraphState) -> Dict[str, Any]:
        index = state["file_index"]
        file_path = state["files_list"][index]

        logger.info(f'writing functions for new src file: {file_path}')

        # json_model = CodeGraphSourceSchema.model_json_schema()
        # logger.info('json model', json_model)

        messages = [
            SystemMessage(content=[
                {
                    "type": "text",
                    "text": FILE_COMPONENT_SYSTEM_PROMPT_TEMPLATE.format(
                        component_name="functions",
                        component_description="describes all non-class function definitions in this file.",
                        component_inputs=INPUTS_PROMPTLET,
                        component_rules=f"{OUTPUT_RULES_PROMPTLET}\n\n{FUNCTIONS_RULES_PROMPTLET}"
                    )
                }
            ]),
            HumanMessage(content=[
                {
                    "type": "text",
                    "text": FILE_INPUT.format(
                        file_path=file_path,
                        file_content=state["files_dict"][file_path]
                    )
                },
                {
                    "type": "text",
                    "text": CODE_GRAPH_CURRENT_SCHEMA_INPUT.format(
                        current_schema=state["acc_file_spec"]
                    )
                }
            ])
        ]

        FunctionsSchema = create_single_field_model(
            base_model=CodeGraphSourceSchema, new_model_name="FunctionsSchema", field_to_keep="functions")
        structured_llm = self.coder_llm.with_structured_output(FunctionsSchema, strict=True)

        functions_list = []
        try:
            try:
                response: BaseModel = await structured_llm.ainvoke(messages)

                response_dict = response.model_dump()
                functions_list: List = response_dict["functions"]
            except (OpenAIBadRequestError, LengthFinishReasonError) as e:
                logger.warning(f'encountered error, trying with alternate openai model: {e}')
                structured_llm = self.fallback_llm_1.with_structured_output(FunctionsSchema)
                response: BaseModel = await structured_llm.ainvoke(messages)

                response_dict = response.model_dump()
                functions_list: List = response_dict["functions"]
        except (OpenAIBadRequestError, LengthFinishReasonError, ValidationError) as e:
            logger.warning(f'encountered error, trying with alternate google model: {e}')
            structured_llm = self.fallback_llm_2.with_structured_output(FunctionsSchema)
            response: BaseModel = await structured_llm.ainvoke(messages)

            if response:
                response_dict = response.model_dump()
                functions_list = response_dict["functions"]
            else:
                logger.warning(f'Encountered error, skipping section for file: {file_path}')

        # logger.info(functions_list)

        self.graph_builder.graph_functions(
            file_path=file_path,
            company_id=self.company_id,
            repo_id=self.repo_id,
            branch_id=self.branch_id,
            functions_list=functions_list.copy()
        )

        state["acc_file_spec"]["functions"] = functions_list

        logger.info(f'functions added to schema for src file: {file_path}')

        return get_state(state=state)

    @archie_exponential_retry(
        exceptions=RETRYABLE_EXCEPTIONS + SUPPLEMENTARY_RETRYABLE_EXCEPTIONS
    )
    async def write_src_exports(self, state: CodeGraphState) -> Dict[str, Any]:
        index = state["file_index"]
        file_path = state["files_list"][index]

        logger.info(f'writing exports for new src file: {file_path}')

        # json_model = CodeGraphSourceSchema.model_json_schema()
        # logger.info('json model', json_model)

        messages = [
            SystemMessage(content=[
                {
                    "type": "text",
                    "text": FILE_COMPONENT_SYSTEM_PROMPT_TEMPLATE.format(
                        component_name="exports",
                        component_description="summarizes the exports for this file.",
                        component_inputs=INPUTS_PROMPTLET,
                        component_rules=f"{OUTPUT_RULES_PROMPTLET}"
                    )
                }
            ]),
            HumanMessage(content=[
                {
                    "type": "text",
                    "text": FILE_INPUT.format(
                        file_path=file_path,
                        file_content=state["files_dict"][file_path]
                    )
                },
                {
                    "type": "text",
                    "text": CODE_GRAPH_CURRENT_SCHEMA_INPUT.format(
                        current_schema=state["acc_file_spec"]
                    )
                }
            ])
        ]

        ExportsSchema = create_single_field_model(
            base_model=CodeGraphSourceSchema, new_model_name="ExportsSchema", field_to_keep="exports")
        structured_llm = self.coder_llm.with_structured_output(ExportsSchema, strict=True)

        exports = {}
        try:
            try:
                response: BaseModel = await structured_llm.ainvoke(messages)

                response_dict = response.model_dump()
                exports = response_dict["exports"]
            except (OpenAIBadRequestError, LengthFinishReasonError) as e:
                logger.warning(f'encountered error, trying with alternate openai model: {e}')
                structured_llm = self.fallback_llm_1.with_structured_output(ExportsSchema)
                response: BaseModel = await structured_llm.ainvoke(messages)

                response_dict = response.model_dump()
                exports = response_dict["exports"]
        except (OpenAIBadRequestError, LengthFinishReasonError, ValidationError) as e:
            logger.warning(f'encountered error, trying with alternate google model: {e}')
            structured_llm = self.fallback_llm_2.with_structured_output(ExportsSchema)
            response: BaseModel = await structured_llm.ainvoke(messages)

            if response:
                response_dict = response.model_dump()
                exports = response_dict["exports"]
            else:
                logger.warning(f'Encountered error, skipping section for file: {file_path}')

        # logger.info(exports)

        self.graph_builder.graph_exports(
            file_path=file_path,
            company_id=self.company_id,
            repo_id=self.repo_id,
            branch_id=self.branch_id,
            exports=exports
        )

        state["acc_file_spec"]["exports"] = exports

        logger.info(f'exports added to schema for src file: {file_path}')

        return get_state(state=state)

    @archie_exponential_retry(
        exceptions=RETRYABLE_EXCEPTIONS + SUPPLEMENTARY_RETRYABLE_EXCEPTIONS
    )
    async def summarize_folders(self, state: CodeGraphState) -> Dict[str, Any]:
        logger.info('Checking for unsummarized files')
        unsummarized_files = self.graph_builder.get_unsummarized_file_paths(
            company_id=self.company_id,
            repo_id=self.repo_id,
            branch_id=self.branch_id
        )
        if len(unsummarized_files):
            state["unsummarized_files_exist"] = True
            logger.info(f'Found {len(unsummarized_files)} unsummarized files, retrying them')
            state["file_index"] = 0
            state["files_list"] = unsummarized_files.copy()
            return get_state(state=state)
        else:
            state["unsummarized_files_exist"] = False
        logger.info('Retrieving folders to summarize')
        folders_to_summarize = self.graph_builder.get_folder_children_summaries(
            company_id=self.company_id,
            repo_id=self.repo_id,
            branch_id=self.branch_id
        )
        if not len(folders_to_summarize):
            raise Exception("Found no folders to summarize")
        for folder_info in folders_to_summarize:
            folder_path = folder_info["folder_path"]
            logger.info(f'Summarizing folder: {"Root" if folder_path == "" else folder_path}')

            messages = [
                SystemMessage(content=[
                    {
                        "type": "text",
                        "text": FOLDER_SUMMARIZER_SYSTEM_PROMPT
                    }
                ]),
                HumanMessage(content=[
                    {
                        "type": "text",
                        "text": FOLDER_SUMMARIZER_INPUT.format(
                            folder_input=json.dumps(folder_info)
                        )
                    }
                ])
            ]

            structured_llm = self.summarizer_llm.with_structured_output(FolderSummary, strict=True)

            summary = {}
            try:
                try:
                    response: BaseModel = await structured_llm.ainvoke(messages)

                    response_dict = response.model_dump()
                    summary = response_dict["summary"]
                except (OpenAIBadRequestError, LengthFinishReasonError) as e:
                    logger.warning(f'encountered error, trying with alternate openai model: {e}')
                    structured_llm = self.fallback_llm_1.with_structured_output(FolderSummary)
                    response: BaseModel = await structured_llm.ainvoke(messages)

                    response_dict = response.model_dump()
                    summary = response_dict["summary"]
            except (OpenAIBadRequestError, LengthFinishReasonError, ValidationError) as e:
                logger.warning(f'encountered error, trying with alternate google model: {e}')
                structured_llm = self.fallback_llm_2.with_structured_output(FolderSummary)
                response: BaseModel = await structured_llm.ainvoke(messages)

                if response:
                    response_dict = response.model_dump()
                    summary = response_dict["summary"]
                else:
                    logger.warning(f'Encountered error, skipping section for folder: {folder_path}')

            self.graph_builder.set_folder_summary(
                company_id=self.company_id,
                repo_id=self.repo_id,
                branch_id=self.branch_id,
                folder_path=folder_path,
                summary=summary
            )

            logger.info(f'Successfully summarized folder: {folder_path}')

        state["root_folder_summarized"] = self.graph_builder.check_root_folder_summary(
            company_id=self.company_id,
            repo_id=self.repo_id,
            branch_id=self.branch_id
        )
        logger.info(f'Root folder summarized: {state["root_folder_summarized"]}')
        return get_state(state=state)

    def folder_summary_router(self, state: CodeGraphState) -> Literal["continue", "retry_files", "end"]:
        if state["unsummarized_files_exist"]:
            return "retry_files"
        elif state["root_folder_summarized"]:
            logger.info("Root folder summarized successfully, ending operation")
            return "end"
        else:
            return "continue"

    def teardown(self, state: CodeGraphState) -> Dict[str, Any]:
        logger.info(f'Marking job complete for 1-indexed batch {self.batch_index + 1} of {self.total_batches}')
        self.graph_builder.update_batch_status(
            company_id=self.company_id,
            repo_id=self.repo_id,
            branch_id=self.branch_id,
            batch_index=self.batch_index,
            status=BatchStatus.COMPLETE
        )
        return get_state(state=state)
