from typing import TypedDict, Dict, Any, List

from blitzy_platform_shared.code_graph.common import CodeGraphFileType

from blitzy_utils.enums import BackpropChangeMode


class CodeGraphState(TypedDict):
    acc_file_spec: Dict[str, Dict[str, Any]]
    change_mode: BackpropChangeMode
    dep_dict: Dict[str, Dict[str, str]]
    file_index: int
    file_type: CodeGraphFileType
    files_dict: Dict[str, str]
    files_list: List[str]
    resume: bool
    root_folder_summarized: bool
    single_file_mode: bool
    unsummarized_files_exist: bool


def get_state(state: CodeGraphState) -> Dict[str, Any]:
    return {
        "acc_file_spec": state["acc_file_spec"],
        "change_mode": state["change_mode"],
        "dep_dict": state["dep_dict"],
        "file_index": state["file_index"],
        "file_type": state["file_type"],
        "files_dict": state["files_dict"],
        "files_list": state["files_list"],
        "resume": state["resume"],
        "root_folder_summarized": state["root_folder_summarized"],
        "single_file_mode": state["single_file_mode"],
        "unsummarized_files_exist": state["unsummarized_files_exist"]
    }
