import json
from typing import Dict, Any, Literal

from langgraph.graph import START, StateGraph, END
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from langgraph.prebuilt import ToolNode
from pydantic import BaseModel
from thefuzz import process

from blitzy_utils.logger import logger
from blitzy_utils.common import get_json_content
from blitzy_utils.enums import BackpropChangeMode
from blitzy_platform_shared.code_graph.builder import CodeGraphBuilder
from blitzy_platform_shared.document.mermaid_fixer import MermaidFixer
from blitzy_platform_shared.document.prompts import TECHNICAL_SECTION_PROMPTS
from blitzy_platform_shared.document.utils import format_document_heading, clean_document, \
    parse_sections_at_heading_level, determine_heading_level
from blitzy_platform_shared.code_graph.tools import search_folders, search_files, get_source_folder_contents, \
    get_file_summary
from blitzy_platform_shared.common.utils import get_formatted_tool_result_messages, get_response_content, archie_exponential_retry, \
    format_messages
from blitzy_platform_shared.common.tools import get_file_contents, ANTHROPIC_WEB_SEARCH_TOOL_DEFINITION
from blitzy_platform_shared.common.consts import RETRYABLE_EXCEPTIONS, SUPPLEMENTARY_RETRYABLE_EXCEPTIONS
from blitzy_platform_shared.common.models import FormattingError
from blitzy_platform_shared.document.tools import get_tech_spec_section
from blitzy_platform_shared.document.prompts import TECH_SPEC_SECTIONS_INPUT, UPDATED_TECHNICAL_SECTION_ADDENDUM

from .state import ReverseDocumentState, get_state
from .models import DocumentSections, DocumentSectionStatus
from .prompts import DOCUMENT_SECTION_SYSTEM_PROMPT_TEMPLATE, DG_INPUTS_PROMPTLET, DG_CONTEXT_RULES_PROMPTLET, \
    TOOL_RULES_PROMPTLET, DG_OUTPUT_RULES_PROMPTLET, SECTION_PROMPT_INPUT, ROOT_FOLDER_INPUT, \
    DOCUMENTER_PERSONA_PROMPTLET, CONTEXT_SEARCH_SYSTEM_PROMPT_TEMPLATE, SEARCH_PERSONA_PROMPTLET, SEARCH_INPUTS_PROMPTLET, \
    SEARCH_RULES_PROMPTLET, SEARCH_OUTPUT_RULES_PROMPTLET, DG_SECTION_CONTEXT_INPUT, DUC_SYSTEM_PROMPT_TEMPLATE, DUC_INPUTS_PROMPTLET, \
    DUC_OUTPUT_RULES_PROMPTLET, DUC_CHANGE_RULES_PROMPTLET, NEW_REQUIREMENTS_INPUT, DU_INPUTS_PROMPTLET, \
    DU_CONTEXT_RULES_PROMPTLET, DU_OUTPUT_RULES_PROMPTLET, DU_SYSTEM_PROMPT_TEMPLATE, DUC_STRUCTURED_SECTION_INPUT, \
    SECTION_CONTENT_INPUT, SECTION_CHANGES_INPUT, DUC_STRUCTURED_SUBHEADINGS_INPUT, SUMMARY_CHANGES_INPUTS, \
    SUMMARY_CHANGES_RULES, ADD_INFO_INPUT, SUMMARY_DOCUMENTER_PERSONA_PROMPTLET, SUMMARY_SECTION_SYSTEM_PROMPT

rd_search_node_tools = [
    get_tech_spec_section,
    get_source_folder_contents,
    get_file_summary,
    get_file_contents,
    search_files,
    search_folders
]
rd_search_tools = rd_search_node_tools + [ANTHROPIC_WEB_SEARCH_TOOL_DEFINITION]
rd_search_tool_node = ToolNode(rd_search_node_tools)

rd_author_node_tools = [get_tech_spec_section]
rd_author_tools = rd_author_node_tools + [ANTHROPIC_WEB_SEARCH_TOOL_DEFINITION]
rd_author_tool_node = ToolNode(rd_author_node_tools)


class ReverseDocumentHelper:
    def __init__(
            self,
            author_llm: BaseChatModel,
            architect_llm: BaseChatModel,
            summarizer_llm: BaseChatModel,
            search_llm: BaseChatModel,
            diagram_llm: BaseChatModel,
            fallback_llm: BaseChatModel,
            job_metadata: Dict[str, Any],
            graph_builder: CodeGraphBuilder,
            head_commit_hash: str,
            markdown_server: str,
            github_server: str
    ):
        self.author_llm = author_llm
        self.architect_llm = architect_llm
        self.summarizer_llm = summarizer_llm
        self.search_llm = search_llm
        self.diagram_llm = diagram_llm
        self.fallback_llm = fallback_llm
        self.job_metadata = job_metadata
        self.graph_builder = graph_builder
        self.head_commit_hash = head_commit_hash
        self.markdown_server = markdown_server
        self.github_server = github_server

        self.company_id = self.job_metadata["company_id"]
        self.team_id = self.job_metadata["team_id"]
        self.user_id = self.job_metadata["user_id"]
        self.repo_id = self.job_metadata["repo_id"]
        self.project_id = self.job_metadata["project_id"]
        self.repo_name = self.job_metadata["repo_name"]
        self.branch_id = self.job_metadata["branch_id"]

        self.generator = self.create_graph()

    def create_graph(self) -> StateGraph:
        # Define the graph
        generator = StateGraph(ReverseDocumentState)

        # Add nodes
        generator.add_node("setup", self.setup)
        generator.add_node("gather_context", self.gather_context)
        generator.add_node("document_section", self.document_section)
        generator.add_node("summarize_changes", self.summarize_changes)
        generator.add_node("identify_changes", self.identify_changes)
        generator.add_node("update_section", self.update_section)

        generator.add_conditional_edges(
            "setup",
            self.setup_router,
            {
                "generate": "gather_context",
                "update": "summarize_changes"
            }
        )

        generator.add_conditional_edges(
            "document_section",
            self.document_router,
            {
                "continue": "gather_context",
                "end": END
            }
        )

        generator.add_conditional_edges(
            "update_section",
            self.document_router,
            {
                "continue": "identify_changes",
                "end": END
            }
        )

        # Set the entry point
        generator.add_edge(START, "setup")
        generator.add_edge("gather_context", "document_section")
        generator.add_edge("summarize_changes", "identify_changes")
        generator.add_edge("identify_changes", "update_section")

        return generator

    def setup(self, state: ReverseDocumentState) -> Dict[str, Any]:
        state["section_prompts"] = TECHNICAL_SECTION_PROMPTS.copy()
        state["section_headings"] = list(state["section_prompts"].keys())
        state["total_sections"] = len(state["section_headings"])
        state["graph_builder"] = self.graph_builder
        state["company_id"] = self.company_id
        state["repo_id"] = self.repo_id
        state["repo_name"] = self.repo_name
        state["branch_id"] = self.branch_id
        state["head_commit_hash"] = self.head_commit_hash
        state["github_server"] = self.github_server
        state["user_id"] = self.user_id
        state["section_index"] = 0
        state["updated_tech_spec"] = ""
        state["previous_tech_spec"] = ""
        state["previous_structured_sections"] = []
        state["summary_of_changes"] = ""
        state["current_tech_spec_sections"] = {}

        if state["current_tech_spec"]:
            state["current_tech_spec_sections"] = parse_sections_at_heading_level(
                text=state["current_tech_spec"],
                heading_level=1
            )
            logger.info(list(state["current_tech_spec_sections"].keys()))

            for heading in state["section_headings"]:
                if state["section_prompts"][heading] == "":
                    # logger.info(state["current_tech_spec_sections"][heading])
                    parsed_subsections = parse_sections_at_heading_level(
                        text=state["current_tech_spec_sections"][heading],
                        heading_level=2
                    )
                    # logger.info(parsed_subsections)
                    state["current_tech_spec_sections"] = state["current_tech_spec_sections"] | parsed_subsections
                    logger.info(f'Expanded and merged heading: {heading}')

        if not state["root_folder_contents"]:
            logger.info('fetching root folder info')
            root_folder_info = self.graph_builder.get_folder_contents(
                folder_path="",
                company_id=self.company_id,
                repo_id=self.repo_id,
                branch_id=self.branch_id
            )
            state["root_folder_contents"] = json.dumps(root_folder_info)
            logger.info("Root folder info fetched.")

        return get_state(state=state)

    def setup_router(self, state: ReverseDocumentState) -> Literal["generate", "update"]:
        if state["mode"] == BackpropChangeMode.GENERATE.value:
            return "generate"
        else:
            return "update"

    @archie_exponential_retry()
    async def gather_context(self, state: ReverseDocumentState) -> Dict[str, Any]:
        index = state["section_index"]
        section_heading = state["section_headings"][index]
        logger.info(f'gathering context for new document section: {section_heading}')

        section_prompt = state["section_prompts"][section_heading]

        if section_prompt:
            state["tech_spec_parsed"] = parse_sections_at_heading_level(
                text=state["updated_tech_spec"],
                heading_level=2
            )

            messages = [
                SystemMessage(content=[
                    {
                        "type": "text",
                        "text": CONTEXT_SEARCH_SYSTEM_PROMPT_TEMPLATE.format(
                            agent_persona=SEARCH_PERSONA_PROMPTLET,
                            section_heading=section_heading,
                            search_inputs=SEARCH_INPUTS_PROMPTLET,
                            search_rules=f"{SEARCH_RULES_PROMPTLET}\n\n"
                            f"{TOOL_RULES_PROMPTLET}\n\n{SEARCH_OUTPUT_RULES_PROMPTLET}"
                        ),
                        "cache_control": {"type": "ephemeral"}
                    }
                ]),
                HumanMessage(content=[
                    {
                        "type": "text",
                        "text": ROOT_FOLDER_INPUT.format(
                            root_folder_contents=state["root_folder_contents"]
                        ),
                    },
                    {
                        "type": "text",
                        "text": ADD_INFO_INPUT.format(
                            add_info=state["add_context"]
                        ),
                    },
                    {
                        "type": "text",
                        "text": TECH_SPEC_SECTIONS_INPUT.format(
                            tech_spec_sections=json.dumps(list(state["tech_spec_parsed"].keys()))
                        ),
                        "cache_control": {"type": "ephemeral"}
                    },
                    {
                        "type": "text",
                        "text": SECTION_PROMPT_INPUT.format(
                            section_heading=section_heading,
                            section_prompt=section_prompt
                        )
                    }
                ])
            ]

            response: AIMessage = await self.search_llm.ainvoke(messages)

            while len(response.tool_calls):
                # fill in the value of local variables
                messages.append(response)
                total_tokens = response.usage_metadata["total_tokens"]
                # logger.info(total_tokens)
                tools_response = await rd_search_tool_node.ainvoke({
                    "messages": [response],
                    "company_id": state["company_id"],
                    "repo_id": state["repo_id"],
                    "repo_name": state["repo_name"],
                    "branch_id": state["branch_id"],
                    "user_id": state["user_id"],
                    "head_commit_hash": state["head_commit_hash"],
                    "graph_builder": state["graph_builder"],
                    "github_server": state["github_server"],
                    "tech_spec_parsed": state["tech_spec_parsed"]
                })

                tool_results = get_formatted_tool_result_messages(
                    tool_message_list=tools_response["messages"],
                    total_tokens=total_tokens,
                    llm=self.search_llm
                )
                # logger.info(tools_response)
                messages += tool_results
                messages = format_messages(
                    messages=messages
                )
                # logger.info(f'sending tool response back to llm for file: {file_path}')
                response: AIMessage = await self.search_llm.ainvoke(messages)

            content = get_response_content(response=response)

            state["section_context"][section_heading] = f"\n\n{section_heading}\n\n{content}"

            logger.info(f'generated context for section: {section_heading}')
        else:
            logger.info(f'Ignoring context for section with empty prompt: {section_heading}')
        # logger.info(state["section_context"][section_heading])

        return get_state(state=state)

    @archie_exponential_retry()
    async def document_section(self, state: ReverseDocumentState) -> Dict[str, Any]:
        index = state["section_index"]
        section_heading = state["section_headings"][index]
        logger.info(f'generating new document section: {section_heading}')
        state["previous_tech_spec"] = state["updated_tech_spec"]

        if state["section_index"] == 0:
            state["updated_tech_spec"] += f"# Technical Specification"

        section_prompt = state["section_prompts"][section_heading]

        if section_prompt:
            split_heading = section_heading.split()
            if len(split_heading) > 1:
                numeric_heading = split_heading[0]
                numeric_heading_parts = numeric_heading.split('.')
                is_multi_part_numeric_heading = False

                if len(numeric_heading_parts) > 1:
                    is_multi_part_numeric_heading = numeric_heading_parts[0].isdigit() and \
                        numeric_heading_parts[1].isdigit()

                if is_multi_part_numeric_heading:
                    state["updated_tech_spec"] += f"\n\n## {section_heading}"
                else:
                    state["updated_tech_spec"] += f"\n\n# {section_heading}"
            else:
                state["updated_tech_spec"] += f"\n\n# {section_heading}"

            state["tech_spec_parsed"] = parse_sections_at_heading_level(
                text=state["updated_tech_spec"],
                heading_level=2
            )
            messages = [
                SystemMessage(content=[
                    {
                        "type": "text",
                        "text": DOCUMENT_SECTION_SYSTEM_PROMPT_TEMPLATE.format(
                            agent_persona=DOCUMENTER_PERSONA_PROMPTLET,
                            section_inputs=DG_INPUTS_PROMPTLET,
                            section_heading=section_heading,
                            section_rules=f"{DG_CONTEXT_RULES_PROMPTLET}\n\n{DG_OUTPUT_RULES_PROMPTLET}"
                        ),
                        "cache_control": {"type": "ephemeral"}
                    }
                ]),
                HumanMessage(content=[
                    {
                        "type": "text",
                        "text": SECTION_PROMPT_INPUT.format(
                            section_heading=section_heading,
                            section_prompt=section_prompt
                        )
                    },
                    {
                        "type": "text",
                        "text": DG_SECTION_CONTEXT_INPUT.format(
                            section_context=state["section_context"][section_heading]
                        )
                    },
                    {
                        "type": "text",
                        "text": TECH_SPEC_SECTIONS_INPUT.format(
                            tech_spec_sections=json.dumps(list(state["tech_spec_parsed"].keys()))
                        ),
                        "cache_control": {"type": "ephemeral"}
                    }
                ])
            ]

            response: AIMessage = await self.author_llm.ainvoke(messages)

            while len(response.tool_calls):
                # fill in the value of local variables
                messages.append(response)
                total_tokens = response.usage_metadata["total_tokens"]
                # logger.info(total_tokens)
                tools_response = await rd_author_tool_node.ainvoke({
                    "messages": [response],
                    "company_id": state["company_id"],
                    "repo_id": state["repo_id"],
                    "repo_name": state["repo_name"],
                    "branch_id": state["branch_id"],
                    "user_id": state["user_id"],
                    "head_commit_hash": state["head_commit_hash"],
                    "graph_builder": state["graph_builder"],
                    "github_server": state["github_server"],
                    "tech_spec_parsed": state["tech_spec_parsed"]
                })

                tool_results = get_formatted_tool_result_messages(
                    tool_message_list=tools_response["messages"],
                    total_tokens=total_tokens,
                    llm=self.author_llm
                )
                # logger.info(tools_response)
                messages += tool_results
                messages = format_messages(
                    messages=messages
                )
                # logger.info(f'sending tool response back to llm for file: {file_path}')
                response: AIMessage = await self.author_llm.ainvoke(messages)

            content = get_response_content(response=response)
            content = get_json_content(content, strict=True)

            if not len(content):
                logger.warning(f'No content block found for section: {section_heading}, retrying')
                state["updated_tech_spec"] = state["previous_tech_spec"]
                raise FormattingError

            if self.markdown_server:
                try:
                    logger.info('validating mermaid diagrams')
                    content = MermaidFixer(self.diagram_llm, self.markdown_server).process_content(content)
                except Exception as e:
                    logger.error(f'Exception while fixing mermaid: {e}')

            state["updated_tech_spec"] += f"\n\n{content}"
            logger.info(f'generated document section: {section_heading}')
        else:
            content = f"# {section_heading}"
            state["updated_tech_spec"] += f"\n\n{content}"
            logger.info(f'Added section heading to document: {section_heading}')
        state["section_index"] += 1

        state["updated_tech_spec"] = clean_document(text=state["updated_tech_spec"])

        # logger.info(state["updated_tech_spec"])

        return get_state(state=state)

    def document_router(self, state: ReverseDocumentState) -> Literal["continue", "end"]:
        if state["section_index"] >= state["total_sections"]:
            return "end"
        return "continue"

    @archie_exponential_retry()
    async def summarize_changes(self, state: ReverseDocumentState) -> Dict[str, Any]:
        section_heading = list(UPDATED_TECHNICAL_SECTION_ADDENDUM.keys())[0]
        logger.info(f'adding section: {section_heading}')
        section_prompt = UPDATED_TECHNICAL_SECTION_ADDENDUM[section_heading]

        state["previous_tech_spec"] = state["updated_tech_spec"]
        state["updated_tech_spec"] += f"# Technical Specification"
        state["updated_tech_spec"] += f"\n\n# {section_heading}"

        messages = [
            SystemMessage(content=[
                {
                    "type": "text",
                    "text": SUMMARY_SECTION_SYSTEM_PROMPT.format(
                        agent_persona=SUMMARY_DOCUMENTER_PERSONA_PROMPTLET,
                        section_inputs=SUMMARY_CHANGES_INPUTS,
                        section_heading=section_heading,
                        section_rules=f"{SUMMARY_CHANGES_RULES}\n\n{SEARCH_RULES_PROMPTLET}"
                    ),
                    "cache_control": {"type": "ephemeral"}
                }
            ]),
            HumanMessage(content=[
                {
                    "type": "text",
                    "text": SECTION_PROMPT_INPUT.format(
                        section_heading=section_heading,
                        section_prompt=section_prompt
                    )
                },
                {
                    "type": "text",
                    "text": TECH_SPEC_SECTIONS_INPUT.format(
                        tech_spec_sections=json.dumps(list(state["tech_spec_parsed"].keys()))
                    )
                },
                {
                    "type": "text",
                    "text": NEW_REQUIREMENTS_INPUT.format(
                        new_requirements=state["new_requirements"]
                    )
                },
                {
                    "type": "text",
                    "text": ROOT_FOLDER_INPUT.format(
                        root_folder_contents=state["root_folder_contents"]
                    ),
                    "cache_control": {"type": "ephemeral"}
                }
            ])
        ]

        response: AIMessage = await self.summarizer_llm.ainvoke(messages)

        while len(response.tool_calls):
            # fill in the value of local variables
            messages.append(response)
            total_tokens = response.usage_metadata["total_tokens"]
            # logger.info(total_tokens)
            tools_response = await rd_search_tool_node.ainvoke({
                "messages": [response],
                "company_id": state["company_id"],
                "repo_id": state["repo_id"],
                "repo_name": state["repo_name"],
                "branch_id": state["branch_id"],
                "user_id": state["user_id"],
                "head_commit_hash": state["head_commit_hash"],
                "graph_builder": state["graph_builder"],
                "github_server": state["github_server"],
                "tech_spec_parsed": state["tech_spec_parsed"]
            })

            tool_results = get_formatted_tool_result_messages(
                tool_message_list=tools_response["messages"],
                total_tokens=total_tokens,
                llm=self.summarizer_llm
            )
            # logger.info(tools_response)
            messages += tool_results
            messages = format_messages(
                messages=messages
            )
            # logger.info(f'sending tool response back to llm for file: {file_path}')
            response: AIMessage = await self.summarizer_llm.ainvoke(messages)

        # logger.info(response)

        content = get_response_content(response=response)
        content = get_json_content(content, strict=True)

        # logger.info(content)

        if not len(content):
            logger.warning(f'No content block found for section: {section_heading}, retrying')
            state["updated_tech_spec"] = state["previous_tech_spec"]
            raise FormattingError

        state["updated_tech_spec"] += f"\n\n{content}"
        state["summary_of_changes"] = content

        logger.info(f'finished adding section: {section_heading}')

        return get_state(state=state)

    @archie_exponential_retry(
        exceptions=RETRYABLE_EXCEPTIONS + SUPPLEMENTARY_RETRYABLE_EXCEPTIONS
    )
    async def identify_changes(self, state: ReverseDocumentState) -> Dict[str, Any]:
        index = state["section_index"]
        section_heading = state["section_headings"][index]
        logger.info(f'identifying changes for new document section: {section_heading}')

        section_prompt = state["section_prompts"][section_heading]

        if section_prompt:
            # logger.info(state["current_tech_spec_sections"])
            heading_key = section_heading
            heading_choices = list(state["current_tech_spec_sections"].keys())
            if section_heading not in heading_choices:
                fuzzy_heading_choices = process.extract(
                    query=heading_key,
                    choices=heading_choices,
                    limit=1
                )
                if len(fuzzy_heading_choices) and fuzzy_heading_choices[0]:
                    heading_key = fuzzy_heading_choices[0][0]
                    logger.warning(f'fuzzy matched heading: {heading_key}')
            # subheadings of the current heading level
            heading_level = determine_heading_level(format_document_heading(sub_heading=heading_key)) + 1
            state["parsed_sub_sections"] = parse_sections_at_heading_level(
                text=state["current_tech_spec_sections"][heading_key],
                heading_level=heading_level
            )
            # logger.info(f'parsed: {state["parsed_sub_sections"]}')
            messages = [
                SystemMessage(content=[
                    {
                        "type": "text",
                        "text": DUC_SYSTEM_PROMPT_TEMPLATE.format(
                            agent_persona=DOCUMENTER_PERSONA_PROMPTLET,
                            section_heading=section_heading,
                            section_inputs=DUC_INPUTS_PROMPTLET,
                            section_rules=f"{DUC_CHANGE_RULES_PROMPTLET}\n\n{DUC_OUTPUT_RULES_PROMPTLET}"
                        )
                    }
                ]),
                HumanMessage(content=[
                    {
                        "type": "text",
                        "text": NEW_REQUIREMENTS_INPUT.format(
                            new_requirements=state["summary_of_changes"]
                        )
                    },
                    {
                        "type": "text",
                        "text": DUC_STRUCTURED_SECTION_INPUT.format(
                            section_heading=section_heading,
                            structured_contents=state["parsed_sub_sections"]
                        )
                    },
                    {
                        "type": "text",
                        "text": DUC_STRUCTURED_SUBHEADINGS_INPUT.format(
                            subheadings=json.dumps(list(state["parsed_sub_sections"].keys()))
                        )
                    }
                ])
            ]

            structured_llm = self.architect_llm.with_structured_output(DocumentSections, strict=True)

            response: BaseModel = await structured_llm.ainvoke(messages)

            response_dict = response.model_dump()
            state["structured_sections"] = response_dict["sections"]

            for section in state["structured_sections"]:
                sub_heading: str = section["heading"]
                if not state["parsed_sub_sections"].get(sub_heading, ""):
                    logger.warning(f'Invalid heading found: {sub_heading}, raising formatting error')
                    raise FormattingError

            logger.info(f'finished identifying changes for section: {section_heading}')
            # logger.info(f'structured sections {state["structured_sections"]}')
        else:
            logger.info(f'Ignoring changes for section with empty prompt: {section_heading}')
        # logger.info(state["section_context"][section_heading])

        return get_state(state=state)

    @archie_exponential_retry()
    async def update_section(self, state: ReverseDocumentState) -> Dict[str, Any]:
        state["previous_tech_spec"] = state["updated_tech_spec"]
        state["previous_structured_sections"] = state["structured_sections"].copy()
        index = state["section_index"]
        section_heading = state["section_headings"][index]
        logger.info(f'updating new document section: {section_heading}')

        section_prompt = state["section_prompts"][section_heading]

        content = ""
        if section_prompt:
            formatted_section_heading = format_document_heading(section_heading)
            state["updated_tech_spec"] += f"\n\n{formatted_section_heading}"
            while len(state["structured_sections"]) > 0:
                section = state["structured_sections"].pop(0)
                sub_heading: str = section["heading"]
                sub_status: DocumentSectionStatus = section["status"]
                subsection_content = state['parsed_sub_sections'][sub_heading]

                if sub_status == DocumentSectionStatus.CHANGED.value:
                    logger.info(f'Working on {sub_status} section: {sub_heading}')
                    formatted_heading = format_document_heading(sub_heading)
                    changes = section["changes"]

                    tech_spec_level_two = parse_sections_at_heading_level(
                        text=state["updated_tech_spec"],
                        heading_level=2
                    )
                    state["tech_spec_parsed"] = tech_spec_level_two

                    state["updated_tech_spec"] += f"\n\n{formatted_heading}"
                    messages = [
                        SystemMessage(content=[
                            {
                                "type": "text",
                                "text": DU_SYSTEM_PROMPT_TEMPLATE.format(
                                    agent_persona=DOCUMENTER_PERSONA_PROMPTLET,
                                    section_inputs=DU_INPUTS_PROMPTLET,
                                    section_heading=sub_heading,
                                    section_rules=f"{DU_CONTEXT_RULES_PROMPTLET}\n\n{DU_OUTPUT_RULES_PROMPTLET}"
                                ),
                                "cache_control": {"type": "ephemeral"}
                            }
                        ]),
                        HumanMessage(content=[
                            {
                                "type": "text",
                                "text": TECH_SPEC_SECTIONS_INPUT.format(
                                    tech_spec_sections=json.dumps(list(state["tech_spec_parsed"].keys()))
                                ),
                                "cache_control": {"type": "ephemeral"}
                            },
                            {
                                "type": "text",
                                "text": SECTION_PROMPT_INPUT.format(
                                    section_heading=sub_heading,
                                    section_prompt=section_prompt
                                )
                            },
                            {
                                "type": "text",
                                "text": SECTION_CONTENT_INPUT.format(
                                    section_heading=sub_heading,
                                    section_content=subsection_content
                                )
                            },
                            {
                                "type": "text",
                                "text": SECTION_CHANGES_INPUT.format(
                                    section_changes=json.dumps(changes)
                                )
                            }
                        ])
                    ]

                    try:
                        response: AIMessage = await self.author_llm.ainvoke(messages)

                        while len(response.tool_calls):
                            # fill in the value of local variables
                            messages.append(response)
                            total_tokens = response.usage_metadata["total_tokens"]
                            # logger.info(total_tokens)
                            tools_response = await rd_author_tool_node.ainvoke({
                                "messages": [response],
                                "company_id": state["company_id"],
                                "repo_id": state["repo_id"],
                                "repo_name": state["repo_name"],
                                "branch_id": state["branch_id"],
                                "user_id": state["user_id"],
                                "head_commit_hash": state["head_commit_hash"],
                                "graph_builder": state["graph_builder"],
                                "github_server": state["github_server"],
                                "tech_spec_parsed": state["tech_spec_parsed"]
                            })

                            tool_results = get_formatted_tool_result_messages(
                                tool_message_list=tools_response["messages"],
                                total_tokens=total_tokens,
                                llm=self.author_llm
                            )
                            # logger.info(tools_response)
                            messages += tool_results
                            messages = format_messages(
                                messages=messages
                            )
                            # logger.info(f'sending tool response back to llm for file: {file_path}')
                            response: AIMessage = await self.author_llm.ainvoke(messages)
                    except RETRYABLE_EXCEPTIONS as e:
                        logger.warning(f'encountered error, retrying: {e}')
                        state["structured_sections"] = state["previous_structured_sections"].copy()
                        raise

                    # logger.info(response)

                    content = get_response_content(response=response)
                    content = get_json_content(content, strict=True)

                    # logger.info(content)

                    if not len(content):
                        logger.warning(f'no content found, retrying')
                        state["updated_tech_spec"] = state["previous_tech_spec"]
                        state["structured_sections"] = state["previous_structured_sections"].copy()
                        raise FormattingError

                    if self.markdown_server:
                        try:
                            logger.info('validating mermaid diagrams')
                            content = MermaidFixer(self.diagram_llm, self.markdown_server).process_content(content)
                        except Exception as e:
                            logger.error(f'Exception while fixing mermaid: {e}')

                    logger.info(f'updated document section: {sub_heading}')
                    state["updated_tech_spec"] += f"\n\n{content}"
                else:
                    logger.info(f'Copying over {sub_status} section: {sub_heading}')
                    formatted_heading = format_document_heading(sub_heading)
                    state["updated_tech_spec"] += f"\n\n{formatted_heading}\n\n{subsection_content}"
                    # logger.info(content)
                # logger.info(state["updated_tech_spec"])
        else:
            content = f"# {section_heading}"
            state["updated_tech_spec"] += f"\n\n{content}"
            logger.info(f'Added section heading to document: {section_heading}')
        state["section_index"] += 1

        state["updated_tech_spec"] = clean_document(text=state["updated_tech_spec"])

        return get_state(state=state)
