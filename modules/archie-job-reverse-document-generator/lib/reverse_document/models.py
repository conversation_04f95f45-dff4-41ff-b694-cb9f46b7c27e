from enum import Enum
from typing import List

from pydantic import BaseModel, Field


class DocumentSectionStatus(str, Enum):
    CHANGED = "CHANGED"
    UNCHANGED = "UNCHANGED"


class DocumentSection(BaseModel):
    heading: str = Field(
        ...,
        description="The section subheading without the '#' symbols or preceeding whitespaces. Must be a valid first-order child of the assigned heading. "
        "Examples: Assigned heading 4., first-order child 4.1; Assigned heading 3.1, first-order child 3.1.1, and so on"
    )
    status: DocumentSectionStatus = Field(
        ...,
        description="Whether or not the content in this section needs any changes to satisfy the new requirements."
    )
    changes: List[str] = Field(
        ...,
        description="Itemized list of changes for each heading in this section, if status is CHANGED. Empty list if and only status is UNCHANGED."
    )


class DocumentSections(BaseModel):
    sections: List[DocumentSection] = Field(
        ...,
        description="List of all sections provided to you as input, processed with details about changes, if any."
    )
