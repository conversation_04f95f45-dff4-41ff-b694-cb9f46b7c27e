from google.cloud import pubsub_v1

from blitzy_utils.common import publish_notification
from blitzy_utils.enums import ProjectPhase, JobStatus

PROJECT_ID = 'blitzy-os-dev'
PLATFORM_EVENTS_TOPIC = 'platform-events'

publisher = pubsub_v1.PublisherClient()

notification_data = {
    "projectId": "project_id",
    "jobId": "job_id",
    "tech_spec_id": "tech_spec_id",
    "org_name": "org_name",
    "repo_id": "repo_id",
    "branch_name": "branch_name",
    "branch_id": "branch_id",
    "head_commit_hash": "head_commit_hash",
    "phase": ProjectPhase.TECHNICAL_SPECIFICATION.value,
    "status": JobStatus.DONE.value,
    "user_id": "user_id",
    "team_id": "team_id",
    "company_id": "company_id",
    "metadata": {
        "propagate": "propagate",
        "repo_name": "repo_name",
        "document_mode": "document_mode"
    }
}
publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)
