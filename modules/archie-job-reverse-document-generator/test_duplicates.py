
from typing import Dict, Optional
import json
import re
from lib.reverse_document.doc import text

from blitzy_platform_shared.document.utils import parse_sections_at_heading_level, get_first_n_headings
# tech_spec_remaining_dict = parse_sections_at_heading_level(
#     text=text,
#     heading_level=2
# )

DOCUMENT_REPLACEMENTS = {
    ('<span style="background-color: rgba(91, 57, 243, 0.2)">```', '```', False),
    ('```</span>', '```', False),
    (r'^(?!.*<span style="background-color: rgba\(91, 57, 243, 0\.2\)">)(\s*- .+)</span>\s*$', r'\1', True),
    ('<span style="background-color: rgba(91, 57, 243, 0.2)">- ', '- ', False),
    (r'^<span style="background-color: rgba\(91, 57, 243, 0.2\)">(#{2,6}\s+)', r'\1', True),  # Span starting a heading
    (r'(^#{2,6}\s+.*?)</span>', r'\1', True)
}


def format_document_heading(sub_heading: str) -> str:
    """
    Format headings based on numbering pattern:

    Args:
        sub_heading (str): The heading text to format

    Returns:
        str: Formatted heading with appropriate markdown
    """
    # Split the heading to check if it starts with a number pattern
    parts = sub_heading.split(maxsplit=1)
    if not parts:
        return "#### "

    first_part = parts[0]

    # Check if the first part is a numbering pattern
    if first_part and first_part.count('.') > 0:
        # Count the dots to determine heading level
        dot_count = first_part.count('.')

        if first_part[-1] != '.':
            return f"{'#' * (dot_count + 1)} {sub_heading}"
        else:
            return f"# {sub_heading}"

    # For all other cases (no numbering), use quadruple hash
    return f"#### {sub_heading}"


def clean_document(text: str) -> str:
    """
    Removes duplicate headings that appear consecutively with only blank lines in between.
    Also validates and corrects heading levels to ensure they follow the rule:
    number of hashes = number of dots in the numbering pattern + 1.

    Also removes highlighting around code snippets.

    Args:
        text (str): The markdown document text

    Returns:
        str: Document with duplicate headings removed and heading levels validated
    """
    lines = text.split('\n\n')
    result = []
    i = 0

    while i < len(lines):
        current_line = lines[i]

        # Check if the current line is a heading
        if current_line.strip() and current_line.lstrip().startswith('#') and current_line.strip() != '# Technical Specification':
            # Validate and fix heading level
            current_line = validate_heading_format(current_line)

            # Add this heading to the result
            result.append(current_line)

            # Check ahead for blank lines followed by the same heading
            j = i + 1
            blank_lines = []

            # Collect blank lines
            while j < len(lines) and not lines[j].strip():
                blank_lines.append(lines[j])
                j += 1

            # Is the next non-blank line the same heading?
            if j < len(lines) and lines[j] == current_line:
                print(f'Removing duplicate heading: {current_line}')
                i = j + 1
            else:
                # No duplicate. Add the blank lines and move i to j
                result.extend(blank_lines)
                i = j
        else:
            # Not a heading
            sublines = current_line.split('\n')
            cleaned_sublines = []

            for subline in sublines:
                for pattern, replacement, is_regex in DOCUMENT_REPLACEMENTS:
                    if is_regex:
                        if re.search(pattern, subline):
                            subline = re.sub(pattern, replacement, subline)
                            print(f'Applied regex replacement to get cleaned subline {subline}')
                    elif pattern in subline:
                        subline = subline.replace(pattern, replacement)
                        print(f'Applied replacement to get {subline}')

                cleaned_sublines.append(subline)

            current_line = '\n'.join(cleaned_sublines)
            result.append(current_line)
            i += 1

    return '\n\n'.join(result)


def validate_heading_format(heading_line: str) -> str:
    """
    Validates and corrects a heading line to ensure the number of hash symbols
    is one more than the number of dots in the numbering pattern.

    Args:
        heading_line (str): The heading line to validate

    Returns:
        str: Corrected heading line
    """
    # Split the heading line into hash part and text part
    parts = heading_line.strip().split(' ', 1)

    # Extract the hash part
    hash_part = parts[0]
    text_part = parts[1]

    # Ensure hash_part only contains hash symbols
    if not all(c == '#' for c in hash_part) or not text_part[0].isnumeric():
        # This is not a properly formatted heading
        return heading_line

    # Extract the heading text
    heading_text = parts[1] if len(parts) > 1 else ""

    # Use format_document_heading to get the correct format
    correct_format = format_document_heading(heading_text)

    # Extract just the hash part from the correct format
    correct_hash_parts = correct_format.split(' ', 1)
    correct_hash = correct_hash_parts[0]

    # If the heading has a different number of hashes than expected, log and fix it
    if hash_part != correct_hash:
        print(f'Correcting heading level: "{heading_line}" -> "{correct_hash} {heading_text}"')
        return f"{correct_hash} {heading_text}"

    # If it's already correct, return as is
    return heading_line


clean_document(text=text)
