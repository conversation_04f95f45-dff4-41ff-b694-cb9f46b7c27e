from google.cloud import pubsub_v1

from blitzy_utils.common import publish_notification
from blitzy_utils.enums import BackpropChangeMode

PROJECT_ID = 'blitzy-os-dev'
REVERSE_DOCUMENT_TOPIC = 'generate-reverse-document'

publisher = pubsub_v1.PublisherClient()

notification_data = {"tech_spec_id": "77653be2-c745-4c23-941d-04b1114764a7", "repo_name": "hao-backprop-test", "repo_id": "1003925140", "branch_id": "19dff804-56df-4f12-ab39-9d5b0057b9de", "branch_name": "main11", "company_id": "39031fee-7463-4c85-96cf-824c8bb351e1", "user_id": "112e7780-729a-4ed6-8cc9-3e8bfc19a54c", "team_id": "bf09b2d1-d6b6-4385-baf4-2eda0f29b9e4",
                     "job_id": "5cad9963-a9f4-4e67-b688-736a14cd7896", "project_id": "e9c04052-f450-4dcf-8b76-1bf23ec3ca8d", "head_commit_hash": "f5c8724e1f2a7d4ec5e9edc14f2398e613b1789e", "prev_head_commit_hash": "f5c8724e1f2a7d4ec5e9edc14f2398e613b1789e", "propagate": False, "previous_tech_spec_id": "d3d31b64-6ad5-49c6-a9c1-29c66f6cc2f7", "document_mode": "UPDATE"}

publish_notification(publisher, notification_data, PROJECT_ID, REVERSE_DOCUMENT_TOPIC)
