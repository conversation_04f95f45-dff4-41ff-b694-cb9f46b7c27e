import os
import json
import asyncio
from typing import Dict, Any

from blitzy_utils.logger import logger
from google.cloud import storage, pubsub_v1
from langgraph.graph import StateGraph

from blitzy_utils.common import publish_notification, download_from_gcs, upload_to_gcs, \
    get_repo_documentation_folder_path, \
    get_tech_spec_name, get_existing_product_tech_spec_name, get_existing_product_updated_tech_spec_name
from blitzy_utils.enums import ProjectPhase, JobStatus, BackpropChangeMode
from blitzy_utils.consts import TECH_SPECIFICATION_NAME, PROMPT_FILE_NAME, DOCUMENT_PROMPT_FILE_NAME

from blitzy_platform_shared.code_graph.builder import CodeGraphBuilder
from blitzy_platform_shared.common.llms import llm_gpt4_1, llm_claude_4_sonnet_low_thinking_med_output, \
    llm_o4_mini, llm_claude_4_opus_med_thinking_max_output
from blitzy_platform_shared.document.utils import parse_sections_at_heading_level

from lib.reverse_document.helper import ReverseDocumentHelper, rd_search_tools, rd_search_node_tools, rd_author_tools
from lib.reverse_document.state import ReverseDocumentState

EVENT_DATA = os.environ["EVENT_DATA"]
PROJECT_ID = os.environ["PROJECT_ID"]
GCS_BUCKET_NAME = os.environ["GCS_BUCKET_NAME"]
PRIVATE_BLOB_NAME = os.environ["PRIVATE_BLOB_NAME"]
PLATFORM_EVENTS_TOPIC = os.environ["PLATFORM_EVENTS_TOPIC"]
NEO4J_SERVER = os.environ["NEO4J_SERVER"]
NEO4J_USERNAME = os.environ["NEO4J_USERNAME"]
NEO4J_PASSWORD = os.environ["NEO4J_PASSWORD"]
MARKDOWN_SERVER = os.environ["MARKDOWN_SERVER"]
GITHUB_SECRET_SERVER = os.environ["GITHUB_SECRET_SERVER"]

ANTHROPIC_API_KEY = os.environ["ANTHROPIC_API_KEY"]
OPENAI_API_KEY = os.environ["OPENAI_API_KEY"]
VOYAGE_API_KEY = os.environ["VOYAGE_API_KEY"]

LANGCHAIN_TRACING_V2 = os.environ["LANGCHAIN_TRACING_V2"]
LANGCHAIN_ENDPOINT = os.environ["LANGCHAIN_ENDPOINT"]
LANGCHAIN_API_KEY = os.environ["LANGCHAIN_API_KEY"]
LANGCHAIN_PROJECT = os.environ["LANGCHAIN_PROJECT"]

storage_client = storage.Client()
publisher = pubsub_v1.PublisherClient()


async def generate_reverse_document(event_data: Dict[str, Any], graph_builder: CodeGraphBuilder):
    repo_name = event_data.get('repo_name')
    project_id = event_data.get('project_id', '')
    job_id = event_data.get('job_id', '')
    propagate = event_data.get('propagate', True)
    user_id = event_data.get('user_id', 'default')
    team_id = event_data.get('team_id', 'default')
    company_id = event_data.get('company_id', 'default')
    repo_id = event_data.get('repo_id', 'repo_id')
    branch_id = event_data.get('branch_id', 'main')
    head_commit_hash = event_data.get('head_commit_hash', '')
    document_mode = event_data.get('document_mode', BackpropChangeMode.GENERATE.value)
    tech_spec_id = event_data.get('tech_spec_id', "")
    previous_tech_spec_id = event_data.get("previous_tech_spec_id", "")
    branch_name = event_data.get('branch_name', 'main')
    org_name = event_data.get('org_name', '')

    notification_data = {
        "projectId": project_id,
        "jobId": job_id,
        "tech_spec_id": tech_spec_id,
        "org_name": org_name,
        "repo_id": repo_id,
        "branch_name": branch_name,
        "phase": ProjectPhase.TECHNICAL_SPECIFICATION.value,
        "status": JobStatus.IN_PROGRESS.value,
        "user_id": user_id,
        "metadata": {
            "propagate": propagate,
            "repo_name": repo_name,
            "document_mode": document_mode
        }
    }
    publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)

    job_metadata = {
        'is_org_repo': True,
        'repo_name': repo_name,
        'repo_id': repo_id,
        'branch_id': branch_id,
        'company_id': company_id,
        'user_id': user_id,
        'team_id': team_id,
        'project_id': project_id,
        'head_commit_hash': head_commit_hash
    }

    document_folder_path = get_repo_documentation_folder_path(
        company_id=company_id,
        team_id=team_id,
        user_id=user_id,
        repo_name=repo_name,
        repo_id=repo_id,
        branch_id=branch_id,
        blob_name=PRIVATE_BLOB_NAME
    )

    document_prompt = ""
    document_prompt_filename = f"{DOCUMENT_PROMPT_FILE_NAME}.md"
    try:
        document_prompt = download_from_gcs(
            storage_client=storage_client,
            bucket_name=GCS_BUCKET_NAME,
            filename=document_prompt_filename,
            blob_name=document_folder_path
        )
    except Exception as e:
        logger.warning(f"Documentation prompt file not found, continuing without it")
        document_prompt = ""

    current_tech_spec = ""
    input_prompt = ""
    ctech_spec_parsed = {}
    if document_mode == BackpropChangeMode.UPDATE.value:
        # download existing spec
        current_tech_spec_filename = f'{TECH_SPECIFICATION_NAME} - {head_commit_hash}.md'
        if previous_tech_spec_id:
            current_tech_spec_filename = get_tech_spec_name(
                tech_spec_id=previous_tech_spec_id
            )
        current_tech_spec = download_from_gcs(
            storage_client,
            GCS_BUCKET_NAME,
            current_tech_spec_filename,
            blob_name=document_folder_path
        )

        ctech_spec_level_two = parse_sections_at_heading_level(
            text=current_tech_spec,
            heading_level=2
        )
        ctech_spec_parsed = ctech_spec_level_two

        input_prompt_filename = f"{PROMPT_FILE_NAME}.md"
        # download prompt
        input_prompt = download_from_gcs(
            storage_client=storage_client,
            bucket_name=GCS_BUCKET_NAME,
            filename=input_prompt_filename,
            blob_name=document_folder_path
        )

    rd_helper = ReverseDocumentHelper(
        search_llm=llm_claude_4_opus_med_thinking_max_output.bind_tools(rd_search_tools),
        architect_llm=llm_o4_mini,
        summarizer_llm=llm_claude_4_opus_med_thinking_max_output.bind_tools(rd_search_tools),
        author_llm=llm_claude_4_sonnet_low_thinking_med_output.bind_tools(rd_author_tools),
        diagram_llm=llm_claude_4_opus_med_thinking_max_output,
        fallback_llm=llm_gpt4_1.bind_tools(rd_search_node_tools),
        job_metadata=job_metadata,
        graph_builder=graph_builder,
        head_commit_hash=head_commit_hash,
        markdown_server=MARKDOWN_SERVER,
        github_server=GITHUB_SECRET_SERVER
    )
    rd_generator: StateGraph = rd_helper.create_graph()
    app = rd_generator.compile()
    initial_state = ReverseDocumentState(
        add_context=document_prompt,
        tech_spec_parsed=ctech_spec_parsed,
        current_tech_spec=current_tech_spec,
        graph_builder=graph_builder,
        mode=document_mode,
        new_requirements=input_prompt,
        parsed_sub_sections={},
        root_folder_contents="",
        section_context={},
        structured_sections=[]
    )
    async for result in app.astream(input=initial_state, config={"recursion_limit": 500}, stream_mode="values"):
        if result.get("updated_tech_spec"):
            if tech_spec_id:
                tech_spec_filename = get_tech_spec_name(
                    tech_spec_id=tech_spec_id
                )
            else:
                tech_spec_filename = get_existing_product_tech_spec_name(
                    head_commit_hash=head_commit_hash
                )
                if document_mode == BackpropChangeMode.UPDATE.value:
                    tech_spec_filename = get_existing_product_updated_tech_spec_name(
                        head_commit_hash=head_commit_hash
                    )
            upload_to_gcs(storage_client=storage_client, bucket_name=GCS_BUCKET_NAME, blob_name=document_folder_path,
                          filename=tech_spec_filename,
                          content_type='text/markdown', data=result["updated_tech_spec"])

            if result["section_index"] < result["total_sections"]:
                notification_data = {
                    "projectId": project_id,
                    "jobId": job_id,
                    "tech_spec_id": tech_spec_id,
                    "org_name": org_name,
                    "repo_id": repo_id,
                    "branch_name": branch_name,
                    "branch_id": branch_id,
                    "head_commit_hash": head_commit_hash,
                    "phase": ProjectPhase.TECHNICAL_SPECIFICATION.value,
                    "status": JobStatus.IN_PROGRESS.value,
                    "user_id": user_id,
                    "team_id": team_id,
                    "company_id": company_id,
                    "metadata": {
                        "repo_name": repo_name,
                        "document_mode": document_mode,
                        "current_index": result["section_index"],
                        "total_steps": result["total_sections"]
                    }
                }
                print(notification_data, flush=True)
                publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)

    notification_data = {
        "projectId": project_id,
        "jobId": job_id,
        "tech_spec_id": tech_spec_id,
        "org_name": org_name,
        "repo_id": repo_id,
        "branch_name": branch_name,
        "branch_id": branch_id,
        "head_commit_hash": head_commit_hash,
        "phase": ProjectPhase.TECHNICAL_SPECIFICATION.value,
        "status": JobStatus.DONE.value,
        "user_id": user_id,
        "team_id": team_id,
        "company_id": company_id,
        "metadata": {
            "propagate": propagate,
            "repo_name": repo_name,
            "document_mode": document_mode
        }
    }
    publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)
    return event_data


if __name__ == "__main__":
    logger.info(f"Generating reverse document for notification data: {EVENT_DATA}")
    event_data = json.loads(EVENT_DATA)
    company_id = event_data.get('company_id', 'default')
    repo_id = event_data.get('repo_id', 'default')
    branch_id = event_data.get('branch_id', 'main')
    head_commit_hash = event_data.get('head_commit_hash', '')
    graph_builder = CodeGraphBuilder(
        uri=NEO4J_SERVER,
        username=NEO4J_USERNAME,
        password=NEO4J_PASSWORD,
        company_id=company_id,
        repo_id=repo_id,
        branch_id=branch_id,
        head_commit_hash=head_commit_hash
    )
    try:
        asyncio.run(generate_reverse_document(event_data=event_data, graph_builder=graph_builder))
    except Exception as e:
        logger.error(f'Failed to generate reverse document: {e}')
        raise
    finally:
        graph_builder.close()
