from google.cloud import pubsub_v1

from blitzy_utils.enums import ProjectPhase, JobStatus, ProjectPhaseAction
from blitzy_utils.common import publish_notification

PROJECT_ID = 'blitzy-os-dev'
PLATFORM_EVENTS_TOPIC = 'platform-events'

publisher = pubsub_v1.PublisherClient()

notification_data = {
    "projectId": '2470436b-2434-4520-867e-804acbd04cf8',
    # "jobId": job_id,
    "phase": ProjectPhase.TECHNICAL_SPECIFICATION.value,
    "action": ProjectPhaseAction.GENERATE_PDF.value,
    "status": JobStatus.IN_PROGRESS.value,
}

# publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)

notification_data = {'projectId': '6fc36cf9-3541-4ea7-9160-7b7216fe3b84', 'jobId': '8fd829da-90b5-4298-a400-3bc2c314499f', 'tech_spec_id': '818cf4a2-7d18-4038-9e0e-f635159e7ac0', 'org_name': '', 'repo_id': '*********', 'branch_name': 'main', 'branch_id': 'fe3bfcf0-494e-4a5e-a86b-7f04b52623b5',
                     'head_commit_hash': '7c36f6008bce64d34f1b8300518fda5b1818a13a', 'phase': 'TECHNICAL_SPECIFICATION', 'status': 'DONE', 'user_id': '692ba245-8903-4184-ab34-bb5599c1f5b3', 'team_id': 'default', 'company_id': 'default', 'metadata': {'propagate': True, 'repo_name': 'nexus-public', 'document_mode': 'UPDATE'}}
publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)
