# Name of the distribution directory
DIST_DIR := dist
PYPROJECT := pyproject.toml
TWINE_REPOSITORY_URL ?= https://us-east1-python.pkg.dev/blitzy-os-dev/python-us-east1/

# Default target
all: install-deps clean update-version build

# Install required dependencies for the project
install-deps:
	pip install --upgrade pip setuptools wheel build twine toml keyrings.google-artifactregistry-auth
	@echo "Installed/updated required dependencies."

# Clean up build artifacts
clean:
	rm -rf $(DIST_DIR) *.egg-info build
	@echo "Cleaned build artifacts."

# Update version in pyproject.toml based on deployed version
update-version:
	python update_version.py
	@echo "Version updated in $(PYPROJECT)."

# Build the package
build: clean
	python -m build
	@echo "Package built successfully."

# Upload the package to the repository
upload: build
	python -m twine upload --repository-url $(TWINE_REPOSITORY_URL) $(DIST_DIR)/*
	@echo "Package uploaded successfully to $(TWINE_REPOSITORY_URL)."

# Force a test upload to test environment (Optional, for testing)
test-upload: build
	python -m twine upload --repository-url https://test.pypi.org/legacy/ $(DIST_DIR)/*
	@echo "Package uploaded successfully to test PyPI."

.PHONY: all install-deps clean update-version build upload test-upload