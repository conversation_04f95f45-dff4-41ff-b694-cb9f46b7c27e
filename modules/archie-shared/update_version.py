import subprocess
import re
import toml  # Added the missing import


def get_latest_version_from_repository(repository_url, package_name):
    """
    Get the latest deployed version of the package from the repository.
    Assumes existence of the `gcloud` CLI for querying Google Artifact Registry.
    """
    try:
        # Use gcloud CLI to list package versions
        result = subprocess.run(
            [
                "gcloud",
                "artifacts",
                "versions",
                "list",
                f"--repository={repository_url}",
                f"--package={package_name}",
                "--location=us-east1",
                "--format=value(name)"
            ],
            capture_output=True,
            text=True,
        )
        if result.returncode != 0:
            raise Exception(f"Error querying the repository: {result.stderr}")

        # Parse the returned versions
        versions = result.stdout.strip().split("\n")
        if not versions or versions == ['']:
            print(f"No versions found for package: {package_name}. Starting with 0.0.0.")
            return "0.0.0"  # Default version if none exist

        # Extract the latest version using semantic sort
        latest_version = sorted(versions, key=semantic_version_key)[-1]
        print(f"Latest version fetched from repository: {latest_version}")
        return latest_version.split("/")[-1]  # Normalize output to version only

    except Exception as e:
        print(f"An error occurred while querying the repository: {str(e)}")
        return "0.0.0"  # Default version in case of failure


def semantic_version_key(version):
    """
    Helper function to sort semantic versions correctly.
    """
    return tuple(map(int, re.findall(r"(\d+)", version)))


def increment_version(version, part="patch"):
    """
    Increment the version based on the specified part ('major', 'minor', 'patch').
    """
    major, minor, patch = map(int, version.split("."))

    if part == "patch":
        patch += 1
    elif part == "minor":
        minor += 1
        patch = 0
    elif part == "major":
        major += 1
        minor = 0
        patch = 0
    else:
        raise ValueError("Invalid version part. Use 'major', 'minor', or 'patch'.")

    return f"{major}.{minor}.{patch}"


def update_pyproject_version(file_path, new_version):
    """
    Update the version field in pyproject.toml without adding or modifying anything else.
    """
    with open(file_path, "r") as f:
        pyproject_data = toml.load(f)

    # Update only the "version" field
    if "project" in pyproject_data and "version" in pyproject_data["project"]:
        pyproject_data["project"]["version"] = new_version
    else:
        raise KeyError("The version field is missing in the [project] section of pyproject.toml.")

    print(f"Updated pyproject.toml version to: {new_version}")

    # Write the changes back
    with open(file_path, "w") as f:
        toml.dump(pyproject_data, f)


if __name__ == "__main__":
    # Configuration (use the exact package name from the repository)
    pyproject_path = "pyproject.toml"
    repository_url = "python-us-east1"  # Name of the repository
    package_name = "blitzy-platform-shared"  # Must match the exact package name

    # Fetch the latest version from the repository
    latest_version = get_latest_version_from_repository(repository_url, package_name)

    # Increment the latest version (patch by default, modify as needed)
    new_version = increment_version(latest_version, part="patch")

    # Update the version in pyproject.toml
    update_pyproject_version(pyproject_path, new_version)
