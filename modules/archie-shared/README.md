# blitzy-platform-shared

Add the following in requirements.txt, replacing <region>, <project_id>, and <repository_name> with the Artifact Registry details

--extra-index-url https://<region>-python.pkg.dev/<project_id>/<repository_name>/simple/
blitzy-platform-shared==0.0.1

## Commands
python3 -m build

python3 -m twine upload --repository-url https://<region>-python.pkg.dev/<project_id>/<repository_name>/ dist/*


## To build and upload

This command will install required dependencies, clean dist folder, update-version using previously published version
and build.
```shell 
make 
```

This command will upload binary to the artifactory
```shell
make upload
```
