import requests
import logging
from google.auth.transport.requests import Request
from google.oauth2 import id_token
from typing import TypedDict

from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from requests.exceptions import JSONDecodeError

from ..common.utils import get_response_content, get_formatted_tool_result_messages, archie_exponential_retry, \
    format_messages
from .prompts import MERMAID_FIXER_SYSTEM_PROMPT, MERMAID_FIXER_HUMAN_PROMPT
from .consts import DIAGRAM_RETRIES

logger = logging.getLogger(__name__)


class ValidationResult(TypedDict):
    error: str
    diagramType: str


def extract_mermaid_content(content, strict=True, start=0):
    """
      Find and extract the body of the first mermaid diagram in the "content" form the "start" position.
      The first mermaid diagram is defined to be between the first "```mermaid\n" and "\n```".
      Extract the body and return a tuple: (extracted_content, body_start, body_end)
    """
    openStr = '```mermaid\n'
    closeStr = '\n```'
    start = content.find(openStr, start)
    end = content.find(closeStr, start + len(openStr))

    if start != -1 and end != -1 and start != end:
        # Extract the content between the backticks
        extracted_content = content[start+len(openStr):end].strip()
        return extracted_content.strip(), start+len(openStr), end
    else:
        logger.warning("No mermaid block found")
        if strict:
            return "", -1, -1


class MermaidFixer:
    def __init__(self, llm: BaseChatModel, validator_endpoint: str):
        self.llm = llm
        self.validator_endpoint = validator_endpoint

    def process_content(self, content: str) -> str:
        next_start = 0
        while True:
            mermaid_text, body_begin, body_end = extract_mermaid_content(content, start=next_start)
            logger.info(f'extracted mermaid: {mermaid_text}')
            if mermaid_text:
                # mermaid_text = "graph TDa\n    A[Start] --> B{Is it raining?}\n    B -->|Yes| C[Bring an umbrella]\n    B -->|No| D[Enjoy the sunshine]\n    C --> E[End]\n    D --> E[End]"
                revised_mermaid_text = self.process_mermaid(mermaid_text)
                if mermaid_text == revised_mermaid_text:
                    next_start = body_end
                else:
                    content = content[:body_begin] + revised_mermaid_text + content[body_end:]
                    next_start = body_begin + len(revised_mermaid_text)
            else:
                break

        return content

    def process_mermaid(self, mermaid_text: str) -> str:
        if not mermaid_text:
            return mermaid_text

        previous_versions = set()  # Track previous versions to detect cycles
        retries_left = DIAGRAM_RETRIES

        while retries_left > 0:
            validation_result = self.validate_mermaid(mermaid_text)
            error = validation_result.get('error')

            if not error:
                logger.info(f'Validated mermaid diagram type: {validation_result.get("diagramType")}')
                break

            # If we've seen this text before, we're in a loop
            if mermaid_text in previous_versions:
                logger.warning("Fix attempts resulted in previously seen diagram - breaking retry loop")
                break

            previous_versions.add(mermaid_text)
            mermaid_text = self.fix_mermaid(mermaid_text, error)
            retries_left -= 1

        return mermaid_text

    @archie_exponential_retry()
    def get_auth_token(self, target_url: str) -> str:
        """Generate authentication token for a service."""
        try:
            auth_req = Request()
            token = id_token.fetch_id_token(auth_req, target_url)
            return token
        except Exception as e:
            logger.error(f"Failed to get auth token: {str(e)}")
            raise

    def validate_mermaid(self, mermaid_text: str) -> ValidationResult:
        """
        Validate a Mermaid diagram spec.
        """
        # Get fresh token and add to headers
        token = self.get_auth_token(self.validator_endpoint)
        headers = {}
        if token:
            headers["Authorization"] = f"Bearer {token}"

        try:
            response = requests.post(
                url=self.validator_endpoint,
                headers=headers,
                json={"diagram": mermaid_text}
            )
        except Exception as e:
            logger.warning(f'Exception requesting to validate mermaid \n```{mermaid_text}```\n: {e}')
            raise e

        if response.status_code != 200:
            logger.warning(f"Failed to validate mermaid diagram ({response.status_code}): {response.text}")
            raise Exception(response.status_code)

        try:
            result: ValidationResult = response.json()
            return result
        except JSONDecodeError as e:
            logger.warning(f"Failed to decode response: {response.text()}")
            raise e

    @archie_exponential_retry()
    def fix_mermaid(self, mermaid_text: str, error: str) -> str:
        messages = [
            SystemMessage(MERMAID_FIXER_SYSTEM_PROMPT),
            HumanMessage(MERMAID_FIXER_HUMAN_PROMPT.format(mermaid_text=mermaid_text, error=error))
        ]

        # Generate the content using the LLM
        response: AIMessage = self.llm.invoke(messages)

        while len(response.tool_calls):
            # fill in the value of local variables
            messages.append(response)
            total_tokens = response.usage_metadata["total_tokens"]
            # logger.info(total_tokens)
            tools_response = self.mermaid_tool_node.invoke({
                "messages": [response]
            })

            tool_results = get_formatted_tool_result_messages(
                tool_message_list=tools_response["messages"],
                total_tokens=total_tokens,
                llm=self.llm
            )
            # logger.info(tools_response)
            messages += tool_results
            messages = format_messages(
                messages=messages
            )
            # logger.info(f'sending tool response back to llm for file: {file_path}')
            response: AIMessage = self.llm.invoke(messages)

        content = get_response_content(response=response)

        fixed_content, _, _ = extract_mermaid_content(content)
        logger.debug(f'fixed mermaid diagram:: {fixed_content}')
        # Fix mermaid diagram
        return fixed_content
