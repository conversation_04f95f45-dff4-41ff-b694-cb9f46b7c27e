DEFAULT_TECHNOLOGIES = """
    Default Technology Stack:

    Core Infrastructure:
    - Cloud Platform: AWS
    - Containerization: Docker
    - Infrastructure as Code: Terraform
    - CI/CD: GitHub Actions

    Backend:
    - Primary Language: Python
    - Framework: Flask
    - Authentication: Auth0
    - Database: MongoDB
    - AI Framework: Langchain

    Frontend:
    - Web: React with TypeScript
    - CSS Framework: TailwindCSS
    - Mobile/Cross-platform: React-Native with TypeScript

    Native Applications:
    - iOS: Swift
    - Android: Kotlin
    - MacOS: Objective-C
    - Desktop: ElectronJS
"""

TECHNICAL_SECTION_PROMPTS = {
    "1. INTRODUCTION": """
        Generate an INTRODUCTION section for the Technical Specification document that provides stakeholders with a clear understanding of the system's purpose, scope, and context.

        EXECUTIVE SUMMARY
            - Brief overview of the project
            - Core business problem being solved
            - Key stakeholders and users
            - Expected business impact and value proposition

        SYSTEM OVERVIEW
            - Project Context
            * Business context and market positioning
            * Current system limitations (if replacing/upgrading existing system)
            * Integration with existing enterprise landscape

            - High-Level Description
            * Primary system capabilities
            * Major system components
            * Core technical approach

            - Success Criteria
            * Measurable objectives
            * Critical success factors
            * Key performance indicators (KPIs)

        SCOPE
            Clearly define both in-scope and out-of-scope elements:

            In-Scope:
            - Core Features and Functionalities
            * Must-have capabilities
            * Primary user workflows
            * Essential integrations
            * Key technical requirements

            - Implementation Boundaries
            * System boundaries
            * User groups covered
            * Geographic/market coverage
            * Data domains included

            Out-of-Scope:
            - Explicitly list excluded features/capabilities
            - Future phase considerations
            - Integration points not covered
            - Unsupported use cases

        Output Format Requirements:
        - Use Markdown tables for structured data, text for unstructured data.
        - Tables should never have more than four columns.
    """,
    "2. PRODUCT REQUIREMENTS": """
        Generate a detailed PRODUCT REQUIREMENTS section that breaks down the product into discrete, testable features:

        Only include sections and items that are actually relevant to this system, based on your analysis of its requirements.
        Don't add any features of your own, or any items that aren't clearly applicable.

        FEATURE CATALOG
            For each feature, document:
            
            - Feature Metadata
                * Unique ID (format: F-XXX)
                * Feature Name
                * Feature Category
                * Priority Level (Critical/High/Medium/Low)
                * Status (Proposed/Approved/In Development/Completed)
                
            - Description
                * Overview
                * Business Value
                * User Benefits
                * Technical Context
                
            - Dependencies
                * Prerequisite Features
                * System Dependencies
                * External Dependencies
                * Integration Requirements

        FUNCTIONAL REQUIREMENTS TABLE
            For each feature, create a requirements table with:
            
            - Requirement Details
                * Requirement ID (format: F-XXX-RQ-YYY)
                * Description
                * Acceptance Criteria
                * Priority (Must-Have/Should-Have/Could-Have)
                * Complexity (High/Medium/Low)
                
            - Technical Specifications
                * Input Parameters
                * Output/Response
                * Performance Criteria
                * Data Requirements
                
            - Validation Rules
                * Business Rules
                * Data Validation
                * Security Requirements
                * Compliance Requirements

        FEATURE RELATIONSHIPS
            Document:
            - Feature dependencies map
            - Integration points
            - Shared components
            - Common services

            Only document feature relationships that are clearly evident in the requirements or source code.
            Don't imagine any feature relationships of your own.

        IMPLEMENTATION CONSIDERATIONS
            For each feature:
            - Technical constraints
            - Performance requirements
            - Scalability considerations
            - Security implications
            - Maintenance requirements

        Output Format:
        - Use markdown tables for structured data with not more than 4 columns per table
        - Include traceability matrix
        - Reference related process flowcharts
        - Link to relevant technical specifications

        Notes:
        - Maintain consistent ID formatting
        - Ensure requirements are testable
        - Include acceptance criteria
        - Reference related documents
        - Track requirement versions
        - Document assumptions and constraints
    """,
    "3. TECHNOLOGY STACK": f"""
        Generate the TECHNOLOGY STACK section adhering to existing architectural decisions and technical constraints based on existing sections of the document.
        Include and justify choices for each applicable subsection:

        Only include sections and items that are actually relevant to this system, based on your analysis of its requirements.
        Don't add any items that aren't clearly applicable.

        PROGRAMMING LANGUAGES
            - List languages by platform/component
            - Justify selection criteria
            - Note any constraints or dependencies

        FRAMEWORKS & LIBRARIES
            - Core frameworks with versions
            - Supporting libraries
            - Compatibility requirements
            - Justification for each major choice

        OPEN SOURCE DEPENDENCIES
            - Third-party / open-source libraries identified
            - Package dependencies, registries and versions

        THIRD-PARTY SERVICES
            - External APIs and integrations
            - Authentication services
            - Monitoring tools
            - Cloud services

        DATABASES & STORAGE
            - Primary and secondary databases
            - Data persistence strategies
            - Caching solutions
            - Storage services

        DEVELOPMENT & DEPLOYMENT
            - Development tools
            - Build system
            - Containerization
            - CI/CD requirements

        Default Technology Stack: {DEFAULT_TECHNOLOGIES}

        Notes:
        - Include version numbers for all components
        - Consider security implications of choices
        - Document integration requirements between components
        - Validate your diagrams to ensure that they compile correctly.
    """,
    "4. PROCESS FLOWCHART": """
        Generate a comprehensive PROCESS FLOWCHART section for the Technical Specification document that includes:

        SYSTEM WORKFLOWS
            - Core Business Processes
                * End-to-end user journeys
                * System interactions
                * Decision points
                * Error handling paths
                
            - Integration Workflows
                * Data flow between systems
                * API interactions
                * Event processing flows
                * Batch processing sequences

        FLOWCHART REQUIREMENTS
            - For each major workflow, include:
                * Start and end points
                * Process steps
                * Decision diamonds
                * System boundaries
                * User touchpoints
                * Error states and recovery paths
                * Timing and SLA considerations
                
            - Validation Rules
                * Business rules at each step
                * Data validation requirements
                * Authorization checkpoints
                * Regulatory compliance checks

        TECHNICAL IMPLEMENTATION
            - State Management
                * State transitions
                * Data persistence points
                * Caching requirements
                * Transaction boundaries
                
            - Error Handling
                * Retry mechanisms
                * Fallback processes
                * Error notification flows
                * Recovery procedures

        REQUIRED DIAGRAMS
            Generate the following using Mermaid.js:
            - High-level system workflow
            - Detailed process flows for each core feature
            - Error handling flowcharts
            - Integration sequence diagrams
            - State transition diagrams

        Notes:
        - Use proper Mermaid.js flowchart syntax
        - Include clear labels and descriptions
        - Add swim lanes for different actors/systems
        - Document all decision points
        - Include timing constraints where applicable
        - Reference related technical requirements
    """,
    "5. SYSTEM ARCHITECTURE": """
        Generate a comprehensive SYSTEM ARCHITECTURE section for the Technical Specifications document, including:
        
        Only include sections and items that are actually relevant to this system, based on your analysis of its requirements.
        Don't add any items that aren't clearly applicable.

        HIGH-LEVEL ARCHITECTURE
            System Overview
                Provide a detailed textual description of:
                - Overall system architecture style and rationale
                - Key architectural principles and patterns
                - System boundaries and major interfaces
                
            Core Components Table
                Create a table with columns:
                - Component Name
                - Primary Responsibility
                - Key Dependencies
                - Integration Points
                - Critical Considerations
                
            Data Flow Description
                Document in clear prose:
                - Primary data flows between components
                - Integration patterns and protocols
                - Data transformation points
                - Key data stores and caches
                
            External Integration Points
                Create a table with columns:
                - System Name
                - Integration Type
                - Data Exchange Pattern
                - Protocol/Format
                - SLA Requirements

        COMPONENT DETAILS
            For each major component, specify:
                - Purpose and responsibilities
                - Technologies and frameworks used
                - Key interfaces and APIs
                - Data persistence requirements
                - Scaling considerations
                
            Required Diagrams (using Mermaid.js):
                - Detailed component interaction diagrams
                - State transition diagrams
                - Sequence diagrams for key flows

        TECHNICAL DECISIONS
            Document and justify with tables and prose:
                - Architecture style decisions and tradeoffs
                - Communication pattern choices
                - Data storage solution rationale
                - Caching strategy justification
                - Security mechanism selection
                
            Required Diagrams (using Mermaid.js):
                - Decision tree diagrams
                - Architecture decision records (ADRs)

        CROSS-CUTTING CONCERNS
            Address with detailed text and tables:
                - Monitoring and observability approach
                - Logging and tracing strategy
                - Error handling patterns
                - Authentication and authorization framework
                - Performance requirements and SLAs
                - Disaster recovery procedures
                
            Required Diagrams (using Mermaid.js):
                - Error handling flows

        Output Format Requirements:
            - Use clear, concise prose for high-level descriptions
            - Create well-structured tables for component relationships
            - Tables should never have more than four columns
            - Include bullet points for lists of features/requirements
            - Use Mermaid.js diagrams only for subsection technical details
            - Maintain consistent terminology throughout

        Notes:
            - Ensure all components are clearly defined
            - Document all architectural assumptions
            - Include rationale for key decisions
            - Reference relevant standards and patterns
            - Address scalability and performance implications
    """,
    "6. SYSTEM COMPONENTS DESIGN": "",
    "6.1 CORE SERVICES ARCHITECTURE": """
        Generate the CORE SERVICES ARCHITECTURE section for the Technical Specifications document.
        
        If the system does not require microservices, distributed architecture, or distinct service components, 
        clearly state "Core Services Architecture is not applicable for this system" and explain why.
        
        Otherwise, address the following areas:
        
        SERVICE COMPONENTS
            - Service boundaries and responsibilities
            - Inter-service communication patterns
            - Service discovery mechanisms
            - Load balancing strategy
            - Circuit breaker patterns
            - Retry and fallback mechanisms
            
        SCALABILITY DESIGN
            - Horizontal/vertical scaling approach
            - Auto-scaling triggers and rules
            - Resource allocation strategy
            - Performance optimization techniques
            - Capacity planning guidelines

        RESILIENCE PATTERNS
            - Fault tolerance mechanisms
            - Disaster recovery procedures
            - Data redundancy approach
            - Failover configurations
            - Service degradation policies
            
        Required Diagrams (using Mermaid.js):
            - Service interaction diagrams
            - Scalability architecture
            - Resilience pattern implementations
            
        Output Format Requirements:
            - Use Markdown tables for structured data
            - Tables should never have more than four columns
            - Ensure all diagrams are properly labeled
            - Include clear section references
    """,

    "6.2 DATABASE DESIGN": """
        Generate the DATABASE DESIGN section for the Technical Specifications document.
        
        If the system does not require or direct database or persistent storage interactions are not clearly evident,
        clearly state "Database Design is not applicable to this system" and explain why.
        
        Otherwise, address the following areas:
        
        SCHEMA DESIGN
            - Entity relationships
            - Data models and structures
            - Indexing strategy
            - Partitioning approach
            - Replication configuration
            - Backup architecture
            
        DATA MANAGEMENT
            - Migration procedures
            - Versioning strategy
            - Archival policies
            - Data storage and retrieval mechanisms
            - Caching policies

        COMPLIANCE CONSIDERATIONS
            - Data retention rules
            - Backup and fault tolerance policies
            - Privacy controls
            - Audit mechanisms
            - Access controls

        PERFORMANCE OPTIMIZATION
            - Query optimization patterns
            - Caching strategy
            - Connection pooling
            - Read/write splitting
            - Batch processing approach
            
        Required Diagrams (using Mermaid.js):
            - Database schema diagrams
            - Data flow diagrams
            - Replication architecture
            
        Output Format Requirements:
            - Use Markdown tables for structured data
            - Tables should never have more than four columns
            - Include ERD diagrams
            - Document all indexes and constraints
    """,

    "6.3 INTEGRATION ARCHITECTURE": """
        Generate the INTEGRATION ARCHITECTURE section for the Technical Specifications document.
        
        If the system does not require integration with external systems or services,
        clearly state "Integration Architecture is not applicable for this system" and explain why.
        
        Otherwise, address the following areas:
        
        API DESIGN
            - Protocol specifications
            - Authentication methods
            - Authorization framework
            - Rate limiting strategy
            - Versioning approach
            - Documentation standards
            
        MESSAGE PROCESSING
            - Event processing patterns
            - Message queue architecture
            - Stream processing design
            - Batch processing flows
            - Error handling strategy

        EXTERNAL SYSTEMS
            - Third-party integration patterns
            - Legacy system interfaces
            - API gateway configuration
            - External service contracts
            
        Required Diagrams (using Mermaid.js):
            - Integration flow diagrams
            - API architecture diagrams
            - Message flow diagrams
            
        Output Format Requirements:
            - Use Markdown tables for API specifications
            - Tables should never have more than four columns
            - Include sequence diagrams for key flows
            - Document all external dependencies
    """,

    "6.4 SECURITY ARCHITECTURE": """
        Generate the SECURITY ARCHITECTURE section for the Technical Specifications document.
        
        If the system does not require specific security considerations beyond standard practices,
        clearly state "Detailed Security Architecture is not applicable for this system" and explain 
        which standard security practices will be followed instead.
        
        Otherwise, address the following areas:
        
        AUTHENTICATION FRAMEWORK
            - Identity management
            - Multi-factor authentication
            - Session management
            - Token handling
            - Password policies
            
        AUTHORIZATION SYSTEM
            - Role-based access control
            - Permission management
            - Resource authorization
            - Policy enforcement points
            - Audit logging

        DATA PROTECTION
            - Encryption standards
            - Key management
            - Data masking rules
            - Secure communication
            - Compliance controls
            
        Required Diagrams (using Mermaid.js):
            - Authentication flow diagrams
            - Authorization flow diagrams
            - Security zone diagrams
            
        Output Format Requirements:
            - Use Markdown tables for security policies
            - Tables should never have more than four columns
            - Include security control matrices
            - Document compliance requirements
    """,

    "6.5 MONITORING AND OBSERVABILITY": """
        Generate the MONITORING AND OBSERVABILITY section for the Technical Specifications document.
        
        If the system does not require specific monitoring beyond basic health checks,
        clearly state "Detailed Monitoring Architecture is not applicable for this system" and explain
        which basic monitoring practices will be followed instead.
        
        Otherwise, address the following areas:
        
        MONITORING INFRASTRUCTURE
            - Metrics collection
            - Log aggregation
            - Distributed tracing
            - Alert management
            - Dashboard design
            
        OBSERVABILITY PATTERNS
            - Health checks
            - Performance metrics
            - Business metrics
            - SLA monitoring
            - Capacity tracking
            
        INCIDENT RESPONSE
            - Alert routing
            - Escalation procedures
            - Runbooks
            - Post-mortem processes
            - Improvement tracking
            
        Required Diagrams (using Mermaid.js):
            - Monitoring architecture
            - Alert flow diagrams
            - Dashboard layouts
            
        Output Format Requirements:
            - Use Markdown tables for metrics definitions
            - Tables should never have more than four columns
            - Include alert threshold matrices
            - Document SLA requirements
    """,

    "6.6 TESTING STRATEGY": """
        Generate the TESTING STRATEGY section for the Technical Specifications document.

        If the system is a simple library, tool, or does not require comprehensive testing,
        clearly state "Detailed Testing Strategy is not applicable for this system" and explain why,
        then document only the basic unit testing approach that will be used.
        
        TESTING APPROACH
            Unit Testing
                - Testing frameworks and tools
                - Test organization structure
                - Mocking strategy
                - Code coverage requirements
                - Test naming conventions
                - Test data management
                
            Integration Testing
                - Service integration test approach
                - API testing strategy
                - Database integration testing
                - External service mocking
                - Test environment management
                
            End-to-End Testing
                - E2E test scenarios
                - UI automation approach
                - Test data setup/teardown
                - Performance testing requirements
                - Cross-browser testing strategy
                
        TEST AUTOMATION
            - CI/CD integration
            - Automated test triggers
            - Parallel test execution
            - Test reporting requirements
            - Failed test handling
            - Flaky test management
            
        QUALITY METRICS
            - Code coverage targets
            - Test success rate requirements
            - Performance test thresholds
            - Quality gates
            - Documentation requirements
            
        Required Diagrams (using Mermaid.js):
            - Test execution flow
            - Test environment architecture
            - Test data flow diagrams
            
        Output Format Requirements:
            - Use Markdown tables for test requirements
            - Tables should never have more than four columns
            - Include test strategy matrices
            - Document all testing tools and frameworks
            - Provide example test patterns
            
        Notes:
            - Maintain consistency with technology choices
            - Consider testing implications for all system components
            - Include security testing requirements
            - Document test environment needs
            - Specify resource requirements for test execution
    """,

    "7. USER INTERFACE DESIGN": """
        Generate a USER INTERFACE DESIGN section of the Technical Specifications.
    
        If the project doesn't define a user interface (UI), leave the section empty with the note "No user interface required".
        
        If a UI is required, document:
        - Core UI technologies involved
        - UI usecases
        - UI / backend interaction boundaries
        - UI schemas
        - Screens required
        - User interactions
        - Visual design considerations
            
        Format using markdown code blocks (```) for proper rendering.
        Use an extreme level of detail and include a key of what each symbol means.
    """,
    "8. INFRASTRUCTURE": """
        Generate the INFRASTRUCTURE section for the Technical Specifications document.
        
        If the system is a standalone application or library that does not require deployment infrastructure,
        clearly state "Detailed Infrastructure Architecture is not applicable for this system" and explain why,
        then document only the minimal build and distribution requirements.
        
        Otherwise, address the following areas:
        
        DEPLOYMENT ENVIRONMENT
            Target Environment Assessment
                - Environment type (on-premises/cloud/hybrid/multi-cloud)
                - Geographic distribution requirements
                - Resource requirements (compute/memory/storage/network)
                - Compliance and regulatory requirements
                
            Environment Management
                - Infrastructure as Code (IaC) approach
                - Configuration management strategy
                - Environment promotion strategy (dev/staging/prod)
                - Backup and disaster recovery plans
        
        CLOUD SERVICES (if applicable)
            If the system does not use cloud services, clearly state why and skip this section.
            Otherwise, document:
                - Cloud provider selection and justification
                - Core services required with versions
                - High availability design
                - Cost optimization strategy
                - Security and compliance considerations
        
        CONTAINERIZATION (if applicable)
            If the system does not use containers, clearly state why and skip this section.
            Otherwise, document:
                - Container platform selection
                - Base image strategy
                - Image versioning approach
                - Build optimization techniques
                - Security scanning requirements
        
        ORCHESTRATION (if applicable)
            If the system does not require orchestration, clearly state why and skip this section.
            Otherwise, document:
                - Orchestration platform selection
                - Cluster architecture
                - Service deployment strategy
                - Auto-scaling configuration
                - Resource allocation policies
        
        CI/CD PIPELINE
            Build Pipeline
                - Source control triggers
                - Build environment requirements
                - Dependency management
                - Artifact generation and storage
                - Quality gates
            
            Deployment Pipeline
                - Deployment strategy (blue-green/canary/rolling)
                - Environment promotion workflow
                - Rollback procedures
                - Post-deployment validation
                - Release management process
        
        INFRASTRUCTURE MONITORING
            - Resource monitoring approach
            - Performance metrics collection
            - Cost monitoring and optimization
            - Security monitoring
            - Compliance auditing
        
        Required Diagrams (using Mermaid.js):
            - Infrastructure architecture diagram
            - Deployment workflow diagram
            - Environment promotion flow
            - Network architecture (if applicable)
            
        Output Format Requirements:
            - Use Markdown tables for configuration details
            - Tables should never have more than four columns
            - Include infrastructure cost estimates
            - Document all external dependencies
            - Provide resource sizing guidelines
        
        Notes:
            - Maintain consistency with security requirements
            - Consider cost implications of choices
            - Document scalability requirements
            - Include maintenance procedures
            - Specify monitoring requirements
            - Address disaster recovery needs
    """,
    "APPENDICES": """
        Generate an APPENDICES section of the Technical Specifications including the following subsections as applicable.
        Use markdown tables or MemaidJS diagrams if and when useful.
        Tables should never have more than four columns.
        Maintain consistency with previous technology and framework choices in the document.
        <SECTION_TITLE>: Any additional technical information mentioned in any of the inputs but not captured so far in this document.
        GLOSSARY: Additional definitions of terms used within the document.
        ACRONYMS: Expanded forms of acronyms used throughout the document.
    """
}

UPDATED_TECHNICAL_SECTION_ADDENDUM = {
    "0. SUMMARY OF CHANGES": """
        Generate a SUMMARY OF CHANGES section that serves as the definitive interpretation of user requirements and their technical implementation.
        
        START WITH INTENT CLARIFICATION
            - Core Objective
                * "Based on the provided requirements, the Blitzy platform understands that the objective is to..."
                * List each requirement with enhanced clarity
                * Surface any implicit requirements detected
            
            - Special Instructions and Constraints
                * CRITICAL: Capture any specific directives (e.g., "only generate documentation", "do not modify existing tests")
                * Document any methodological requirements (e.g., "use TDD approach", "maintain backward compatibility")
                * Preserve any examples provided by the user with clear labeling: "User Example: [exact example]"
            
            - Technical Interpretation
                * "These requirements translate to the following technical implementation strategy..."
                * Map each requirement to specific technical actions
                * Use format: "To [achieve goal], we will [modify/create/integrate] [specific component] by [specific approach]"

        TECHNICAL SCOPE
            - Primary Objectives with Implementation Approach
                * For each objective: "Achieve [goal] by modifying [components] to [specific changes]"
                * Include rationale for technical decisions
                * Identify critical success factors

            - Component Impact Analysis
                * Direct modifications required
                    - Component A: Modify [specific aspect] to enable [capability]
                    - Component B: Extend [interface/class] to support [feature]
                * Indirect impacts and dependencies
                    - Component C: Update [configuration/interface] due to changes in Component A
                * New components introduction
                    - Component D: Create [type] to handle [responsibility]
                
            - File and Path Mapping
                * Context paths (reference documentation, schemas, configs)
                * Source paths (baseline code to be modified)
                * Target paths (where changes will be implemented)
                * Create comprehensive mapping table with columns:
                    | Target File/Module | Source Reference | Context Dependencies | Modification Type |

        IMPLEMENTATION DESIGN
            - Technical Approach
                * NOT a timeline, but a logical implementation flow
                * "First, establish [foundation] by modifying [component]"
                * "Next, integrate [feature] by extending [module] with [capability]"
                * "Finally, ensure [quality] by implementing [safeguards] in [location]"
            
            - User-Provided Examples Integration
                * If examples were provided, show how they map to the implementation
                * "The user's example of [example] will be implemented in [component] as [approach]"
                * Maintain fidelity to the user's intent in the examples
            
            - Critical Implementation Details
                * Specific design patterns to be employed
                * Key algorithms or approaches to be used
                * Integration strategies between components
                * Data flow modifications required
                
            - Dependency Analysis
                * Required dependencies for implementation
                * Version constraints and compatibility requirements
                * Justification for each dependency choice

        SCOPE BOUNDARIES
            - Explicitly In Scope
                * Comprehensive list of ALL affected files/modules
                * All configuration changes required
                * All test modifications needed
                * All documentation updates required
            
            - Explicitly Out of Scope
                * What the user might expect but isn't included
                * Related areas deliberately not touched
                * Future considerations not addressed now

        VALIDATION CHECKLIST
            - Implementation Verification Points
                * Specific criteria to verify each requirement is met
                * Observable changes that confirm successful implementation
                * Integration points that must be tested

        EXECUTION PARAMETERS
            - Special Execution Instructions
                * Document any process-specific requirements (e.g., "documentation only", "skip deployment")
                * List any tools or platforms specifically mentioned or excluded
                * Note any quality or style requirements mentioned
            
            - Constraints and Boundaries
                * Technical constraints specified by the user
                * Process constraints (what should/shouldn't be done)
                * Output constraints (what should/shouldn't be generated)

        Output Requirements:
            - Focus on HOW to achieve goals, not WHEN
            - Every requirement must map to specific technical actions
            - PRESERVE all user examples and special instructions verbatim where appropriate
            - Use clear cause-and-effect language
            - Provide exhaustive file/component listings
            - Include decision rationale where non-obvious
    """
}

MERMAID_FIXER_SYSTEM_PROMPT = """
    You are an expert in mermaid diagrams.
    
    You must fix all syntax errors in diagrams provided to you and produce working diagrams.

    You must always follow these rules:
    1. Always the think tool and log your thought process as you analyze the diagram and try to identify the issue.
    2. Always ensure that node labels don't have any unescaped characters. Use double-quotes on node-labels where supported.
        - For example, replace Node[a < b] with Node["a < b"], Node [a/b/c] needs to be Node["a/b/c"], and so on
    3. Double check all node relationships and ensure that all items are defined before they are used.
    4. Where necessary, prioritize simplicity over style. Strip any potentially problematic comments from diagrams.
    5. Strictly use the following template for your output, including the triple backticks.
        ```mermaid
            <fixed_diagram>
        ```
"""

MERMAID_FIXER_HUMAN_PROMPT = """
    Please fix the following error(s) in the mermaid diagram below. Preseve as much of the original content as possible.

    {error}
    
    {mermaid_text}
"""

UPDATED_TECH_SPEC_INPUT = """

    First few sections of the new and updated Technical Specification for the project:

    {new_tech_spec}

    """

TECH_SPEC_SECTIONS_INPUT = """

    List of potentially relevant sections from the technical specification, for which content can be retrieved using get_tech_spec_section:

    {tech_spec_sections}

    """

TECH_SPEC_HEADINGS_PROMPTLET = """
    Context: The Technical Specification document contains multiple sections that provide comprehensive system documentation. You need to identify and retrieve content from relevant sections to gather complete information.
    
    Available section headings for the get_tech_spec_section tool:
    - Think deeply about which sections would contain information relevant to your assigned topic
    - Systematically work through each potentially relevant section
    - Retrieve detailed content using only the exact headings listed in the document
    - Ensure comprehensive coverage by checking all related sections
    """
