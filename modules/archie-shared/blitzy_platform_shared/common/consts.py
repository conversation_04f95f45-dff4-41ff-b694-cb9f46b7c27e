from ssl import SSLError
from anthropic._exceptions import InternalServerError as AInternalServerError, APIConnectionError as AAPIConnectionError, \
    ServiceUnavailableError as AServiceUnavailableError, OverloadedError as AOverloadedError, RateLimitError as ARateLimitError, \
    DeadlineExceededError as ADeadlineExceededError
from openai._exceptions import InternalServerError as OInternalServerError, APIConnectionError as OAPIConnectionError, \
    RateLimitError as ORateLimitError
from google.api_core.exceptions import TooManyRequests as GTooManyRequests
from neo4j.exceptions import ServiceUnavailable as NServiceUnavailable
from pydantic import ValidationError
from langchain_openai.chat_models.base import OpenAIRefusalError
from requests.exceptions import ConnectionError, RetryError, ReadTimeout
from github.GithubException import GithubException

from .models import FormattingError

MAX_TOKEN_LIMIT_KEYWORD = "MAX_TOKEN_LIMIT_REACHED"

CONTEXT_128K = 128000
CONTEXT_200K = 200000
CONTEXT_500K = 500000
CONTEXT_1M = 1000000
CONTEXT_2M = 2000000

# Default configuration
DEFAULT_MAX_RETRIES = 10  # Retry up to 10 times
DEFAULT_MIN_WAIT = 60  # Start with 60 second wait
DEFAULT_MAX_WAIT = 60 * 2**9  # Maximum wait
DEFAULT_MULTIPLIER = 2  # Exponential multiplier (doubling)

RETRYABLE_EXCEPTIONS = (
    AInternalServerError,
    AAPIConnectionError,
    AServiceUnavailableError,
    AOverloadedError,
    ARateLimitError,
    ADeadlineExceededError,
    OInternalServerError,
    OAPIConnectionError,
    ORateLimitError,
    GTooManyRequests,
    UnicodeError,
    SSLError,
    NServiceUnavailable,
    FormattingError,
    OpenAIRefusalError,
    ConnectionError,
    RetryError,
    GithubException,
    ReadTimeout
)

SUPPLEMENTARY_RETRYABLE_EXCEPTIONS = (
    ValidationError,
)

GITHUB_FILE_RETRIEVAL_ERROR = "Error: File path not found. Double-check the path and prefix, if applicable."

THINK_TOOL_NAME = "think"
