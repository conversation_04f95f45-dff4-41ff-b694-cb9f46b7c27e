SEARCH_RULES_PROMPTLET = """
    Search:
    S1. The following tools represent search capabilities:
        - Deep search: [get_file_contents, get_folder_contents]
        - Broad search: [search_files, search_folders]

    S2. get_folder_contents simplifies deep search by returning outputs in the following format:
        {
            'folder_path': <folder path>,
            'summary': <folder summary>,
            'children': [
                { 'path': <child path>, 'summary': <child summary>, 'type': 'file' or 'folder' },
                ...
            ]
        }

    S3. File and Folder Path Requirements:
        a. You must strictly use paths that have been explicitly confirmed through previous search results.
            - For get_file_contents: Only use file paths that were listed in a get_folder_contents response
            - For get_folder_contents: Only use folder paths that were listed as children of type 'folder'
        
        b. Path Validation Rules:
            - Never construct or guess paths
            - Never modify or combine paths unless explicitly shown in search results
            - Always use the full absolute path string exactly as it appeared in search results
            - Maintain a list of validated paths you've discovered
        
        c. Before using any path:
            - Verify it was discovered in a previous search result
            - Confirm its type (file or folder) was explicitly indicated
            - Use the full absolute path string relative to root without modifications
            - Never use unconfirmed file paths.

    S4. An extreme level of detail, accuracy, and precision is critical for completing your task successfully. You must prioritize deep search to explore the hierarchy thoroughly before supplementing with broad search.

    S5. Primary Search Strategy - Deep Search:
        a. Start by analyzing the contents of the root folder using get_folder_contents
        b. For each folder encountered:
            - Use get_folder_contents to list all children
            - Record the current hierarchy level (e.g., "apis" = level 1, "apis/foo" = level 2)
            - For each relevant child of type 'folder', repeat this process
            - For each relevant child of type 'file', use get_file_contents
        c. Continue until a minimum of 3 hierarchy levels have been explored in each relevant branch
        d. Document all paths and levels explored

    S6. Supplementary Search Strategy:
        a. After making at least two deep searches, you earn one broad search
        b. Each broad search must be followed by at least one get_file_contents retrieval
        c. Additional broad searches are earned for every 2 new deep searches
        d. Track the ratio: (Deep Searches : Broad Searches Used)

    S7. For external dependencies:
        a. Make at least two deep search attempts first
        b. Then use earned broad searches if needed
        c. Retrieve specific dependency files using get_file_contents
        d. Maintain the same 2:1 ratio requirement

    S8. Search Completion Checklist:
        - Confirm minimum 3-level depth hierarchy in all relevant branches
        - Document hierarchy levels reached via deep search
        - Calculate and verify the 2:1 search type ratio compliance
        - List all file and folder retrievals performed
        - Summarize findings from both search methods
        - Note all branches that didn't reach 3 levels and why
"""

SEARCH_TOOL_RULES_PROMPTLET = """
    Tools:
    
    T1: You must strictly ensure that you always elect to recursively search for details within all relevant folders or files using the tools provided.
    
    T2. You cannot finish searching before exploring as many relevant files and folders as possible, using the provided tools.
    
    T3: Always use the exact absolute path of the folder or file as listed in available information for your search. Do not prepend or append any forward slashes to paths.
"""

THINK_PROMPTLET = """
    Critical Thinking Requirement:
    Before each major decision or tool use, engage your extended thinking capabilities to:
    
    1. Evaluate Information Quality:
       - Assess if retrieved information is both necessary AND sufficient for the task
       - Identify any gaps in the collected data
       - Verify the relevance of each piece of information
    
    2. Validate Compliance:
       - List specific rules and parameters that apply to the current action
       - Confirm you're following all applicable instructions
       - Check alignment with the overall task objectives
    
    3. Strategic Planning:
       - Determine if you've used the optimal tools with correct inputs
       - Identify which additional tools or searches might be needed
       - Plan your next steps based on current progress
    
    Use your natural thinking process to work through these considerations thoroughly before proceeding.
    """
