from typing import List, <PERSON>
from functools import wraps
import logging
import async<PERSON>

from thefuzz import process

from langchain_anthropic import <PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain_google_vertexai import ChatVertexA<PERSON>
from langchain_openai import ChatOpenAI
from langchain_core.messages import ToolMessage, AIMessage, BaseMessage
from tenacity import retry, retry_if_exception_type, stop_after_attempt, wait_exponential, \
    before_sleep_log

from blitzy_utils.logger import logger

from .models import FormattingError
from .consts import MAX_TOKEN_LIMIT_KEYWORD, DEFAULT_MAX_RETRIES, \
    DEFAULT_MAX_WAIT, DEFAULT_MIN_WAIT, DEFAULT_MULTIPLIER, RETRYABLE_EXCEPTIONS
from .consts import GITHUB_FILE_RETRIEVAL_ERROR, CONTEXT_200K


def get_formatted_tool_result_messages(
    tool_message_list: List[ToolMessage],
    total_tokens: int,
    llm: Union[ChatOpenAI, ChatAnthropic, ChatVertexAI],
    validate_token_count=True,
    model_context=CONTEXT_200K,
    is_error=False
) -> List[ToolMessage]:
    messages = []
    tool_call_tokens = 0

    for index, tool_message in enumerate(tool_message_list):

        tool_content: str = tool_message.content
        if validate_token_count:
            if tool_content:
                tool_call_tokens = llm.get_num_tokens(tool_content)
                logger.debug(f'Tool call added {tool_call_tokens} tokens')
                content_tokens = total_tokens + tool_call_tokens
                logger.debug(f'Total context window tokens so far: {content_tokens}')
                max_tokens = model_context - llm.max_tokens
                if content_tokens >= max_tokens:
                    logger.warning('Max token limit reached, asking the llm to abort tool calling')
                    is_error = True
                    tool_content = MAX_TOKEN_LIMIT_KEYWORD
                else:
                    tool_content = tool_message.content
            else:
                tool_call_tokens = 0
                content_tokens = total_tokens
                tool_content = ""

        tool_content_block = {
            "type": "text",
            "text": tool_content
        }

        if is_error:
            tool_content_block["is_error"] = True

        messages.append(
            ToolMessage(
                content=[
                    tool_content_block
                ],
                tool_call_id=tool_message.tool_call_id,
                name=tool_message.name
            )
        )
    return messages


def format_messages(messages: List[BaseMessage]):
    # Process all messages except the last one
    for i, msg in enumerate(messages[:-1]):  # Skip the last message
        if msg.type != 'system':
            if isinstance(msg.content, list):
                for content_item in msg.content:
                    if isinstance(content_item, dict) and content_item.get('cache_control', ''):
                        content_item.pop('cache_control', None)
                        logger.debug('removed cache control')

    # Handle the last message separately
    if messages and len(messages):
        last_msg = messages[-1]
        if last_msg.type != 'system':
            if isinstance(last_msg.content, list):
                last_item = last_msg.content[-1]
                if isinstance(last_item, dict):
                    # For the last message, add cache_control
                    logger.debug('added cache control to last message')
                    last_item['cache_control'] = {"type": "ephemeral"}

    return messages


def get_response_content(response: AIMessage) -> str:
    content = ""
    if isinstance(response.content, list):
        for item in response.content:
            if isinstance(item, dict):
                if item.get("type", "") == "text":
                    content += item["text"]
            elif isinstance(item, str):
                content += item
    else:
        content = response.content
    if not content:
        raise FormattingError
    return content

# Create a common exponential backoff retry decorator


def archie_exponential_retry(
    max_retries=DEFAULT_MAX_RETRIES,
    min_wait=DEFAULT_MIN_WAIT,
    max_wait=DEFAULT_MAX_WAIT,
    multiplier=DEFAULT_MULTIPLIER,
    exceptions=RETRYABLE_EXCEPTIONS
):
    """
    A decorator that implements exponential backoff retry logic for API calls.
    Supports both sync and async functions.
    """
    def decorator(func):
        # Check if the function is async
        if asyncio.iscoroutinefunction(func):
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                # For async functions, we need tenacity's retry_event_loop
                @retry(
                    retry=retry_if_exception_type(exceptions),
                    stop=stop_after_attempt(max_retries),
                    wait=wait_exponential(multiplier=multiplier, min=min_wait, max=max_wait),
                    before_sleep=before_sleep_log(logger.logger, logging.WARNING),
                    retry_error_callback=lambda retry_state: retry_state.outcome.result(),
                    reraise=True
                )
                async def _async_retry():
                    return await func(*args, **kwargs)

                return await _async_retry()
            return async_wrapper
        else:
            @wraps(func)
            @retry(
                retry=retry_if_exception_type(exceptions),
                stop=stop_after_attempt(max_retries),
                wait=wait_exponential(multiplier=multiplier, min=min_wait, max=max_wait),
                before_sleep=before_sleep_log(logger.logger, logging.WARNING)
            )
            def sync_wrapper(*args, **kwargs):
                return func(*args, **kwargs)

            return sync_wrapper
    return decorator


def get_fuzzy_path(
    path: str,
    potential_paths: List[str]
) -> str:
    if path not in potential_paths:
        fuzzy_path_choices = process.extract(
            query=path,
            choices=potential_paths,
            limit=1
        )
        if len(fuzzy_path_choices) and fuzzy_path_choices[0]:
            path = fuzzy_path_choices[0][0]
            logger.warning(f'fuzzy matched path: {path}')
    return path


def clean_path(path: str):
    if path.startswith('/'):
        path = path[1:]
    if path.endswith('/'):
        path = path[:-1]
    return path


def read_range(view_range: List[int], file_text: str, prepend_line_numbers=True):
    """
    Given a list view_range containing two 1-indexed integers representing line numbers,
    and a string representing the file's text, returns the portion that is being read
    with line numbers prepended to each line.

    If the second integer extends the length or is -1, return up to the end of the file.
    Negative indices count from the end of the file (-1 is last line, -2 is second to last, etc.)

    Args:
        view_range (list): A list containing two integers representing line numbers.
                          Can be 1-indexed positive numbers or negative indices.
        file_text (str): A string representing the file's text.

    Returns:
        str: The portion of the file that is being read with line numbers prepended.
    """
    # Split the file text into lines
    lines = file_text.split('\n')

    # Get the total number of lines
    total_lines = len(lines)

    # Check if the file is empty
    if total_lines == 0:
        return ""

    # Extract start and end line numbers
    start_line = view_range[0]
    end_line = view_range[1]

    # Special case: if file has only empty content (single empty line)
    if total_lines == 1 and lines[0] == '':
        return ""

    # Handle negative indices
    if start_line < 0:
        # Convert negative index to positive (e.g., -1 becomes last line)
        start_line = total_lines + start_line + 1

    if end_line < 0 and end_line != -1:
        # Convert negative index to positive, but preserve -1 as "to end"
        end_line = total_lines + end_line + 1

    # Convert to 0-indexed for Python list operations
    start_idx = start_line - 1

    # If end_line is -1 or exceeds total lines, set it to the last line
    if end_line == -1 or end_line > total_lines:
        end_idx = total_lines  # This will include all lines to the end
    else:
        end_idx = end_line  # Since end is exclusive in Python slicing, this is correct

    # Ensure start_idx is not negative and not beyond file
    start_idx = max(0, min(start_idx, total_lines - 1))

    # Ensure end_idx is not less than start_idx
    end_idx = max(start_idx, min(end_idx, total_lines))

    # Extract the requested lines
    requested_lines = lines[start_idx:end_idx]

    if prepend_line_numbers:
        # Prepend line numbers to each line
        numbered_lines = []
        for i, line in enumerate(requested_lines):
            # Calculate the actual line number (1-indexed)
            line_number = start_idx + i + 1
            numbered_lines.append(f"{line_number}: {line}")
        # Join the lines back together with newlines
        return '\n'.join(numbered_lines)
    else:
        return '\n'.join(requested_lines)
