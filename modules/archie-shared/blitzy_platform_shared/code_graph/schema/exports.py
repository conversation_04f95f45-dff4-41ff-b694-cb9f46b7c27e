from enum import Enum
from typing import List, Optional
from pydantic import BaseModel, Field, ConfigDict


class ExportKind(str, Enum):
    """
    Classifies the type of exported item to determine how it should be consumed and used.
    This typing information is crucial for static analysis and code generation.
    """
    FUNCTION = "function"    # Callable procedures and methods
    CLASS = "class"          # Object constructors and prototypes
    VALUE = "value"          # Constants, variables, and primitive values
    TYPE = "type"            # Type definitions and interfaces
    NAMESPACE = "namespace"  # Logical groupings of related exports
    MODULE = "module"        # Entire module re-exports
    BARREL = "barrel"


class Export(BaseModel):
    """
    Represents an export from a module. An export can be the default export or a named export:

    - When `is_default` is True, it indicates the primary (default) export of the module,
      which can be imported without using a named specifier. Only one default export
      is allowed per module.
    - When `is_default` is False, it is a named export. Named exports can have an
      optional `alias` to avoid naming conflicts or provide clarity.

    The `kind` field classifies how the export is used (e.g., function, class, value, etc.).
    The `source` field tracks where this export originated, which is useful for tooling
    and documentation. The `alias` field is only relevant for named exports.
    """
    model_config = ConfigDict(extra='forbid')

    name: str = Field(
        ...,
        description=(
            "Identifier for the exported item, must be unique within the module "
            "and follow language naming conventions. This name is used internally "
            "but may not match the import name."
        )
    )
    kind: ExportKind = Field(
        ...,
        description=(
            "Classification of the exported item's type, determining how it can be "
            "used by importers. This affects type checking, code completion, and usage patterns."
        )
    )
    source: str = Field(
        ...,
        description=(
            "Original location where the exported item was defined, including file path and "
            "optionally line number. For re-exports, this tracks the item's original source."
        )
    )
    is_default: bool = Field(
        description=(
            "Indicates whether this export is the module's primary default export. "
            "A module can only have one default export; if multiple exports are marked "
            "as default, validation or code generation should raise an error."
        )
    )
    alias: Optional[str] = Field(
        description=(
            "Optional alternative name for the export when imported, used to avoid naming "
            "conflicts or provide more context-appropriate names. Must be a valid identifier "
            "in the target language. This field is typically used only for named exports "
            "(i.e., when `is_default` is False)."
        )
    )
    summary: str = Field(
        ...,
        description=(
            "IMPORTANT: Must never be blank. A concise summary capturing the overall content, functionality, and export responsibilities "
            "of the file in which this export is declared. This summary is intended for vector indexing "
            "and semantic search, providing key insights into the module's design, main features, and "
            "architectural decisions."
        )
    )


class CodeGraphExportsSchema(BaseModel):
    """
    Comprehensive representation of a module's public interface, capturing all ways the
    module can be consumed by other code. This schema describes the complete export
    surface area of a module, including any default or named exports, as well as
    organizational groupings. It serves as the contract between the module and its consumers.
    """
    model_config = ConfigDict(extra='forbid')

    exports: List[Export] = Field(
        ...,
        description=(
            "Collection of all exports from this module. This list can contain exactly one export "
            "where `is_default=True` to denote the default export, and any number of named exports. "
            "Unique names are required for each export, unless an alias is provided."
        )
    )
