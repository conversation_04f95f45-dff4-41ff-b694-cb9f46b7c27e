from enum import Enum
from typing import List, Optional
from pydantic import BaseModel, Field, ConfigDict

from blitzy_platform_shared.code_graph.schema.globals import Visibility
from blitzy_platform_shared.code_graph.schema.functions import FunctionSignature, Decorator, Step, GenericParameter

# --- Enums ---


class ClassKind(str, Enum):
    """
    Fundamental class categorization across major programming languages.

    Represents the core nature/behavior of a class, covering patterns from:
    - Java/C#/TypeScript (regular, abstract, sealed, final classes)
    - C++ (regular classes, abstract classes, structs)
    - Python (regular classes, ABC abstract classes)
    - Ruby (regular classes, singleton classes)
    - Kotlin (regular, abstract, sealed, data classes)
    - Swift (regular, final classes)
    - Rust (regular structs, tuple structs)

    Note: Interface-like constructs (interfaces, protocols, traits) belong in type definitions,
    not as class kinds.
    """

    REGULAR = "regular"  # Standard class with no special inheritance or instantiation restrictions

    ABSTRACT = "abstract"  # Class that cannot be instantiated directly and may contain abstract members.
    # Examples: abstract classes in Java/C#, ABCs in Python

    FINAL = "final"  # Class that cannot be inherited from, forming a leaf in the inheritance tree.
    # Examples: final classes in Java/Kotlin, sealed classes in Swift

    SEALED = "sealed"  # Class that restricts which other classes can inherit from it to a known set.
    # Examples: sealed classes in C#/Kotlin, permits in Java 17+

    STRUCT = "struct"  # Value-oriented class with different default visibility/inheritance rules.
    # Examples: structs in C++/Rust, value types in C#

    DATA = "data"  # Class primarily focused on holding data with auto-generated methods.
    # Examples: data classes in Kotlin, case classes in Scala, dataclasses in Python

    SINGLETON = "singleton"  # Class restricted to a single instance through type system enforcement.
    # Examples: object declarations in Scala/Kotlin
    # Note: Does not include runtime singleton pattern implementations


class PropertyKind(str, Enum):
    """
    Classifies the fundamental types of class properties/fields/attributes across major programming languages.

    Covers property patterns from:
    - Java (instance fields, static fields, final fields, synthetic fields)
    - C# (instance properties, static properties, auto-properties, computed properties)
    - Python (instance attributes, class attributes, descriptors, properties)
    - TypeScript (instance props, static props, accessors, abstract props)
    - Ruby (instance vars, class vars, attr_reader/writer/accessor)
    - C++ (instance members, static members, const members, mutable members)
    - Kotlin (properties, late-init props, const props, delegated props)
    """

    INSTANCE = "instance"  # Regular instance-level property unique to each object
    # Examples: self.x in Python, this.x in Java/TS

    STATIC = "static"  # Class-level property shared across all instances
    # Examples: @staticmethod in Python, static in Java/C#

    CONST = "const"  # Immutable property that cannot be modified after initialization
    # Examples: final in Java, readonly in TS, const in C++

    COMPUTED = "computed"  # Property defined through getter/setter methods
    # Examples: @property in Python, { get; set; } in C#

    ABSTRACT = "abstract"  # Property declared but not implemented in abstract class
    # Examples: abstract properties in C#/TS/Kotlin

    VIRTUAL = "virtual"  # Property that can be overridden by inheriting classes
    # Examples: virtual in C#, open in Kotlin

    LATE_INIT = "late_init"  # Property initialized after declaration/construction
    # Examples: lateinit in Kotlin, definite assignment in C#

    DELEGATED = "delegated"  # Property that delegates get/set operations
    # Examples: property delegates in Kotlin, descriptors in Python

    CLASS_FIELD = "class_field"  # Field declared at class scope without accessors
    # Distinguished from properties with accessor methods


class MethodKind(str, Enum):
    """
    Classifies the fundamental types of class methods across major programming languages.

    Covers method patterns from:
    - Java (instance methods, static methods, abstract methods, final methods)
    - C# (instance, static, virtual, abstract, override methods)
    - Python (instance methods, class methods, static methods, abstract methods)
    - TypeScript (instance, static, abstract methods)
    - Ruby (instance methods, class methods, singleton methods)
    - C++ (instance, static, virtual, pure virtual methods, operators)
    - Kotlin (member functions, extension functions, operator functions)
    - Go (methods, interface methods)

    Note: Constructor/destructor methods are handled separately in dedicated schemas
    since they have special semantics and constraints.
    """

    INSTANCE = "instance"  # Regular instance method bound to object instances
    # Examples: def method(self) in Python, void method() in Java

    STATIC = "static"  # Class-level method not bound to instances
    # Examples: @staticmethod in Python, static in Java/C#

    CLASS = "class"  # Method that receives class rather than instance as first parameter
    # Examples: @classmethod in Python, static in Ruby

    ABSTRACT = "abstract"  # Method declared without implementation in abstract class
    # Examples: abstract methods in Java/C#, @abstractmethod in Python

    VIRTUAL = "virtual"  # Method that can be overridden by inheriting classes
    # Examples: virtual in C#, virtual in C++

    OVERRIDE = "override"  # Method that overrides a parent class implementation
    # Examples: @Override in Java, override in C#

    FINAL = "final"  # Method that cannot be overridden by inheriting classes
    # Examples: final in Java, sealed in C#

    ASYNC = "async"  # Method that executes asynchronously
    # Examples: async/await in C#/TS, coroutines in Python

    GENERATOR = "generator"  # Method that yields multiple values over time
    # Examples: yield in Python, IEnumerable in C#

    OPERATOR = "operator"  # Method that implements operator overloading
    # Examples: operator overloads in C++, operator methods in Python

    EXTENSION = "extension"  # Method added to existing type from outside
    # Examples: extension methods in C#/Kotlin

# --- Base Components ---


class ClassInheritance(BaseModel):
    """
    Represents the inheritance and type implementation structure of a class across major programming languages.

    Handles inheritance patterns from:
    - Single inheritance languages (Java, Ruby)
    - Multiple inheritance languages (C++, Python)
    - Mixin-based languages (Ruby, Scala)
    - Interface-based languages (Java, TypeScript)
    - Trait-based languages (Rust, PHP)

    Note: The schema normalizes different inheritance mechanisms into a consistent structure
    while preserving the original language's semantics.
    """

    model_config = ConfigDict(extra='forbid')

    extends: List[str] = Field(
        default_factory=list,
        description="Base classes this class inherits from directly. "
        "For single-inheritance languages, only the first entry is used. "
        "For multiple-inheritance languages, the order represents the method resolution order. "
        "Each entry should be a fully-qualified class name including module/package path. "
        "Examples: ['BaseClass'], ['FirstParent', 'SecondParent']"
    )

    implements: List[str] = Field(
        default_factory=list,
        description="Interfaces or protocols that this class implements. "
        "Used in languages with explicit interface implementation (Java, C#, TypeScript). "
        "For languages without explicit interfaces, leave empty. "
        "Each entry should be a fully-qualified interface name. "
        "Order of implementation is preserved when relevant for resolution. "
        "Examples: ['IDisposable'], ['Serializable', 'Cloneable']"
    )

    mixins: List[str] = Field(
        default_factory=list,
        description="Mixins, traits, or composable behavior modules included in this class. "
        "Used for languages supporting trait/mixin composition (Ruby, Scala, Rust traits). "
        "For languages without mixin support, leave empty. "
        "Order is significant as it determines composition precedence. "
        "Each entry should be a fully-qualified mixin/trait name. "
        "Examples: ['Enumerable', 'Comparable'], ['JsonSerializableTrait']"
    )


class Property(BaseModel):
    """
    Represents a class property/field/attribute with its complete metadata across major programming languages.

    Handles property patterns from:
    - Direct fields (Java fields, Python attributes)
    - Properties with accessors (C# properties, Python @property)
    - Computed properties (TypeScript get/set, Ruby attr methods)
    - Class-level properties (static fields, class variables)
    - Visibility-controlled properties (private, protected fields)
    """

    model_config = ConfigDict(extra='forbid')

    name: str = Field(
        ...,
        description="Identifier of the property following language-specific naming conventions. "
        "Must match exactly as it appears in source, including any prefixes/naming patterns "
        "(e.g. '__private' in Python, '_private' in Ruby). "
        "Examples: 'firstName', '_privateField', '__mangled'"
    )

    type: str = Field(
        ...,
        description="Fully-qualified type annotation in the property's native language syntax. "
        "For dynamic languages, use type hints/comments when available. "
        "For generic types, include full type parameters. "
        "Should preserve original type syntax including nullability markers. "
        "Examples: 'List[str]', 'Map<String, Integer>', 'number | null'"
    )

    visibility: Visibility = Field(
        description="Access control level of the property. "
        "Maps language-specific visibility to canonical forms. "
        "Preserves original visibility semantics even when syntax differs. "
        "See Visibility enum for complete mapping of language-specific patterns"
    )

    kind: PropertyKind = Field(
        description="Semantic classification of the property's behavior and scope. "
        "Determines how the property is stored, accessed, and inherited. "
        "Maps language-specific patterns to canonical property kinds. "
        "See PropertyKind enum for complete mapping of language patterns"
    )

    initial_value: Optional[str] = Field(
        description="Literal representation of property's initial value in source code syntax. "
        "Should preserve exact syntax including quotes, type markers, etc. "
        "For complex initializers, captures the complete initialization expression. "
        "Examples: '\"default\"', '42', 'new HashMap<>()', 'null'"
    )

    decorators: List[str] = Field(
        default_factory=list,
        description="Language-specific annotations, decorators, or attributes applied to property. "
        "Preserves complete decorator syntax including parameters. "
        "Order matches source code order. "
        "Examples: ['@deprecated', '@Column(name=\"first_name\")']"
    )


class ConstructorParameter(BaseModel):
    """Represents a parameter in the class constructor."""

    model_config = ConfigDict(extra='forbid')

    name: str = Field(
        ...,
        description="Name of the parameter"
    )
    type: str = Field(
        ...,
        description="Type annotation of the parameter"
    )
    optional: bool = Field(
        description="Whether the parameter is optional"
    )
    default_value: Optional[str] = Field(
        description="Default value of the parameter if any"
    )
    description: str = Field(
        description="Description of the parameter's purpose"
    )


class Constructor(BaseModel):
    """
    Represents a class's constructor/initializer across major programming languages.

    Handles constructor patterns from:
    - Explicit constructors (Java/C# constructors, Python __init__)
    - Multiple constructors (C++ multiple constructors, C# constructor overloads)
    - Factory constructors (Kotlin secondary constructors, Dart factory constructors)
    - Initialization blocks (Java initializer blocks, Ruby initialize blocks)
    - Dependency injection patterns (Angular/Spring/ASP.NET injection)
    """

    model_config = ConfigDict(extra='forbid')

    parameters: List[ConstructorParameter] = Field(
        default_factory=list,
        description="Formal parameters of the constructor in declaration order. "
        "For languages with multiple constructors, represents primary constructor. "
        "Includes injection parameters and their metadata. "
        "See ConstructorParameter schema for parameter details"
    )

    initialization: List[str] = Field(
        default_factory=list,
        description="Ordered sequence of initialization operations performed by constructor. "
        "Each entry represents a discrete initialization step in execution order. "
        "Includes: field initialization, superclass calls, dependency setup. "
        "Format: one logical operation per entry in source language syntax. "
        "Examples: ['super(name, id)', 'this.logger = LogFactory.create()', "
        "'this.cache.initialize(size)']"
    )

    injected_dependencies: List[str] = Field(
        default_factory=list,
        description="Fully-qualified names of services/dependencies injected into constructor. "
        "Used for dependency injection frameworks and container-managed instances. "
        "References the actual service type, not the injection token/key. "
        "Order matches constructor parameter order. "
        "Examples: ['ILogger', 'UserRepository', 'ConfigService']"
    )

    superclass_call: Optional[str] = Field(
        description="Explicit call to parent class constructor if required by language. "
        "Captures complete call syntax including parameters. "
        "Null if language doesn't require explicit super() or no superclass exists. "
        "Examples: 'super(id, name)', 'parent::__construct($id)'"
    )

    pre_initialization: List[str] = Field(
        default_factory=list,
        description="Initialization steps that must occur before main constructor body. "
        "Includes field initializers, instance initializer blocks, etc. "
        "Preserves source ordering of pre-initialization operations. "
        "Examples: ['private final id = UUID.randomUUID()', 'logger.setup()']"
    )

    post_initialization: List[str] = Field(
        default_factory=list,
        description="Operations that must occur after main constructor initialization. "
        "Includes post-construct hooks, delayed initialization, etc. "
        "Executes after all other initialization is complete. "
        "Examples: ['@PostConstruct setup()', 'validateState()']"
    )

    # --- Method Related Models ---


class Method(BaseModel):
    """
    Represents a complete method definition with its signature, implementation, and metadata
    across major programming languages.

    Handles method patterns from:
    - Instance methods (Python methods, Java instance methods)
    - Static methods (Java static methods, C# static methods)
    - Virtual/abstract methods (C++ virtual, Java abstract)
    - Extension methods (C# extensions, Kotlin extensions)
    - Async methods (C# async/await, JS async methods)
    - Generator methods (Python generators, JS generators)
    - Operator overloads (C++ operators, Python special methods)
    - Property accessors (get/set methods)
    """

    model_config = ConfigDict(extra='forbid')

    name: str = Field(
        ...,
        description="Method identifier as it appears in source code. "
        "Includes special method names (e.g. Python __str__, operator overloads). "
        "For accessors, includes get_/set_ prefix if language requires. "
        "Must match exact source declaration including any prefixes. "
        "Examples: 'calculate', '__str__', 'operator+', 'get_value'"
    )

    kind: MethodKind = Field(
        description="Semantic classification of method type and behavior. "
        "Determines method's scope, inheritance behavior, and calling convention. "
        "Maps language-specific method types to canonical forms. "
        "See MethodKind enum for complete classification. "
        "Examples: INSTANCE, STATIC, VIRTUAL, ABSTRACT"
    )

    visibility: Visibility = Field(
        description="Access control level of the method. "
        "Maps language-specific visibility to canonical forms. "
        "Includes package/module/assembly visibility levels. "
        "See Visibility enum for complete visibility mapping. "
        "Examples: PUBLIC, PRIVATE, PROTECTED, INTERNAL"
    )

    signature: FunctionSignature = Field(
        ...,
        description="Complete method signature including parameters and return type. "
        "Captures full type information and error handling. "
        "See MethodSignature schema for detailed signature structure. "
        "Must include all information needed to call method correctly"
    )

    decorators: List[Decorator] = Field(
        default_factory=list,
        description="Language-specific annotations, decorators, or attributes. "
        "Preserves complete decorator syntax including parameters. "
        "Order matches source code declaration order. "
        "Examples: ['@Override', '@Transactional(readOnly=true)']"
    )

    steps: List[Step] = Field(
        ...,
        description="Ordered sequence of logical operations in method implementation. "
        "Each step represents one semantic operation in execution order. "
        "For abstract methods, contains abstract/interface contract steps. "
        "Steps should be granular enough for analysis but preserve logic flow. "
        "See Step schema for step structure and formatting"
    )

    pure: Optional[bool] = Field(
        description="Indicates if method is pure/side-effect free. "
        "True for methods that only depend on inputs and state. "
        "False for methods with side effects. "
        "Null if purity cannot be determined statically"
    )

    summary: str = Field(
        ...,
        description=(
            "IMPORTANT: Must never be blank. A concise summary that encapsulates the overall functionality, key operations, "
            "and behavioral intent of the method. This summary should highlight the method's core purpose, "
            "its input-output contract, and all notable implementation details such as asynchronous behavior, "
            "resource management, and critical execution steps. It is designed to support search and vector indexing."
        )
    )


class ClassSummary(BaseModel):
    """
    Comprehensive summary of a class's purpose, behavior, and relationships within the codebase.

    Provides essential context for:
    - Architectural role and responsibilities
    - Dependency relationships and contracts
    - Usage patterns and constraints
    - State management and lifecycle
    - Integration points and system boundaries
    - Testing and maintenance considerations
    """

    model_config = ConfigDict(extra='forbid')

    purpose: str = Field(
        ...,
        description="Primary responsibility and role of the class in the system. "
        "Should clearly state what problem the class solves or service it provides. "
        "Must be specific enough to distinguish from similar classes. "
        "Limited to 1-2 sentences for clarity. "
        "Examples: 'Manages user authentication state and session persistence', "
        "'Transforms raw sensor data into normalized measurement records'"
    )

    key_behaviors: List[str] = Field(
        default_factory=list,
        description="Core capabilities and significant behaviors implemented by the class. "
        "Each entry describes one distinct responsibility or feature. "
        "Should cover both public interface and important internal behaviors. "
        "Entries should be complete sentences describing what/why, not just method names. "
        "Examples: ['Validates user credentials against LDAP directory', "
        "'Caches authentication tokens with configurable expiration', "
        "'Emits events on authentication state changes']"
    )

    dependencies: List[str] = Field(
        default_factory=list,
        description="External systems, services, and modules this class interacts with. "
        "Includes both direct dependencies and significant indirect dependencies. "
        "Each entry should be fully qualified name/identifier of the dependency. "
        "Critical for understanding class's integration points and requirements. "
        "Examples: ['AuthenticationService', 'UserRepository', 'EventBus']"
    )

    usage_context: str = Field(
        ...,
        description="When, where, and how this class should be used in the codebase. "
        "Describes typical usage patterns and integration approaches. "
        "Includes initialization requirements and lifecycle management. "
        "Should note any important constraints or assumptions. "
        "Examples: 'Initialize once per user session in the authentication pipeline', "
        "'Use as a singleton through dependency injection'"
    )

    state_management: str = Field(
        ...,
        description="How the class manages and maintains its internal state. "
        "Describes state initialization, modification patterns, and persistence. "
        "Includes thread safety and concurrent access considerations. "
        "Should explain any invariants or consistency requirements. "
        "Example: 'Maintains thread-safe session cache with LRU eviction'"
    )


class CodeGraphClassSchema(BaseModel):
    """
    Comprehensive representation of a class's complete structure, behavior, and context
    across major programming languages.

    Provides complete class information for:
    - Static analysis and code understanding
    - Documentation generation
    - Dependency analysis
    - API contract validation
    - Refactoring impact analysis
    - Code review assistance
    - Architecture conformance checking

    Handles class patterns from all major languages while preserving
    language-specific semantics and implementation details.
    """

    model_config = ConfigDict(extra='forbid')

    name: str = Field(
        ...,
        description="Fully qualified class name including namespace/package path. "
        "Must match exact source declaration including case and separators. "
        "For nested classes, includes parent class path. "
        "Examples: 'com.example.UserService', 'Data.Collections.HashMap'"
    )

    kind: ClassKind = Field(
        description="Fundamental classification of the class's nature and constraints. "
        "Determines inheritance, instantiation, and usage patterns. "
        "Maps language-specific class types to canonical forms. "
        "See ClassKind enum for complete classification"
    )

    exported: bool = Field(
        description="Whether class is accessible outside its defining module. "
        "Maps language-specific export/visibility mechanisms. "
        "True if class can be imported by other modules. "
        "False if class is module-private/internal"
    )

    generic_params: List[GenericParameter] = Field(
        default_factory=list,
        description="Generic type parameters declared on the class. "
        "Includes complete parameter declarations with constraints. "
        "Preserves source ordering and relationships. "
        "Critical for understanding generic class contracts. "
        "Examples: ['T extends Comparable<T>', 'K, V']"
    )

    inheritance: ClassInheritance = Field(
        default_factory=ClassInheritance,
        description="Complete inheritance structure including parents and interfaces. "
        "Maps inheritance mechanisms across different type systems. "
        "See ClassInheritance schema for structure details"
    )

    decorators: List[Decorator] = Field(
        default_factory=list,
        description="Language-specific annotations, decorators, or attributes. "
        "Preserves complete decorator syntax including parameters. "
        "Order matches source code declaration order. "
        "Examples: ['@Injectable()', '@Entity(name=\"users\")']"
    )

    properties: List[Property] = Field(
        default_factory=list,
        description="All class properties/fields with complete metadata. "
        "Order matches source declaration order. "
        "See Property schema for detailed property structure"
    )

    constructor: Optional[Constructor] = Field(
        description="Class initialization and construction definition. "
        "Null for classes without explicit constructors. "
        "See Constructor schema for detailed structure. "
        "Critical for understanding object lifecycle"
    )

    methods: List[Method] = Field(
        default_factory=list,
        description="All class methods with complete implementations. "
        "Order matches source declaration order. "
        "Includes both instance and static methods. "
        "See Method schema for detailed method structure"
    )

    summary: ClassSummary = Field(
        ...,
        description="IMPORTANT: Must never be blank. High-level class context and relationships. "
        "Essential for understanding class's role and usage. "
        "See ClassSummary schema for detailed structure"
    )
