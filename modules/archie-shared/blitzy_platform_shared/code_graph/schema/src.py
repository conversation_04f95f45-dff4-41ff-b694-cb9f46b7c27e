from pydantic import BaseModel, Field, ConfigDict
from typing import List

from blitzy_platform_shared.code_graph.schema.imports import CodeGraphImportsSchema
from blitzy_platform_shared.code_graph.schema.globals import CodeGraphGlobalsSchema
from blitzy_platform_shared.code_graph.schema.functions import CodeGraphFunctionSchema
from blitzy_platform_shared.code_graph.schema.classes import CodeGraphClassSchema
from blitzy_platform_shared.code_graph.schema.exports import CodeGraphExportsSchema
from blitzy_platform_shared.code_graph.schema.summary import FileSummary


class CodeGraphSourceSchema(BaseModel):
    """
    This schema captures declarations and definitions that appear at file-level scope,
    outside of any function or class definition. This includes type definitions,
    constant declarations, variable initializations, and external entity references
    that are directly in the module's top-level scope.
    """

    model_config = ConfigDict(extra='forbid')

    path: str = Field(
        ...,
        description="Absolute path to the source code file within the repository"
    )

    imports: CodeGraphImportsSchema = Field(
        ...,
        description="Comprehensive collection of all file-level dependency inclusion mechanisms, "
        "including: language-specific imports (import/from in Python, import/require in JS), "
        "preprocessor directives (#include in C/C++), module includes (require in Ruby), "
        "using directives (C#), and package imports (Go). Captures both compile-time and "
        "runtime dependencies, grouped into internal (same-repository) and external (third-party) "
        "imports. Each import entry tracks its style, mechanism, usage patterns, and architectural "
        "purpose. Should comprehensively document all ways the file brings in external code, "
        "regardless of syntax or loading strategy. This includes conditional imports, re-exports, "
        "type-only imports, and side-effect imports (those used only for their initialization)."
    )

    globals: CodeGraphGlobalsSchema = Field(
        ...,
        description="Captures declarations and definitions that appear at the top level of the file. "
        "A symbol can either be declared (like external references, forward declarations) or "
        "defined (like type definitions, constant values) but not both. "
        "This schema specifically EXCLUDES: dependency and package imports, directives and modules includes and requires, "
        "any globals defined inside classes/functions, and implementations like classes and functions. "
        "Only declarations and definitions directly at file scope should be included."
    )

    functions: List[CodeGraphFunctionSchema] = Field(
        ...,
        description="A comprehensive list of all functions defined or declared at the file level. "
        "Each function entry encapsulates its complete signature—including parameter types, return type, "
        "and declared exceptions—its asynchronous behavior, export and visibility attributes, and any generic "
        "parameters. The function’s implementation is broken down into ordered steps that detail control flow, "
        "side effects, resource management, and interdependencies. Additionally, decorators and runtime "
        "configurations (such as timeouts, retry policies, and cancellation capabilities) are captured, along "
        "with a high-level summary that describes the function’s purpose, input/output contracts, and usage context. "
        "Functions are ordered by their appearance in the source file to reflect the file’s operational logic."
    )

    classes: List[CodeGraphClassSchema] = Field(
        ...,
        description=(
            "A comprehensive list of all classes defined in the file. Each class entry encapsulates its fully qualified name, "
            "classification (e.g., regular, abstract, final, sealed, struct, data, singleton), and export status. It includes generic parameters, "
            "detailed inheritance information (base classes, implemented interfaces, mixins), and any decorators applied. Additionally, each class "
            "captures its properties with associated metadata, constructor details (including initialization steps and dependency injection), and a complete "
            "set of method definitions with full signatures and implementation steps. Finally, a high-level summary outlines the class’s purpose, key behaviors, "
            "dependencies, usage context, and state management strategy. Classes are ordered according to their appearance in the source file."
        )
    )

    exports: CodeGraphExportsSchema = Field(
        ...,
        description=(
            "A comprehensive mapping of the module's public interface. This schema aggregates all export declarations—"
            "including the default export and named exports—to define the contract by which the module can be consumed. "
            "Each export entry records a unique identifier, its semantic classification (such as function, class, value, type, "
            "or namespace), and the original source location of its definition or re-export. Optional aliasing is also captured "
            "to resolve naming conflicts. This detailed export specification is essential for enforcing API contracts, supporting "
            "static analysis, and guiding refactoring and documentation efforts."
        )
    )

    summary: FileSummary = Field(
        ...,
        description="Comprehensive overview of the file's role, context, and relationships within the project. "
        "Includes its primary purpose, dependency relationships, usage patterns, known limitations, "
        "and maintenance requirements. Provides both technical and architectural context to understand "
        "the file's place in the codebase and its impact on the overall system. Essential for "
        "maintainers to understand what would break if this file were modified or removed, and "
        "what considerations to keep in mind when making changes."
    )
