from enum import Enum
from typing import List, Optional
from pydantic import BaseModel, Field, ConfigDict

from blitzy_platform_shared.code_graph.schema.globals import Visibility


class Criticality(str, Enum):
    """Indicates how critical/important something is to function operation"""
    REQUIRED = "required"        # Must be present/satisfied
    RECOMMENDED = "recommended"  # Should be present but can proceed without
    OPTIONAL = "optional"        # Nice to have but not necessary
    FALLBACK = "fallback"       # Used only when primary option fails


class InteractionPattern(str, Enum):
    """Describes how a function interacts with an external system"""
    READ = "read"                # Read-only access
    WRITE = "write"             # Write-only access
    READ_WRITE = "read_write"   # Bidirectional access
    NOTIFICATION = "notification"  # Event/message based
    CONTROL = "control"         # System control operations
    QUERY = "query"             # Complex read operations
    COMMAND = "command"         # Complex write operations


class DependencyType(str, Enum):
    """Categories of external dependencies"""
    SERVICE = "service"          # External service/API
    DATABASE = "database"        # Database system
    FILE_SYSTEM = "file_system"  # Filesystem
    CACHE = "cache"             # Caching system
    QUEUE = "queue"             # Message queue
    STREAM = "stream"           # Data stream
    STORE = "store"             # Key-value store
    SEARCH = "search"           # Search engine
    METRICS = "metrics"         # Metrics/monitoring
    CONFIG = "config"           # Configuration system


class EffectScope(str, Enum):
    """Scope of impact for side effects"""
    LOCAL = "local"              # Function/method scope
    MODULE = "module"            # File/module scope
    PACKAGE = "package"          # Package/namespace scope
    GLOBAL = "global"           # Program/process scope
    EXTERNAL = "external"       # External system scope


class BehaviorCategory(str, Enum):
    """Categorizes the types of behaviors a function can exhibit"""
    DATA_TRANSFORMATION = "data_transformation"  # Data processing/conversion
    VALIDATION = "validation"                    # Input/state validation
    PERSISTENCE = "persistence"                  # Data storage/retrieval
    COMPUTATION = "computation"                  # Mathematical/logical operations
    COORDINATION = "coordination"                # Process/thread coordination
    INTEGRATION = "integration"                  # External system interaction
    ORCHESTRATION = "orchestration"             # Complex workflow management
    OPTIMIZATION = "optimization"                # Performance optimization


class SideEffectCategory(str, Enum):
    """Categorizes types of side effects a function may have"""
    STATE_MUTATION = "state_mutation"           # Changes to program state
    IO_OPERATION = "io_operation"               # File/network/database operations
    EVENT_EMISSION = "event_emission"           # Event/signal emission
    RESOURCE_ALLOCATION = "resource_allocation"  # Resource acquisition/release
    CACHE_MODIFICATION = "cache_modification"   # Cache updates
    LOG_EMISSION = "log_emission"              # Logging operations
    METRIC_EMISSION = "metric_emission"         # Metrics/telemetry
    CONFIG_CHANGE = "config_change"            # Configuration changes


class FunctionKind(str, Enum):
    REGULAR = "regular"
    GENERATOR = "generator"


class ReferenceUsageType(str, Enum):
    READ = "read"              # Access of a value or property
    WRITE = "write"           # Assignment or mutation
    CALL = "call"             # Function/method invocation
    TYPE = "type"             # Type reference in declarations or casts
    INHERIT = "inherit"       # Usage in inheritance or implementation
    DECORATE = "decorate"     # Usage as a decorator or attribute
    TEMPLATE = "template"     # Usage in template/generic instantiation
    REFLECT = "reflect"       # Reflection or metaprogramming usage
    OPERATOR = "operator"     # Operator overloading or operator usage
    USING = "using"          # Resource management usage


class GlobalKind(str, Enum):
    TYPE_DEFINITION = "type_definition"
    DECLARATION = "declaration"
    EXTERNAL = "external"


class ImportCategory(str, Enum):
    """Categorizes the source of imports"""
    INTERNAL = "internal"     # From within the same project/repository
    EXTERNAL = "external"     # From external dependencies/packages
    SYSTEM = "system"        # From language/platform standard libraries
    RELATIVE = "relative"    # Relative imports within same package


class GenericParameter(BaseModel):
    """Represents a generic type parameter in a class or function definition."""

    model_config = ConfigDict(extra='forbid')

    name: str = Field(
        ...,
        description="Name of the type parameter"
    )
    constraints: str = Field(
        description="Type constraints for the generic parameter"
    )
    default_type: str = Field(
        description="Default type for the generic parameter"
    )


class SignatureParameter(BaseModel):
    """Represents a single parameter in a function's signature."""

    model_config = ConfigDict(extra='forbid')

    name: str = Field(
        ...,
        description="Name of the parameter"
    )
    type: str = Field(
        ...,
        description="Type annotation of the parameter"
    )
    optional: bool = Field(
        ...,
        description="Whether the parameter is optional"
    )
    default_value: Optional[str] = Field(
        description="Default value of the parameter if any"
    )
    rest_parameter: bool = Field(
        ...,
        description="Whether this is a rest parameter (...args)"
    )
    description: str = Field(
        description="Description of the parameter's purpose"
    )


class FunctionSignature(BaseModel):
    """Represents the complete signature of a function."""

    model_config = ConfigDict(extra='forbid')

    parameters: List[SignatureParameter] = Field(
        ...,
        description="List of parameters in the function signature"
    )
    return_type: str = Field(
        ...,
        description="Return type of the function"
    )
    throws: List[str] = Field(
        default_factory=list,
        description="List of possible exceptions that may be thrown"
    )


class Decorator(BaseModel):
    """Represents a decorator applied to the function."""

    model_config = ConfigDict(extra='forbid')

    name: str = Field(
        ...,
        description="Name of the decorator"
    )
    arguments: List[str] = Field(
        default_factory=list,
        description="Arguments passed to the decorator"
    )
    purpose: str = Field(
        description="Description of the decorator's purpose"
    )


class ReferenceItem(BaseModel):
    """
    Base model for any symbol reference in code, capturing how the reference
    is used and its impact on the codebase.
    """
    model_config = ConfigDict(extra='forbid')

    name: str = Field(
        ...,
        description="The identifier or fully qualified name of the referenced symbol. "
        "Examples: variable name, class name, method name, or complete path like 'module.class.member'"
    )
    usage_type: ReferenceUsageType = Field(
        ...,
        description="How the reference is used in the code, covering all major paradigms: "
        "value access (read), mutation (write), invocation (call), type usage (type), "
        "inheritance (inherit), decoration (decorate), templating (template), "
        "reflection (reflect), operators (operator), and resource management (using)"
    )


class ImportReference(BaseModel):
    """
    Represents a module/package import statement and its used members,
    supporting all major language import mechanisms.
    """
    model_config = ConfigDict(extra='forbid')

    category: ImportCategory = Field(
        ...,
        description="Categorizes the import source: internal (project), external (dependencies), "
        "system (standard library), or relative (same package). Handles concepts like: "
        "Java packages, Python modules, C++ includes, Ruby requires, C# using directives"
    )
    source_name: str = Field(
        ...,
        description="Module/package identifier being imported. Examples: module path in Python, "
        "package name in Java, header name in C++, namespace in C#. May include version "
        "constraints for package managers."
    )
    import_style: str = Field(
        ...,
        description="Import mechanism used: 'import' statement, 'using' directive, '#include', "
        "'require', etc. Includes modifiers like 'static', 'typedef', or 'as'. Also covers "
        "conditional imports (#ifdef), type-only imports, and side-effect imports."
    )
    members: List[ReferenceItem] = Field(
        default_factory=list,
        description="Specific symbols imported from the source. Handles: named imports, "
        "wildcard imports (*), aliased imports (as), static imports, type imports. "
        "Each member tracks its usage pattern via ReferenceItem."
    )


class GlobalReference(ReferenceItem):
    """
    Represents references to file-level symbols, including types,
    values, and external declarations.
    """
    model_config = ConfigDict(extra='forbid')

    kind: str = Field(
        ...,
        description="Category of the global reference: 'type_definition' (classes, interfaces, "
        "enums, type aliases), 'declaration' (constants, variables, function declarations), "
        "'external' (imported symbols), 'macro' (preprocessor definitions), or 'namespace' "
        "(modules, packages, namespaces)"
    )
    scope: str = Field(
        ...,
        description="Visibility scope of the reference: 'file' (private), 'package' (internal), "
        "'public' (exported), or 'global' (ambient declarations). Maps to language-specific "
        "concepts like Java package-private, C++ anonymous namespaces, TypeScript ambient modules."
    )


class ClassReference(ReferenceItem):
    """
    Represents references to classes and their members, supporting
    all object-oriented and prototype-based patterns.
    """
    model_config = ConfigDict(extra='forbid')

    members: List[ReferenceItem] = Field(
        default_factory=list,
        description="Referenced class members: methods, properties, fields, nested types. "
        "Handles static and instance members, access modifiers, virtual/abstract members, "
        "and special members like constructors, destructors, and operators. Also covers "
        "prototype chain members in languages like JavaScript."
    )
    inheritance_type: Optional[str] = Field(
        description="Type of inheritance relationship if reference is used in inheritance: "
        "'extends' (class inheritance), 'implements' (interface), 'with'/'using' (mixins/traits), "
        "'friend' (C++ friend), or null if not inheritance-related."
    )


class References(BaseModel):
    """
    Comprehensive collection of all code references within a scope,
    organizing them by their source and usage pattern.
    """
    model_config = ConfigDict(extra='forbid')

    imports: List[ImportReference] = Field(
        default_factory=list,
        description="All module/package imports used in the scope, organized by their import "
        "mechanism and categorized by source. Tracks both direct imports and transitive "
        "dependencies, including conditional and type-only imports."
    )
    globals: List[GlobalReference] = Field(
        default_factory=list,
        description="References to file-level declarations across all categories: types, "
        "values, macros, and namespaces. Includes both defined symbols and external "
        "references, with their visibility and usage patterns."
    )
    classes: List[ClassReference] = Field(
        default_factory=list,
        description="References to classes and their members, capturing both inheritance "
        "relationships and usage patterns. Covers all object-oriented features including "
        "static members, virtual dispatch, and special member functions."
    )


class StepType(str, Enum):
    """Categorizes the fundamental type of operation being performed in a step"""
    COMPUTATION = "computation"      # Pure computation without side effects
    MUTATION = "mutation"           # State changes to local variables/objects
    IO = "io"                      # File, network, or other I/O operations
    CONTROL = "control"            # Flow control (loops, conditions, etc)
    RESOURCE = "resource"          # Resource acquisition/release
    CONCURRENT = "concurrent"      # Parallel/concurrent operations
    TRANSACTION = "transaction"    # Atomic/transactional operations
    ERROR = "error"               # Error handling and recovery


class ResourceType(str, Enum):
    """Types of resources that might need management"""
    FILE = "file"                 # File handles
    NETWORK = "network"           # Network connections
    DATABASE = "database"         # Database connections
    MEMORY = "memory"            # Explicit memory allocation
    LOCK = "lock"                # Synchronization primitives
    HANDLE = "handle"            # System/OS handles
    CONTEXT = "context"          # Context managers/disposables


class Step(BaseModel):
    """
    Represents a single logical step in a function's implementation,
    capturing its purpose, dependencies, and resource usage.
    """
    model_config = ConfigDict(extra='forbid')

    id: str = Field(
        ...,
        description="Unique identifier for the step within the function scope. "
        "Should be stable across refactorings and meaningful for cross-referencing."
    )

    type: StepType = Field(
        ...,
        description="Fundamental category of operation being performed, helping analyze "
        "behavior and identify potential issues like side effects or resource leaks."
    )

    description: str = Field(
        ...,
        description="Natural language description of the step's purpose and behavior. "
        "Should explain WHAT is being done, WHY it's needed, and any important "
        "assumptions or invariants being maintained."
    )

    is_async: bool = Field(
        ...,
        description="Indicates asynchronous operations requiring special handling: "
        "await in Python/JS/C#, async/Promise in JS, Future in Java, "
        "goroutines in Go, or std::future in C++."
    )

    critical_section: bool = Field(
        description="Indicates if this step requires synchronization or represents "
        "a critical section needing concurrent access control."
    )

    resources: List[ResourceType] = Field(
        default_factory=list,
        description="Resources acquired, used, or released in this step. "
        "Used to verify proper resource management across all execution paths."
    )

    depends_on: List[str] = Field(
        default_factory=list,
        description="IDs of steps that must complete before this step can execute. "
        "Used for dependency tracking, parallelization opportunities, and "
        "validating execution order constraints."
    )

    references: Optional[References] = Field(
        description="External and internal dependencies referenced within this step. "
        "Tracks all symbols used for static analysis and refactoring support."
    )


class BackoffConfig(BaseModel):
    model_config = ConfigDict(extra='forbid')

    time_seconds: float = Field(
        ...,
        description="Initial delay time in seconds between retry attempts",
    )
    is_exponential: bool = Field(
        ...,
        description="Whether to use exponential backoff (true) or fixed delay (false) between retries"
    )
    max_time_seconds: Optional[float] = Field(
        description="Maximum delay time in seconds between retries when using exponential backoff",
    )


class RetryPolicy(BaseModel):
    model_config = ConfigDict(extra='forbid')

    max_attempts: int = Field(
        ...,
        description="Maximum number of retry attempts before considering the "
        "operation permanently failed. Must be > 0."
    )
    backoff: BackoffConfig = Field(
        ...,
        description="Configuration for timing and behavior of retry delays"
    )


class FunctionConfiguration(BaseModel):
    model_config = ConfigDict(extra='forbid')

    cancellable: bool = Field(
        description="Indicates whether the function execution can be cancelled mid-operation"
    )
    timeout: Optional[float] = Field(
        description="Maximum time in seconds allowed for function execution before timing out. None means no timeout",
    )
    retry_policy: Optional[RetryPolicy] = Field(
        default_factory=dict,
        description="Configuration for retry behavior when the function encounters recoverable failures"
    )


class Behavior(BaseModel):
    """
    Describes a specific behavior or feature of the function.
    """
    model_config = ConfigDict(extra='forbid')

    category: BehaviorCategory = Field(
        ...,
        description="Category of the behavior helping classify its purpose "
        "and potential impacts"
    )

    description: str = Field(
        ...,
        description="Detailed description of the behavior including its purpose, "
        "implementation approach, and any notable characteristics"
    )

    conditions: List[str] = Field(
        default_factory=list,
        description="Conditions under which this behavior is activated or relevant. "
        "Describes when and why the behavior occurs."
    )


class SideEffect(BaseModel):
    """
    Describes a mutation or observable change to system state that occurs as a 
    consequence of function execution, beyond its direct return value.
    """
    model_config = ConfigDict(extra='forbid')

    category: SideEffectCategory = Field(
        ...,
        description="The type of side effect being performed, such as state mutation, "
        "I/O operation, or event emission. Used to analyze potential impacts on system "
        "behavior and identify areas requiring synchronization or transaction handling."
    )

    description: str = Field(
        ...,
        description="Precise description of: 1) what state is being modified or action performed, "
        "2) conditions that trigger the side effect, 3) guarantees provided about the change, "
        "4) potential failure modes or error conditions, and 5) implications for system invariants. "
        "Should enable reasoning about thread safety and transaction boundaries."
    )

    scope: EffectScope = Field(
        ...,
        description="The boundary of impact for this side effect, determining visibility and "
        "lifetime of the change. Ranges from function-local temporary state to permanent "
        "external system modifications. Critical for reasoning about isolation and "
        "error recovery."
    )

    reversible: bool = Field(
        ...,
        description="Indicates if the side effect can be reliably undone through compensating "
        "actions. True means the function either provides built-in rollback or the change "
        "can be reversed through well-defined operations. Critical for transaction "
        "management and error recovery flows."
    )


class FunctionSummary(BaseModel):
    """
    Provides a high-level textual overview of the function's purpose, its inputs and outputs,
    significant behaviors, potential side effects, and general usage context. This summary
    is intended to guide both code analysis and code generation tools in understanding the
    function's broader contract without digging into its detailed implementation.
    """
    model_config = ConfigDict(extra='forbid')

    purpose: str = Field(
        ...,
        description=(
            "A concise, focused statement that explains the function's primary objective "
            "and the rationale behind its existence. Helps code analysis and code generation "
            "tools rapidly identify the function's role and scope within a larger system."
        )
    )
    inputs: Optional[List[str]] = Field(
        description=(
            "A list of textual notes or constraints describing expected function inputs. "
            "Each entry may detail required data types, valid ranges, or preconditions "
            "the caller must fulfill. Useful for validating usage and informing code "
            "generation about how to properly call the function."
        )
    )
    outputs: Optional[List[str]] = Field(
        description=(
            "A list of textual descriptions clarifying the function's output structures "
            "or return values. Each entry may cover return type details, success conditions, "
            "error outcomes, or any post-execution guarantees. Helps code analysis and code "
            "generation reason about the function's result."
        )
    )
    behaviors: Optional[List[str]] = Field(
        default_factory=list,
        description=(
            "A collection of notes highlighting the function's distinct operational traits. "
            "Each item may describe a unique algorithmic approach, caching mechanism, retry "
            "logic, or any specialized behavior triggered under certain conditions. Guides "
            "tools in understanding how the function operates at a higher level."
        )
    )
    side_effects: Optional[List[str]] = Field(
        default_factory=list,
        description=(
            "A collection of descriptions outlining any state changes or external interactions "
            "the function may initiate beyond its return value. Each entry may detail database "
            "writes, file modifications, network calls, or similar. Essential for understanding "
            "the function's impact on external systems and data flows."
        )
    )
    usage_context: str = Field(
        ...,
        description=(
            "A general guideline for when and how to use the function, emphasizing typical "
            "use cases, constraints, or recommended practices. Helps code analysis and code "
            "generation tools place the function correctly within a broader application "
            "or workflow."
        )
    )


class CodeGraphFunctionSchema(BaseModel):
    """
    Complete representation of a function definition, capturing both its
    interface and implementation details. Used for static analysis,
    documentation generation, and code understanding tools.
    """
    model_config = ConfigDict(extra='forbid')

    name: str = Field(
        ...,
        description="Identifier for the function that follows language naming conventions. "
        "For methods, should include class/type context. Must be unique within its scope. "
        "IMPORTANT: Must never be blank. Assign a unique, inferred name when the function has no name, for example unnamed1"
    )

    kind: FunctionKind = Field(
        ...,
        description="Primary categorization of function behavior pattern: regular (single return), "
        "generator (yields values), coroutine (async/await), or special form (operator/conversion). "
        "Determines invocation syntax and execution model."
    )

    is_async: bool = Field(
        ...,
        description="Indicates if function uses async/await or equivalent asynchronous execution "
        "model. Affects callsite requirements, error handling patterns, and runtime behavior."
    )

    exported: bool = Field(
        description="Controls symbol visibility at package/module boundary. True means the "
        "function is part of the public API and should maintain backward compatibility."
    )

    visibility: Visibility = Field(
        description="Access control modifier that determines where function can be called from. "
        "Affects encapsulation boundaries and usage patterns across the codebase."
    )

    generic_params: Optional[List[GenericParameter]] = Field(
        default_factory=list,
        description="Type parameters for generic/templated functions, each specifying: "
        "1) parameter name, 2) constraints/bounds, and 3) variance rules. Used for "
        "static type checking and specialization."
    )

    signature: FunctionSignature = Field(
        ...,
        description="Complete type interface including: 1) parameter types and modifiers, "
        "2) return type structure, 3) throws/effects clauses, and 4) type constraints. "
        "Represents the function's static contract."
    )

    decorators: Optional[List[Decorator]] = Field(
        default_factory=list,
        description="Function modifiers or wrappers that alter behavior, each specifying: "
        "1) decorator type/name, 2) configuration parameters, and 3) behavioral impact. "
        "Used for cross-cutting concerns like logging or validation."
    )

    steps: List[Step] = Field(
        ...,
        description="IMPORTANT: Must never be blank. Ordered sequence of logical operations that implement the function. "
                    "Represents execution flow and data dependencies."
    )

    configuration: Optional[FunctionConfiguration] = Field(
        default_factory=dict,
        description="Runtime behavior controls including: 1) timeout thresholds, "
        "2) retry policies, 3) cancellation behavior, and 4) resource limits. "
        "Critical for operational characteristics."
    )

    summary: FunctionSummary = Field(
        ...,
        description="IMPORTANT: Must never be blank. High-level overview capturing the function's purpose, contract, "
        "behaviors and usage patterns. Primary reference for understanding function's "
        "role without implementation details."
    )
