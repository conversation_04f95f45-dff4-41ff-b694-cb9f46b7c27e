from enum import Enum
from typing import List, Optional
from pydantic import BaseModel, Field, ConfigDict

# Enums for various fixed choices


class ImportStyle(str, Enum):
    BARE = "bare"          # Simple import without qualification (e.g., 'import foo')
    NAMED = "named"        # Explicitly named imports (e.g., 'from foo import bar')
    NAMESPACE = "namespace"  # Imports creating a namespace object (e.g., 'import foo as ns')
    DESTRUCTURED = "destructured"  # Unpacking specific items (e.g., 'from foo import {bar, baz}')
    MODULE = "module"      # Full module import (e.g., 'import module_name')
    REFERENCE = "reference"  # Reference-only imports (e.g., 'from typing import TYPE_CHECKING')
    INCLUDE = "include"    # File-level inclusion (e.g., C++ #include, Ruby require)
    DYNAMIC = "dynamic"    # Runtime/dynamic imports (e.g., 'await import()', 'importlib.import_module()')


class ImportCategory(str, Enum):
    """
    Categorizes the semantic nature of the imported entity.
    This helps distinguish between different kinds of imports for static analysis
    and understanding code dependencies.
    """
    CLASS = "class"        # Class definitions and constructors
    FUNCTION = "function"  # Standalone functions and callable entities
    MODULE = "module"      # Entire modules or namespaces
    CONSTANT = "constant"  # Immutable values and configuration
    TYPE = "type"         # Type definitions, interfaces, and type aliases
    INTERFACE = "interface"  # Pure interface definitions without implementation
    ENUM = "enum"         # Enumerated types and constants
    NAMESPACE = "namespace"  # Organizational units containing related code
    AGGREGATE = "aggregate"  # Multiple different kinds of entities imported together
    RESOURCE = "resource"  # Non-code assets like text, templates, or data files


class UsageType(str, Enum):
    TYPE = "type"
    VALUE = "value"
    BOTH = "both"


class MemberCategory(str, Enum):
    """
    Categorizes the kind of member being accessed from an imported entity.
    Helps distinguish between different kinds of code organization and access patterns.
    """
    CLASS = "class"          # Class definitions, including abstract and concrete classes
    METHOD = "method"        # Instance, static, and class methods
    PROPERTY = "property"    # Data descriptors, computed properties, and fields
    TYPE = "type"           # Type aliases, generics, and type definitions
    EVENT = "event"         # Event emitters, handlers, and callbacks
    MODULE = "module"       # Module-level entities and namespaces
    CONSTANT = "constant"   # Immutable values, including frozen objects
    ENUM = "enum"          # Enum members and associated values
    DECORATOR = "decorator"  # Function and class decorators
    ANNOTATION = "annotation"  # Runtime metadata annotations (distinct from type annotations)


class AccessType(str, Enum):
    READ = "read"
    WRITE = "write"
    BOTH = "both"


class PackageCategory(str, Enum):
    """
    Categorizes the source and scope of imported packages to help understand
    dependencies and their roles in the codebase.
    """
    STANDARD_LIBRARY = "standard_library"  # Language's built-in libraries and modules
    SDK = "sdk"                           # Official platform/vendor development kits
    PACKAGE = "package"                   # Standalone, single-purpose dependencies
    FRAMEWORK = "framework"               # Large, multi-purpose application frameworks
    PLUGIN = "plugin"                     # Extensions for other packages or frameworks
    POLYFILL = "polyfill"                # Compatibility and feature backports


class Registry(str, Enum):
    # Python ecosystem
    PYPI = "pypi"  # Python Package Index
    CONDA_FORGE = "conda-forge"  # Conda Forge channel
    ANACONDA_CLOUD = "anaconda-cloud"  # Anaconda Cloud repositories

    # JavaScript/Node.js ecosystem
    NPM = "npm"  # Node Package Manager Registry (npmjs.com)

    # Java ecosystem
    MAVEN_CENTRAL = "maven-central"  # Maven Central Repository
    JCENTER = "jcenter"  # JCenter Repository

    # .NET ecosystem
    NUGET = "nuget"  # NuGet Gallery

    # iOS/Apple ecosystem
    COCOAPODS = "cocoapods"  # CocoaPods Trunk
    SWIFT_PACKAGE_INDEX = "swift-package-index"  # Swift Package Index

    # Android/Java ecosystem
    GOOGLE_MAVEN = "google-maven"  # Google's Maven Repository
    JITPACK = "jitpack"  # JitPack Repository

    # Other language-specific registries
    CRATES_IO = "crates-io"  # Rust's crate registry
    PACKAGIST = "packagist"  # PHP's Composer repository
    RUBYGEMS = "rubygems"  # Ruby's gem registry
    HEX = "hex"  # Elixir/Erlang package registry
    CPAN = "cpan"  # Perl's package registry

    # Container and binary registries
    DOCKER_HUB = "docker-hub"  # Docker container registry
    GHCR = "ghcr"  # GitHub Container Registry
    QUAY = "quay"  # Red Hat's Quay container registry

    # Cloud provider registries
    GITHUB_PACKAGES = "github-packages"  # GitHub Packages
    GITLAB_PACKAGES = "gitlab-packages"  # GitLab Package Registry
    AZURE_ARTIFACTS = "azure-artifacts"  # Azure Artifacts
    AWS_CODEARTIFACT = "aws-codeartifact"  # AWS CodeArtifact

    # Enterprise/Self-hosted solutions
    ARTIFACTORY = "artifactory"  # JFrog Artifactory
    NEXUS = "nexus"  # Sonatype Nexus Repository
    CLOUDSMITH = "cloudsmith"  # Cloudsmith package registry

    # Operating system package repositories
    DEBIAN = "debian"  # Debian package repository
    UBUNTU = "ubuntu"  # Ubuntu package repository
    FEDORA = "fedora"  # Fedora package repository
    HOMEBREW = "homebrew"  # macOS Homebrew tap repositories


class ModuleSystem(str, Enum):
    ESM = "esm"               # JavaScript/TypeScript ES Modules
    COMMONJS = "commonjs"     # Node.js require
    PYTHON = "python"         # Python import system
    JAVA_MODULE = "jpms"      # Java Platform Module System
    GO = "go"                 # Go modules
    RUST = "rust"             # Rust crate system
    CPP = "cpp"              # C++ include system
    RUBY = "ruby"            # Ruby require system
    CSHARP = "csharp"        # C# using system


class LoadingStrategy(str, Enum):
    STATIC = "static"         # Regular imports
    DYNAMIC = "dynamic"       # Runtime imports (import(), require())
    LAZY = "lazy"            # Lazy-loaded imports
    CONDITIONAL = "conditional"  # Platform/environment specific


class VisibilityScope(str, Enum):
    PUBLIC = "public"
    PRIVATE = "private"
    PROTECTED = "protected"
    INTERNAL = "internal"     # Go/C# internal packages
    MODULE = "module"         # Java module scope
    PACKAGE = "package"


class ImportMechanism(BaseModel):
    """
    Defines the specific syntactic and semantic mechanism used to import code.
    This model captures both how the import is written in the source code and
    how it affects symbol resolution and namespace management.
    """

    model_config = ConfigDict(extra='forbid')

    style: ImportStyle = Field(
        ...,
        description="The syntactic style of the import statement. This determines the structure "
        "of the import declaration and how the imported symbols are accessed. For example, "
        "'import foo' uses BARE style while 'from foo import bar as baz' combines NAMED style "
        "with an alias. The style affects both the import syntax and symbol resolution rules."
    )

    module_system: ModuleSystem = Field(
        ...,
        description="Specifies the language-specific module resolution mechanism. ESM uses export/import syntax "
        "and static analysis. CommonJS uses require() with dynamic resolution. Python combines module "
        "and package systems with dotted paths. JPMS uses explicit module declarations. Go uses go.mod "
        "based resolution. Each system affects how imports are resolved, loaded and cached."
    )

    loading_strategy: LoadingStrategy = Field(
        description="Defines when and how the import is resolved and loaded. Static imports are resolved "
        "at compile/parse time and loaded synchronously. Dynamic imports use runtime resolution "
        "and async loading. Lazy imports defer loading until first use. Conditional imports depend "
        "on runtime conditions like platform or environment. Affects build optimization and chunking."
    )

    visibility_scope: VisibilityScope = Field(
        description="Controls symbol visibility and access across module boundaries. Public allows unrestricted "
        "access. Private limits to defining module. Protected allows access by inheriting modules. "
        "Internal restricts to same assembly/package. Module scope follows language-specific module "
        "encapsulation rules. Critical for enforcing architectural boundaries and information hiding."
    )

    alias: Optional[str] = Field(
        description="Alternative name used when importing. This can be an 'as' clause in Python "
        "(e.g., 'import foo as bar'), a renamed import in JavaScript (e.g., 'import { foo as bar }'), "
        "or similar renaming mechanisms in other languages. None indicates the original name is used. "
        "The alias becomes the primary way to reference the import in the importing module's scope."
    )

    is_type_only: bool = Field(
        description="Indicates whether this is a type-system-only import that should be erased at runtime. "
        "Examples include 'import type' in TypeScript, TYPE_CHECKING guards in Python, or similar "
        "mechanisms in other languages with erasable type systems. Type-only imports should not "
        "affect runtime behavior or bundle size."
    )

    is_default: bool = Field(
        ...,
        description="Whether this import uses the default export/import mechanism. Examples include "
        "'export default' in JavaScript/TypeScript or '__all__' in Python. Default exports affect "
        "both how the symbol is imported and interoperability with different module systems."
    )

    is_namespace: bool = Field(
        ...,
        description="Whether this import creates a new namespace object that contains the imported symbols. "
        "Examples include 'import * as ns' in JavaScript or 'import foo' (where foo is a package) in Python. "
        "Namespace imports affect symbol resolution and can help avoid naming conflicts."
    )

    is_reexported: bool = Field(
        ...,
        description="Whether this import is immediately re-exported from the importing module. "
        "Examples include 'export { foo } from \"bar\"' in JavaScript or '__all__ = [\"foo\"]' after "
        "importing in Python. Re-exports affect the module's public interface and dependency graph."
    )


class ImportKind(BaseModel):
    """
    Describes the nature and usage pattern of an imported entity at module scope.
    This classification helps understand how the import affects the module's interface
    and its dependencies.
    """

    model_config = ConfigDict(extra='forbid')

    category: ImportCategory = Field(
        ...,
        description="The semantic category of the imported entity (e.g., class, function, module). "
        "This indicates what kind of code construct is being imported, which affects how it can be used. "
        "For example, a CLASS import can be instantiated or subclassed, while a MODULE import provides a namespace."
    )

    usage: UsageType = Field(
        ...,
        description="How the imported entity is used within the importing module's code. "
        "TYPE indicates use only in type annotations, VALUE indicates runtime-only usage, "
        "and BOTH indicates the entity is used for both typing and runtime purposes. "
    )


class MemberKind(BaseModel):
    """
    Describes the nature and usage pattern of a specific member accessed from an imported entity.
    This provides detailed information about how individual members of an import are used,
    which is more granular than ImportKind.
    """

    model_config = ConfigDict(extra='forbid')

    category: MemberCategory = Field(
        ...,
        description="The structural category of the accessed member (e.g., method, property, constant). "
        "This indicates what kind of access patterns are valid for this member. For example, "
        "a METHOD can be called, a PROPERTY can be get/set, and a CONSTANT should only be read."
    )

    usage: UsageType = Field(
        ...,
        description="How this specific member is used in the code. Indicates whether the member "
        "is used for type annotations (TYPE), runtime values (VALUE), or both purposes (BOTH). "
        "For example, a class method might be used both as a callable at runtime and in type positions."
    )


class MemberAccess(BaseModel):
    """Describes how a member of the imported item is accessed."""

    model_config = ConfigDict(extra='forbid')

    name: str = Field(
        ...,
        description="Name of the accessed member"
    )
    kind: MemberKind = Field(
        ...,
        description="Kind of the member being accessed"
    )
    access_type: AccessType = Field(
        ...,
        description="How the member is accessed (read/write/both)"
    )
    sub_members: List[str] = Field(
        default_factory=list,
        description="Sub-members accessed on the member, like classes, functions, etc"
    )
    optional: bool = Field(
        ...,
        description="Whether the member access is optional"
    )


class Source(BaseModel):
    """
    Describes source location and import characteristics for internal imports within the repository.
    This model captures both the physical location and logical organization of the imported code.
    """

    model_config = ConfigDict(extra='forbid')

    module_path: str = Field(
        description="Import path as used in the source code (e.g., '../utils/helpers' or 'mypackage.submodule'). "
        "This is the exact path string used in the import statement, whether relative or absolute."
    )

    full_path: str = Field(
        ...,
        description="Absolute filesystem path to the source file (e.g., '/home/<USER>/project/src/utils/helpers.py'). "
        "Always uses forward slashes and is normalized to the repository root."
    )

    is_relative: bool = Field(
        ...,
        description="Whether the import uses relative path notation (../foo) rather than absolute package paths (mypackage.foo)"
    )

    is_side_effect: bool = Field(
        description="Whether this import is executed solely for its side effects (e.g., module initialization, monkey patching, "
        "registration of handlers) rather than for accessing its exports. Examples include importing for "
        "automatic registration of CLI commands or loading environment variables."
    )

    root_relative_path: str = Field(
        description="Path relative to repository root (e.g., 'src/utils/helpers.py'). "
        "Useful for mapping imports across different checkout locations. Always uses forward slashes."
    )

    is_conditional: bool = Field(
        ...,
        description="Whether this import occurs inside a conditional block (if/try/etc) rather than at module level. "
    )


class Package(BaseModel):
    """Package information for external imports."""

    model_config = ConfigDict(extra='forbid')

    name: str = Field(
        ...,
        description="Name of the package"
    )
    version: str = Field(
        ...,
        description="Version of the package"
    )
    category: PackageCategory = Field(
        ...,
        description="Categorizes the package source: standard_library (language built-ins), "
        "sdk (platform/vendor SDKs), package (third-party dependencies), or "
        "framework (larger bundled collections of functionality)"
    )
    registry: Registry = Field(
        description="Package registry (npm, pypi, etc.) and not package manager."
    )
    is_dev_dependency: bool = Field(
        description="Whether this is a dependency that only required during development"
    )

# Main import definition models


class InternalImport(BaseModel):
    """
    Defines an import of code from within the same repository or project.
    This model captures detailed information about imports between modules that are
    part of the same codebase, including their relationships, usage patterns, and organization.
    Internal imports are distinguished from external imports by being under source control
    in the same repository and typically following project-specific architecture patterns.
    """
    model_config = ConfigDict(extra='forbid')

    name: str = Field(
        ...,
        description="The symbol name as written in the import statement. For named imports this is "
                    "the specific symbol name (e.g., 'MyClass' in 'from .models import MyClass'). "
                    "For module imports this is the module name (e.g., 'utils' in 'import utils'). "
                    "This name is used for symbol resolution before any aliasing is applied."
    )

    import_mechanism: ImportMechanism = Field(
        ...,
        description="Specifies how the import is syntactically written and semantically resolved. "
                    "This includes information about import style, aliasing, and type-only usage. "
                    "The mechanism affects how the imported symbols are accessed in the importing module."
    )

    kind: ImportKind = Field(
        ...,
        description="Categorizes what is being imported (class, function, etc.) and how it's used "
                    "(type annotations, runtime values, or both). This helps understand the dependency "
                    "relationship between modules and aids in static analysis and refactoring."
    )

    members_accessed: List[MemberAccess] = Field(
        default_factory=list,
        description="Tracks which specific members of the imported entity are actually used in the code. "
                    "This helps identify unused imports and understand module coupling. An empty list implies "
                    "either the import is unused or the entire module namespace is accessed."
    )

    source: Source = Field(
        ...,
        description="Details about where and how the imported code is located within the repository. "
                    "Includes both filesystem paths and module paths, helping to understand project structure "
                    "and module organization. Critical for dependency analysis and refactoring."
    )

    purpose: str = Field(
        ...,
        description="Documents the architectural or functional reason for this import. Explains why this "
                    "dependency exists and what role it plays within the system design."
    )

    summary: str = Field(
        ...,
        description="IMPORTANT: Must never be blank. A concise summary capturing the overall content, functionality, and architectural "
                    "context of the source file from which this internal import is declared. This summary "
                    "should include key insights into the file's responsibilities and design, making it highly "
                    "useful for search and vector indexing."
    )


class ExternalImport(BaseModel):
    """
    Defines an import from a third-party package, framework, or library outside the repository.
    This model captures detailed information about external dependencies, including their
    source, versioning, and usage patterns. External imports are distinguished from internal
    imports by being maintained outside the repository and distributed via package managers
    or registries.
    """
    model_config = ConfigDict(extra='forbid')

    name: str = Field(
        ...,
        description="The symbol name as written in the import statement. For named imports, this is "
                    "the specific imported symbol (e.g., 'DataFrame' in 'from pandas import DataFrame'). "
                    "For package imports, this is the package name (e.g., 'requests' in 'import requests'). "
                    "Must match the exported symbol name from the external package."
    )

    import_mechanism: ImportMechanism = Field(
        ...,
        description="Specifies how the import is syntactically written and semantically resolved. "
                    "This includes details about import style, aliasing, and type-only usage. For external "
                    "imports, it reflects package-specific patterns and best practices."
    )

    kind: ImportKind = Field(
        ...,
        description="Categorizes what is being imported (class, function, etc.) and how it is used. "
                    "For external imports, this helps in tracking API usage patterns and identifying potential "
                    "breaking changes in upstream dependencies."
    )

    package: Package = Field(
        ...,
        description="Metadata about the external package including its name, version, registry, and category. "
                    "This information is essential for dependency management, security auditing, and ensuring "
                    "reproducible builds. It also indicates whether the dependency is used at runtime or solely "
                    "for development."
    )

    members_accessed: List[MemberAccess] = Field(
        ...,
        description="Tracks which specific members of the imported package are used in the code. This information "
                    "aids in identifying unnecessary dependencies and understanding API usage patterns. An empty "
                    "list suggests either the import is unused or that the entire namespace is accessed."
    )

    purpose: str = Field(
        ...,
        description="Documents the reason for incorporating this external dependency, including the specific "
                    "capabilities it provides and why an internal implementation is not sufficient."
    )

    min_version_required: Optional[str] = Field(
        description="The minimum version required of the external package for the features being used. This helps "
                    "ensure compatibility and prevent issues due to breaking changes in newer versions. The format "
                    "should adhere to the package's versioning scheme."
    )

    summary: str = Field(
        ...,
        description="IMPORTANT: Must never be blank. A concise summary capturing the overall content, functionality, and contextual purpose of the "
                    "file where this external import is declared. This summary should highlight the key aspects of "
                    "the dependency, making it useful for search and vector indexing by reflecting its role in "
                    "dependency management and system architecture."
    )

# Root model


class CodeGraphImportsSchema(BaseModel):
    """
    Root model for analyzing and documenting all import relationships in a source file.
    This schema provides a comprehensive view of both internal (same-repository) and
    external (third-party) dependencies, their usage patterns, and their architectural implications.

    The schema distinguishes between imports based on their source and scope:
    - Internal imports are from modules within the same version-controlled repository
    - External imports are from packages distributed via registries and package managers

    This distinction is critical for:
    - Dependency management and security auditing
    - Architectural boundary enforcement
    - Build optimization and tree-shaking
    - Refactoring and maintenance planning
    """

    model_config = ConfigDict(extra='forbid')

    internal: List[InternalImport] = Field(
        ...,
        description="List of imports from modules within the same repository or project. "
        "These represent architectural relationships between components under direct "
        "project control. Internal imports are identified by being under the same "
        "version control and following project-specific module organization rules. "
        "Examples include imports from './utils', '../models', or 'myapp.core'."
    )

    external: List[ExternalImport] = Field(
        ...,
        description="List of imports from third-party packages, libraries, or frameworks. "
        "These represent dependencies on code maintained outside the project and distributed "
        "via package managers or registries. External imports typically require explicit "
        "version management and security monitoring. Examples include framework imports "
        "like 'react', utility libraries like 'lodash', or type packages like '@types/node'."
    )
