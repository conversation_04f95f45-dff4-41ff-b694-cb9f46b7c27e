from pydantic import BaseModel, Field, ConfigDict


class FileSummary(BaseModel):
    """
    Comprehensive summary of a source-adjacent file's purpose, context, and relationships
    within the project. This summary provides both technical and contextual information 
    to help understand the file's role and proper usage.
    """

    model_config = ConfigDict(extra='forbid')

    summary: str = Field(
        ...,
        description="A comprehensive and precise summary of the file's role within the project. "
        "Include the file's primary function and responsibility (what it configures, defines, or documents, and what would break if removed), "
        "an ordered list of its dependencies (referenced files, environment variables, external services, and related configuration files with full paths or identifiers), "
        "and detailed usage context (which components reference it, when it's processed, what triggers changes, and applicable conditions or environments). "
        "This consolidated field captures the file's complete technical and contextual profile for use in repository analysis and documentation."
    )


class FolderSummary(BaseModel):
    model_config = ConfigDict(extra='forbid')

    summary: str = Field(
        ...,
        description="A comprehensive and precise summary of the folder’s contents. Encapsulate both high-level design aspects "
        "(including frameworks, dependencies, tools, and overall architectural patterns) and specific implementation details "
        "(such as processes, actions, interfaces, and any mechanisms for content traversal or static analysis). "
        "The focus should be on the folder’s immediate (first order) children, with only high-level details for any nested children, "
        "as each sub-folder is assumed to have its own detailed summary. This field is intended for use by folder traversal algorithms"
        "and vector search mechanisms when analyzing repository contents."
    )
