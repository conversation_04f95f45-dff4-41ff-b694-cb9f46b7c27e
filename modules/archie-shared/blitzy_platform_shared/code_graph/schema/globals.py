from enum import Enum
from typing import List, Optional
from pydantic import BaseModel, Field, ConfigDict


class TypeKind(str, Enum):
    INTERFACE = "interface"
    TYPE = "type"
    ENUM = "enum"
    UNION = "union"
    ALIAS = "alias"
    MODULE = "module"


class Visibility(str, Enum):
    """
    Comprehensive visibility modifiers across major programming languages

    Covers access control patterns from:
    - Java (public, private, protected, package-private)
    - C# (public, private, protected, internal, protected internal, private protected)
    - C++ (public, private, protected, friend)
    - Python (public, name mangling/private by convention)
    - TypeScript/JavaScript (public, private, protected, namespace scope)
    - Ruby (public, private, protected)
    - Go (exported/unexported based on case)
    - Rust (pub, pub(crate), pub(super), pub(in path))
    """
    # Universal visibility levels
    PUBLIC = "public"                    # Accessible from anywhere
    PRIVATE = "private"                  # Only accessible within defining class/scope
    PROTECTED = "protected"              # Accessible by class and its descendants

    # Package/module level visibility
    PACKAGE_PRIVATE = "package_private"  # Java's default: visible within package only
    INTERNAL = "internal"                # C#: visible within same assembly
    MODULE = "module"                    # TypeScript/ES modules: module-scope visibility

    # Mixed visibility patterns
    PROTECTED_INTERNAL = "protected_internal"  # C#: visible in assembly and to derived classes
    PRIVATE_PROTECTED = "private_protected"    # C#: visible in assembly to derived classes

    # Language-specific patterns
    FRIEND = "friend"                    # C++: explicit visibility grant to specific classes/functions
    CRATE = "crate"                      # Rust: visible within current crate
    CRATE_SUPER = "crate_super"          # Rust: visible in parent and ancestor modules
    CRATE_PATH = "crate_path"            # Rust: visible to specific module path

    # File-level visibility
    FILE_PRIVATE = "file_private"        # Swift/Kotlin: visible only within defining file

    # Special cases
    DEFAULT = "default"                  # Language-dependent default visibility
    NAME_MANGLED = "name_mangled"        # Python's name mangling with double underscore
    EXPORTED = "exported"                # Go's uppercase export visibility


class Mutability(str, Enum):
    READONLY = "readonly"
    FROZEN = "frozen"
    MUTABLE = "mutable"


class Scope(str, Enum):
    MODULE = "module"
    GLOBAL = "global"


class Lifecycle(str, Enum):
    STATIC = "static"
    RUNTIME = "runtime"


class ExternalKind(str, Enum):
    AMBIENT = "ambient"      # For declaring types/vars that exist at runtime but aren't defined in code
    # e.g., 'declare const window: Window' in TypeScript

    LINK = "link"           # For declaring linkage to external symbols
    # e.g., 'extern int global_counter;' in C/C++

    NATIVE = "native"       # For declaring platform/native API interfaces
    # e.g., 'declare interface WebGLRenderingContext' in TS/JS

    INTRINSIC = "intrinsic"  # For declaring built-in language constructs
    # e.g., 'declare interface Array<T>' in TS


class TypeDefinition(BaseModel):
    model_config = ConfigDict(extra='forbid')

    name: str = Field(
        ...,
        description="Identifier for this type. Must be unique within the module and adhere to language naming conventions."
    )
    kind: TypeKind = Field(
        ...,
        description="Specifies the category of the type (e.g., interface, type, enum, union, alias, module).""This classification influences its usage and how it is interpreted by consumers."
    )
    definition: str = Field(
        ...,
        description="The complete structural definition or implementation details of the type, including its members, underlying data structures, and any applicable constraints."
    )
    exported: bool = Field(
        ...,
        description="Indicates whether the type is exposed outside its defining module, making it available for import in other parts of the codebase."
    )
    visibility: Visibility = Field(
        ...,
        description="Specifies the access level of the type within the codebase (e.g., public, private, protected), following language-specific visibility rules."
    )
    summary: str = Field(
        ...,
        description="IMPORTANT: Must never be blank. A concise summary that captures the essence, purpose, and design of the type. This summary should provide clear insights into what the type represents, its intended usage, and any key design decisions."
    )
    extends: List[str] = Field(
        default_factory=list,
        description="A list of parent or base types from which this type inherits, providing context for its role in an inheritance or interface implementation hierarchy."
    )


class DeclarationSource(str, Enum):
    LOCAL = "local"      # Defined in this module
    AMBIENT = "ambient"  # Runtime globals
    LINK = "link"        # External linked symbols
    NATIVE = "native"    # Platform/native APIs
    INTRINSIC = "intrinsic"  # Language builtins


class Declaration(BaseModel):
    model_config = ConfigDict(extra='forbid')

    name: str = Field(
        ...,
        description="Identifier for this declaration. Must uniquely identify the symbol within its scope and follow the language's naming conventions."
    )
    type: str = Field(
        ...,
        description="The type signature or data type associated with the declaration, detailing the structure or interface of the declared symbol."
    )
    source: DeclarationSource = Field(
        ...,
        description="Specifies the origin of the declaration, indicating whether it is defined locally within the module or represents an external construct (ambient, linked, native, or intrinsic)."
    )
    exported: bool = Field(
        ...,
        description="Determines whether the declaration is exposed outside its module, thereby forming part of the module's public API."
    )
    mutability: Mutability = Field(
        ...,
        description="Describes the mutability characteristics of the declared symbol, indicating whether it is immutable (readonly or frozen) or can be modified (mutable)."
    )
    scope: Scope = Field(
        ...,
        description="Specifies the scope level at which the declaration is valid, such as module-level or global, which affects its visibility and lifetime."
    )
    initial_value: Optional[str] = Field(
        None,
        description="An optional field that provides the initial value assigned to the declaration, offering insights into its default state or behavior."
    )
    lifecycle: Lifecycle = Field(
        ...,
        description="Describes when or how the value of the declaration is computed or becomes available, such as during static initialization or at runtime."
    )
    summary: str = Field(
        ...,
        description="IMPORTANT: Must never be blank. A concise summary that captures the essential context, purpose, and design considerations of the declaration. This summary should highlight the role, usage, and any noteworthy implementation details of the symbol within the system."
    )

# Root Schema


class CodeGraphGlobalsSchema(BaseModel):
    """
    Captures all file-level declarations and definitions that appear outside of functions 
    and classes. This includes type definitions (like interfaces, enums, type aliases),
    and declarations of both local values (constants, variables) and external entities
    (ambient declarations, linked symbols, native APIs, language constructs).
    """

    model_config = ConfigDict(extra='forbid')

    type_definitions: List[TypeDefinition] = Field(
        ...,
        description="File-level type definitions (not declarations) including interfaces, enums, "
        "unions, and type aliases."
    )

    declarations: List[Declaration] = Field(
        ...,
        description="File-level declarations (not definitions) of symbols including variables, "
        "constants, and external references. The source field distinguishes between forward "
        "declarations of local symbols and different types of external declarations"
    )
