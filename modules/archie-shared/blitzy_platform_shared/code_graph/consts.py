from enum import Enum

COMMON_RETRIEVAL_QUERY = """
    WITH node, score
    WHERE score >= $min_score
      AND node.company_id = $company_id
      AND node.repo_id = $repo_id
      AND node.branch_id = $branch_id
    RETURN 
    REDUCE(s = '', key IN $properties |
        s + CASE WHEN node[key] IS NOT NULL 
        THEN '\n' + toUpper(key) + ': ' + node[key]
        ELSE '' END
    ) AS text,
    { 
        path: node.path,
        score: score,
        company_id: node.company_id,
        repo_id: node.repo_id,
        branch_id: node.branch_id
    } AS metadata,
    score
"""

STEP_RETRIEVAL_QUERY = """
    WITH node, score
    WHERE score >= $min_score
      AND node.company_id = $company_id
      AND node.repo_id = $repo_id
      AND node.branch_id = $branch_id
    RETURN 
    REDUCE(s = '', key IN $properties |
        s + CASE 
        WHEN key = 'function_name' AND node.function_name IS NOT NULL 
            THEN '\nFUNCTION: ' + node.function_name
        WHEN key = 'method_name' AND node.method_name IS NOT NULL AND node.class_name IS NOT NULL
            THEN '\nMETHOD: ' + node.class_name + '.' + node.method_name
        WHEN key NOT IN ['function_name', 'method_name', 'class_name'] AND node[key] IS NOT NULL
            THEN '\n' + toUpper(key) + ': ' + node[key]
        ELSE '' 
        END
    ) AS text,
    { 
        path: node.path,
        score: score,
        company_id: node.company_id,
        repo_id: node.repo_id,
        branch_id: node.branch_id
    } AS metadata,
    score
"""

EMBEDDING_PROPERTY = "embedding"


class GraphNodeName(str, Enum):
    FILE = "FILE"
    FOLDER = "FOLDER"
    TYPE_DEFINITION = "TYPE_DEFINITION"
    DECLARATION = "DECLARATION"
    INTERNAL_IMPORT = "INTERNAL_IMPORT"
    EXTERNAL_IMPORT = "EXTERNAL_IMPORT"
    FUNCTION = "FUNCTION"
    STEP = "STEP"
    CLASS = "CLASS"
    METHOD = "METHOD"
    EXPORT = "EXPORT"
    SECTION = "SECTION"


class VectorIndexName(str, Enum):
    FILES = "files"
    FOLDERS = "folders"
    TYPE_DEFINITIONS = "type_definitions"
    DECLARATIONS = "declarations"
    EXTERNAL_IMPORTS = "external_imports"
    INTERNAL_IMPORTS = "internal_imports"
    FUNCTIONS = "functions"
    STEPS = "steps"
    CLASSES = "classes"
    METHODS = "methods"
    EXPORTS = "exports"
    SECTIONS = "sections"


COMMON_VECTOR_INDEX_PROPERTIES = [
    "path",
    "summary"
]
ALT_VECTOR_INDEX_PROPERTIES = [
    "name",
    "file_path",
    "summary"
]
IMPORT_VECTOR_INDEX_PROPERTIES = [
    "name",
    "file_path",
    "purpose",
    "summary"
]
STEP_VECTOR_INDEX_PROPERTIES = [
    "name",
    "function_name",
    "method_name",
    "class_name",
    "file_path",
    "summary"
]
METHOD_VECTOR_INDEX_PROPERTIES = [
    "name",
    "class_name",
    "file_path",
    "summary"
]


class BatchStatus(str, Enum):
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETE = "COMPLETE"
