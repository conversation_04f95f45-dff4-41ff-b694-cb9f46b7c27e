from enum import Enum
from typing import List

from pydantic import BaseModel, Field


class AnthropicTextEditorCommand(str, Enum):
    view = "view"
    str_replace = "str_replace"
    create = "create"
    insert = "insert"


class FileOrFolderStatus(str, Enum):
    CREATED = "CREATED",
    UPDATED = "UPDATED"
    DELETED = "DELETED"


class BaseFile(BaseModel):
    key_changes: List[str] = Field(
        ...,
        description="A detailed list of changes that need be incorporated into this new file based on the summary of changes and technical specification."
    )
    is_dependency_file: bool = Field(
        ...,
        description="Whether this file declares external/third-party package dependencies (e.g., package.json, pom.xml, requirements.txt, Gemfile, go.mod, Cargo.toml, composer.json). Excludes pure build configuration files."
    )
    depends_on_files: List[str] = Field(
        ...,
        description="A list of files from the destination repository that this file imports. For example, if this is a class that imports an interface file, list that here. If the Summary of Changes calls out any source or context files for this file, ensure that you list them here."
    )


class CreatedFile(BaseFile):
    dest_path: str = Field(
        ...,
        description="New absolute path of the file in the destination repository, refactored according to the technical specification."
    )
    summary: str = Field(
        ...,
        description="Concise description of the file's primary function and responsibility within the project, including what it configures, defines, or documents. Should clearly state what would break or be missing if this file were removed"
    )
    requirements: List[str] = Field(
        ...,
        description="A list of requirements from the updated technical specification that led to this file being created."
    )
    source_files: List[str] = Field(
        ...,
        description="A list of 1:1 mirrored file paths from the existing repository that contain important context about this new file. "
        "IMPORTANT: Must be a list of FILE paths from the existing repository. Must never be a folder or the root folder."
    )


class UpdatedFile(BaseFile):
    dest_path: str = Field(
        ...,
        description="Absolute path of the existing file that is being updated. IMPORTANT: Must be unchanged."
    )


class ExcludedItem(BaseModel):
    dest_path: str = Field(
        ...,
        description="Absolute path of the file or folder from the existing repository"
    )
    reason: str = Field(
        ...,
        description="Reason for being excluded from the destination repository"
    )
    replaced_by: List[str] = Field(
        ...,
        description="List of absolute paths fot new files or folders that replace this excluded item. "
    )
