def prepend_line_numbers(file_content: str) -> str:
    """
    Prepends line numbers to each line of the file content, starting at 1.

    Args:
        file_content: Content of the file as a string

    Returns:
        String with line numbers prepended to each line
    """
    if not file_content:
        return file_content

    lines = file_content.split('\n')
    numbered_lines = [f"{i+1}: {line}" for i, line in enumerate(lines)]
    return '\n'.join(numbered_lines)
