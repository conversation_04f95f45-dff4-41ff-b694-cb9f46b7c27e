
from google.cloud import pubsub_v1
from blitzy_utils.common import publish_notification

PROJECT_ID = 'blitzy-os-dev'
GENERATE_CODE_TOPIC = 'generate-code'

publisher = pubsub_v1.PublisherClient()

notification_data = {"repo_name": "page-object-model-eyegxw", "project_id": "bc81cf02-1fd1-42d0-af2e-5d444756a09c",
                     "job_id": "ee38da14-793d-4918-b17c-d48e87165f6b", "propagate": True, "user_id": "a2912363-d975-44a9-8395-97fdf846aa73"}
publish_notification(publisher, notification_data, PROJECT_ID, GENERATE_CODE_TOPIC)
