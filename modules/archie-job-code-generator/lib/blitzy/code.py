import os

import json
from typing import TypedDict, Dict, Any, Literal, List

from langgraph.graph import START, StateGraph, END
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.tools import tool
from langgraph.prebuilt import ToolNode
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage, ToolMessage
from anthropic import BadRequestError as AnthropicBadRequestError
from tenacity import RetryError

from lib.blitzy.prompts import TECH_SPEC_SECTIONS_INPUT, GITHUB_REPO_INPUT, CODE_GENERATOR_SYSTEM_PROMPT, \
    HUMAN_TASKS_GENERATOR_SYSTEM_PROMPT, HUMAN_TASKS_TEMPLATE, DEP_MANIFEST_ADDENDUM, IMPORTED_FILES_ADDENDUM

from blitzy_utils.common import get_json_content, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>nt<PERSON><PERSON><PERSON>, \
    upload_text_to_gcs_using_admin_service
from blitzy_utils.consts import DEPENDENCY_FILES
from blitzy_utils.logger import logger

from blitzy_platform_shared.document.tools import get_tech_spec_section
from blitzy_platform_shared.common.utils import get_formatted_tool_result_messages, get_response_content, \
    archie_exponential_retry
from blitzy_platform_shared.common.consts import RETRYABLE_EXCEPTIONS, CONTEXT_1M
from blitzy_platform_shared.common.utils import format_messages
from blitzy_platform_shared.common.tools import ANTHROPIC_WEB_SEARCH_TOOL_DEFINITION


class CodeState(TypedDict):
    files_list: List[str]
    index: int
    file_spec_dict: Dict[str, Dict[str, Any]]
    current_file_spec: Dict[str, Any]
    is_retry: bool
    retry_count: int
    ht_sect_idx: int  # human task section that's currently being worked on
    ht_sections: List[str]  # ordered list of human task sections
    tech_spec: str
    tech_spec_parsed: Dict[str, str]
    dep_dict: Dict[str, Dict[str, str]]
    files_dict: Dict[str, str]
    changes_dict: Dict[str, Dict[str, str]]
    thinking_dict: Dict[str, str]


thinking_helper = DictFileHelper(files={})
changes_helper = DictFileHelper(files={})
code_files_dict_helper = DictFileHelper(files={})
code_spec_dict_helper = DictFileHelper(files={})
tech_spec_helper = FileContentHelper(file="")


def reset_dict(dict: Dict[Any, Any]):
    dict = {}
    return dict


@tool("get_file_implementation", parse_docstring=True)
def get_file_implementation(imported_file_path: str):
    """
        Attempts to retrieve the implementation of a potential dependency file within a GitHub repo.
        Useful for understanding the schema, structure and imports of potential dependency files, when those file paths are queried using imported_file_path.

        Args:
            imported_file_path: A file path in the GitHub repo which might be imported inside the file.
    """
    implementation_files_dict = code_files_dict_helper.get_dict()
    file = implementation_files_dict.get(imported_file_path)
    if file:
        logger.info(f'retrieved existing file implementation: {imported_file_path}')
        # logger.info(file)
        return file
    else:
        return f"file implementation does not exist: {imported_file_path}"


code_tools_base = [get_file_implementation, get_tech_spec_section]
code_tools = code_tools_base + [ANTHROPIC_WEB_SEARCH_TOOL_DEFINITION]
code_tools_node = ToolNode(code_tools_base)


class CodeGeneratorHelper:
    def __init__(
            self,
            generator_llm: BaseChatModel,
            human_tasks_llm: BaseChatModel,
            fallback_llm: BaseChatModel,
            repo_name: str,
            blob_name: str,
            bucket_name: str,
            storage_client,
            files_dict: Dict[str, Dict[str, Any]],
            tech_spec: str,
            company_id: str,
            git_project_repo_id: str):
        self.generator_llm = generator_llm
        self.human_tasks_llm = human_tasks_llm
        self.fallback_llm = fallback_llm
        self.repo_name = repo_name
        self.blob_name = blob_name
        self.bucket_name = bucket_name
        self.company_id = company_id
        self.storage_client = storage_client
        self.code_spec_dict_helper = code_spec_dict_helper
        self.code_spec_dict_helper.set_dict(files_dict)
        self.code_files_dict_helper = code_files_dict_helper
        self.tech_spec_helper = tech_spec_helper
        self.tech_spec_helper.set_file(file=tech_spec)
        self.git_project_repo_id = git_project_repo_id

    def create_graph(self) -> StateGraph:
        # Define the graph
        code_generator = StateGraph(CodeState)

        # Add nodes
        code_generator.add_node("generate_file", self.generate_file)
        code_generator.add_node("generate_human_tasks_section", self.generate_human_tasks_section)

        code_generator.add_conditional_edges(
            "generate_file",
            self.router,
            {
                "continue": "generate_file",
                "end": "generate_human_tasks_section"
            }
        )

        code_generator.add_conditional_edges(
            "generate_human_tasks_section",
            self.human_task_section_router,
            {
                "continue": "generate_human_tasks_section",
                "end": END
            }
        )

        # Set the entry point
        code_generator.add_edge(START, "generate_file")
        return code_generator

    def router(self, state: CodeState) -> Literal["continue", "end"]:
        files_list = state["files_list"]
        if state["index"] >= len(files_list):
            return "end"
        return "continue"

    def human_task_section_router(self, state: CodeState) -> Literal["continue", "end"]:
        """ route to the generation of next human task section until all sections are generated """
        if state["ht_sect_idx"] >= len(HUMAN_TASKS_TEMPLATE):
            return "end"
        return "continue"

    def load_imported_files(self, file_spec, state: CodeState, processed_files=None):
        """
        Recursively get all imported files and their dependencies.

        Args:
            file_spec: File specification dictionary
            processed_files: Set to track processed files to avoid circular dependencies

        Returns:
            tuple: (imported_files_string, imported_files_list)
        """
        if processed_files is None:
            processed_files = set()

        imported_files = ""
        imported_files_list = []

        if 'imports' in file_spec and 'internal' in file_spec['imports']:
            for dep in file_spec['imports']['internal']:
                dep_path = dep['path']

                # Skip if already processed
                if dep_path in processed_files:
                    continue

                processed_files.add(dep_path)

                if dep_path not in imported_files_list:
                    imported_files_list.append(dep_path)
                    file_implementation = state["files_dict"].get(dep_path)
                    code_files_dict_helper.get_dict()[dep_path] = file_implementation
                    if file_implementation:

                        imported_files += f"file path: {dep_path} \n\n{json.dumps(file_implementation)}\n\n"

                        # Get dependency's file spec
                        dep_file_spec = state["file_spec_dict"].get(dep_path)
                        if dep_file_spec:
                            # Recursively get dependencies of this dependency
                            recursive_imports, recursive_list = self.load_imported_files(
                                dep_file_spec,
                                state,
                                processed_files
                            )
                            imported_files += recursive_imports
                            imported_files_list.extend([f for f in recursive_list if f not in imported_files_list])

        return imported_files, imported_files_list

    @archie_exponential_retry()
    def generate_file(self, state: CodeState) -> Dict[str, Any]:
        file_path = state["files_list"][state["index"]]
        file_spec = state["file_spec_dict"][file_path]
        state["current_file_spec"] = file_spec

        state["is_retry"] = False

        logger.info(f"generating file: {file_path}")

        _, imported_files_list = self.load_imported_files(file_spec=file_spec, state=state)

        # logger.info(f'imported files: {imported_files}')

        messages = [
            SystemMessage(content=[
                {
                    "type": "text",
                    "text": CODE_GENERATOR_SYSTEM_PROMPT
                }
            ])
        ]

        file_parts = file_path.split('/')
        filename = file_parts[-1]
        human_message_content = [
            {
                "type": "text",
                "text": TECH_SPEC_SECTIONS_INPUT.format(
                    tech_spec_sections=json.dumps(list(state["tech_spec_parsed"].keys()))
                ),
                "cache_control": {"type": "ephemeral"}
            },
            {
                "type": "text",
                "text": "file JSON specification: {current_file_spec}".format(current_file_spec=state["current_file_spec"])
            },
            {
                "type": "text",
                "text": IMPORTED_FILES_ADDENDUM.format(imported_files=imported_files_list)
            }
        ]
        if filename in DEPENDENCY_FILES:
            subfolder_name = file_parts[1] if len(file_parts) > 1 else file_parts[0]
            dep_manifest = state["dep_dict"].get(subfolder_name)
            logger.info(f'including addendum for dep file {filename} in subfolder {subfolder_name}')
            # logger.info(f'dep manifest: {dep_manifest}')
            human_message_content.append(
                {
                    "type": "text",
                    "text": DEP_MANIFEST_ADDENDUM.format(dep_manifest=json.dumps(dep_manifest))
                }
            )
        messages.append(
            HumanMessage(
                content=human_message_content
            )
        )

        orig_messages = messages.copy()

        try:
            response: AIMessage = self.generator_llm.invoke(messages)

            while len(response.tool_calls):
                # fill in the value of local variables
                messages.append(response)
                total_tokens = response.usage_metadata["total_tokens"]
                # logger.info(total_tokens)
                tools_response = code_tools_node.invoke({
                    "messages": [response]
                }, config={
                    "configurable": {
                        "tech_spec_parsed": state["tech_spec_parsed"]
                    }
                })

                tool_results = get_formatted_tool_result_messages(
                    tool_message_list=tools_response["messages"],
                    total_tokens=total_tokens,
                    llm=self.generator_llm
                )
                # logger.info(tools_response)
                messages += tool_results
                messages = format_messages(
                    messages=messages
                )
                # logger.info(f'sending tool response back to llm for file: {file_path}')
                response: AIMessage = self.generator_llm.invoke(messages)

            # logger.info(response)
        except AnthropicBadRequestError as e:
            logger.warning(f'Anthropic bad request error, trying with fallback: {e}')
            response: AIMessage = self.fallback_llm.invoke(orig_messages)

            while len(response.tool_calls):
                # fill in the value of local variables
                messages.append(response)
                total_tokens = response.usage_metadata["total_tokens"]
                # logger.info(total_tokens)
                tools_response = code_tools_node.invoke({
                    "messages": [response]
                }, config={
                    "configurable": {
                        "tech_spec_parsed": state["tech_spec_parsed"]
                    }
                })

                tool_results = get_formatted_tool_result_messages(
                    tool_message_list=tools_response["messages"],
                    total_tokens=total_tokens,
                    model_context=CONTEXT_1M,
                    llm=self.generator_llm
                )
                # logger.info(tools_response)
                messages += tool_results
                messages = format_messages(
                    messages=messages
                )
                # logger.info(f'sending tool response back to llm for file: {file_path}')
                response: AIMessage = self.fallback_llm.invoke(messages)
        except RETRYABLE_EXCEPTIONS as e:
            logger.warning(f'encountered error, retrying: {e}')
            raise
            # logger.info(response)
        except RetryError as e:
            logger.warning(f'skipping file {file_path} due to error {e}')  # Update the state
            next_index = state["index"] + 1
            state["index"] = next_index

        content = get_response_content(response=response)
        content = get_json_content(content)
        if content == "":
            logger.info(f"No code block found for file: {file_path}, retrying")

            if state["retry_count"] > 0:
                state["retry_count"] -= 1
                state["is_retry"] = True

        # logger.info(content)

        if state["is_retry"] == False:
            state["files_dict"][file_path] = content
            self.code_files_dict_helper.files_dict[file_path] = content

            logger.info(f"Finished generating file: {file_path}")

            blob_name = f"{self.blob_name}/{self.repo_name}"
            destination_filepath = f"{blob_name}/{file_path}"
            upload_text_to_gcs_using_admin_service(
                file_path=destination_filepath,
                company_id=self.company_id,
                data=content
            )

            # Update the state
            next_index = state["index"] + 1
            state["index"] = next_index

            if next_index < len(state["files_list"]):
                file_path = state["files_list"][next_index]
                file_spec = state["file_spec_dict"][file_path]

        return {
            "current_file_spec": file_spec,
            "index": state["index"],
            "file_spec_dict": state["file_spec_dict"],
            "is_retry": state["is_retry"],
            "files_list": state["files_list"],
            "tech_spec": state["tech_spec"],
            "dep_dict": state["dep_dict"],
            "files_dict": state["files_dict"],
            "changes_dict": state["changes_dict"],
            "thinking_dict": state["thinking_dict"],
            "retry_count": state["retry_count"],
            "ht_sect_idx": state["ht_sect_idx"],
            "ht_sections": state["ht_sections"],
            "tech_spec_parsed": state["tech_spec_parsed"]
        }

    @archie_exponential_retry()
    def generate_human_tasks_section(self, state: CodeState) -> Dict[str, Any]:
        ht_sect_idx = state["ht_sect_idx"]

        logger.info(f'generating human tasks document section {ht_sect_idx}')
        messages = [
            SystemMessage(
                content=[
                    {
                        "type": "text",
                        "text": HUMAN_TASKS_GENERATOR_SYSTEM_PROMPT
                    }
                ]
            ),
            HumanMessage(content=[
                {
                    "type": "text",
                    "text": TECH_SPEC_SECTIONS_INPUT.format(
                        tech_spec_sections=json.dumps(list(state["tech_spec_parsed"].keys()))
                    )
                },
                {
                    "type": "text",
                    "text": GITHUB_REPO_INPUT.format(
                        github_files=json.dumps(state["files_list"])
                    )
                },
                {
                    "type": "text",
                    "text": "Document template: {document_template}".format(
                        document_template=HUMAN_TASKS_TEMPLATE[ht_sect_idx]
                    ),
                    "cache_control": {"type": "ephemeral"}
                }
            ])
        ]

        orig_messages = messages.copy()

        try:
            response: AIMessage = self.human_tasks_llm.invoke(messages)

            # logger.info(response)

            while len(response.tool_calls):
                messages.append(response)
                total_tokens = response.usage_metadata["total_tokens"]
                # logger.info(total_tokens)
                tools_response = code_tools_node.invoke({
                    "messages": [response]
                }, config={
                    "configurable": {
                        "tech_spec_parsed": state["tech_spec_parsed"]
                    }
                })
                tool_results = get_formatted_tool_result_messages(
                    tool_message_list=tools_response["messages"],
                    total_tokens=total_tokens,
                    llm=self.generator_llm
                )
                # logger.info(tools_response)
                messages += tool_results
                messages = format_messages(
                    messages=messages
                )
                # logger.info(f'sending tool response back to llm for file: {file_path}')
                response: AIMessage = self.human_tasks_llm.invoke(messages)
        except AnthropicBadRequestError as e:
            logger.warning(f'Anthropic bad request error, trying with fallback: {e}')
            response: AIMessage = self.fallback_llm.invoke(orig_messages)
        except RETRYABLE_EXCEPTIONS as e:
            logger.warning(f'encountered error, retrying: {e}')
            raise
        except RetryError as e:
            logger.error(f'Cound not generate human tasks {e}')
            # logger.info(response)

            while len(response.tool_calls):
                orig_messages.append(response)
                total_tokens = response.usage_metadata["total_tokens"]
                # logger.info(total_tokens)
                tools_response: Dict[str, List[ToolMessage]] = code_tools_node.invoke({
                    "messages": [response]
                }, config={
                    "configurable": {
                        "tech_spec_parsed": state["tech_spec_parsed"]
                    }
                })
                tool_results = get_formatted_tool_result_messages(
                    tool_message_list=tools_response["messages"],
                    total_tokens=total_tokens,
                    llm=self.generator_llm
                )
                # logger.info(tools_response)
                orig_messages += tool_results
                # logger.info(f'sending tool response back to llm for file: {file_path}')
                response: AIMessage = self.fallback_llm.invoke(orig_messages)

        content = get_response_content(response=response)

        sect_content = get_json_content(content)

        # logger.info(content)

        # logger.info(state["current_file_spec_str"])
        updated_ht_sections = state["ht_sections"].copy()
        updated_ht_sections.append(sect_content)

        return {
            "current_file_spec": state["current_file_spec"],
            "index": state["index"],
            "file_spec_dict": state["file_spec_dict"],
            "is_retry": state["is_retry"],
            "files_list": state["files_list"],
            "tech_spec": state["tech_spec"],
            "dep_dict": state["dep_dict"],
            "files_dict": state["files_dict"],
            "changes_dict": state["changes_dict"],
            "thinking_dict": state["thinking_dict"],
            "retry_count": state["retry_count"],
            "ht_sect_idx": ht_sect_idx + 1,
            "ht_sections": updated_ht_sections,
            "tech_spec_parsed": state["tech_spec_parsed"]
        }
