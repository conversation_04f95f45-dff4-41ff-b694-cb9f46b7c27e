# Use Python 3.12 as base image
FROM python:3.12-slim

# Install Node.js, npm and other dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
    && apt-get install -y nodejs \
    && npm install -g typescript \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Install keyring for artifact registry auth
RUN pip install keyrings.google-artifactregistry-auth

# Copy requirements.txt and install Python dependencies
COPY requirements.txt .

RUN --mount=type=secret,id=google_credentials \
    export GOOGLE_APPLICATION_CREDENTIALS=/run/secrets/google_credentials && \
    pip install -r requirements.txt

# Copy your application code
COPY . .

# Command to run your application
CMD ["python", "main.py"]
