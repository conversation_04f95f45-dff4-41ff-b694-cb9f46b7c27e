import os
import json
from blitzy_utils.logger import logger
from typing import Dict
from google.cloud import storage, pubsub_v1
from langgraph.graph import StateGraph
from langchain_anthropic import ChatAnthropic
from langchain_openai import ChatOpenAI

from lib.blitzy.code import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CodeState, code_tools

from blitzy_utils.consts import CODE_STRUCTURE_NAME, TECH_SPECIFICATION_NAME, REPO_STRUCTURE_NAME, \
    DEPENDENCY_MANIFEST_NAME, PROJECT_GUIDE_NAME
from blitzy_utils.common import publish_notification, download_text_file_from_gcs_using_admin_service, \
    upload_text_to_gcs_using_admin_service
from blitzy_utils.enums import ProjectPhase, JobStatus


EVENT_DATA = "{\"repo_name\": \"hello-world2-0r6b4x\"}"
PROJECT_ID = 'blitzy-os-dev'
GCS_BUCKET_NAME = 'blitzy-os-internal'
BLOB_NAME = 'public-samples'
UPLOAD_CODE_TOPIC = 'upload-code'
PLATFORM_EVENTS_TOPIC = 'platform-events'
ANTHROPIC_API_KEY = "************************************************************************************************************"
OPENAI_API_KEY = "********************************************************************************************************************************************************************"
# LANGCHAIN_TRACING_V2 = os.environ["LANGCHAIN_TRACING_V2"]
# LANGCHAIN_ENDPOINT = os.environ["LANGCHAIN_ENDPOINT"]
# LANGCHAIN_API_KEY = os.environ["LANGCHAIN_API_KEY"]
# LANGCHAIN_PROJECT = os.environ["LANGCHAIN_PROJECT"]

os.environ["ANTHROPIC_API_KEY"] = ANTHROPIC_API_KEY
os.environ["OPENAI_API_KEY"] = OPENAI_API_KEY
from blitzy_platform_shared.common.llms import llm_claude_3_7_sonnet_low_thinking_max_output, llm_claude_3_7_sonnet_low_output, \
    llm_gemini_flash

CODE_STRUCTURE_NAME = "Code Structure"
TECH_SPECIFICATION_NAME = "Technical Specifications"
DEPENDENCY_MANIFEST_NAME = f"{CODE_STRUCTURE_NAME} - manifest"

storage_client = storage.Client()
publisher = pubsub_v1.PublisherClient()


def generate_code(event_data_str: str):
    event_data = json.loads(event_data_str)
    repo_name = event_data.get('repo_name')
    project_id = event_data.get('project_id', '')
    job_id = event_data.get('job_id', '')
    propagate = event_data.get('propagate', True)
    user_id = event_data.get('user_id', '')
    company_id = event_data.get('company_id', '')
    git_project_repo_id = event_data.get('git_project_repo_id', '')

    # notification_data = {
    #     "projectId": project_id,
    #     "jobId": job_id,
    #     "phase": ProjectPhase.CODE_GENERATION.value,
    #     "status": JobStatus.IN_PROGRESS.value,
    #     "user_id": user_id,
    #     "metadata": {
    #         "propagate": propagate,
    #         "repo_name": repo_name,
    #         "user_id": user_id
    #     }
    # }
    # publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)

    blob_name = f"{BLOB_NAME}/{repo_name}/blitzy/documentation"
    cs_filename = f"{CODE_STRUCTURE_NAME}.json"
    code_structure_filepath = f"{blob_name}/{cs_filename}"
    code_structure = download_text_file_from_gcs_using_admin_service(
        file_path=code_structure_filepath,
        company_id=company_id,
    )
    files: Dict = json.loads(code_structure)

    repo_structure_filename = f'{REPO_STRUCTURE_NAME}.md'
    repo_structure_filepath = f"{blob_name}/{repo_structure_filename}"
    repo_files_str = download_text_file_from_gcs_using_admin_service(
        file_path=repo_structure_filepath,
        company_id=company_id,
    )
    repo_files_list = json.loads(repo_files_str)
    files_list = repo_files_list

    tech_spec_filename = f'{TECH_SPECIFICATION_NAME}.md'
    blob_name_path = f"{blob_name}/{tech_spec_filename}"
    tech_spec = download_text_file_from_gcs_using_admin_service(
        file_path=blob_name_path,
        company_id=company_id,
    )

    dep_dict_filename = f'{DEPENDENCY_MANIFEST_NAME}.json'
    dep_dict_filepath = f"{blob_name}/{dep_dict_filename}"
    dep_dict = download_text_file_from_gcs_using_admin_service(
        file_path=dep_dict_filepath,
        company_id=company_id,
    )

    code_helper = CodeGeneratorHelper(
        generator_llm=llm_claude_3_7_sonnet_low_thinking_max_output,
        human_tasks_llm=llm_claude_3_7_sonnet_low_output.bind_tools(code_tools),
        fallback_llm=llm_gemini_flash,
        repo_name=repo_name,
        blob_name=BLOB_NAME,
        bucket_name=GCS_BUCKET_NAME,
        storage_client=storage_client,
        files_dict=files,
        tech_spec=tech_spec,
        company_id=company_id,
        git_project_repo_id=git_project_repo_id
    )

    code_generator: StateGraph = code_helper.create_graph()
    app = code_generator.compile()

    file_path = files_list[0]
    file_spec = files[file_path]

    initial_state = CodeState(
        file_spec_dict=files,
        files_list=files_list,
        current_file_spec=file_spec,
        index=0,
        is_retry=False,
        retry_count=3,
        tech_spec=tech_spec,
        dep_dict=dep_dict,
        files_dict={},
        changes_dict={},
        thinking_dict={},
        ht_sect_idx=0,
        ht_sections=[]
    )
    result = app.invoke(initial_state, {"recursion_limit": 10000})

    data = "\n\n".join(result["ht_sections"])

    # upload human tasks
    filename = f"{PROJECT_GUIDE_NAME}.md"
    destination_filepath = f"{blob_name}/{filename}"
    upload_text_to_gcs_using_admin_service(
        file_path=destination_filepath,
        company_id=company_id,
        data=data
    )

    # notification_data = {
    #     'repo_name': repo_name,
    #     'project_id': project_id,
    #     'job_id': job_id,
    #     'propagate': propagate,
    #     'user_id': user_id
    # }
    # publish_notification(publisher, notification_data, PROJECT_ID, UPLOAD_CODE_TOPIC)

    return event_data


if __name__ == "__main__":
    logger.info(f"Generating code for notification data: {EVENT_DATA}")
    generate_code(event_data_str=EVENT_DATA)
