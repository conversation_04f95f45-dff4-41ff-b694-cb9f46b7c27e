from google.cloud import pubsub_v1

from blitzy_utils.common import publish_notification

PROJECT_ID = 'blitzy-os-dev'
REVERSE_THINKING_TOPIC = 'generate-reverse-thinking'

publisher = pubsub_v1.PublisherClient()


notification_data = {"repo_name": "findecision-lo", "code_gen_id": "6be99f96-c1f0-4135-92ee-f7bc4db84bf8", "repo_id": "7a9e5c92-c5a1-460e-bc48-3bf9ffa3e202", "branch_id": "600b8551-cf9b-4d52-9b11-9b4d7265234d", "branch_name": "master", "company_id": "158af1d8-b01a-4941-8348-0604f166f143", "user_id": "439042e0-5440-4af1-a611-6c734a731d8f", "team_id": "b1df7d3f-6b45-4d48-b608-0b6575e844ca",
                     "job_id": "", "project_id": "", "head_commit_hash": "678521a3ecb85ae4e28ec503123751fc5870720f", "dest_repo_name": "findecision-lo-java-new", "dest_repo_id": "49ebe2d8-5d24-4a0e-a3e0-890a06854a5a", "dest_branch_id": "d8a57b89-f03b-4933-89d6-2ae8b2a4e0df", "dest_branch_name": "blitzy-6be99f96-c1f0-4135-92ee-f7bc4db84bf8", "is_new_dest_repo": True, "change_mode": "REFACTOR_CODE",
                     "resume": True, "propagate": True}


publish_notification(publisher, notification_data, PROJECT_ID, REVERSE_THINKING_TOPIC)
