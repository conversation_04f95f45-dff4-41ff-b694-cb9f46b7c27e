from typing import TypedDict, Dict, Any, List, Union

from .models import CreatedFileSchema, UpdatedFileSchema, DeletedFileSchema


class ReverseThinkerState(TypedDict):
    file_index: int
    file_paths: List[str]
    file_schemas: Dict[str, Union[CreatedFileSchema, UpdatedFileSchema, DeletedFileSchema]]
    resume: bool
    tech_spec_first_n: str
    tech_spec_parsed: Dict[str, str]


def get_state(state=ReverseThinkerState) -> Dict[str, Any]:
    return {
        "file_index": state["file_index"],
        "file_paths": state["file_paths"],
        "file_schemas": state["file_schemas"],
        "resume": state["resume"],
        "tech_spec_first_n": state["tech_spec_first_n"],
        "tech_spec_parsed": state["tech_spec_parsed"],
    }
