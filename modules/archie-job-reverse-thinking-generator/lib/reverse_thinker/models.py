from typing import List

from pydantic import BaseModel, Field
from blitzy_platform_shared.code_generation.models import CreatedFile, UpdatedFile, ExcludedItem, FileOrFolderStatus


class ThinkingImport(BaseModel):
    name: str = Field(
        ...,
        description=(
            "The symbol name as written in the import statement. For named imports, this is "
            "the specific imported symbol (e.g., 'DataFrame' in 'from pandas import DataFrame', "
            "'UserService' in 'from services.user import UserService'). "
            "For module imports, this is the module name (e.g., 'requests' in 'import requests', "
            "'utils' in 'import utils'). "
            "Must match the exported symbol name from the source module/package."
        )
    )

    purpose: str = Field(
        ...,
        description=(
            "Documents the reason for incorporating this dependency, including the specific "
            "capabilities it provides. Examples: 'HTTP client for making API requests to external services', "
            "'Data manipulation and analysis with DataFrame operations', 'User authentication and session management'"
        )
    )

    category: str = Field(
        ...,
        description=(
            "The semantic category of the imported entity (e.g., 'class', 'function', 'module', 'constant', 'type', 'interface', 'enum'). "
            "This indicates what kind of code construct is being imported, which affects how it can be used. "
            "Examples: 'class' (DataFrame, UserService), 'function' (sqrt, validateEmail), 'module' (os, utils), "
            "'constant' (PI, MAX_RETRIES), 'type' (Optional, UserDTO), 'interface' (IUserRepository), 'enum' (UserRole)."
        )
    )

    members_accessed: List[str] = Field(
        ...,
        description=(
            "Specific items accessed or referenced from this import after it's imported. "
            "Examples: ['DataFrame.merge()', 'DataFrame.groupby()', 'UserService.findById()', "
            "'validateEmail', 'USER_CONSTANTS.MAX_LOGIN_ATTEMPTS']. "
            "Leave empty if the import itself is used directly without accessing members."
        )
    )


class ThinkingInternalImportUniqueKey(BaseModel):
    source_file_path: str = Field(
        ...,
        description=(
            "Absolute path to the file that contains the imported items. "
            "Examples: 'src/utils/helpers.py', 'lib/services/UserService.ts'"
        )
    )


class ThinkingInternalImport(ThinkingImport, ThinkingInternalImportUniqueKey):
    is_compiled: bool = Field(
        ...,
        description=(
            "True if removing this import would cause compilation/runtime errors. "
            "False if it's only used as a reference without actually being imported "
            "Examples: True for an interface import in a class, False for 'README' file references."
        )
    )


class ThinkingExternalImportUniqueKey(BaseModel):
    package_name: str = Field(
        ...,
        description=(
            "Name of the package as it appears in the package manager. Examples: 'pandas', 'requests', "
            "'@angular/core', 'express', 'junit', 'lodash'. For standard library imports, use the module name "
            "(e.g., 'os', 'json', 'datetime')."
        )
    )


class ThinkingExternalImport(ThinkingImport, ThinkingExternalImportUniqueKey):
    package_version: str = Field(
        ...,
        description=(
            "Version of the package in semantic versioning format. Examples: '2.1.3', '^4.17.0', '~1.0.0', "
            "'>=3.8.0', 'latest'. For standard library modules, use the language version (e.g., 'python3.9', 'node18')."
        )
    )

    package_registry: str = Field(
        description="Package registry where this package is hosted. Examples: 'npm', 'pypi', 'maven', 'nuget', 'crates.io', "
                    "'rubygems'. For standard library modules, use 'standard_library'."
    )

    is_dev_dependency: bool = Field(
        description=(
            "Whether this is a dependency only required during development (testing, building, linting). "
            "Examples: True for 'jest', 'pytest', 'eslint', 'webpack'. False for 'express', 'pandas', 'react'."
        )
    )


class ThinkingExportUniqueKey(BaseModel):
    name: str = Field(
        ...,
        description=(
            "The exported symbol name as it will be imported by other modules. "
            "For named exports, this is the exact identifier (e.g., 'UserService', 'calculateTax'). "
            "For default exports, this is a descriptive name of what's being exported (e.g., 'App', 'config'). "
            "Must be unique within the module's exports and follow language naming conventions."
        )
    )


class ThinkingExport(BaseModel, ThinkingExportUniqueKey):
    kind: str = Field(
        ...,
        description=(
            "The type of construct being exported (e.g., 'class', 'function', 'constant', 'interface', 'type', 'enum', 'object'). "
            "This determines how importers can use it: 'class' can be instantiated, 'function' can be called, "
            "'interface' can be implemented, 'type' can be used in type annotations, 'object' provides properties/methods. "
            "Examples: 'class' (UserService), 'function' (validateEmail), 'constant' (API_URL), 'interface' (IUser)."
        )
    )
    members_exposed: List[str] = Field(
        ...,
        description=(
            "Public methods, properties, or nested items accessible on this export after importing. "
            "For classes: public methods and static members (e.g., ['findById()', 'findAll()', 'MAX_RESULTS']). "
            "For objects/modules: available properties (e.g., ['config.apiUrl', 'config.timeout', 'utils.formatDate()']). "
            "For functions/constants: leave empty. Only list what's publicly accessible to importers."
        )
    )
    is_default: bool = Field(
        ...,
        description=(
            "True if this is the default export (e.g., 'export default class UserService'). "
            "False for named exports (e.g., 'export class UserService'). "
            "A module can have at most one default export."
        )
    )


class CreatedFileThinking(BaseModel):
    internal_imports: List[ThinkingInternalImport] = Field(
        ...,
        description=(
            "Imports from modules within this codebase/repository. Internal imports use relative paths "
            "(e.g., './utils', '../models/User') or project-specific module paths (e.g., '@app/services', 'src/utils'). "
            "These imports establish architectural relationships between components you control directly. "
            "Each source_file_path must only appear once in the list."
        )
    )
    external_imports: List[ThinkingExternalImport] = Field(
        ...,
        description=(
            "Imports from third-party packages, standard library modules, or external dependencies. "
            "These are resolved through package managers (npm, pip, maven) or come built-in with the language/runtime. "
            "External imports require version management and appear in package manifests (package.json, requirements.txt). "
            "Each package_name must only appear once in the list."
        )
    )
    exports: List[ThinkingExport] = Field(
        ...,
        description=(
            "All symbols this module makes available to other modules. Includes both default and named exports. "
            "A module can have zero or one default export (where is_default=True) and any number of named exports. "
            "The combination of exports defines this module's public API."
        )
    )


class UpdatedFileThinking(BaseModel):
    new_internal_imports: List[ThinkingInternalImport] = Field(
        ...,
        description=(
            "NEW internal imports being added to this file during the update. Only includes imports "
            "that don't already exist in the file."
        )
    )
    new_external_imports: List[ThinkingExternalImport] = Field(
        ...,
        description=(
            "NEW external dependencies being introduced to this file. Only includes packages/modules "
            "not previously imported"
        )
    )
    new_exports: List[ThinkingExport] = Field(
        ...,
        description=(
            "NEW exports being added to this module's public API. Only includes exports that don't "
            "already exist. These additions expand what other modules can import from this file."
        )
    )


class CreatedFileSchema(CreatedFile, CreatedFileThinking):
    status: FileOrFolderStatus


class UpdatedFileSchema(UpdatedFile, UpdatedFileThinking):
    status: FileOrFolderStatus


class DeletedFileSchema(ExcludedItem):
    status: FileOrFolderStatus
