import gc
import json
from typing import Dict, Any, Literal, List
from langgraph.graph import START, StateGraph, END
from langchain_core.language_models.chat_models import BaseChatModel
from langgraph.prebuilt import ToolNode
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage, ToolMessage, BaseMessage
from anthropic import BadRequestError as AnthropicBadRequestError
from github.PullRequest import PullRequest

from blitzy_utils.common import download_from_gcs, upload_to_gcs, DictFileHelper
from blitzy_utils.consts import DEFAULT_MAX_RETRIES
from blitzy_utils.logger import logger
from blitzy_utils.github import get_github_repo, create_github_commit, create_all_pull_requests, setup_github_branch, \
    download_all_git_files_to_disk, get_head_commit_hash
from blitzy_utils.disk import write_file_to_disk

from blitzy_platform_shared.common.utils import get_formatted_tool_result_messages, archie_exponential_retry, clean_path, format_messages
from blitzy_platform_shared.common.tools import ANTHROPIC_WEB_SEARCH_TOOL_DEFINITION
from blitzy_platform_shared.code_graph.utils import is_source_adjacent_file, is_source_file
from blitzy_platform_shared.code_graph.builder import CodeGraphBuilder
from blitzy_platform_shared.document.tools import get_tech_spec_section
from blitzy_platform_shared.document.prompts import UPDATED_TECH_SPEC_INPUT, AGGREGATED_FOLDER_MAPPING_INPUT, TECH_SPEC_SECTIONS_INPUT
from blitzy_platform_shared.code_generation.tools import ANTHROPIC_TEXT_EDITOR_TOOL_DEFINITION, ANTHROPIC_TEXT_EDITOR_TOOL_NAME
from blitzy_platform_shared.code_generation.models import AnthropicTextEditorCommand

from .state import ReverseThinkerState, get_state


code_files_dict_helper = DictFileHelper(files={})


tool_node_tools = [get_tech_spec_section]
code_tools = tool_node_tools + [ANTHROPIC_TEXT_EDITOR_TOOL_DEFINITION, ANTHROPIC_WEB_SEARCH_TOOL_DEFINITION]
code_tools_node = ToolNode(tool_node_tools)


class ReverseThinkerHelper:
    def __init__(
            self,
            generator_llm: BaseChatModel,
            fallback_llms: List[BaseChatModel],
            job_metadata: Dict[str, Any],
            blob_name: str,
            bucket_name: str,
            storage_client,
            compressed_files_map: Dict[str, List[Dict[str, Any]]],
            file_mapping: Dict[str, List[Dict[str, Any]]],
            head_commit_hash: str,
            github_server: str,
            state_metadata_filename: str,
            graph_builder: CodeGraphBuilder):
        self.graph_builder = graph_builder
        self.generator_llm = generator_llm
        self.fallback_llms = fallback_llms
        self.blob_name = blob_name
        self.bucket_name = bucket_name
        self.storage_client = storage_client
        self.code_files_dict_helper = code_files_dict_helper
        self.job_metadata = job_metadata
        self.head_commit_hash = head_commit_hash
        self.github_server = github_server
        self.state_metadata_filename = state_metadata_filename

        self.company_id = self.job_metadata["company_id"]
        self.repo_id = self.job_metadata["repo_id"]
        self.branch_id = self.job_metadata["branch_id"]
        self.user_id = self.job_metadata["user_id"]
        self.repo_name = self.job_metadata["repo_name"]
        self.create_dest_repo = self.job_metadata["is_new_dest_repo"]
        self.dest_repo_name = self.repo_name
        if self.create_dest_repo:
            self.dest_repo_name = self.job_metadata["dest_repo_name"]
        self.dest_branch_name = self.job_metadata["dest_branch_name"]
        self.base_branch_name = self.job_metadata["branch_name"]

        self.compressed_files_map = compressed_files_map.copy()
        self.files_map = file_mapping.copy()
        self.github_repo, _ = get_github_repo(
            repo_name=self.dest_repo_name,
            user_id=self.user_id,
            server=self.github_server,
            create=self.create_dest_repo
        )

    def create_graph(self) -> StateGraph:
        # Define the graph
        rt_generator = StateGraph(ReverseThinkerState)

        # Add nodes
        rt_generator.add_node("setup", self.setup)
        rt_generator.add_node("setup_file", self.setup_file)
        rt_generator.add_node("create_file", self.create_file)
        rt_generator.add_node("upload_code", self.upload_code)
        rt_generator.add_node("update_file", self.update_file)
        rt_generator.add_node("delete_file", self.delete_file)
        rt_generator.add_node("teardown", self.teardown)

        rt_generator.add_conditional_edges(
            "setup_file",
            self.setup_router,
            {
                "create": "create_file",
                "update": "update_file",
                "delete": "delete_file",
                "end": "teardown"
            }
        )

        rt_generator.add_conditional_edges(
            "upload_code",
            self.file_router,
            {
                "continue": "setup_file",
                "end": "teardown"
            }
        )

        rt_generator.add_conditional_edges(
            "teardown",
            self.file_router,
            {
                "continue": "setup_file",
                "end": END
            }
        )

        # Set the entry point
        rt_generator.add_edge(START, "setup")
        rt_generator.add_edge("setup", "setup_file")
        rt_generator.add_edge("create_file", "upload_code")
        rt_generator.add_edge("update_file", "upload_code")
        rt_generator.add_edge("delete_file", "upload_code")
        return rt_generator

    def setup_router(self, state: ReverseThinkerState) -> Literal["create", "update", "delete", "end"]:
        if not state["current_file_info"]:
            return "end"
        if state["current_file_info"]["status"] == "CREATED":
            return "create"
        elif state["current_file_info"]["status"] == "DELETED":
            return "delete"
        else:
            return "update"

    def file_router(self, state: ReverseThinkerState) -> Literal["continue", "end"]:
        if state["folder_index"] >= state["folder_count"] and len(state["current_folder_files_list"]) == 0:
            return "end"
        return "continue"

    @archie_exponential_retry()
    def setup(self, state: ReverseThinkerState) -> Dict[str, Any]:
        state["file_mapping"] = self.files_map
        state["compressed_files_map"] = self.compressed_files_map
        state["folder_paths"] = list(state["file_mapping"].keys())
        state["folder_count"] = len(state["folder_paths"])

        state["all_files_list"] = []
        state["all_file_paths"] = []
        for file_list in state["file_mapping"].values():
            state["all_files_list"].extend(file_list)
            state["all_file_paths"]

        state["file_schemas"] = {}
        for file in state["all_files_list"]:
            state["file_schemas"]["dest_path"] = file
        state["pr_data"] = {}

        if state["resume"]:
            # Restore state
            logger.info('Attempting to resume state')
            try:
                state_metadata: Dict[str, str] = json.loads(download_from_gcs(
                    storage_client=self.storage_client,
                    bucket_name=self.bucket_name,
                    blob_name=self.blob_name,
                    filename=self.state_metadata_filename
                ))
            except Exception as e:
                logger.warning(f'Failed to resume, running a fresh operation')
                state["resume"] = False
                return self.setup(state=state)
            state["folder_index"] = state_metadata["folder_index"]
            state["current_folder_path"] = state_metadata["current_folder_path"]
            state["current_folder_files_list"] = state_metadata["current_folder_files_list"]
            state["retry_files_map"] = state_metadata["retry_files_map"]
            state["files_to_upload"] = state_metadata.get("files_to_upload", {})
            state["current_file_info"] = state_metadata["current_file_info"]
            state["unformatted_response"] = state_metadata["unformatted_response"]
            state["retry_count"] = state_metadata["retry_count"]
            state["json_retry_count"] = state_metadata["json_retry_count"]
            state["target_file_path"] = state_metadata.get("target_file_path", "")

            state["seen_pending_files"] = state_metadata.get("seen_pending_files", [])
            self.code_files_dict_helper.seen_pending_files = set(state["seen_pending_files"])
        else:
            state["folder_index"] = 0
            state["current_folder_path"] = state["folder_paths"][state["folder_index"]]
            state["current_folder_files_list"] = state["file_mapping"][state["current_folder_path"]].copy()
            state["retry_files_map"] = {}
            state["files_to_upload"] = {}
            state["current_file_info"] = {}
            state["unformatted_response"] = ""
            state["retry_count"] = 0
            state["json_retry_count"] = 0
            state["target_file_path"] = ""
            state["seen_pending_files"] = []

        delete_existing_branch = not state["resume"]
        setup_github_branch(
            repo=self.github_repo,
            branch_name=self.dest_branch_name,
            base_branch=self.base_branch_name,
            create_new_branch=True,
            delete_existing_branch=delete_existing_branch
        )

        logger.info('Downloading all source branch files to local container')
        download_all_git_files_to_disk(
            repo_name=self.repo_name,
            branch_name=self.base_branch_name,
            user_id=self.user_id,
            server=self.github_server,
            commit_hash=self.head_commit_hash
        )
        logger.info('Source branch files downloaded successfully.')

        logger.info('Downloading all destination branch files to local container')
        dest_head_commit_hash = get_head_commit_hash(
            repo_name=self.dest_repo_name,
            user_id=self.user_id,
            server=self.github_server,
            branch_name=self.dest_branch_name
        )
        download_all_git_files_to_disk(
            repo_name=self.dest_repo_name,
            branch_name=self.dest_branch_name,
            user_id=self.user_id,
            server=self.github_server,
            commit_hash=dest_head_commit_hash
        )
        logger.info('Destination branch files downloaded successfully.')

        return get_state(state=state)

    def setup_file(self, state: ReverseThinkerState) -> Dict[str, Any]:
        # Run manual garbage collection
        gc.collect()

        is_files_list_valid = len(state["current_folder_files_list"]) > 0
        while not is_files_list_valid:
            state["folder_index"] += 1
            if state["folder_index"] < state["folder_count"]:
                state["current_folder_path"] = state["folder_paths"][state["folder_index"]]
                state["current_folder_files_list"] = state["file_mapping"][state["current_folder_path"]].copy()
                is_files_list_valid = len(state["current_folder_files_list"]) > 0
            else:
                is_files_list_valid = False
                break

        if is_files_list_valid:
            state["current_file_info"] = state["current_folder_files_list"].pop(0)
        else:
            state["current_file_info"] = None
        # logger.info(f"paths left: {len(state["current_folder_files_list"])}")

        return get_state(state=state)

    def teardown(self, state: ReverseThinkerState) -> Dict[str, Any]:
        retry_folder_paths = list(state["retry_files_map"].keys())
        if len(retry_folder_paths) > 0:
            state["file_mapping"] = state["retry_files_map"].copy()
            state["retry_files_map"].clear()
            state["folder_paths"] = list(state["file_mapping"].keys())
            state["folder_count"] = len(state["folder_paths"])
            state["folder_index"] = 0
            state["current_folder_path"] = state["folder_paths"][state["folder_index"]]
            state["current_folder_files_list"] = state["file_mapping"][state["current_folder_path"]].copy()
            state["retry_count"] = 0
            state["files_to_upload"] = {}
            state["target_file_path"] = ""
        else:
            logger.info(f'Evaluating job end')
            if self.base_branch_name != self.dest_branch_name:
                prs: List[PullRequest] = create_all_pull_requests(
                    repo=self.github_repo,
                    head_branch=self.dest_branch_name,
                    user_id=self.user_id,
                    server=self.github_server,
                    base_branch=self.base_branch_name,
                    is_new_repo=self.create_dest_repo
                )
                state["pr_data"] = prs[0].raw_data
        return get_state(state=state)
