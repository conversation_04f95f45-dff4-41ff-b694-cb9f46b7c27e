import json
from typing import Dict, <PERSON>, <PERSON>, Annotated

from pydantic import BaseModel, Field
from langgraph.prebuilt import InjectedState
from langchain_core.tools import tool

from blitzy_utils.logger import logger

from blitzy_platform_shared.common.utils import clean_path

from .models import CreatedFileSchema, UpdatedFileSchema, DeletedFileSchema, FileOrFolderStatus, ThinkingInternalImport, \
    ThinkingInternalImportUniqueKey, ThinkingExternalImport, ThinkingExternalImportUniqueKey, ThinkingExport, ThinkingExportUniqueKey

FILE_PATH_NOT_FOUND = "Error: File path not found or not marked for an update, change or deletion. You may double check your path and try again with a different path that you haven't tried so far."
FILE_PATH_INVALID = """
    Error: File path invalid. file_path must be your assigned file path and source_file_path must be the path that you are trying to import from.
    Alternatively, this file could be marked UNCHANGED and unavailable for modification.
    """
CIRCULAR_DEPENDENCY_ERROR = """
    Error: Circular depedency detected from {first_file} to {second_file}. Please execute one of the following options.
    1. Avoid adding this internal import dependency on {first_file}, OR
    2. Remove this internal import dependency from {second_file} using remove_internal_import.

    Review the schemas of {first_file} and {second_file} and make your decision.
    """
ITEM_NOT_FOUND = "Error: No {item} found for {key}: {value} in {file_path}. Check your {key} and try again if you haven't already."
ITEM_REMOVED_RESPONSE = "{item} of {value} removed successfully for {file_path}"
ITEM_ADDED_RESPONSE = "{item} added successfully for {file_path}"
FILE_ERROR_RESPONSE = """
    File: {file_path}.
    {message}
    """

FileSchemaType = Dict[str, Union[CreatedFileSchema, UpdatedFileSchema, DeletedFileSchema]]


class GetItemInput(BaseModel):
    file_path: str = Field(
        description="Absolute path of the target file"
    )
    file_schemas: Annotated[FileSchemaType, InjectedState("file_schemas")]


@tool(args_schema=GetItemInput)
def get_file_schema(
    file_path: str,
    file_schemas: FileSchemaType
) -> str:
    """
    Fetches the complete schema of a pending file including key_changes, internal_imports, external_imports, exports, and so on.
    Useful when you want to retrieve details about file changes that are not available from other more specific tools for each property.
    """
    file_path = clean_path(file_path)
    logger.info(f'Retrieving file schema for file: {file_path}')
    file_schema = file_schemas.get(file_path)
    if not file_schema:
        return FILE_ERROR_RESPONSE.format(file_path=file_path, message=FILE_PATH_NOT_FOUND)
    return json.dumps(file_schema)


@tool(args_schema=GetItemInput)
def get_internal_imports(
    file_path: str,
    file_schemas: FileSchemaType
) -> str:
    """
    Fetches the new internal imports for an UPDATED file and the full set of internal imports for a CREATED file.
    Useful for identifying repository level local dependencies for a given file.
    """
    file_path = clean_path(file_path)
    logger.info(f'Retrieving internal imports for file: {file_path}')
    return json.dumps(
        get_property(
            file_path=file_path,
            file_schemas=file_schemas,
            updated_file_property='new_internal_imports',
            default_property='internal_imports'
        )
    )


def get_property(
    file_path: str,
    file_schemas: FileSchemaType,
    updated_file_property: str,
    default_property: str
):
    file_schema = file_schemas.get(file_path)
    if not file_schema:
        return FILE_ERROR_RESPONSE.format(file_path=file_path, message=FILE_PATH_NOT_FOUND)
    file_status = file_schema.status

    if file_status == FileOrFolderStatus.UPDATED.value:
        return file_schema[updated_file_property]
    else:
        return file_schema[default_property]


class AddUpdateInternalImportInput(GetItemInput, ThinkingInternalImport):
    pass


@tool(args_schema=AddUpdateInternalImportInput)
def add_or_update_internal_import(
    file_path: str,
    source_file_path: str,
    is_compiled: bool,
    name: str,
    purpose: str,
    category: str,
    members_accessed: List[str],
    file_schemas: FileSchemaType
):
    file_path = clean_path(file_path)
    internal_imports: List[ThinkingInternalImport] = get_property(
        file_path=file_path,
        file_schemas=file_schemas,
        updated_file_property='new_internal_imports',
        default_property='internal_imports'
    )
    if FILE_PATH_NOT_FOUND in internal_imports:
        return FILE_ERROR_RESPONSE.format(
            file_path=file_path,
            message=FILE_PATH_INVALID
        )

    source_file_imports: List[ThinkingInternalImport] = get_property(
        file_path=source_file_path,
        file_schemas=file_schemas,
        updated_file_property='new_internal_imports',
        default_property='internal_imports'
    )
    if not FILE_PATH_NOT_FOUND in source_file_imports:
        for src_int_import in source_file_imports:
            if src_int_import.source_file_path == file_path:
                return FILE_ERROR_RESPONSE.format(
                    file_path=file_path,
                    message=CIRCULAR_DEPENDENCY_ERROR.format(
                        first_file=file_path,
                        second_file=source_file_path
                    )
                )

    # Find existing import
    existing_import = None
    for imported_item in internal_imports:
        if imported_item.source_file_path == source_file_path:
            existing_import = imported_item
            break

    if existing_import == None:
        # Create new import instance and append
        new_import = ThinkingInternalImport(
            source_file_path=source_file_path,
            name=name,
            purpose=purpose,
            category=category,
            members_accessed=members_accessed,
            is_compiled=is_compiled
        )
        internal_imports.append(new_import)
    else:
        # Update existing import through attributes
        existing_import.source_file_path = source_file_path
        existing_import.name = name
        existing_import.purpose = purpose
        existing_import.category = category
        existing_import.members_accessed = members_accessed
        existing_import.is_compiled = is_compiled
    return ITEM_ADDED_RESPONSE.format(item="Internal import", file_path=file_path)


class RemoveInternalImportInput(GetItemInput, ThinkingInternalImportUniqueKey):
    pass


@tool(args_schema=RemoveInternalImportInput)
def remove_internal_import(
    file_path: str,
    source_file_path: str,
    file_schemas: FileSchemaType
):
    file_path = clean_path(file_path)
    internal_imports: List[ThinkingInternalImport] = get_property(
        file_path=file_path,
        file_schemas=file_schemas,
        updated_file_property='new_internal_imports',
        default_property='internal_imports'
    )
    if FILE_PATH_NOT_FOUND in internal_imports:
        return FILE_ERROR_RESPONSE.format(
            file_path=file_path,
            message=FILE_PATH_INVALID
        )

    # Find and remove by index
    for i, imported_item in enumerate(internal_imports):
        if imported_item.source_file_path == source_file_path:
            internal_imports.pop(i)
            return ITEM_REMOVED_RESPONSE.format(
                item="Internal import",
                value=f"{source_file_path}",
                file_path=f"{file_path}"
            )

    # Not found
    return FILE_ERROR_RESPONSE.format(
        file_path=file_path,
        message=ITEM_NOT_FOUND.format(
            item="Internal import",
            key="source_file_path",
            value=f"{source_file_path}",
            file_path=file_path
        )
    )


@tool(args_schema=GetItemInput)
def get_external_imports(
    file_path: str,
    file_schemas: Dict[str, Union[CreatedFileSchema, UpdatedFileSchema, DeletedFileSchema]]
) -> str:
    """
    Fetches the new external imports for an UPDATED file and the full set of external imports for a CREATED file.
    Useful for identifying third party, open-source, or built-in package dependencies for a given file.
    """
    file_path = clean_path(file_path)
    logger.info(f'Retrieving external imports for file: {file_path}')
    return json.dumps(
        get_property(
            file_path=file_path,
            file_schemas=file_schemas,
            updated_file_property='new_external_imports',
            default_property='external_imports'
        )
    )


class AddUpdateExternalImportInput(GetItemInput, ThinkingExternalImport):
    pass


@tool(args_schema=AddUpdateExternalImportInput)
def add_or_update_external_import(
    file_path: str,
    package_name: str,
    package_version: str,
    package_registry: str,
    is_dev_dependency: bool,
    name: str,
    purpose: str,
    category: str,
    members_accessed: List[str],
    file_schemas: Dict[str, Union[CreatedFileSchema, UpdatedFileSchema, DeletedFileSchema]]
):
    file_path = clean_path(file_path)
    external_imports: List[ThinkingExternalImport] = get_property(
        file_path=file_path,
        file_schemas=file_schemas,
        updated_file_property='new_external_imports',
        default_property='external_imports'
    )
    if FILE_PATH_NOT_FOUND in external_imports:
        return FILE_ERROR_RESPONSE.format(
            file_path=file_path,
            message=FILE_PATH_INVALID
        )

    # Find existing import
    existing_import = None
    for imported_item in external_imports:
        if imported_item.package_name == package_name:
            existing_import = imported_item
            break

    if existing_import == None:
        # Create new import instance and append
        new_import = ThinkingExternalImport(
            package_name=package_name,
            package_version=package_version,
            package_registry=package_registry,
            is_dev_dependency=is_dev_dependency,
            name=name,
            purpose=purpose,
            category=category,
            members_accessed=members_accessed
        )
        external_imports.append(new_import)
    else:
        # Update existing import through attributes
        existing_import.package_name = package_name
        existing_import.package_version = package_version
        existing_import.package_registry = package_registry
        existing_import.is_dev_dependency = is_dev_dependency
        existing_import.name = name
        existing_import.purpose = purpose
        existing_import.category = category
        existing_import.members_accessed = members_accessed
    return ITEM_ADDED_RESPONSE.format(item="External import", file_path=file_path)


class RemoveExternalImportInput(GetItemInput, ThinkingExternalImportUniqueKey):
    pass


@tool(args_schema=RemoveExternalImportInput)
def remove_external_import(
    file_path: str,
    package_name: str,
    file_schemas: Dict[str, Union[CreatedFileSchema, UpdatedFileSchema, DeletedFileSchema]]
):
    file_path = clean_path(file_path)
    external_imports: List[ThinkingExternalImport] = get_property(
        file_path=file_path,
        file_schemas=file_schemas,
        updated_file_property='new_external_imports',
        default_property='external_imports'
    )
    if FILE_PATH_NOT_FOUND in external_imports:
        return FILE_ERROR_RESPONSE.format(
            file_path=file_path,
            message=FILE_PATH_INVALID
        )

    # Find and remove by index
    for i, imported_item in enumerate(external_imports):
        if imported_item.package_name == package_name:
            external_imports.pop(i)
            return ITEM_REMOVED_RESPONSE.format(
                item="External import",
                value=f"{package_name}",
                file_path=f"{file_path}"
            )

    # Not found
    return FILE_ERROR_RESPONSE.format(
        file_path=file_path,
        message=ITEM_NOT_FOUND.format(
            item="External import",
            key="package_name",
            value=f"{package_name}",
            file_path=file_path
        )
    )


@tool(args_schema=GetItemInput)
def get_exports(
    file_path: str,
    file_schemas: Dict[str, Union[CreatedFileSchema, UpdatedFileSchema, DeletedFileSchema]]
) -> str:
    """
    Fetches the new exports for an UPDATED file and the full set of exports for a CREATED file.
    Useful for identifying items contained in and exported by a given file.
    """
    file_path = clean_path(file_path)
    logger.info(f'Retrieving exports for file: {file_path}')
    return json.dumps(
        get_property(
            file_path=file_path,
            file_schemas=file_schemas,
            updated_file_property='new_exports',
            default_property='exports'
        )
    )


class AddUpdateExportInput(GetItemInput, ThinkingExport):
    pass


@tool(args_schema=AddUpdateExportInput)
def add_or_update_export(
    file_path: str,
    name: str,
    kind: str,
    members_exposed: List[str],
    is_default: bool,
    file_schemas: Dict[str, Union[CreatedFileSchema, UpdatedFileSchema, DeletedFileSchema]]
):
    file_path = clean_path(file_path)
    exports: List[ThinkingExport] = get_property(
        file_path=file_path,
        file_schemas=file_schemas,
        updated_file_property='new_exports',
        default_property='exports'
    )
    if FILE_PATH_NOT_FOUND in exports:
        return FILE_ERROR_RESPONSE.format(
            file_path=file_path,
            message=FILE_PATH_INVALID
        )

    # Find existing export
    existing_export = None
    for exported_item in exports:
        if exported_item.name == name:
            existing_export = exported_item
            break

    if existing_export == None:
        # Create new export instance and append
        new_export = ThinkingExport(
            name=name,
            kind=kind,
            members_exposed=members_exposed,
            is_default=is_default
        )
        exports.append(new_export)
    else:
        # Update existing import through attributes
        existing_export.name = name
        existing_export.kind = kind
        existing_export.members_exposed = members_exposed
        existing_export.is_default = is_default
    return ITEM_ADDED_RESPONSE.format(item="Export", file_path=file_path)


class RemoveExportInput(GetItemInput, ThinkingExportUniqueKey):
    pass


@tool(args_schema=RemoveExportInput)
def remove_export(
    file_path: str,
    name: str,
    file_schemas: Dict[str, Union[CreatedFileSchema, UpdatedFileSchema, DeletedFileSchema]]
):
    file_path = clean_path(file_path)
    exports: List[ThinkingExport] = get_property(
        file_path=file_path,
        file_schemas=file_schemas,
        updated_file_property='new_exports',
        default_property='exports'
    )
    if FILE_PATH_NOT_FOUND in exports:
        return FILE_ERROR_RESPONSE.format(
            file_path=file_path,
            message=FILE_PATH_INVALID
        )

    # Find and remove by index
    for i, exported_item in enumerate(exports):
        if exported_item.name == name:
            exports.pop(i)
            return ITEM_REMOVED_RESPONSE.format(
                item="Export",
                value=f"{name}",
                file_path=f"{file_path}"
            )

    # Not found
    return FILE_ERROR_RESPONSE.format(
        file_path=file_path,
        message=ITEM_NOT_FOUND.format(
            item="Export",
            key="name",
            value=f"{name}",
            file_path=file_path
        )
    )


@tool(args_schema=GetItemInput)
def mark_file_complete(
    file_path: str
):
    file_path = clean_path(file_path)
    logger.info(f'File {file_path} marked completed')
