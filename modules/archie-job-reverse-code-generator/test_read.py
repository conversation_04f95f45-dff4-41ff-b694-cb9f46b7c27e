import unittest
from typing import List


def read_range(view_range: List[int], file_text: str):
    """
    Given a list view_range containing two 1-indexed integers representing line numbers,
    and a string representing the file's text, returns the portion that is being read
    with line numbers prepended to each line.

    If the second integer extends the length or is -1, return up to the end of the file.
    Negative indices count from the end of the file (-1 is last line, -2 is second to last, etc.)

    Args:
        view_range (list): A list containing two integers representing line numbers.
                          Can be 1-indexed positive numbers or negative indices.
        file_text (str): A string representing the file's text.

    Returns:
        str: The portion of the file that is being read with line numbers prepended.
    """
    # Split the file text into lines
    lines = file_text.split('\n')

    # Get the total number of lines
    total_lines = len(lines)

    # Check if the file is empty
    if total_lines == 0:
        return ""

    # Extract start and end line numbers
    start_line = view_range[0]
    end_line = view_range[1]

    # Special case: if file has only empty content (single empty line)
    if total_lines == 1 and lines[0] == '':
        return ""

    # Handle negative indices
    if start_line < 0:
        # Convert negative index to positive (e.g., -1 becomes last line)
        start_line = total_lines + start_line + 1

    if end_line < 0 and end_line != -1:
        # Convert negative index to positive, but preserve -1 as "to end"
        end_line = total_lines + end_line + 1

    # Convert to 0-indexed for Python list operations
    start_idx = start_line - 1

    # If end_line is -1 or exceeds total lines, set it to the last line
    if end_line == -1 or end_line > total_lines:
        end_idx = total_lines  # This will include all lines to the end
    else:
        end_idx = end_line  # Since end is exclusive in Python slicing, this is correct

    # Ensure start_idx is not negative and not beyond file
    start_idx = max(0, min(start_idx, total_lines - 1))

    # Ensure end_idx is not less than start_idx
    end_idx = max(start_idx, min(end_idx, total_lines))

    # Extract the requested lines
    requested_lines = lines[start_idx:end_idx]

    # Prepend line numbers to each line
    numbered_lines = []
    for i, line in enumerate(requested_lines):
        # Calculate the actual line number (1-indexed)
        line_number = start_idx + i + 1
        numbered_lines.append(f"{line_number}: {line}")

    # Join the lines back together with newlines
    return '\n'.join(numbered_lines)


class TestReadRange(unittest.TestCase):
    """
    Test cases for read_range function.

    Important: The end line in view_range is INCLUSIVE.
    For example, [2, 4] returns lines 2, 3, and 4.
    """

    def setUp(self):
        # Sample file content for testing
        self.simple_file = """line 1
line 2
line 3
line 4
line 5"""

        # The example from the user's request
        self.prime_example = """def is_prime(n):
    \"\"\"Check if a number is prime.\"\"\"
    if n <= 1:
        return False
    if n <= 3:
        return True
    if n % 2 == 0 or n % 3 == 0:
        return False
    i = 5
    while i * i <= n:
        if n % i == 0 or n % (i + 2) == 0:
            return False
        i += 6
    return True

def get_primes(limit):
    \"\"\"Generate a list of prime numbers up to the given limit.\"\"\"
    primes = []
    for num in range(2, limit + 1):
        if is_prime(num):
            primes.append(num)
    return primes

def main():
    \"\"\"Main function to demonstrate prime number generation.\"\"\"
    limit = 100
    prime_list = get_primes(limit)
    print(f\"Prime numbers up to {limit}:\")
    print(prime_list)
    print(f\"Found {len(prime_list)} prime numbers.\")

if __name__ == \"__main__\":
    main()"""

    def test_basic_range(self):
        """Test basic range selection"""
        result = read_range([2, 4], self.simple_file)
        expected = "2: line 2\n3: line 3\n4: line 4"  # Inclusive end
        self.assertEqual(result, expected)

    def test_single_line(self):
        """Test selecting a single line"""
        result = read_range([3, 3], self.simple_file)  # Changed to [3, 3] for single line
        expected = "3: line 3"
        self.assertEqual(result, expected)

    def test_from_beginning(self):
        """Test range from beginning"""
        result = read_range([1, 3], self.simple_file)
        expected = "1: line 1\n2: line 2\n3: line 3"  # Inclusive end
        self.assertEqual(result, expected)

    def test_to_end(self):
        """Test range to end of file using -1"""
        result = read_range([3, -1], self.simple_file)
        expected = "3: line 3\n4: line 4\n5: line 5"
        self.assertEqual(result, expected)

    def test_beyond_end(self):
        """Test when end line exceeds file length"""
        result = read_range([4, 10], self.simple_file)
        expected = "4: line 4\n5: line 5"
        self.assertEqual(result, expected)

    def test_negative_indices_last_lines(self):
        """Test getting last N lines with negative indices"""
        result = read_range([-3, -1], self.simple_file)
        expected = "3: line 3\n4: line 4\n5: line 5"
        self.assertEqual(result, expected)

    def test_negative_indices_middle(self):
        """Test negative indices for middle section"""
        result = read_range([-4, -2], self.simple_file)
        expected = "2: line 2\n3: line 3\n4: line 4"  # -4 is line 2, -2 is line 4 (inclusive)
        self.assertEqual(result, expected)

    def test_empty_file(self):
        """Test with empty file"""
        result = read_range([1, 5], "")
        self.assertEqual(result, "")

    def test_single_line_file(self):
        """Test with single line file"""
        result = read_range([1, -1], "only line")
        expected = "1: only line"
        self.assertEqual(result, expected)

    def test_out_of_bounds_start(self):
        """Test when start is beyond file"""
        result = read_range([10, 15], self.simple_file)
        expected = "5: line 5"
        self.assertEqual(result, expected)

    def test_prime_example_full(self):
        """Test the full prime example"""
        result = read_range([1, -1], self.prime_example)
        lines = result.split('\n')

        # Check first few lines
        self.assertEqual(lines[0], "1: def is_prime(n):")
        self.assertEqual(lines[1], "2:     \"\"\"Check if a number is prime.\"\"\"")
        self.assertEqual(lines[2], "3:     if n <= 1:")

        # Check last line
        self.assertEqual(lines[-1], "33:     main()")

        # Check total number of lines
        self.assertEqual(len(lines), 33)

    def test_prime_example_reverse_lookup(self):
        """Test reverse lookup on prime example"""
        result = read_range([-5, -1], self.prime_example)
        lines = result.split('\n')

        self.assertEqual(lines[0], "29:     print(prime_list)")
        self.assertEqual(lines[1], "30:     print(f\"Found {len(prime_list)} prime numbers.\")")
        self.assertEqual(lines[2], "31: ")
        self.assertEqual(lines[3], "32: if __name__ == \"__main__\":")
        self.assertEqual(lines[4], "33:     main()")

    def test_prime_example_middle_section(self):
        """Test middle section of prime example"""
        result = read_range([16, 22], self.prime_example)
        expected = """16: def get_primes(limit):
17:     \"\"\"Generate a list of prime numbers up to the given limit.\"\"\"
18:     primes = []
19:     for num in range(2, limit + 1):
20:         if is_prime(num):
21:             primes.append(num)
22:     return primes"""  # Now includes line 22
        self.assertEqual(result, expected)

    def test_negative_50_lines(self):
        """Test getting last 50 lines (more than file has)"""
        result = read_range([-50, -1], self.simple_file)
        expected = "1: line 1\n2: line 2\n3: line 3\n4: line 4\n5: line 5"
        self.assertEqual(result, expected)

    def test_start_greater_than_end(self):
        """Test when start > end (should return empty or handle gracefully)"""
        result = read_range([5, 3], self.simple_file)
        # Should return empty since start > end
        self.assertEqual(result, "")

    def test_inclusive_end_behavior(self):
        """Test that end line is inclusive"""
        # Request lines 2 through 2 (just line 2)
        result = read_range([2, 2], self.simple_file)
        self.assertEqual(result, "2: line 2")

        # Request lines 1 through 5 (all lines)
        result = read_range([1, 5], self.simple_file)
        expected = "1: line 1\n2: line 2\n3: line 3\n4: line 4\n5: line 5"
        self.assertEqual(result, expected)

    def test_file_with_empty_lines(self):
        """Test file with empty lines"""
        file_with_empty = """line 1

line 3

line 5"""
        result = read_range([1, -1], file_with_empty)
        expected = "1: line 1\n2: \n3: line 3\n4: \n5: line 5"
        self.assertEqual(result, expected)


if __name__ == "__main__":
    # Run the tests
    unittest.main(verbosity=2)
