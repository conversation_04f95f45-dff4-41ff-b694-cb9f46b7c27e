import json
import os

from blitzy_utils.enums import BackpropCommand

EVENT_DATA = json.dumps({"code_gen_id": "2314bd2d-e1d1-4241-bab5-40aa8287a4a8", "tech_spec_id": "ddd575a9-77d2-4e45-9ba3-f1ac83331b4f", "repo_name": "hao-backprop-test", "repo_id": "*********", "branch_id": "0b7eee13-b78f-46b0-853e-4df3b1ff5684", "branch_name": "main3", "company_id": "company-1", "user_id": "b388177a-cc79-46eb-bf6f-6b7e1c63fde6", "team_id": "default", "job_id": "5cb035df-dce9-4ab0-8c1f-683bc9621794",
                        "propagate": True, "project_id": "0eb71d6d-ee69-4ff0-8934-828401b07f48", "head_commit_hash": "60fbb99b67ff3228fd9d105d75c2f122a2d80a81", "dest_repo_name": "hao-backprop-test", "dest_repo_id": "*********", "dest_branch_id": "52eff5ab-6a0d-4c4b-88c3-c2e9ebac1eca", "dest_branch_name": "blitzy-2314bd2d-e1d1-4241-bab5-40aa8287a4a8", "is_new_dest_repo": False, "change_mode": "REFACTOR_CODE"})
os.environ["EVENT_DATA"] = EVENT_DATA

os.environ["PROJECT_ID"] = 'blitzy-os-dev'
os.environ["GCS_BUCKET_NAME"] = 'blitzy-os-internal'
os.environ["PRIVATE_BLOB_NAME"] = 'private-src'
os.environ["PLATFORM_EVENTS_TOPIC"] = 'platform-events'
os.environ["ANTHROPIC_API_KEY"] = "************************************************************************************************************"
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"
os.environ["VOYAGE_API_KEY"] = "pa-5WI8Vwyw7YUpoWeX4ckHbDd7dN4w74S61zXHQKD0wTE"
os.environ["GOOGLE_API_KEY"] = "AIzaSyAA_WvkItpwfiL3D8czRWsT5dbA2iXmF_I"

NEO4J_SERVER = "neo4j://34.134.29.39:7687"
NEO4J_USERNAME = "neo4j"
NEO4J_PASSWORD = "wySsik-cisboq-fizve1"
os.environ["NEO4J_SERVER"] = NEO4J_SERVER
os.environ["NEO4J_USERNAME"] = NEO4J_USERNAME
os.environ["NEO4J_PASSWORD"] = NEO4J_PASSWORD

DEV_GITHUB_SECRET_SERVER = "https://archie-secret-manager-464705070478.us-central1.run.app"
PROD_GITHUB_SECRET_SERVER = "https://archie-secret-manager-648803317587.us-central1.run.app"
os.environ["GITHUB_SECRET_SERVER"] = DEV_GITHUB_SECRET_SERVER

os.environ["LANGCHAIN_TRACING_V2"] = ""
os.environ["LANGCHAIN_ENDPOINT"] = ""
os.environ["LANGCHAIN_API_KEY"] = ""
os.environ["LANGCHAIN_PROJECT"] = ""
