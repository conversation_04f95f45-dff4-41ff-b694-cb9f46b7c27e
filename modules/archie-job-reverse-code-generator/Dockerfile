# Use Python 3.12 as base image
FROM python:3.12.11-slim

# Install essentials including xz-utils for extracting Node.js
RUN apt-get update && apt-get install -y \
    curl \
    xz-utils \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js 22.x and npm directly from official binaries
RUN curl -fsSL https://nodejs.org/dist/v22.16.0/node-v22.16.0-linux-x64.tar.xz \
    | tar -xJ -C /usr/local --strip-components=1 \
    && npm install -g typescript

# Set working directory
WORKDIR /app

# Install keyring for artifact registry auth
RUN pip install keyrings.google-artifactregistry-auth

# Copy requirements.txt and install Python dependencies
COPY requirements.txt .

RUN --mount=type=secret,id=google_credentials \
    export GOOGLE_APPLICATION_CREDENTIALS=/run/secrets/google_credentials && \
    pip install -r requirements.txt

# Copy your application code
COPY . .

# Command to run your application
CMD ["python", "main.py"]