import re

content = "Based on the technical specification and the requirements for the Python Flask implementation, I'll now create the requirements.txt file.\n\n# requirements.txt\n```\nFlask==2.0.1\n```"


def get_json_content(content: str, strict=True):
    # Find the content between the first and last set of triple backticks
    start = content.find('```')
    end = content.rfind('```')

    if start != -1 and end != -1 and start != end:
        # Extract the content between the backticks
        extracted_content = content[start + 3:end].strip()

        # Check if there's a language prefix on the first line
        lines = extracted_content.split('\n')
        if lines and len(lines) > 1:
            first_line = lines[0].strip()
            if re.match(r'^[a-zA-Z0-9]+$', first_line):
                extracted_content = '\n'.join(lines[1:])

        return extracted_content.strip()
    else:
        print("No code block found")
        if strict:
            return ""
        return content


print(get_json_content(content=content, strict=True))
