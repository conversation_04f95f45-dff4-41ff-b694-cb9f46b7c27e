import json
import os

EVENT_DATA = json.dumps({"tech_spec_id": "3506412f-ac71-4d4d-be27-7ca743be4191", "repo_name": "BlitzyClientPortalParent", "repo_id": "1011128959", "branch_id": "3e9a6efc-c0f1-4cd7-94a9-d868ceab8c98", "branch_name": "master", "company_id": "default",
                        "user_id": "6f72d8e4-4a82-4f38-88a2-ac87fdcc0e9e", "team_id": "default", "job_id": "285e2ef5-662c-4179-b662-401d2e73b593", "project_id": "3c705ef3-5c19-440e-a51e-ae91080117f6", "head_commit_hash": "b6aa5adb888c9c89b3c5405bd641d7f88797159c", "prev_head_commit_hash": "", "propagate": True})
os.environ["EVENT_DATA"] = EVENT_DATA

os.environ["GRAPH_CODE_TOPIC"] = "graph-code"
os.environ["PROJECT_ID"] = 'blitzy-platform-prod'
os.environ["GCS_BUCKET_NAME"] = 'blitzy-platform-prod'
os.environ["PRIVATE_BLOB_NAME"] = 'private-src'
os.environ["PLATFORM_EVENTS_TOPIC"] = 'platform-events'

DEV_GITHUB_SECRET_SERVER = "https://archie-secret-manager-464705070478.us-central1.run.app"
PROD_GITHUB_SECRET_SERVER = "https://archie-secret-manager-648803317587.us-east1.run.app"
os.environ["GITHUB_SECRET_SERVER"] = PROD_GITHUB_SECRET_SERVER
