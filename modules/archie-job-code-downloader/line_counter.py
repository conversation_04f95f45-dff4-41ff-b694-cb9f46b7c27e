from blitzy_platform_shared.code_graph.utils import is_source_adjacent_file, is_source_file, test_file_type
import os
import glob

REPO_NAME = "blitzy-evaluation-parent-project"


def count_lines(directory: str):
    # Dictionary to store statistics by extension
    stats = {}

    for filename in glob.iglob(f'{directory}/**/*', recursive=True):
        if os.path.isfile(filename) and (is_source_file(filename) or is_source_adjacent_file(filename)):
            # Get the file extension
            _, ext = os.path.splitext(filename)
            ext = ext.lower() if ext else "[no extension]"

            # Initialize stats for this extension if not already present
            if ext not in stats:
                stats[ext] = {
                    "files": 0,
                    "blank_lines": 0,
                    "non_blank_lines": 0,
                    "total_lines": 0
                }

            # Increment file count for this extension
            stats[ext]["files"] += 1

            try:
                with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
                    for line in f:
                        if line.strip():
                            stats[ext]["non_blank_lines"] += 1
                        else:
                            stats[ext]["blank_lines"] += 1
                        stats[ext]["total_lines"] += 1
            except Exception as e:
                print(f"Error processing {filename}: {e}")

    # Print the results in a sorted table
    print("\nFile Extension Statistics:\n")
    print(f"{'Extension':<15} {'Files':<10} {'Blank Lines':<15} {'Non-Blank Lines':<20} {'Total Lines':<15}")
    print("-" * 75)

    # Sort by total line count (descending)
    for ext, data in sorted(stats.items(), key=lambda x: x[1]["total_lines"], reverse=True):
        print(
            f"{ext:<15} {data['files']:<10} {data['blank_lines']:<15} {data['non_blank_lines']:<20} {data['total_lines']:<15}")

    # Calculate and print totals
    total_files = sum(data["files"] for data in stats.values())
    total_blank_lines = sum(data["blank_lines"] for data in stats.values())
    total_non_blank_lines = sum(data["non_blank_lines"] for data in stats.values())
    total_lines = sum(data["total_lines"] for data in stats.values())

    print("-" * 75)
    print(f"{'TOTAL':<15} {total_files:<10} {total_blank_lines:<15} {total_non_blank_lines:<20} {total_lines:<15}")

    return stats


count_lines(f'{"./blitzy"}/{REPO_NAME}')
