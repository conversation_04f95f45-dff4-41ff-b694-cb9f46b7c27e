PORT ?= 8080
IMAGE_TAG ?= latest
ARTIFACTORY_REGION ?= us-east1
PROJECT_ID ?= blitzy-os-dev
REPOSITORY ?= gcf-artifacts
IMAGE_NAME ?= archie-job-code-downloader
SERVICE_ACCOUNT_NAME ?= <EMAIL>
ENV ?= dev

# Full image path
IMAGE_PATH := $(ARTIFACTORY_REGION)-docker.pkg.dev/$(PROJECT_ID)/$(REPOSITORY)/$(IMAGE_NAME):$(IMAGE_TAG)

all: build

install-deployment-utils:
	python -m pip install --upgrade pip keyrings.google-artifactregistry-auth toml
	@echo "Installing deployment utils"
	pip install --extra-index-url https://us-east1-python.pkg.dev/blitzy-os-dev/python-us-east1/simple deployment-utils


init:
	pip install -r requirements.txt

build:
	@if [ "$(GITHUB_ACTIONS)" = "true" ]; then \
         DOCKER_BUILDKIT=1 docker build \
            --secret id=google_credentials,src=$$GOOGLE_APPLICATION_CREDENTIALS \
            -t $(IMAGE_PATH) .; \
    else \
        DOCKER_BUILDKIT=1 docker build \
            --secret id=google_credentials,src=$$SERVICE_ACCOUNT_KEY_PATH \
            -t $(IMAGE_PATH) .; \
    fi
	@echo "Building docker image with tag $(IMAGE_PATH)"

clean:
	docker rmi -f $(docker images -f "dangling=true" -q)

deploy:
	@if [ -n "$(tag)" ]; then \
		echo "Deploying with tag from command line: $(tag)"; \
		deploy-to-cloud-run --type job --image-tag=$(tag) --yaml-file=$(YAML_FILE) --vars-file=env_config/env-$(ENV).yaml --skip-confirmation; \
	else \
		echo "Deploying with IMAGE_PATH: $(IMAGE_PATH)"; \
		deploy-to-cloud-run --type job --image-tag=$(IMAGE_PATH) --yaml-file=$(YAML_FILE) --vars-file=env_config/env-$(ENV).yaml --skip-confirmation; \
	fi


.PHONY: init deploy
