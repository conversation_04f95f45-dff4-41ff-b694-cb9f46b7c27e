from blitzy_utils.common import publish_notification
from google.cloud import pubsub_v1

PROJECT_ID = 'blitzy-os-dev'
DOWLOAD_CODE_TOPIC = 'download-code'

publisher = pubsub_v1.PublisherClient()

notification_data = {"tech_spec_id": "07c5ae3f-5531-403f-886e-6be4fcde4d0a", "repo_name": "existing-project-test", "repo_id": "*********", "branch_id": "fb784b0e-1065-49a3-8668-b78c180bc63b", "branch_name": "main", "company_id": "default", "user_id": "47465c52-f865-412e-93b6-36530ef07a1f",
                     "team_id": "0f4b5821-01d5-42c6-a84c-84019e9466a0", "job_id": "798be853-ece2-41e5-9e69-4c502b1f1080", "project_id": "17112d75-f2b7-4236-a2a6-dc150213df51", "head_commit_hash": "2e0d3aa36384d1d72d2bb3480fd463dd15baa006", "prev_head_commit_hash": "", "propagate": True}
publish_notification(publisher, notification_data, PROJECT_ID, DOWLOAD_CODE_TOPIC)
