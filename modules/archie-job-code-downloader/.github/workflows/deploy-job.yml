name: Deploy Cloud Run Job

on:
  push:
    branches:
      - main

concurrency: 
  group: dev-deployments
  cancel-in-progress: true

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: dev
    permissions:
      contents: 'read'
      id-token: 'write'
    
    env:
      ARTIFACTORY_DOMAIN: us-east1-docker.pkg.dev
      ARTIFACTORY_IMAGE_DOMAIN: us-east1-docker.pkg.dev/${{ vars.PROJECT_ID_DEV }}/${{ vars.REPOSITORY }}
      IMAGE_NAME: archie-job-code-downloader
      IMAGE_TAG: latest
      SERVICE_NAME: archie-job-code-downloader

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v1
      with:
        credentials_json: ${{ secrets.GCP_CREDENTIALS_DEV }}

    - name: Set up Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: '${{ vars.PROJECT_ID_DEV }}'

    - name: Configure Docker for GCP Artifactory
      run: |
        gcloud auth configure-docker $ARTIFACTORY_DOMAIN

    - name: Build Docker image
      run: |
        make

    - name: Install deployment utils
      run: make install-deployment-utils

    - name: Tag Docker image
      run: |
        docker tag $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:$IMAGE_TAG $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:${{ github.sha }}

    - name: Push Docker image
      run: |
        echo "Pushing $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:$IMAGE_TAG"
        echo "Pushing image with SHA $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:${{ github.sha }}"
        docker push $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:${{ github.sha }}
        docker push $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:$IMAGE_TAG

    - name: 'Use gcloud CLI'
      run: 'gcloud info'

    - name: Deploy Cloud Run job
      run: |
        gcloud --quiet beta run jobs deploy code-downloader \
          --image $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:${{ github.sha }} \
          --region ${{ vars.REGION }} \
          --vpc-connector ${{ vars.VPC_CONNECTOR_DEV }} \
          --vpc-egress ${{ vars.VPC_EGRESS }} \
          --service-account ${{ vars.SERVICE_ACCOUNT_DEV }} \
          --memory ${{ vars.MEMORY }} \
          --cpu ${{ vars.CPU }} \
          --task-timeout ${{ vars.BETA_TIMEOUT }} \
          --max-retries ${{ vars.MAX_RETRIES }} \
          --set-env-vars "\
            SERVICE_NAME=$SERVICE_NAME,\
            PROJECT_ID=${{ vars.PROJECT_ID_DEV }},\
            GCS_BUCKET_NAME=${{ vars.GCS_BUCKET_NAME_DEV }},\
            PRIVATE_BLOB_NAME=${{ vars.PRIVATE_BLOB_NAME }},\
            PLATFORM_EVENTS_TOPIC=${{ vars.PLATFORM_EVENTS_TOPIC }},\
            GRAPH_CODE_TOPIC=${{ vars.GRAPH_CODE_TOPIC }},\
            GITHUB_SECRET_SERVER=${{ vars.SECRET_SERVER_DEV }},\
            SERVICE_URL_ADMIN=${{ vars.SERVICE_URL_ADMIN_DEV }},\
            SERVICE_URL_GITHUB=${{ vars.SERVICE_URL_GITHUB_DEV }},\
            OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY_DEV }}"

    - name: Notify Slack
      if: always()
      uses: slackapi/slack-github-action@v1.24.0
      env:
        SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
      with:
        channel-id: ${{ vars.SLACK_BOT_CHANNEL }}
        slack-message: |
          >>> *Build Status:* ${{ job.status == 'success' && '✅ Success' || '❌ Failure' }}
          *Repository:* ${{ github.repository }}
          *Branch:* `${{ github.ref_name }}`
          *Triggered by:* ${{ github.actor }}
          *Commit:* <${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }}|${{ github.sha }}>
          *Action:* <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Build>
