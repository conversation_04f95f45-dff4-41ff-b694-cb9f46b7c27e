from blitzy_utils.logger import logger

from set_env import EVENT_DATA
import json

from blitzy_platform_shared.code_graph.builder import CodeGraphBuilder

from main import process_event

if __name__ == "__main__":
    logger.info(f"Processing notification data: {EVENT_DATA}")
    event_data = json.loads(EVENT_DATA)
    repo_name = event_data.get('repo_name')
    project_id = event_data.get('project_id', '')
    job_id = event_data.get('job_id', '')
    propagate = event_data.get('propagate', True)
    user_id = event_data.get('user_id', 'default')
    org_name = event_data.get('org_name', '')
    repo_id = event_data.get('repo_id', 'repo_id')
    branch_name = event_data.get('branch_name', 'main')
    tech_spec_id = event_data.get('tech_spec_id', "")
    try:
        process_event(event_data_str=EVENT_DATA)
    except Exception as e:
        logger.error(f'Failed to download code: {e}')
        # notification_data = {
        #     "projectId": project_id,
        #     "jobId": job_id,
        #     "tech_spec_id": tech_spec_id,
        #     "org_name": org_name,
        #     "repo_id": repo_id,
        #     "branch_name": branch_name,
        #     "phase": ProjectPhase.CODE_DOWNLOAD.value,
        #     "status": JobStatus.FAILED.value,
        #     "user_id": user_id,
        #     "repo_name": repo_name,
        #     "metadata": {
        #         "propagate": propagate,
        #     }
        # }
        # publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)
        raise
