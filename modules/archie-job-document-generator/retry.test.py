from blitzy_utils.common import publish_notification
from google.cloud import pubsub_v1

PROJECT_ID = 'blitzy-os-dev'
GENERATE_DOCUMENT_TOPIC = 'generate-document'

publisher = pubsub_v1.PublisherClient()

notification_data = {"user_id": "4428a4b5-0dd0-4734-8b95-f5dd81b702a4", "repo_name": "mca-test-l0ea7c", "order": 1,
                     "project_id": "d32dfeb2-fdb1-4b30-97cb-2c7dac222362", "job_id": "19954a02-1f20-4c39-a085-220d840806fa", "propagate": False}
publish_notification(publisher, notification_data, PROJECT_ID, GENERATE_DOCUMENT_TOPIC)
