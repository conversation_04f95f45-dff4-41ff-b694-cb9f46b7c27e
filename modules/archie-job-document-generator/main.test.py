import os
import asyncio
import json
from blitzy_utils.logger import logger

EVENT_DATA = {
    "repo_name": "hello-world2-0r6b4x",
    "order": 1, # tech spec
    "project_id": "blitzy-os-dev",
    "job_id": "dummy",
    "propagate": False
}

os.environ["EVENT_DATA"] = "" # dummy valuye
os.environ["PROJECT_ID"] = 'blitzy-os-dev'
os.environ["GCS_BUCKET_NAME"] = 'blitzy-os-internal'
os.environ["BLOB_NAME"] = 'public-samples'
os.environ["GENERATE_DOCUMENT_TOPIC"] = 'generate-document'
os.environ["MARKDOWN_SERVER"] =  "http://localhost:9090/v1/mermaid/validate" #"https://archie-service-markdown-464705070478.us-east1.run.app/v1/mermaid/validate"
os.environ["PLATFORM_EVENTS_TOPIC"] = 'platform-events' # dummy value
os.environ["GENERATE_REPO_STRUCTURE_TOPIC"] = 'generate-repo-structure' #dummy value
os.environ["ANTHROPIC_API_KEY"] = "************************************************************************************************************"
os.environ["OPENAI_API_KEY"] = "" # dummy value
os.environ["LANGCHAIN_TRACING_V2"] = "" # dummy value
os.environ["LANGCHAIN_ENDPOINT"] = "" # dummy value
os.environ["LANGCHAIN_API_KEY"] = "" # dummy value
os.environ["LANGCHAIN_PROJECT"] = "" # dummy value
os.environ["MARKDOWN_SERVER"] = "http://localhost:9090/v1/mermaid/validate"

from main import generate_document

if __name__ == "__main__":
    logger.info(f"Generating document for notification data: {EVENT_DATA}")
    asyncio.run(generate_document(event_data_str=json.dumps(EVENT_DATA)))
