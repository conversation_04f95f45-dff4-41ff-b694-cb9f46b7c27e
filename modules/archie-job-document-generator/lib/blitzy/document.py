from blitzy_utils.logger import logger
from typing import Dict
from typing import TypedDict
from typing import Dict, Any, Literal
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import SystemMessage, HumanMessage
from langgraph.graph import START, StateGraph, <PERSON><PERSON>
from anthropic import BadRequest<PERSON><PERSON>r as AntBadRequestError

from blitzy_platform_shared.document.mermaid_fixer import MermaidFixer
from blitzy_platform_shared.document.utils import clean_document
from blitzy_platform_shared.common.utils import archie_exponential_retry, get_response_content
from blitzy_platform_shared.common.tools import ANTHROPIC_WEB_SEARCH_TOOL_DEFINITION

from lib.blitzy.prompts import SECTION_GENERATOR_SYSTEM_PROMPT, SECTION_GENERATOR_SP_CURRENT_SECTION, \
    SECTION_GENERATOR_SP_FINAL_DOC, SECTION_GENERATOR_SP_BACKGROUND_INFO


document_tools = [ANTHROPIC_WEB_SEARCH_TOOL_DEFINITION]


class DocumentState(TypedDict):
    user_input: str
    current_section: str
    generated_content: Dict[str, str]
    final_document: str
    index: int
    total_sections: int
    additional_documents: str
    document_type: str


class DocumentGeneratorHelper:
    def __init__(self, llm: BaseChatModel, fallback_llm: BaseChatModel, section_prompts: Dict[str, str], additional_documents: str,
                 markdown_server: str | None):
        self.llm = llm
        self.fallback_llm = fallback_llm
        self.section_prompts = section_prompts
        self.document_generator = self.create_graph()
        self.additional_documents = additional_documents
        self.markdown_server = markdown_server

    def create_graph(self) -> StateGraph:
        # Define the graph
        document_generator = StateGraph(DocumentState)

        # Add nodes
        document_generator.add_node("process_section", self.process_section)

        # Add edges
        document_generator.add_conditional_edges(
            "process_section",
            self.router,
            {
                "continue": "process_section",
                "end": END
            }
        )

        # Set the entry point
        document_generator.add_edge(START, "process_section")
        return document_generator

    @archie_exponential_retry()
    def process_section(self, state: DocumentState) -> Dict[str, Any]:
        section = state["current_section"]

        if state["index"] == 0:
            state["final_document"] += f"# {state["document_type"]}"

        messages = [
            SystemMessage(
                content=[
                    {
                        "text": SECTION_GENERATOR_SYSTEM_PROMPT.format(document_type=state["document_type"]),
                        "type": "text",
                    }
                ]
            ),
            HumanMessage(
                content=[
                    {
                        "text": SECTION_GENERATOR_SP_CURRENT_SECTION.format(current_section=section),
                        "type": "text",
                    },
                    {
                        "text": SECTION_GENERATOR_SP_FINAL_DOC.format(document_type=state["document_type"],
                                                                      final_document=state["final_document"]),
                        "type": "text"
                    },
                    {
                        "text": SECTION_GENERATOR_SP_BACKGROUND_INFO.format(user_input=state["user_input"]),
                        "type": "text",
                    },
                    {
                        "type": "text",
                        "text": "{section_prompt}".format(section_prompt=self.section_prompts[section])
                    }
                ]
            )
        ]

        # Generate the content using the LLM
        try:
            response = self.llm.invoke(messages)
        except AntBadRequestError as e:
            logger.warning(f'Anthropic bad request error, trying with fallback: {e}')
            response = self.fallback_llm.invoke(messages)

        content = get_response_content(response=response)

        logger.info(f'generated {state["document_type"]} section with index {state["index"]}')  # For debugging purposes

        if self.markdown_server:
            try:
                content = MermaidFixer(self.llm, self.markdown_server).process_content(content)
            except Exception as e:
                logger.error(f'Exception while fixing mermaid: {e}')

        # Update the state
        state["generated_content"][section] = content
        state["final_document"] += f"\n\n{content}"
        state["final_document"] = clean_document(text=state["final_document"])
        sections = list(self.section_prompts.keys())
        next_index = state["index"] + 1
        state["index"] = next_index

        if next_index < state["total_sections"]:
            section = sections[next_index]

        return {
            "current_section": section,
            "generated_content": state["generated_content"],
            "final_document": state["final_document"],
            "index": state["index"],
            "user_input": state["user_input"],
            "additional_documents": state["additional_documents"],
            "document_type": state["document_type"],
            "total_sections": state["total_sections"]
        }

    def router(self, state: DocumentState) -> Literal["continue", "end"]:
        if state["index"] >= state["total_sections"]:
            return "end"
        return "continue"
