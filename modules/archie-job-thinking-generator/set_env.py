import json
import os

from blitzy_utils.enums import BackpropChangeMode

os.environ["EVENT_DATA"] = "{\"repo_name\": \"insure-pilot-new-iu8v0a\"}"
EVENT_DATA = json.loads(os.environ["EVENT_DATA"])
os.environ["BLOB_NAME"] = "public-samples"
os.environ["PROJECT_ID"] = 'blitzy-os-dev'
os.environ["GCS_BUCKET_NAME"] = 'blitzy-os-internal'
os.environ["GENERATE_CODE_TOPIC"] = ''
os.environ["PLATFORM_EVENTS_TOPIC"] = 'platform-events'
os.environ["ANTHROPIC_API_KEY"] = "************************************************************************************************************"
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"
os.environ["VOYAGE_API_KEY"] = "pa-5WI8Vwyw7YUpoWeX4ckHbDd7dN4w74S61zXHQKD0wTE"
os.environ["GOOGLE_API_KEY"] = "AIzaSyAA_WvkItpwfiL3D8czRWsT5dbA2iXmF_I"

os.environ["LANGCHAIN_TRACING_V2"] = ""
os.environ["LANGCHAIN_ENDPOINT"] = ""
os.environ["LANGCHAIN_API_KEY"] = ""
os.environ["LANGCHAIN_PROJECT"] = ""
