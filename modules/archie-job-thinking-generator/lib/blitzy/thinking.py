import json
import json5
import gc
from typing import TypedDict, Dict, Any, Literal, List, Set, Tuple

from blitzy_utils.logger import logger
from langgraph.graph import START, StateGraph, END
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import AIMessage, SystemMessage, HumanMessage, ToolMessage
from langchain_core.tools import tool
from langgraph.prebuilt import ToolNode
from anthropic import BadRequestError as AntBadRequestError
import networkx as nx

from lib.blitzy.prompts import CODE_REPRESENTATION_SYSTEM_PROMPT, FILE_PATH_INPUT, CODE_SCHEMA, \
    TECHNICAL_SPECIFICATION_INPUT, GITHUB_REPO_INPUT, JSON_VALIDATOR_SYSTEM_PROMPT, INVALID_JSON_INPUT, \
    CIRC_DEP_FIXER_SYSTEM_PROMPT

from blitzy_utils.consts import DEFAULT_MAX_RETRIES, CODE_STRUCTURE_NAME
from blitzy_utils.common import get_json_content, DictFileHelper, upload_to_gcs, FileContent<PERSON>elper

from blitzy_platform_shared.common.utils import get_formatted_tool_result_messages, get_response_content, \
    archie_exponential_retry, format_messages


class ThinkingState(TypedDict):
    tech_spec: str
    current_file_path: str
    circ_dep_file_path: str
    current_circ_dep_chain: List[str]
    current_file_spec_str: str
    current_route: str
    original_files_list: List[str]
    files_list: List[str]
    file_index: int
    file_spec_dict: Dict[str, Dict[str, Any]]
    json_invalid: bool
    json_error: str
    json_fix_attempts_left: int
    dep_dict: Dict[str, Dict[str, str]]
    changes_dict: Dict[str, List[Dict[str, str]]]
    thinking_dict: Dict[str, str]


thinking_helper = DictFileHelper(files={})
changes_helper = DictFileHelper(files={})
change_spec_tracker = DictFileHelper(files={})
code_spec_dict_helper = DictFileHelper(files={})
tech_spec_helper = FileContentHelper(file="")


@tool("get_technical_specification", parse_docstring=True)
def get_technical_specification():
    """
        Retrieves the contents of the technical specification document.
        Useful for retrieving additional context about the project like design, architecture, technologies, requirements, and other specifications.
    """
    logger.info('retrieved technical specification')
    content = tech_spec_helper.get_file()
    return content if content else ""


@tool("get_file_specification", parse_docstring=True)
def get_file_specification(imported_file_path: str, assigned_file_path: str):
    """
        Retrieves a JSON specification of a potential dependency file or import from a GitHub repository.
        Useful for understanding the schema, structure and imports of potential dependency files, when those file paths are queried using imported_file_path.

        Args:
            imported_file_path: A file path in the GitHub repo which might be imported inside the assigned_file_path file.
            assigned_file_path: The file path from the GitHub repo that you are currently investigating, which might import file_path file.
    """
    if imported_file_path == assigned_file_path:
        # logger.info(f'tool get_file_specification invoked with the same arguments: {imported_file_path}')
        return ""
    files_dict = code_spec_dict_helper.get_dict()
    file = files_dict.get(imported_file_path)
    if file:
        logger.info(f'retrieved existing file specification: {imported_file_path}')
        # logger.info(file)
        if imported_file_path in code_spec_dict_helper.seen_pending_files:
            code_spec_dict_helper.add_pending_file(file_path=assigned_file_path)
            # logger.info(f'file {imported_file_path} is in pending files, will add {assigned_file_path} to pending files')
        return file
    code_spec_dict_helper.add_pending_file(file_path=assigned_file_path)
    logger.info(f'file specfication does not exist yet: {imported_file_path}')
    return ""


thinking_tools = [get_file_specification]
code_spec_tools_node = ToolNode(thinking_tools)


class ThinkingHelper:
    def __init__(
            self,
            generator_llm: BaseChatModel,
            validator_llm: BaseChatModel,
            toolless_llm: BaseChatModel,
            fallback_llm: BaseChatModel,
            repo_name: str,
            blob_name: str,
            bucket_name: str,
            storage_client,
            tech_spec: str
    ):
        self.generator_llm = generator_llm
        self.validator_llm = validator_llm
        self.toolless_llm = toolless_llm
        self.fallback_llm = fallback_llm
        self.repo_name = repo_name
        self.blob_name = blob_name
        self.bucket_name = bucket_name
        self.storage_client = storage_client

        self.generator = self.create_graph()
        self.code_spec_dict_helper = code_spec_dict_helper
        self.tech_spec_helper = tech_spec_helper
        self.tech_spec_helper.set_file(file=tech_spec)
        self.thinking_helper = thinking_helper
        self.changes_helper = changes_helper
        self.change_spec_tracker = change_spec_tracker

    def create_graph(self) -> StateGraph:
        # Define the graph
        generator = StateGraph(ThinkingState)

        # Add nodes
        generator.add_node("process_file", self.process_file)
        generator.add_node("fix_json", self.fix_json)
        generator.add_node("fix_circ_dep", self.fix_circ_dep)
        generator.add_node("sort_files_list", self.sort_files_list)

        generator.add_conditional_edges(
            "process_file",
            self.router,
            {
                "process": "process_file",
                "fix_json": "fix_json",
                "fix_circ_dep": "fix_circ_dep",
                "end": "sort_files_list"
            }
        )

        generator.add_conditional_edges(
            "fix_json",
            self.router,
            {
                "fix_json": "fix_json",
                "process": "process_file",
                "fix_circ_dep": "fix_circ_dep",
                "end": "sort_files_list"
            }
        )

        generator.add_conditional_edges(
            "fix_circ_dep",
            self.router,
            {
                "process": "process_file",
                "fix_json": "fix_json",
                "fix_circ_dep": "fix_circ_dep",
                "end": "sort_files_list"
            }
        )

        # Set the entry point
        generator.add_edge(START, "process_file")
        generator.add_edge("sort_files_list", END)
        return generator

    @archie_exponential_retry()
    def process_file(self, state: ThinkingState) -> Dict[str, Any]:
        # manual gc
        gc.collect()

        index = state["file_index"]
        state["current_file_path"] = state["files_list"][index]
        file_path = state["current_file_path"]

        filename = f"{CODE_STRUCTURE_NAME}.json"
        upload_to_gcs(
            storage_client=self.storage_client, bucket_name=self.bucket_name, blob_name=self.blob_name,
            filename=filename,
            content_type='application/json', data=json.dumps(state["file_spec_dict"])
        )

        logger.info(f'generating specification for file: {file_path}')
        messages = [
            SystemMessage(content=[
                {
                    "type": "text",
                    "text": CODE_REPRESENTATION_SYSTEM_PROMPT.format(
                        code_schema=CODE_SCHEMA
                    ),
                    "cache_control": {"type": "ephemeral"}
                },
                {
                    "type": "text",
                    "text": TECHNICAL_SPECIFICATION_INPUT.format(
                        tech_spec=state["tech_spec"]
                    ),
                    "cache_control": {"type": "ephemeral"}
                },
                {
                    "type": "text",
                    "text": GITHUB_REPO_INPUT.format(
                        github_files=json.dumps(state["files_list"])
                    ),
                    "cache_control": {"type": "ephemeral"}
                }
            ]),
            HumanMessage(content=[
                {
                    "type": "text",
                    "text": FILE_PATH_INPUT.format(
                        file_path=state["current_file_path"]
                    )
                }
            ])
        ]

        orig_messages = messages.copy()

        try:
            response: AIMessage = self.generator_llm.invoke(messages)

            # logger.info(response)

            while len(response.tool_calls):
                messages.append(response)
                total_tokens = response.usage_metadata["total_tokens"]
                tools_response = code_spec_tools_node.invoke({
                    "messages": [response]
                })
                tool_results = get_formatted_tool_result_messages(
                    tool_message_list=tools_response["messages"],
                    total_tokens=total_tokens,
                    llm=self.generator_llm
                )
                # logger.info(tools_response)
                messages += tool_results
                messages = format_messages(
                    messages=messages
                )
                # logger.info(f'sending tool response back to llm for file: {file_path}')
                response: AIMessage = self.generator_llm.invoke(messages)
        except AntBadRequestError as e:
            logger.warning(f'Anthropic bad request error, trying with fallback: {e}')
            response: AIMessage = self.fallback_llm.invoke(orig_messages)

            # logger.info(response)

            while len(response.tool_calls):
                orig_messages.append(response)
                total_tokens = response.usage_metadata["total_tokens"]
                tools_response: Dict[str, List[ToolMessage]] = code_spec_tools_node.invoke({
                    "messages": [response]
                })
                tool_results = get_formatted_tool_result_messages(
                    tool_message_list=tools_response["messages"],
                    total_tokens=total_tokens,
                    llm=self.fallback_llm
                )
                # logger.info(tools_response)
                orig_messages += tool_results
                # logger.info(f'sending tool response back to llm for file: {file_path}')
                response: AIMessage = self.fallback_llm.invoke(orig_messages)

        content = get_response_content(response=response)

        # logger.info(content)

        state["current_file_spec_str"] = content
        state["current_file_spec_str"] = get_json_content(content=content, strict=False)

        # logger.info(state["current_file_spec_str"])

        state = self.parse_and_save_json_spec(state=state, file_path=file_path)

        if state["json_invalid"] != True:
            logger.info(f'finished generating specification for file: {file_path}')
            state["circ_dep_file_path"] = self.get_circular_dependency_file_path(state=state)
            if not state["circ_dep_file_path"]:
                state["current_circ_dep_chain"] = []
                state["file_index"] += 1

                if len(self.code_spec_dict_helper.pending_files_list):
                    for pending_file_path in self.code_spec_dict_helper.pending_files_list:
                        state["files_list"].append(pending_file_path)
                        logger.info(f'added {pending_file_path} to specification files list')
                        self.code_spec_dict_helper.clear_pending_files()
            else:
                state["current_circ_dep_chain"].append(state["circ_dep_file_path"])

        return self.get_state(state=state)

    def parse_and_save_json_spec(self, state: ThinkingState, file_path: str) -> Dict[str, Any]:
        try:
            parsed_file_spec = json5.loads(state["current_file_spec_str"])

            state = self.validate_imports_in_json(parsed_file_spec=parsed_file_spec, state=state, file_path=file_path)

            state["file_spec_dict"][file_path] = parsed_file_spec
            code_spec_dict_helper.set_dict(state["file_spec_dict"])
        except json5.JSON5DecodeError as e:
            logger.warning(f'Failed to parse JSON due to error {e.msg}, passing to validator agent')
            state["json_invalid"] = True
            state["json_error"] = e.msg
        except Exception as e:
            logger.warning(f'Exception raised: {e}')
            state["json_invalid"] = True
            state["json_error"] = e

        return state

    def validate_imports_in_json(self, parsed_file_spec: Dict[str, Any], state: ThinkingState, file_path: str) -> Dict[
            str, Any]:
        if 'imports' in parsed_file_spec:
            if 'external' in parsed_file_spec['imports']:
                for dep in parsed_file_spec['imports']['external']:
                    file_parts = file_path.split('/')
                    if len(file_parts) > 1:
                        subfolder_name = file_parts[1]
                    else:
                        subfolder_name = "root"
                    if not state["dep_dict"].get(subfolder_name, None):
                        state["dep_dict"][subfolder_name] = {}
                    package_name = dep.get("package_name", dep.get("name"))
                    package_version = dep.get("package_version", "")
                    state["dep_dict"][subfolder_name][package_name] = package_version

            if 'internal' in parsed_file_spec['imports']:
                for dep in parsed_file_spec['imports']['internal']:
                    dep_path = dep['path']

                    if dep_path not in state["files_list"]:
                        raise Exception(f"invalid dependency detected: {dep_path}")
        return state

    def get_state(self, state: ThinkingState) -> Dict[str, Any]:
        return {
            "files_list": state["files_list"],
            "original_files_list": state["original_files_list"],
            "current_file_path": state["current_file_path"],
            "circ_dep_file_path": state["circ_dep_file_path"],
            "current_file_spec_str": state["current_file_spec_str"],
            "current_route": state["current_route"],
            "file_index": state["file_index"],
            "file_spec_dict": state["file_spec_dict"],
            "json_invalid": state["json_invalid"],
            "json_error": state["json_error"],
            "current_circ_dep_chain": state["current_circ_dep_chain"],
            "tech_spec": state["tech_spec"],
            "dep_dict": state["dep_dict"],
            "changes_dict": state["changes_dict"],
            "thinking_dict": state["thinking_dict"],
            "json_fix_attempts_left": state["json_fix_attempts_left"]
        }

    @archie_exponential_retry()
    def fix_json(self, state: ThinkingState) -> Dict[str, Any]:
        index = state["file_index"]
        state["current_file_path"] = state["files_list"][index]
        file_path = state["current_file_path"]

        logger.info(f'fixing json for file: {file_path}')
        messages = [
            SystemMessage(content=[
                {
                    "type": "text",
                    "text": JSON_VALIDATOR_SYSTEM_PROMPT
                }
            ]),
            HumanMessage(content=[
                {
                    "type": "text",
                    "text": INVALID_JSON_INPUT.format(
                        invalid_json=state["current_file_spec_str"]
                    )
                },
                {
                    "type": "text",
                    "text": "JSON error: {json_error}".format(
                        json_error=state["json_error"]
                    )
                }
            ])
        ]

        orig_messages = messages.copy()

        try:
            response: AIMessage = self.toolless_llm.invoke(messages)
        except AntBadRequestError as e:
            logger.warning(f'Anthropic bad request error, trying with fallback: {e}')
            response: AIMessage = self.fallback_llm.invoke(orig_messages)

        content = get_response_content(response=response)

        # logger.info(content)

        try:
            state["current_file_spec_str"] = content
            state["current_file_spec_str"] = get_json_content(content=content, strict=False)

            # logger.info(state["current_file_spec_str"])
            parsed_file_spec = json5.loads(state["current_file_spec_str"])

            state = self.validate_imports_in_json(parsed_file_spec=parsed_file_spec, state=state, file_path=file_path)

            state["file_spec_dict"][file_path] = parsed_file_spec
            code_spec_dict_helper.set_dict(state["file_spec_dict"])

            state["json_invalid"] = False
            state["json_error"] = ""
            state["json_fix_attempts_left"] = DEFAULT_MAX_RETRIES
        except json5.JSON5DecodeError as e:
            logger.warning(f'Validator agent could not fix JSON due to error {e} for file {file_path}')
            state["file_spec_dict"][file_path] = {}
            state["json_error"] = e.msg
            state["json_fix_attempts_left"] -= 1
        except Exception as e:
            logger.warning(f'Exception raised: {e}')
            state["file_spec_dict"][file_path] = {}
            state["json_error"] = e
            state["json_fix_attempts_left"] -= 1

        if state["json_invalid"] != True:
            logger.info(f'finished fixing json for file: {file_path}')
            state["circ_dep_file_path"] = self.get_circular_dependency_file_path(state=state)
            if not state["circ_dep_file_path"]:
                state["current_circ_dep_chain"] = []
                state["file_index"] += 1

                if len(self.code_spec_dict_helper.pending_files_list):
                    for pending_file_path in self.code_spec_dict_helper.pending_files_list:
                        state["files_list"].append(pending_file_path)
                        logger.info(f'added {pending_file_path} to specification files list')
                        self.code_spec_dict_helper.clear_pending_files()
            else:
                state["current_circ_dep_chain"].append(state["circ_dep_file_path"])

        if state["json_fix_attempts_left"] <= 0:
            e = f'exhaused all attempts to fix json for file: {file_path}'
            logger.info(e)
            state["json_invalid"] = False
            state["json_error"] = ""
            state["json_fix_attempts_left"] = DEFAULT_MAX_RETRIES
            state["file_spec_dict"][file_path] = {}
            code_spec_dict_helper.set_dict(state["file_spec_dict"])
            state["file_index"] += 1

        return self.get_state(state=state)

    def get_circular_dependency_file_path(self, state: ThinkingState) -> str:
        """
        Detects circular dependencies using DFS to find cycles in the dependency graph.
        Returns the first-level dependency file path that leads to a circular dependency,
        or empty string if none exists.
        """

        def find_cycle(current_path: List[str], visited: Set[str], file_path: str, first_dep: str = None) -> Tuple[
                str, str]:
            if file_path in current_path:
                # Found a cycle
                cycle_start_index = current_path.index(file_path)
                cycle = current_path[cycle_start_index:]
                logger.info(f"Circular dependency detected: {' -> '.join(cycle + [file_path])}")
                return first_dep, file_path

            if file_path in visited:
                return "", ""

            visited.add(file_path)
            current_path.append(file_path)

            # Check if file exists in specs
            if file_path not in state["file_spec_dict"]:
                logger.info(f'File spec does not exist yet to evaluate circular deps: {file_path}')
                current_path.pop()
                return "", ""

            file_info = state["file_spec_dict"][file_path]

            # Check dependencies
            if 'imports' in file_info and 'internal' in file_info['imports']:
                for dep in file_info['imports']['internal']:
                    dep_path = dep['path']
                    # If this is a first-level dependency, update first_dep
                    current_first_dep = first_dep if first_dep is not None else dep_path
                    result_dep, result_cycle = find_cycle(current_path, visited, dep_path, current_first_dep)
                    if result_dep:
                        return result_dep, result_cycle

            current_path.pop()
            return "", ""

        # Start the search from the current file
        current_file_path = state["current_file_path"]
        visited = set()
        first_level_dep, _ = find_cycle([], visited, current_file_path)
        return first_level_dep

    @archie_exponential_retry()
    def fix_circ_dep(self, state: ThinkingState) -> Dict[str, Any]:
        index = state["file_index"]
        state["current_file_path"] = state["files_list"][index]
        file_path = state["current_file_path"]

        logger.info(f'fixing circular dependency for file: {file_path}')
        messages = [
            SystemMessage(content=[
                {
                    "type": "text",
                    "text": CIRC_DEP_FIXER_SYSTEM_PROMPT
                }
            ]),
            HumanMessage(content=[
                {
                    "type": "text",
                    "text": TECHNICAL_SPECIFICATION_INPUT.format(
                        tech_spec=state["tech_spec"]
                    ),
                    "cache_control": {"type": "ephemeral"}
                },
                {
                    "type": "text",
                    "text": GITHUB_REPO_INPUT.format(
                        github_files=json.dumps(state["files_list"])
                    ),
                    "cache_control": {"type": "ephemeral"}
                },
                {
                    "type": "text",
                    "text": "JSON specification for the assigned file containing a circular dependency: {new_json_spec}".format(
                        new_json_spec=json.dumps(state["current_file_spec_str"])
                    )
                },
                {
                    "type": "text",
                    "text": "List of forbidden file paths that are circularly dependent on the assigned file: {forbidden_files}".format(
                        forbidden_files=json.dumps(state["current_circ_dep_chain"])
                    )
                }
            ])
        ]

        orig_messages = messages.copy()

        try:
            response: AIMessage = self.validator_llm.invoke(messages)

            # logger.info(response)

            while len(response.tool_calls):
                messages.append(response)
                total_tokens = response.usage_metadata["total_tokens"]
                tools_response = code_spec_tools_node.invoke({
                    "messages": [response]
                })
                tool_results = get_formatted_tool_result_messages(
                    tool_message_list=tools_response["messages"],
                    total_tokens=total_tokens,
                    llm=self.validator_llm
                )
                # logger.info(tools_response)
                messages += tool_results
                messages = format_messages(
                    messages=messages
                )
                # logger.info(f'sending tool response back to llm for file: {file_path}')
                response: AIMessage = self.validator_llm.invoke(messages)
        except AntBadRequestError as e:
            logger.warning(f'Anthropic bad request error, trying with fallback: {e}')
            response: AIMessage = self.fallback_llm.invoke(orig_messages)

            # logger.info(response)

            while len(response.tool_calls):
                orig_messages.append(response)
                total_tokens = response.usage_metadata["total_tokens"]
                tools_response: Dict[str, List[ToolMessage]] = code_spec_tools_node.invoke({
                    "messages": [response]
                })
                tool_results = get_formatted_tool_result_messages(
                    tool_message_list=tools_response["messages"],
                    total_tokens=total_tokens,
                    llm=self.fallback_llm
                )
                # logger.info(tools_response)
                orig_messages += tool_results
                # logger.info(f'sending tool response back to llm for file: {file_path}')
                response: AIMessage = self.fallback_llm.invoke(orig_messages)

        content = get_response_content(response=response)

        # logger.info(content)

        state["current_file_spec_str"] = content
        state["current_file_spec_str"] = get_json_content(content=content, strict=False)

        # logger.info(state["current_file_spec_str"])

        state = self.parse_and_save_json_spec(state=state, file_path=file_path)

        if state["json_invalid"] != True:
            logger.info(f'finished fixing circular dependency for file: {file_path}')
            state["circ_dep_file_path"] = self.get_circular_dependency_file_path(state=state)
            if not state["circ_dep_file_path"]:
                state["current_circ_dep_chain"] = []
                state["file_index"] += 1

                if len(self.code_spec_dict_helper.pending_files_list):
                    for pending_file_path in self.code_spec_dict_helper.pending_files_list:
                        state["files_list"].append(pending_file_path)
                        logger.info(f'added {pending_file_path} to specification files list')
                        self.code_spec_dict_helper.clear_pending_files()
            else:
                state["current_circ_dep_chain"].append(state["circ_dep_file_path"])

        return self.get_state(state=state)

    def sort_files_list(self, state: ThinkingState) -> Dict[str, Any]:
        logger.info(f'previous list: {state["files_list"]}')
        state["original_files_list"] = self.create_dependency_sorted_list(state["file_spec_dict"])
        state["files_list"] = state["original_files_list"].copy()
        logger.info(f'new list: {state["files_list"]}')
        code_spec_dict_helper.set_dict(state["file_spec_dict"])

        state["file_index"] = 0

        # upload intermediate code spec for debugging
        filename = f"{CODE_STRUCTURE_NAME} - intermediate.json"
        upload_to_gcs(storage_client=self.storage_client, bucket_name=self.bucket_name, blob_name=self.blob_name,
                      filename=filename,
                      content_type='application/json', data=json.dumps(state["file_spec_dict"]))

        return {
            "files_list": state["files_list"],
            "original_files_list": state["original_files_list"],
            "current_file_path": state["current_file_path"],
            "circ_dep_file_path": state["circ_dep_file_path"],
            "current_file_spec_str": state["current_file_spec_str"],
            "current_route": state["current_route"],
            "file_index": state["file_index"],
            "file_spec_dict": state["file_spec_dict"],
            "json_invalid": state["json_invalid"],
            "json_error": state["json_error"],
            "current_circ_dep_chain": state["current_circ_dep_chain"],
            "tech_spec": state["tech_spec"],
            "dep_dict": state["dep_dict"],
            "changes_dict": state["changes_dict"],
            "thinking_dict": state["thinking_dict"],
            "json_fix_attempts_left": state["json_fix_attempts_left"]
        }

    def create_dependency_sorted_list(self, files_dict: Dict) -> List[str]:
        """
        Creates a sorted list of files based on their dependencies.
        Files with no dependencies appear first, followed by files that depend on them.

        Args:
            files_dict: Dictionary containing file information

        Returns:
            List of file paths sorted by dependency order
        """

        # Create a directed graph
        dep_graph = nx.DiGraph()

        # Add all files as nodes
        for file_path in files_dict.keys():
            dep_graph.add_node(file_path)

        # Add edges based on internal dependencies
        for file_path, file_info in files_dict.items():
            if 'imports' in file_info and 'internal' in file_info['imports']:
                for dep in file_info['imports']['internal']:
                    # Add edge from file to its dependency
                    # If A depends on B, create edge A → B
                    dep_graph.add_edge(file_path, dep['path'])

        try:
            # Perform topological sort
            # This will raise NetworkXUnfeasible if there's a circular dependency
            sorted_files = list(reversed(list(nx.topological_sort(dep_graph))))
            # Reverse the list because topological sort returns in reverse dependency order
            return sorted_files
        except nx.NetworkXUnfeasible:
            logger.warning("Circular dependency detected in the files")
            raise ValueError("Circular dependency detected in the files")

    def get_imported_file_specs(self, file_spec, state: ThinkingState, processed_files=None):
        """
        Recursively get all imported files and their dependencies.

        Args:
            file_spec: File specification dictionary
            processed_files: Set to track processed files to avoid circular dependencies

        Returns:
            tuple: (imported_files_string, imported_files_list)
        """
        if processed_files is None:
            processed_files = set()

        imported_file_specs = ""
        imported_file_specs_list = []

        if 'imports' in file_spec and 'internal' in file_spec['imports']:
            for dep in file_spec['imports']['internal']:
                dep_path = dep['path']

                # Skip if already processed
                if dep_path in processed_files:
                    continue

                processed_files.add(dep_path)

                if dep_path not in imported_file_specs_list:
                    imported_file_specs_list.append(dep_path)
                    file_spec = state["file_spec_dict"][dep_path]
                    imported_file_specs += f"file path: {dep_path} \n\n{json.dumps(file_spec)}\n\n"

                    recursive_imports, recursive_list = self.get_imported_file_specs(
                        file_spec,
                        state,
                        processed_files
                    )
                    imported_file_specs += recursive_imports
                    imported_file_specs_list.extend([f for f in recursive_list if f not in imported_file_specs_list])

        return imported_file_specs, imported_file_specs_list

    def router(self, state: ThinkingState) -> Literal[
            "process", "extend", "fix_json", "fix_circ_dep", "sort_files_list", "end"]:
        if state["json_invalid"] == True:
            return "fix_json"
        elif state["circ_dep_file_path"]:
            return "fix_circ_dep"
        elif state["file_index"] >= len(state["files_list"]):
            return "end"
        else:
            return state["current_route"]
