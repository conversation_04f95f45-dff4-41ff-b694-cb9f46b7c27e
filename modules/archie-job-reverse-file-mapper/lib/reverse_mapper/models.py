from typing import List
from enum import Enum

from pydantic import BaseModel, Field

from blitzy_platform_shared.code_generation.models import CreatedFile, UpdatedFile, ExcludedItem


class CreatedFolder(BaseModel):
    dest_path: str = Field(
        ...,
        description="REQUIRED. New absolute path of the folder in the destination repository, refactored according to the technical specification."
    )
    summary: str = Field(
        ...,
        description="REQUIRED. A comprehensive description of the new folder’s purpose within the repository"
    )
    source_folders: List[str] = Field(
        ...,
        description="REQUIRED. A list of 1:1 mirrored folder paths from the existing repository that contain important context about this new folder. "
        "IMPORTANT: Must strictly be a non-empty list of valid FOLDER (and not FILE) path from the existing repository and not your own."
    )


class UpdatedFolder(BaseModel):
    dest_path: str = Field(
        ...,
        description="REQUIRED. Absolute path of the existing folder whose files are being updated or modified. "
        "IMPORTANT: Must be unchanged."
    )
    key_changes: List[str] = Field(
        ...,
        description="REQUIRED. A detailed list of updates or modifications required for this folder such as added or updated subfolders and files, to satisfy the updated technical specification"
    )


class RepositoryCreatedFolderContents(BaseModel):
    new_files: List[CreatedFile] = Field(
        ...,
        description="REQUIRED. List of all new direct file children from this folder in the new repository"
        "Must not be empty unless the assigned folder doesn't have any files. "
    )
    excluded_files: List[ExcludedItem] = Field(
        ...,
        description="REQUIRED. List of all direct file children from this folder in the existing repository that are excluded from the new repository. "
        "A file must never appear in both, new_files and excluded_files"
    )
    new_folders: List[CreatedFolder] = Field(
        ...,
        description="REQUIRED. List of all new direct subfolders of this folder in the new repository"
        "Must not be empty unless the assigned folder doesn't have any subfolders."
    )
    excluded_folders: List[ExcludedItem] = Field(
        ...,
        description="REQUIRED. List of all direct subfolder children from this folder in the existing repository that are excluded from the new repository. "
        "A folder must never appear in both, new_folders and excluded_folders"
    )


class RepositoryUpdatedFolderContents(BaseModel):
    created_files: List[CreatedFile] = Field(
        ...,
        description="REQUIRED. List of all new direct children files that need to be created in this folder, as required by the updated technical specification."
    )
    modified_files: List[UpdatedFile] = Field(
        ...,
        description="REQUIRED. List of all direct children files that need to be modified in this folder, as required by the updated technical specification."
    )
    deleted_files: List[ExcludedItem] = Field(
        ...,
        description="REQUIRED. List of all direct children files that need to be deleted in this folder. "
        "IMPORTANT: Only delete files if you are 100 percent confident that they have been replaced, invalidated or deprecated by your created or modified items."
    )
    unchanged_files: List[ExcludedItem] = Field(
        ...,
        description="REQUIRED. List of all unchanged, excluded or out of scope direct children files in this folder. "
        "A file must never appear in both, created_files or modified_files, and unchanged_files. "
        "IMPORTANT: All created files must only be direct children of the current folder with the same number of '/'s in the path."
    )
    created_folders: List[CreatedFolder] = Field(
        ...,
        description="REQUIRED. List of all new direct children subfolders that don't already exist and need to be created in this folder, as required by the updated technical specification. "
        "IMPORTANT: All created folders must only be direct children of the current folder with the same number of '/'s in the path."
    )
    modified_folders: List[UpdatedFolder] = Field(
        ...,
        description="REQUIRED. List of all existing direct children subfolders from this folder that may contain one or more modified files. This must never be the current folder itself."
    )
    unchanged_folders: List[ExcludedItem] = Field(
        ...,
        description="REQUIRED. List of all unchanged, excluded or out of scope direct children subfolders in this folder. "
        "A folder must never appear in both, created_folders or modified_folders, and unchanged_folders. "
        "Never list the parent folder itself here."
    )
