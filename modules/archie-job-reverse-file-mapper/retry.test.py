from google.cloud import pubsub_v1

from blitzy_utils.common import publish_notification
from blitzy_utils.enums import BackpropCommand

PROJECT_ID = 'blitzy-os-dev'
REVERSE_FILE_MAP_TOPIC = 'generate-reverse-file-map'

publisher = pubsub_v1.PublisherClient()


notification_data = {"code_gen_id": "2314bd2d-e1d1-4241-bab5-40aa8287a4a8", "tech_spec_id": "ddd575a9-77d2-4e45-9ba3-f1ac83331b4f", "repo_name": "hao-backprop-test", "repo_id": "*********", "branch_id": "0b7eee13-b78f-46b0-853e-4df3b1ff5684", "branch_name": "main3", "company_id": "company-1", "user_id": "b388177a-cc79-46eb-bf6f-6b7e1c63fde6", "team_id": "default", "job_id": "5cb035df-dce9-4ab0-8c1f-683bc9621794",
                     "propagate": True, "project_id": "0eb71d6d-ee69-4ff0-8934-828401b07f48", "head_commit_hash": "60fbb99b67ff3228fd9d105d75c2f122a2d80a81", "dest_repo_name": "hao-backprop-test", "dest_repo_id": "*********", "dest_branch_id": "52eff5ab-6a0d-4c4b-88c3-c2e9ebac1eca", "dest_branch_name": "blitzy-2314bd2d-e1d1-4241-bab5-40aa8287a4a8", "is_new_dest_repo": False, "change_mode": "REFACTOR_CODE"}

publish_notification(publisher, notification_data, PROJECT_ID, REVERSE_FILE_MAP_TOPIC)
