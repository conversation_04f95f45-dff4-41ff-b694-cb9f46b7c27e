import json
import os

from blitzy_utils.enums import BackpropCommand

EVENT_DATA = json.dumps({
    'repo_name': 'hamlet',
    'repo_id': 'repo_id',
    "branch_id": "main",
    'branch_name': "main",
    'company_id': 'Symphony42',
    'user_id': '4428a4b5-0dd0-4734-8b95-f5dd81b702a4',
    "team_id": "default",
    "job_id": "",
    "project_id": "a9f2b391-7e7c-4a71-beda-3cffd0da0662",
    "head_commit_hash": "6c75f2228c11b50427f1d64aa78f729f9c48f1e8",
    "dest_repo_name": "hamlet-python",
    "dest_repo_id": "4ca4927d-089b-4344-af90-d3f78d642854",
    "dest_branch_id": "b79b8c43-394d-4a9a-b8c2-d5b7f45053c5",
    "dest_branch_name": "main",
    "is_new_dest_repo": True,
    "change_mode": BackpropCommand.REFACTOR_CODE.value
})
os.environ["EVENT_DATA"] = EVENT_DATA

os.environ["PROJECT_ID"] = 'blitzy-os-dev'
os.environ["GCS_BUCKET_NAME"] = 'blitzy-os-internal'
os.environ["PRIVATE_BLOB_NAME"] = 'private-src'
os.environ["PLATFORM_EVENTS_TOPIC"] = 'platform-events'
os.environ["ANTHROPIC_API_KEY"] = "************************************************************************************************************"
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"
os.environ["VOYAGE_API_KEY"] = "pa-5WI8Vwyw7YUpoWeX4ckHbDd7dN4w74S61zXHQKD0wTE"
os.environ["GOOGLE_API_KEY"] = "AIzaSyAA_WvkItpwfiL3D8czRWsT5dbA2iXmF_I"

NEO4J_SERVER = "neo4j://34.134.29.39:7687"
NEO4J_USERNAME = "neo4j"
NEO4J_PASSWORD = "wySsik-cisboq-fizve1"
os.environ["NEO4J_SERVER"] = NEO4J_SERVER
os.environ["NEO4J_USERNAME"] = NEO4J_USERNAME
os.environ["NEO4J_PASSWORD"] = NEO4J_PASSWORD

DEV_GITHUB_SECRET_SERVER = "https://archie-secret-manager-464705070478.us-central1.run.app"
PROD_GITHUB_SECRET_SERVER = "https://archie-secret-manager-648803317587.us-east1.run.app"
os.environ["GITHUB_SECRET_SERVER"] = DEV_GITHUB_SECRET_SERVER

os.environ["LANGCHAIN_TRACING_V2"] = ""
os.environ["LANGCHAIN_ENDPOINT"] = ""
os.environ["LANGCHAIN_API_KEY"] = ""
os.environ["LANGCHAIN_PROJECT"] = ""
