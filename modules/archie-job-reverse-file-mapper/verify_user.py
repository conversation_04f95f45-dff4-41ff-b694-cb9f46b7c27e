import firebase_admin
from firebase_admin import credentials, auth
from firebase_admin.auth import UserNotFoundError

# Initialize Firebase Admin SDK
# Option 1: Using service account key file


def initialize_firebase_with_file():
    cred = credentials.Certificate('path/to/your/serviceAccountKey.json')
    firebase_admin.initialize_app(cred)

# Option 2: Using default credentials (e.g., in Google Cloud environment)


def initialize_firebase_default():
    firebase_admin.initialize_app()

# Function to mark user as verified by UID


def mark_user_verified_by_uid(uid):
    try:
        # Update user's email verification status
        auth.update_user(
            uid,
            email_verified=True
        )
        print(f"Successfully marked user {uid} as verified")

        # Optionally, retrieve and display updated user info
        user = auth.get_user(uid)
        print(f"User email: {user.email}")
        print(f"Email verified: {user.email_verified}")

        return True

    except UserNotFoundError:
        print(f"No user found with UID: {uid}")
        return False
    except Exception as e:
        print(f"Error updating user: {e}")
        return False

# Function to mark user as verified by email


def mark_user_verified_by_email(email):
    try:
        # First, get user by email
        user = auth.get_user_by_email(email)

        # Then update their verification status
        auth.update_user(
            user.uid,
            email_verified=True
        )
        print(f"Successfully marked user {email} as verified")
        print(f"User UID: {user.uid}")
        print(f"Email verified: True")

        return True

    except UserNotFoundError:
        print(f"No user found with email: {email}")
        return False
    except Exception as e:
        print(f"Error updating user: {e}")
        return False

# Function to mark multiple users as verified


def mark_multiple_users_verified(user_identifiers, by='uid'):
    """
    Mark multiple users as verified

    Args:
        user_identifiers: List of UIDs or emails
        by: 'uid' or 'email' to specify identifier type
    """
    results = {'success': [], 'failed': []}

    for identifier in user_identifiers:
        if by == 'uid':
            success = mark_user_verified_by_uid(identifier)
        elif by == 'email':
            success = mark_user_verified_by_email(identifier)
        else:
            print(f"Invalid identifier type: {by}")
            continue

        if success:
            results['success'].append(identifier)
        else:
            results['failed'].append(identifier)

    print(f"\nSummary:")
    print(f"Successfully verified: {len(results['success'])} users")
    print(f"Failed: {len(results['failed'])} users")

    return results

# Function to list all unverified users and optionally verify them


def find_and_verify_unverified_users(verify=False, limit=1000):
    """
    Find all unverified users and optionally mark them as verified

    Args:
        verify: If True, will mark all found unverified users as verified
        limit: Maximum number of users to process
    """
    unverified_users = []
    page = auth.list_users()

    while page:
        for user in page.users:
            if not user.email_verified and user.email:
                unverified_users.append({
                    'uid': user.uid,
                    'email': user.email
                })

                if len(unverified_users) >= limit:
                    break

        if len(unverified_users) >= limit:
            break

        # Get next page
        page = page.get_next_page()

    print(f"Found {len(unverified_users)} unverified users")

    if verify and unverified_users:
        print("Marking users as verified...")
        uids = [user['uid'] for user in unverified_users]
        mark_multiple_users_verified(uids, by='uid')
    else:
        # Just display the unverified users
        for user in unverified_users[:10]:  # Show first 10
            print(f"- {user['email']} (UID: {user['uid']})")
        if len(unverified_users) > 10:
            print(f"... and {len(unverified_users) - 10} more")

    return unverified_users


# Main execution
if __name__ == "__main__":
    # Initialize Firebase Admin SDK (choose one method)
    initialize_firebase_default()  # or use initialize_firebase_default()

    # Example 1: Mark a single user as verified by UID
    user_uid = "FGfAlt2ifwX2TisFeL2CyNFPgWw1"
    mark_user_verified_by_uid(user_uid)

    # # Example 2: Mark a single user as verified by email
    # user_email = "<EMAIL>"
    # mark_user_verified_by_email(user_email)

    # # Example 3: Mark multiple users as verified
    # user_uids = ["uid1", "uid2", "uid3"]
    # mark_multiple_users_verified(user_uids, by='uid')

    # # Example 4: Find all unverified users (without verifying them)
    # find_and_verify_unverified_users(verify=False, limit=100)

    # Example 5: Find and verify all unverified users (use with caution!)
    # find_and_verify_unverified_users(verify=True, limit=100)
