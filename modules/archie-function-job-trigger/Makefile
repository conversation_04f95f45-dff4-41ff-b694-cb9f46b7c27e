ENV ?= dev

all: deploy-functions

init:
	pip install -r requirements.txt

# Deploy all functions
deploy-functions:
	@echo "Deploying all functions using config from env-$(ENV).yaml"
	python scripts/deploy_functions.py --config=env_config/env-$(ENV).yaml

# Deploy single function
deploy-single-function:
	@if [ -z "$(function)" ]; then \
		echo "Error: function name not provided. Use 'make deploy-single-function function=<function-name>'"; \
		exit 1; \
	fi; \
	echo "Deploying function $(function) using config from env-$(ENV).yaml"; \
	python scripts/deploy_functions.py --config=env_config/env-$(ENV).yaml --function=$(function)

# Deploy to specific environments
deploy-dev:
	$(MAKE) deploy-functions ENV=dev

# Dry run
dry-run:
	python scripts/deploy_functions.py --config=env_config/env-$(ENV).yaml --dry-run

.PHONY: all init deploy-functions deploy-single-function deploy-dev dry-run