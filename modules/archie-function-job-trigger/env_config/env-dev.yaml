project_id: blitzy-os-dev
region: us-central1
service_account: <EMAIL>
runtime: python312
timeout: 540
gcs_bucket: blitzy-os-internal
blob_name: public-samples
vpc_connector: platform-connector-dev
topic_name: blitzy-job-tracker

functions:
  document-generator-trigger:
    trigger_topic: generate-document
    job_name: projects/blitzy-os-dev/locations/us-central1/jobs/document-generator
    env_vars:
      FUNCTION_TYPE: "document"

  repo-structure-generator-trigger:
    trigger_topic: generate-repo-structure
    job_name: projects/blitzy-os-dev/locations/us-central1/jobs/repo-structure-generator
    env_vars:
      FUNCTION_TYPE: "repo"

  thinking-generator-trigger:
    trigger_topic: generate-code-spec
    job_name: projects/blitzy-os-dev/locations/us-central1/jobs/code-spec-generator
    env_vars:
      FUNCTION_TYPE: "thinking"

  code-generator-trigger:
    trigger_topic: generate-code
    job_name: projects/blitzy-os-dev/locations/us-central1/jobs/code-generator
    env_vars:
      FUNCTION_TYPE: "code"

  code-validator-trigger:
    trigger_topic: validate-code
    job_name: projects/blitzy-os-dev/locations/us-central1/jobs/code-validator
    env_vars:
      FUNCTION_TYPE: "validator"

  code-uploader-trigger:
    trigger_topic: upload-code
    job_name: projects/blitzy-os-dev/locations/us-central1/jobs/code-uploader
    env_vars:
      FUNCTION_TYPE: "uploader"

  code-graph-trigger:
    trigger_topic: graph-code
    job_name: projects/blitzy-os-dev/locations/us-central1/jobs/code-graph-generator
    env_vars:
      FUNCTION_TYPE: "graph"

  code-downloader-trigger:
    trigger_topic: download-code
    job_name: projects/blitzy-os-dev/locations/us-central1/jobs/code-downloader
    env_vars:
      FUNCTION_TYPE: "downloader"

  reverse-document-generator-trigger:
    trigger_topic: generate-reverse-document
    job_name: projects/blitzy-os-dev/locations/us-central1/jobs/reverse-document-generator
    env_vars:
      FUNCTION_TYPE: "reverse-document"

  reverse-file-mapper-trigger:
    trigger_topic: generate-reverse-file-map
    job_name: projects/blitzy-os-dev/locations/us-central1/jobs/reverse-file-mapper
    env_vars:
      FUNCTION_TYPE: "reverse-file-mapper"

  reverse-code-generator-trigger:
    trigger_topic: generate-reverse-code
    job_name: projects/blitzy-os-dev/locations/us-central1/jobs/reverse-code-generator
    env_vars:
      FUNCTION_TYPE: "reverse-code-generator"