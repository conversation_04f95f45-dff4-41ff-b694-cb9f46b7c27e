# Use Python 3.12 as base image
FROM python:3.12-slim

# Set working directory
WORKDIR /app

# Install keyring for artifact registry auth
RUN pip install keyrings.google-artifactregistry-auth

# Copy requirements.txt and install Python dependencies
COPY requirements.txt .

RUN --mount=type=secret,id=google_credentials \
    export GOOGLE_APPLICATION_CREDENTIALS=/run/secrets/google_credentials && \
    pip install -r requirements.txt

# Copy your application code
COPY . .

# Command to run your application
CMD ["python", "main.py"]
