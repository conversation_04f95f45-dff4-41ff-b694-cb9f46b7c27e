from google.cloud import pubsub_v1

from blitzy_utils.common import publish_notification

PROJECT_ID = 'blitzy-os-dev'
GENERATE_REPO_STRUCTURE_TOPIC = 'generate-repo-structure'

publisher = pubsub_v1.PublisherClient()

# 'microsoft-excel-v3-pin2pc'
# 'jump-inventory-fulfillment-system-v4-9gkmjx'
# 'femtosense-ai-voice-model-building-env-7xdiy7'

notification_data = {"user_id": "4428a4b5-0dd0-4734-8b95-f5dd81b702a4", "repo_name": "insure-pilot-new-iu8v0a",
                     "project_id": "7639c590-9978-436e-904e-793aaaf47947", "job_id": "821af776-5478-4e8c-801e-439942a5cef1"}
publish_notification(publisher, notification_data, PROJECT_ID, GENERATE_REPO_STRUCTURE_TOPIC)
