from blitzy_utils.common import publish_notification
from google.cloud import pubsub_v1

PROJECT_ID = 'blitzy-os-dev'
UPLOAD_CODE_TOPIC = 'upload-code'

publisher = pubsub_v1.PublisherClient()

notification_data = {"repo_name": "strawberry-xdabaz", "project_id": "bbeb20b1-47fa-499a-a3f2-90f68804b53e", "job_id": "ac57d403-7582-42fc-a2ad-2e127ee099ba", "propagate": True,
                     "user_id": "b388177a-cc79-46eb-bf6f-6b7e1c63fde6", "tech_spec_id": "feb313f3-72c4-4838-ab2d-8d4cb67ce341", "code_gen_id": "ef5cdb95-d6d8-4f5d-92b7-75d5d763a101"}


publish_notification(publisher, notification_data, PROJECT_ID, UPLOAD_CODE_TOPIC)
