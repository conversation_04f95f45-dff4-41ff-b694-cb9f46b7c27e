import os
import json
import datetime
import jwt
import time
import requests
from blitzy_utils.logger import logger
from google.cloud import storage, pubsub_v1
from github import Github, GithubException
import google.auth.transport.requests
import google.oauth2.id_token

from blitzy_utils.consts import CODE_STRUCTURE_NAME, REPO_STRUCTURE_NAME
from blitzy_utils.common import gcs_bucket_walk, publish_notification
from blitzy_utils.enums import ProjectPhase, JobStatus

PROJECT_ID = 'blitzy-platform-prod'
PLATFORM_EVENTS_TOPIC = 'platform-events'
EVENT_DATA = json.dumps({
    'repo_name': 'brian-16dec-zpcir1',
    'project_id': 'a087577e-5040-4b54-990a-cdb1238867e7',
    'job_id': 'e89d2849-a537-423f-bfdf-7f284284ba43',
    'user_id': 'ac937cc1-2bf4-43d0-8d43-14eb2d37520d'
})
GCS_BUCKET_NAME = 'blitzy-platform-prod'
BLOB_NAME = 'public-samples'
GITHUB_SECRET_SERVER = "https://archie-secret-manager-648803317587.us-central1.run.app"
GITHUB_APP_ID = "1086418"
GITHUB_CLIENT_ID = "********************"
GITHUB_CLIENT_SECRET = "72db1b995dc3dd4d90b8d445b24dafbfce86c468"

storage_client = storage.Client()
publisher = pubsub_v1.PublisherClient()


def get_authorized_request(url):
    auth_req = google.auth.transport.requests.Request()
    id_token = google.oauth2.id_token.fetch_id_token(auth_req, url)
    headers = {'Authorization': f'Bearer {id_token}'}
    return headers


def upload_code(event_data_str: str):
    event_data = json.loads(event_data_str)
    repo_name = event_data.get('repo_name')
    project_id = event_data.get('project_id', '')
    job_id = event_data.get('job_id', '')
    propagate = event_data.get('propagate', True)
    user_id = event_data.get('user_id', '')

    logger.info(f"Uploading {repo_name} to GitHub")
    repo, is_new_repo = get_github_repo(repo_name=repo_name, user_id=user_id)
    upload_files_to_github_repo(repo=repo, repo_name=repo_name, is_new_repo=is_new_repo)

    notification_data = {
        "projectId": project_id,
        "jobId": job_id,
        "phase": ProjectPhase.CODE_GENERATION.value,
        "status": JobStatus.DONE.value,
        "user_id": user_id,
        "metadata": {
            "propagate": propagate,
            "repo_name": repo_name,
            "repo_url": repo.html_url,
            "user_id": user_id
        }
    }
    publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)

    return event_data


def get_user_secret_info(user_id: str):
    """Fetch GitHub access token from the secret server."""

    try:
        url = f"{GITHUB_SECRET_SERVER}/secret/{user_id}"
        # headers = get_authorized_request(url)
        response = requests.get(url, headers={})
        logger.info(response)
        if response.status_code == 200:
            data = response.json()
            logger.info(data)
            return data.get('accessToken'), data.get('installationID')
        else:
            logger.info(f"Failed to fetch access token. Status code: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"Error fetching access token: {e}")
        return None


def get_github_installations(access_token: str):
    """Exchange the OAuth code for a user access token."""

    headers = {
        'Accept': 'application/vnd.github+json',
        'Authorization': f'Bearer {access_token}',
        'X-GitHub-Api-Version': '2022-11-28'
    }

    response = requests.get(
        'https://api.github.com/user/installations',
        headers=headers
    )

    if response.status_code == 200:
        data = response.json()
        return data.get('installations')
    else:
        logger.info(f"Failed to get installations. Status code: {response.status_code}")
        return None


def get_github_repo(repo_name: str, user_id: str):
    """Get or create a GitHub repository using GitHub App authentication.

    Handles both user and organization repositories based on the installation target type.
    """
    # Get user access token
    access_token, installationID = get_user_secret_info(user_id=user_id)
    if not access_token:
        raise Exception("Failed to get GitHub access token")

    # Initialize GitHub client with user token
    g = Github(access_token)
    installations = get_github_installations(access_token=access_token)

    login_name = ''
    target_type = 'User'
    for installation in installations:
        if str(installation['id']) == installationID:
            login_name = installation['account']['login']
            target_type = installation['account']['type']
            break

    repo = None
    is_new_repo = False

    try:
        # Handle organization repositories
        if target_type == 'Organization' and login_name:
            org = g.get_organization(login_name)
            full_repo_name = f"{login_name}/{repo_name}"
            try:
                repo = g.get_repo(full_repo_name)
                logger.info(f"Repository {full_repo_name} exists.")
            except GithubException as e:
                if e.status == 404:
                    # Create new organization repository
                    repo = org.create_repo(
                        repo_name,
                        description="Repository created autonomously by BlitzCode"
                    )
                    is_new_repo = True
                else:
                    raise
        else:
            # Handle user repositories
            full_repo_name = f"{login_name}/{repo_name}"
            user = g.get_user()
            try:
                repo = g.get_repo(full_repo_name)
                logger.info(f"Repository {full_repo_name} exists.")
            except GithubException as e:
                if e.status == 404:
                    # Create new user repository
                    repo = user.create_repo(
                        repo_name,
                        description="Repository created autonomously by BlitzCode"
                    )
                    is_new_repo = True
                else:
                    raise

    except GithubException as e:
        logger.error(f"An error occurred: {e}")
        raise

    return repo, is_new_repo


def upload_files_to_github_repo(repo_name: str, repo, is_new_repo: bool):
    bucket_name = GCS_BUCKET_NAME
    folder_prefix = f"{BLOB_NAME}/{repo_name}/"

    # Create a new branch if the repo already exists
    if not is_new_repo:
        # Get the main branch's latest commit
        main_branch = repo.get_branch("main")
        source_commit = main_branch.commit

        # Create a new branch
        branch_name = f"update-{datetime.datetime.now().strftime('%Y%m%d-%H%M%S')}"
        try:
            repo.create_git_ref(f"refs/heads/{branch_name}", source_commit.sha)
        except GithubException as e:
            logger.error(f"Error creating branch: {e}")
            return

    # Walk through the GCS bucket
    for path, _, files in gcs_bucket_walk(storage_client, bucket_name, prefix=folder_prefix):
        logger.info(f"Processing path: {path}")
        for file_name in files:
            blob_path = f"{file_name}"
            bucket = storage_client.get_bucket(bucket_name)
            blob = bucket.blob(blob_path)
            content = blob.download_as_text()
            github_file_path = blob_path[len(folder_prefix):]

            if CODE_STRUCTURE_NAME in github_file_path or REPO_STRUCTURE_NAME in github_file_path:
                continue

            try:
                # Check if file already exists in the repo
                file = repo.get_contents(github_file_path, ref="main" if is_new_repo else branch_name)
                repo.update_file(
                    github_file_path,
                    f"Update {file_name}",
                    content,
                    file.sha,
                    branch='main' if is_new_repo else branch_name
                )
                logger.info(f"Updated {file_name}")
            except:
                # File doesn't exist, so create it
                repo.create_file(
                    github_file_path,
                    f"Add {file_name}",
                    content,
                    branch='main' if is_new_repo else branch_name
                )
                logger.info(f"Created {file_name}")

    # Create pull request if the repo already exists
    if not is_new_repo:
        try:
            pr = repo.create_pull(
                title=f"Update code {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                body="Automated code update",
                head=branch_name,
                base="main"
            )
            logger.info(f"Created pull request: {pr.html_url}")
        except GithubException as e:
            logger.error(f"Error creating pull request: {e}")


if __name__ == "__main__":
    logger.info(f"Uploading code for notification data: {EVENT_DATA}")
    upload_code(event_data_str=EVENT_DATA)
