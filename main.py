# Copyright 2019 Google, LLC.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# [START cloudrun_pubsub_server]
import base64
import json
from json import JSONDecodeError

from blitzy_utils.consts import CORRELATION_ID_VAR_NAME, REQUEST_ID_VAR_NAME
from blitzy_utils.logger import logger
from flask import Flask, g, request
from flask_utils.middleware.request_context import RequestContextMiddleware

from src.process_event import process_payload

app = Flask(__name__)

middleware = RequestContextMiddleware(app, logger=logger)


@app.route("/", methods=["POST"])
def index():
    """Receive and parse Pub/Sub messages."""
    envelope = request.get_json()
    if not isinstance(envelope, dict) or "message" not in envelope:
        msg = "invalid Pub/Sub message format"
        logger.error(f"error: {msg}")
        return f"Bad Request: {msg}", 400

    logger.debug(f"Platform event listener got pub/sub envelope: {envelope}")
    pubsub_message = envelope["message"]
    _set_logging_tracking(pubsub_message)
    try:
        if isinstance(pubsub_message, dict) and "data" in pubsub_message:
            decoded_result = base64.b64decode(pubsub_message["data"]).decode("utf-8").strip()

            payload = json.loads(decoded_result)
            logger.info(f"Processing payload: {payload}")
            process_payload(payload)
    except JSONDecodeError as e:
        logger.error(f"Failed to process payload: {str(e)}")
    return "Event processed successfully", 200


def _set_logging_tracking(pubsub_message: dict):
    """
    Functions just sets `request_id`  and `correlation_id`, to ensure request logs can be tracked across
    services by correlation_id or by request_id
    """
    if pubsub_message.get('attributes'):
        logger.debug(f"Message attributes {pubsub_message['attributes']}")
        request_id = pubsub_message['attributes'].get(REQUEST_ID_VAR_NAME)
        correlation_id = pubsub_message['attributes'].get(CORRELATION_ID_VAR_NAME)
        if correlation_id or request_id:
            logger.set_context(request_id, correlation_id)
            # We also update global state variable, to ensure all outgoing HTTP calls have right tracking headers
            if correlation_id:
                g.request_id = request_id
            if correlation_id:
                g.correlation_id = correlation_id
        else:
            logger.warning(
                "Both `correlation_id` and `request_id` are missing in the message attributes, "
                "will skip setting in logs")
    else:
        logger.warning(
            "For some reason pubsub message doesn't have attributes, "
            f"will skip setting correlation and request IDs in logging. Message: {pubsub_message}")


if __name__ == "__main__":
    # Running application locally, outside of a Google Cloud Environment
    # handles Ctrl-C termination
    print("Starting the server....")
    app.run(host="localhost", port=8081, debug=True)
