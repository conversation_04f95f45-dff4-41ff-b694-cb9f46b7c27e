import os
import json

from blitzy_utils.logger import logger
from google.cloud import storage, pubsub_v1
from langgraph.graph import StateGraph

from lib.blitzy.repo_structure import RepoStructureGenerator<PERSON>elper, RepoStructureState, repo_structure_tools

from blitzy_utils.consts import TECH_SPECIFICATION_NAME, REPO_STRUCTURE_NAME
from blitzy_utils.common import publish_notification, get_tech_spec_name, \
    download_text_file_from_gcs_using_admin_service, upload_text_to_gcs_using_admin_service
from blitzy_utils.enums import ProjectPhase, JobStatus
from blitzy_platform_shared.common.llms import llm_claude_4_sonnet_med_thinking_max_output, llm_o3
from blitzy_platform_shared.document.utils import parse_sections_at_heading_level

EVENT_DATA = os.environ["EVENT_DATA"]
PROJECT_ID = os.environ["PROJECT_ID"]
GCS_BUCKET_NAME = os.environ["GCS_BUCKET_NAME"]
BLOB_NAME = os.environ["BLOB_NAME"]
PLATFORM_EVENTS_TOPIC = os.environ["PLATFORM_EVENTS_TOPIC"]
GENERATE_CODE_SPEC_TOPIC = os.environ["GENERATE_CODE_SPEC_TOPIC"]
ANTHROPIC_API_KEY = os.environ["ANTHROPIC_API_KEY"]
OPENAI_API_KEY = os.environ["OPENAI_API_KEY"]
LANGCHAIN_TRACING_V2 = os.environ["LANGCHAIN_TRACING_V2"]
LANGCHAIN_ENDPOINT = os.environ["LANGCHAIN_ENDPOINT"]
LANGCHAIN_API_KEY = os.environ["LANGCHAIN_API_KEY"]
LANGCHAIN_PROJECT = os.environ["LANGCHAIN_PROJECT"]

storage_client = storage.Client()
publisher = pubsub_v1.PublisherClient()


def generate_repo_structure(event_data_str: str):
    event_data = json.loads(event_data_str)
    repo_name = event_data.get('repo_name')
    repo_id = event_data.get('repo_id')
    project_id = event_data.get('project_id', '')
    job_id = event_data.get('job_id', '')
    propagate = event_data.get('propagate', True)
    user_id = event_data.get('user_id', '')
    tech_spec_id = event_data.get('tech_spec_id', "")
    code_gen_id = event_data.get('code_gen_id', "")
    dest_repo_name = event_data.get('dest_repo_name', repo_name)
    company_id = event_data.get('company_id', '')
    git_project_repo_id = event_data.get('git_project_repo_id', '')

    notification_data = {
        "projectId": project_id,
        "jobId": job_id,
        "phase": ProjectPhase.THINKING.value,
        "status": JobStatus.IN_PROGRESS.value,
        "user_id": user_id,
        "repo_id": repo_id,
        "git_project_repo_id": git_project_repo_id,
        "metadata": {
            "propagate": propagate,
            "repo_name": repo_name
        }
    }
    publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)

    tech_spec_filename = f'{TECH_SPECIFICATION_NAME}.md'
    if tech_spec_id:
        tech_spec_filename = get_tech_spec_name(
            tech_spec_id=tech_spec_id
        )
    blob_name = f"{BLOB_NAME}/{repo_name}/blitzy/documentation"
    technical_spec_file_path = f"{blob_name}/{tech_spec_filename}"
    try:
        tech_spec = download_text_file_from_gcs_using_admin_service(
            file_path=technical_spec_file_path,
            company_id=company_id,
        )
    except Exception as e:
        print(f'retrying download at alternate path: {e}')
        blob_name = f"{BLOB_NAME}/{repo_name}/documentation"
        technical_spec_file_path = f"{blob_name}/{tech_spec_filename}"
        tech_spec = download_text_file_from_gcs_using_admin_service(
            file_path=technical_spec_file_path,
            company_id=company_id,
        )
        blob_name = f"{BLOB_NAME}/{repo_name}/blitzy/documentation"
        destination_blob_name = f"{blob_name}/{tech_spec_filename}"
        upload_text_to_gcs_using_admin_service(
            file_path=destination_blob_name,
            company_id=company_id,
            data=tech_spec,
        )

    tech_spec_parsed = parse_sections_at_heading_level(
        text=tech_spec,
        heading_level=2
    )

    rs_helper = RepoStructureGeneratorHelper(
        generator_llm=llm_claude_4_sonnet_med_thinking_max_output.bind_tools(repo_structure_tools),
        validator_llm=llm_o3.bind_tools(repo_structure_tools)
    )
    rs_generator: StateGraph = rs_helper.create_graph()
    app = rs_generator.compile()
    initial_state = RepoStructureState(
        current_files_list_str="",
        files_list=[],
        src_folder_list=[],
        src_folder_index=0,
        src_file_dict={},
        tech_spec=tech_spec,
        tech_spec_parsed=tech_spec_parsed,
        src_folder_list_content=""
    )
    result = app.invoke(initial_state, {"recursion_limit": 500})
    # upload repo structure
    filename = f"{REPO_STRUCTURE_NAME}.md"
    destination_blob_name = f"{blob_name}/{filename}"
    upload_text_to_gcs_using_admin_service(
        file_path=destination_blob_name,
        company_id=company_id,
        data=json.dumps(result["files_list"]),
    )

    publish_notification(publisher, event_data, PROJECT_ID, GENERATE_CODE_SPEC_TOPIC)
    return event_data


if __name__ == "__main__":
    logger.info(f"Generating repo structure for notification data: {EVENT_DATA}")
    generate_repo_structure(event_data_str=EVENT_DATA)
