import json

from blitzy_utils.logger import logger
from google.api_core import exceptions
from google.cloud import secretmanager

from src.consts import PROJECT_ID
from src.error.errors import SecretNotFound


class SecretManagerOperations:
    """
    A class to handle operations with Google Cloud Secret Manager.
    """
    # Map secret types to their prefixes
    prefix_map = {
        'user': "user-secret",
        'github': "github-secret",
        'azure': "azure-secret"
    }

    def __init__(self):
        self.client = secretmanager.SecretManagerServiceClient()
        self.project_id = PROJECT_ID
        self.parent = f"projects/{PROJECT_ID}"

    def create_or_update_github_secret(self, installation_id, secret_data):
        """
        Create a new secret for a github installation if it doesn't exist, or add a new version if it does.

        :param installation_id: Identifier to create/update secret for
        :param secret_data: Dictionary containing the secret data to be stored as JSON
        :return: Dict containing operation result with keys:
                - operation: Either 'created' or 'updated'
                - secret_name: Full path of the secret
                - version_name: Full path of the version
                - state: State of the secret version
        """
        return self._create_or_update_secret('github', installation_id, secret_data)

    def create_or_update_azure_secret(self, tenant_id: str, secret_data: dict) -> dict:
        """
        Create a new secret for a azure installation if it doesn't exist, or add a new version if it does.

        :param tenant_id: Identifier to create/update secret for
        :param secret_data: Dictionary containing the secret data to be stored as JSON
        :return: Dict containing operation result with keys:
                - operation: Either 'created' or 'updated'
                - secret_name: Full path of the secret
                - version_name: Full path of the version
                - state: State of the secret version
        """
        return self._create_or_update_secret('azure', tenant_id, secret_data)

    def create_or_update_user_secret(self, user_id, secret_data) -> dict:
        """
        Create a new secret for a user if it doesn't exist, or add a new version if it does.

        :param user_id: User identifier to create/update secret for
        :param secret_data: Dictionary containing the secret data to be stored as JSON
        :return: Dict containing operation result with keys:
                - operation: Either 'created' or 'updated'
                - secret_name: Full path of the secret
                - version_name: Full path of the version
                - state: State of the secret version
        """
        return self._create_or_update_secret('user', user_id, secret_data)

    def get_github_secret(self, installation_id, version_id="latest") -> dict:
        """
        Get the value of a github installation secret and parse it as JSON.

        :param installation_id: Identifier to retrieve secret for
        :param version_id: Version of the secret (default is "latest")
        :return: Dictionary containing the decoded JSON data
        """
        return self._get_secret('github', installation_id, version_id)

    def get_azure_secret(self, tenant_id: str, version_id: str = "latest") -> dict:
        """
        Get the value of an azure tenant secret and parse it as JSON.

        :param tenant_id: Identifier to retrieve secret for
        :param version_id: Version of the secret (default is "latest")
        :return: Dictionary containing the decoded JSON data
        """
        return self._get_secret('azure', tenant_id, version_id)

    def get_user_secret(self, user_id, version_id="latest") -> dict:
        """
        Get the value of a user's secret and parse it as JSON.

        :param user_id: User identifier to retrieve secret for
        :param version_id: Version of the secret (default is "latest")
        :return: Dictionary containing the decoded JSON data
        """
        return self._get_secret('user', user_id, version_id)

    def _create_or_update_secret(self, secret_type: str, identifier: str, secret_data: dict) -> dict:
        """
        Generic method to create or update secrets for different types (user, github, azure).

        :param secret_type: Type of secret ('user', 'github', 'azure')
        :param identifier: Unique identifier for the secret (user_id, installation_id, tenant_id)
        :param secret_data: Dictionary containing the secret data to be stored as JSON
        :return: Dict containing operation result with keys:
                - operation: Either 'created' or 'updated'
                - secret_name: Full path of the secret
                - version_name: Full path of the version
                - state: State of the secret version
        """

        if secret_type not in self.prefix_map:
            raise ValueError(f"Invalid secret type: {secret_type}. Must be one of: {list(self.prefix_map.keys())}")

        secret_id = f"{self.prefix_map[secret_type]}-{identifier}"

        try:
            # Convert dict to JSON string
            secret_value = json.dumps(secret_data)

            try:
                # Try to create a new secret
                secret = self.client.create_secret(
                    request={
                        "parent": self.parent,
                        "secret_id": secret_id,
                        "secret": {
                            "replication": {
                                "automatic": {},
                            },
                        },
                    }
                )
                logger.debug(f"Secret {secret_id} created")

                # Add the first version
                version = self.client.add_secret_version(
                    request={
                        "parent": secret.name,
                        "payload": {"data": secret_value.encode("UTF-8")},
                    }
                )
                logger.debug(f"Version {version.name} added to secret {secret_id}")
                return {
                    "operation": "created",
                    "secret_name": secret.name,
                    "version_name": version.name,
                    "state": version.state.name
                }

            except exceptions.AlreadyExists:
                # If secret exists, add a new version
                parent = f"{self.parent}/secrets/{secret_id}"
                logger.info(f"Secret {secret_id} already exists. Adding new version")
                version = self.client.add_secret_version(
                    request={
                        "parent": parent,
                        "payload": {"data": secret_value.encode("UTF-8")},
                    }
                )
                logger.debug(f"Version {version.name} added to secret {secret_id}")
                return {
                    "operation": "updated",
                    "secret_name": parent,
                    "version_name": version.name,
                    "state": version.state.name
                }

        except TypeError as e:
            raise ValueError(f"Invalid JSON data: {str(e)}")
        except Exception as e:
            raise Exception(f"Error managing secret: {str(e)}")

    def _get_secret(self, secret_type: str, identifier: str, version_id: str = "latest") -> dict:
        """
        Generic method to retrieve secrets for different types (user, github, azure).

        :param secret_type: Type of secret ('user', 'github', 'azure')
        :param identifier: Unique identifier for the secret (user_id, installation_id, tenant_id)
        :param version_id: Version of the secret (default is "latest")
        :return: Dictionary containing the decoded JSON data
        """
        error_descriptions = {
            'user': f"user {identifier}",
            'github': f"github installation {identifier}",
            'azure': f"azure tenant {identifier}"
        }

        if secret_type not in self.prefix_map:
            raise ValueError(f"Invalid secret type: {secret_type}. Must be one of: {list(self.prefix_map.keys())}")

        secret_id = f"{self.prefix_map[secret_type]}-{identifier}"

        try:
            name = f"{self.parent}/secrets/{secret_id}/versions/{version_id}"
            logger.debug(f"Accessing secret {secret_id} with version {version_id}")
            response = self.client.access_secret_version(request={"name": name})
            json_string = response.payload.data.decode("UTF-8")
            try:
                return json.loads(json_string)
            except json.JSONDecodeError as e:
                raise ValueError(f"Invalid JSON in stored secret: {str(e)}")
        except exceptions.NotFound as exc:
            raise SecretNotFound(f"Secret for {error_descriptions[secret_type]} not found") from exc
        except Exception as e:
            raise Exception(f"Error accessing secret: {str(e)}")
