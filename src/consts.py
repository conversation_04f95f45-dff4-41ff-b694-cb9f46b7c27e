import base64
import os

from common_models.models import (BlitzyCommitStatus, ReportStatus, Status,
                                  SubscriptionStatus, TechSpecJobType,
                                  UsageType)
from google.cloud import pubsub_v1, secretmanager, storage

from src.api.models import Action, RepoType
from src.api.models import TechSpecJobType as TechSpecJobTypeModel

# Setting up application constants.
JWT_EXPIRY_LENGTH = 3600
AUTHORIZATION_HEADER_NAME = 'Authorization'

# Initialising required environment variable for the app.
ID_TOKEN_HEADER_NAME = os.environ["ID_TOKEN_HEADER_NAME"]
BASIC_AUTH_HEADER_NAME = os.environ["BASIC_AUTH_HEADER_NAME"]
UI_SA_EMAIL = os.environ["UI_SA_EMAIL"]
JWT_AUDIENCE = os.environ["JWT_AUDIENCE"]
PROJECT_ID = os.environ["PROJECT_ID"]
UI_AUTH_SECRET_ID = os.environ["UI_AUTH_SECRET_ID"]
FIREBASE_API_KEY = os.environ["FIREBASE_API_KEY"]
GCS_BUCKET_NAME = os.environ["GCS_BUCKET_NAME"]
BLOB_NAME = os.environ["BLOB_NAME"]
GENERATE_DOCUMENT_TOPIC = os.environ["GENERATE_DOCUMENT_TOPIC"]
GENERATE_CODE_TOPIC = os.environ["GENERATE_CODE_TOPIC"]
GITHUB_TOKEN = os.environ["GITHUB_TOKEN"]
STRIPE_BLITZY_PRO_PRICE_ID = os.environ["STRIPE_BLITZY_PRO_PRICE_ID"]
DOCUMENT_EXPIRY_TIME = os.getenv("DOCUMENT_EXPIRY_TIME", 900)
"""Document expiry time in seconds. Default is set to 15 mins i.e. 900 seconds"""

SOFTWARE_REQUIREMENTS_FILE_SIZE = os.getenv("SOFTWARE_REQUIREMENTS_FILE_SIZE", 5)
"""Software requirements file size in MB. Default is 5MB."""

TECHNICAL_SPECIFICATION_FILE_SIZE = os.getenv("SOFTWARE_REQUIREMENTS_FILE_SIZE", 5)
"""Technical specification file size in MB. Default is 5MB."""

GENERATE_REPO_STRUCTURE_TOPIC = os.getenv("GENERATE_REPO_STRUCTURE_TOPIC", "generate-repo-structure")
"""Topic used to trigger repo generation."""

FREE_USER_DOCUMENT_SUBMISSION_LIMIT = int(os.getenv("FREE_USER_DOCUMENT_SUBMISSION_LIMIT", "10"))
"""Free user document submission limit."""

FREE_USER_LIMIT_RESETTING_TIME_HR = int(os.getenv("FREE_USER_LIMIT_RESETTING_TIME_HR", "24"))
"""Free user limit resetting time in hours."""

PRO_USER_TRIAL_SUBMISSION_LIMIT = int(os.getenv("PRO_USER_TRIAL_SUBMISSION_LIMIT", "100"))
"""System-wide pro user trial document submission count. Default value is 100."""

PRO_USER_ALLOWED_JOB_RUNS = int(os.getenv("PRO_USER_ALLOWED_JOB_RUNS"))
"""Allowed job runs for pro user."""

GITHUB_CLIENT_ID = os.environ["GITHUB_CLIENT_ID"]
"""Github client id."""

GITHUB_CLIENT_SECRET = os.environ["GITHUB_CLIENT_SECRET"]
"""Github client secret."""

HUBSPOT_API_KEY = os.environ["HUBSPOT_API_KEY"]
"""Hubspot api key"""

IPINFO_TOKEN = os.environ["IPINFO_TOKEN"]
"""IP info token"""

IPINFO_API_URL = os.environ["IPINFO_API_URL"]
"""IP info API url"""

SENDGRID_API_KEY = os.environ["SENDGRID_API_KEY"]
"""Sendgrid api key"""

SENDGRID_API_URL = os.environ["SENDGRID_API_URL"]
"""Sendgrid api url"""

STRIPE_BLITZY_PRO_PRODUCT_ID = os.environ["STRIPE_BLITZY_PRO_PRODUCT_ID"]
"""Stripe Blitzy Pro Product Id"""

ENVIRONMENT = os.getenv("ENVIRONMENT", "dev")
"""Environment"""

SERVICE_URL_SECRET = os.environ["SERVICE_URL_SECRET"]
"""Secret manager service url"""

PLATFORM_URL = os.environ["PLATFORM_URL"]
"""Platform url"""

# Initialising clients.
STORAGE_CLIENT = storage.Client()
SECRETS_CLIENT = secretmanager.SecretManagerServiceClient()
publisher = pubsub_v1.PublisherClient()

STATUS_500_RESPONSE = "Something went wrong. Please try again."

STATUS_ORDER = {
    Status.FAILED: 0,
    Status.TODO: 1,
    Status.SUBMITTED: 2,
    Status.QUEUED: 3,
    Status.IN_PROGRESS: 4,
    Status.DONE: 5
}

STRIPE_STATUS_MAPPING = {
    "trialing": SubscriptionStatus.TRIALING,
    "active": SubscriptionStatus.ACTIVE,
    "canceled": SubscriptionStatus.CANCELLED,
    "past_due": SubscriptionStatus.PAST_DUE
}


def is_dev_env() -> bool:
    """
    Check if the current environment is development.

    Returns:
        bool: True if running in development environment, False otherwise
    """
    return ENVIRONMENT.lower() in ['dev', 'qa']


IN_PROGRESS_STATUS = [Status.QUEUED, Status.IN_PROGRESS]


def load_private_key_from_base64(base64_key: str) -> str:
    try:
        # Decode base64 to bytes
        key_bytes = base64.b64decode(base64_key)
        # Convert bytes to string
        private_key = key_bytes.decode("utf-8")
        return private_key
    except Exception as e:
        raise ValueError(f"Failed to decode private key: {str(e)}")


GITHUB_APP_ID = os.environ["GITHUB_APP_ID"]
GITHUB_BASE64_PRIVATE_KEY = os.environ["GITHUB_PRIVATE_KEY"]

GITHUB_PRIVATE_KEY = load_private_key_from_base64(GITHUB_BASE64_PRIVATE_KEY)

TECH_SPEC_JOB_TYPE_TO_MODEL_MAPPER = {
    TechSpecJobTypeModel.NEW_PRODUCT: TechSpecJobType.NEW_PRODUCT,
    TechSpecJobTypeModel.EXISTING_PRODUCT: TechSpecJobType.EXISTING_PRODUCT,
    TechSpecJobTypeModel.ADD_FEATURE: TechSpecJobType.ADD_FEATURE,
    TechSpecJobTypeModel.REFACTOR_CODE: TechSpecJobType.REFACTOR_CODE,
    TechSpecJobTypeModel.SYNC_TECH_SPEC: TechSpecJobType.SYNC_TECH_SPEC,
    TechSpecJobTypeModel.FIX_BUGS: TechSpecJobType.FIX_BUGS,
    TechSpecJobTypeModel.FIX_CVES: TechSpecJobType.FIX_CVES,
    TechSpecJobTypeModel.ADD_TESTING: TechSpecJobType.ADD_TESTING,
    TechSpecJobTypeModel.DOCUMENT_CODE: TechSpecJobType.DOCUMENT_CODE,
    TechSpecJobTypeModel.CUSTOM: TechSpecJobType.CUSTOM,
}

LATEST = "latest"

REPO_TYPE_PYDANTIC_TO_MODEL_MAPPER = {
    RepoType.SOURCE: UsageType.SOURCE,
    RepoType.TARGET: UsageType.TARGET,
}

IN_PROGRESS_REPORT_STATUS = [ReportStatus.IN_PROGRESS, ReportStatus.QUEUED]

GITHUB_REPO_PR_ACTION = {
    Action.MERGE: BlitzyCommitStatus.MERGED,
    Action.CLOSE: BlitzyCommitStatus.CLOSED
}

GITHUB_CREATE_REPO_DESCRIPTION = "Created by Blitzy"
