import os

from google.cloud import pubsub_v1, secretmanager, storage

# Environment Variables
SERVICE_NAME = os.environ.get("SERVICE_NAME", "flask-microservice")
PORT = int(os.environ.get("PORT", 8080))
PROJECT_ID = os.environ.get("PROJECT_ID")
LOG_LEVEL = os.environ.get("LOG_LEVEL", "INFO")
ENVIRONMENT = os.environ.get("ENVIRONMENT")

GCS_BUCKET_NAME = os.environ["GCS_BUCKET_NAME"]

# Authentication Constants
AUTHORIZATION_HEADER_NAME = "Authorization"
ID_TOKEN_HEADER_NAME = os.environ.get("ID_TOKEN_HEADER_NAME", "id-token")
BASIC_AUTH_HEADER_NAME = os.environ.get("BASIC_AUTH_HEADER_NAME", "basic-auth")

JWT_EXPIRY_LENGTH = 3600  # 1 hour in seconds

# Google Cloud Clients
storage_client = storage.Client()
secrets_client = secretmanager.SecretManagerServiceClient()
publisher = pubsub_v1.PublisherClient()

GKE_ZONE = os.environ["GKE_ZONE"]
GKE_CLUSTER_NAME = os.environ["GKE_CLUSTER"]
WINDOWS_JOB_IMAGE = os.environ["WINDOWS_JOB_IMAGE"]

WINDOWS_JOB_REQUESTS_CPU = os.environ["WINDOWS_JOB_REQUESTS_CPU"]
"""CPU requested for each job"""

WINDOWS_JOB_REQUESTS_MEMORY = os.environ["WINDOWS_JOB_REQUESTS_MEMORY"]
"""Memory requested for each job in Gi"""

WINDOWS_JOB_LIMITS_CPU = os.environ["WINDOWS_JOB_LIMITS_CPU"]
"""CPU at max for each job."""

WINDOWS_JOB_LIMITS_MEMORY = os.environ["WINDOWS_JOB_LIMITS_MEMORY"]
"""Memory at max for each job in Gi"""

WINDOWS_JOB_NAMESPACE = os.environ.get("WINDOWS_JOB_NAMESPACE", "default")
"""Namespace for Windows jobs"""

WINDOWS_JOB_IMAGE_PULL_POLICY = os.environ.get("WINDOWS_JOB_IMAGE_PULL_POLICY", "IfNotPresent")
"""Image pull policy for Windows jobs"""

GCP_SERVICE_ACCOUNT_SECRET_NAME = os.environ.get("GCP_SERVICE_ACCOUNT_SECRET_NAME", "google-service-account")
"""Name of the secret containing the GCP service account credentials"""


def is_dev_env():
    """
    Determine if running in development environment.
    Cloud Run sets K_SERVICE environment variable, so we can use that to detect production.
    """
    # If K_SERVICE is set, we're in Cloud Run (production)
    if os.environ.get("K_SERVICE"):
        return False

    # Check explicit ENVIRONMENT variable
    if ENVIRONMENT.lower() in ["dev", "development", "local"]:
        return True

    # Default to production to be safe
    return False
