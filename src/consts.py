import base64
import os
from enum import Enum

from blitzy_utils.consts import get_project_id

from blitzy_utils.consts import get_project_id

GITHUB_WEBHOOK_SECRET = os.environ["GITHUB_WEBHOOK_SECRET"]


def load_private_key_from_base64(base64_key: str) -> str:
    try:
        # Decode base64 to bytes
        key_bytes = base64.b64decode(base64_key)
        # Convert bytes to string
        private_key = key_bytes.decode("utf-8")
        return private_key
    except Exception as e:
        raise ValueError(f"Failed to decode private key: {str(e)}")


GITHUB_APP_ID = os.environ["GITHUB_APP_ID"]
GITHUB_BASE64_PRIVATE_KEY = os.environ["GITHUB_PRIVATE_KEY"]

GITHUB_PRIVATE_KEY = load_private_key_from_base64(GITHUB_BASE64_PRIVATE_KEY)

PROJECT_ID = get_project_id()

GITHUB_CREATE_REPO_DESCRIPTION = "Created by Blitzy"


class PRStatus(Enum):
    """Enum for Pull Request status values."""

    PENDING = "PENDING"
    MERGED = "MERGED"
    REJECTED = "REJECTED"
    CLOSED = "CLOSED"
    DONE = "DONE"


class GitHubPRState(Enum):
    """Enum for GitHub PR state values."""

    OPEN = "open"
    CLOSED = "closed"


AZURE_BASE_URL = "https://dev.azure.com"
