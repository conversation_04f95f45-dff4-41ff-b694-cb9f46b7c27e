import base64
from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Dict, Optional, Union


@dataclass
class BlitzyFileContent:
    """
    Unified file content representation across different SCM platforms.
    Simple object that stores file data and can hold platform-specific metadata.
    """

    # Core attributes - what every file has
    path: str
    content: Union[str, bytes]

    # Common metadata
    sha: Optional[str] = None
    size: Optional[int] = None
    encoding: str = "utf-8"  # Simplified to string
    ref: Optional[str] = None  # branch, tag, or commit

    # Timestamps and attribution
    last_modified: Optional[datetime] = None
    author: Optional[str] = None
    commit_message: Optional[str] = None

    # Store any platform-specific data here
    platform_data: Dict[str, Any] = field(default_factory=dict)

    @property
    def decoded_content(self) -> str:
        """Get content as decoded string"""
        if self.encoding == "base64":
            # If content is bytes, decode to str first
            if isinstance(self.content, (bytes, bytearray, memoryview)):
                content_bytes = bytes(self.content)
            elif isinstance(self.content, str):
                content_bytes = self.content.encode('utf-8')
            else:
                raise TypeError("Unsupported content type for decoding")
            return base64.b64decode(content_bytes).decode('utf-8')
        elif isinstance(self.content, bytes):
            return self.content.decode(self.encoding)
        return str(self.content)

    @property
    def encoded_content(self) -> str:
        """Get content as base64 encoded string"""
        if isinstance(self.content, (bytes, bytearray, memoryview)):
            content_bytes = bytes(self.content)
            return base64.b64encode(content_bytes).decode('utf-8')
        elif isinstance(self.content, str) and self.encoding != "base64":
            return base64.b64encode(self.content.encode('utf-8')).decode('utf-8')
        elif isinstance(self.content, str):
            return self.content
        else:
            raise TypeError("Unsupported content type for encoding")

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses"""
        result = {
            'path': self.path,
            'content': self.content if isinstance(self.content, str) else self.encoded_content,
            'encoding': self.encoding,
            'size': self.size,
            'sha': self.sha,
            'ref': self.ref,
            'last_modified': self.last_modified.isoformat() if self.last_modified else None,
            'author': self.author,
            'commit_message': self.commit_message,
        }
        # Merge platform_data at the same level for easy access
        result.update(self.platform_data)

        if not isinstance(result['content'], (str, bytes, dict)):
            raise TypeError(f"Content is not string, bytes, or dict. Actual type: {type(result['content'])}")
        return result
