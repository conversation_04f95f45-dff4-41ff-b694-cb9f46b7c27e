from typing import Optional

from common_models.db_client import get_db_session
from common_models.models import GeoLocation
from sqlalchemy.orm import Session


def get_user_geolocation_by_id(user_id: str, session: Optional[Session] = None) -> Optional[GeoLocation]:
    """
    Get user's geolocation information by user ID.
    :param user_id: User ID
    :param session: Session if any
    :return: GeoLocation if found
    """
    with get_db_session(session) as session:
        geolocation = (
            session.query(GeoLocation)
            .filter(GeoLocation.user_id == user_id)
            .filter(GeoLocation.is_deleted.is_(False))
            .order_by(GeoLocation.created_at.desc())
            .first()
        )

        if geolocation and not session:
            session.expunge(geolocation)
        return geolocation
