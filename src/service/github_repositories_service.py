from typing import Optional

from common_models.db_client import get_db_session
from common_models.models import GitHubRepository
from sqlalchemy.orm import Session


def save_github_repository(repository: GitHubRepository, session: Optional[Session] = None) -> GitHubRepository:
    """
    Saves a GitHub repository object into the database.
    :param repository: Instance of `GitHubRepository` to be saved into the database.
    :param session: Client session if any.
    :return: The saved `GitHubRepository` object after persisting it into the database.
    """
    with get_db_session(session) as session:
        session.add(repository)
        session.flush()
        return repository
