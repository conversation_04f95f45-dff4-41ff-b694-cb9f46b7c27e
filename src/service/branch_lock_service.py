from typing import Optional

from common_models.db_client import get_db_session
from common_models.models import BranchLock
from sqlalchemy.orm import Session


def has_active_lock(branch_pattern_id: str, session: Optional[Session] = None):
    """
    Check if an active lock exists for a given branch pattern.

    :param branch_pattern_id: Identifier of the branch pattern to check.
    :type branch_pattern_id: str
    :param session: Optional database session to use.
    :type session: Optional[Session]
    :return: True if an active lock is found, False otherwise.
    :rtype: bool
    """
    with get_db_session(session) as session:
        active_lock_exists = session.query(BranchLock).filter_by(
            branch_pattern_id=branch_pattern_id, is_active=True
        ).first() is not None

        return active_lock_exists


def save_branch_lock(branch_lock: BranchLock, session: Optional[Session] = None):
    """
    Save a branch lock to the database.

    :param branch_lock: The BranchLock object to save.
    :param session: Optional; A database session to use. If not provided, a new
        session will be created.
    :return: None
    """
    with (get_db_session(session) as session):
        session.add(branch_lock)
        session.flush()
