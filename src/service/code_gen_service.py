from typing import Any, Dict, Optional

from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import CodeGeneration
from sqlalchemy.orm import Session

from src.consts import LATEST
from src.error.errors import CodeGenerationUpdateError


def get_code_gen_by_project_id(project_id: str, session: Optional[Session] = None) -> Optional[CodeGeneration]:
    """
    Get project code generation metadata by project id.
    :param project_id: Project ID.
    :param session: Client session if any.
    :return: Code generation object.
    """
    with get_db_session(session) as session:
        code_generation = (session.query(CodeGeneration).filter(CodeGeneration.project_id == project_id).first())
        if code_generation and not session:
            session.expunge(code_generation)
        return code_generation


def save_code_gen(code_gen: CodeGeneration, session: Optional[Session] = None) -> CodeGeneration:
    """
    Save code generation to database.
    :param code_gen: Code gen object to save.
    :param session: Session if any.
    :return: Code gen object.
    """
    with get_db_session(session) as session:
        session.add(code_gen)
        session.flush()
        return code_gen


def update_code_gen_by_project_id(project_id: str, update_payload: Dict[Any, Any], session: Optional[Session] = None):
    """
    Update code generation metadata by project id.
    :param project_id: Project ID.
    :param update_payload: Update payload.
    :param session: Session if any.
    """
    with get_db_session(session) as session:
        code_generation_updated = session.query(CodeGeneration).filter(
            CodeGeneration.project_id == project_id,
        ).update(update_payload)

        if not code_generation_updated:
            logger.error(f"Failed to update code generation table for project {project_id}")
            raise CodeGenerationUpdateError(
                f"Failed to update code generation for project {project_id}")


def get_latest_code_gen_by_project_id(project_id: str, session: Optional[Session] = None) -> Optional[CodeGeneration]:
    """
    Retrieve the latest code generation record associated with a given project ID.

    :param project_id: The ID of the project for which the latest CodeGeneration
        record is being fetched.
    :param session: An optional database session. If none is provided, a new
        session will be created.
    :return: The latest CodeGeneration record associated with the given
        `project_id`, or None if no such record exists.
    """
    with get_db_session(session) as session:
        code_gen = (session.query(CodeGeneration).filter(CodeGeneration.project_id == project_id).order_by(
            CodeGeneration.created_at.desc()).first())
        if code_gen and not session:
            session.expunge(code_gen)
        return code_gen


def get_tech_spec_by_project_id_and_code_gen_id(project_id: str, code_gen_id: str,
                                                session: Optional[Session] = None) -> Optional[CodeGeneration]:
    """
    Fetches the code generation specification for the given project ID and
    code generation ID. If the code generation ID is set to 'LATEST', it
    retrieves the latest code generation associated with the project ID. If
    a specific code generation ID is provided, it fetches the corresponding
    record. Optionally, a database session can be provided, otherwise a new
    session is created and used internally.

    :param project_id: The unique identifier for the project.
    :param code_gen_id: The identifier of the code generation record. Use
                        'LATEST' to retrieve the most recent code generation
                        associated with the project.
    :param session: Optional database session object for querying the
                    database.
    """
    # Get latest code gen information.
    if code_gen_id == LATEST:
        return get_latest_code_gen_by_project_id(project_id, session)

    # Get code gen by ID.
    with get_db_session(session) as session:
        code_gen = (session.query(CodeGeneration).filter(CodeGeneration.project_id == project_id,
                                                         CodeGeneration.id == code_gen_id).first())
        if code_gen and not session:
            session.expunge(code_gen)
        return code_gen


def get_code_gen_by_tech_spec_id(tech_spec_id: str, session: Optional[Session] = None):
    """
    Fetches a CodeGeneration instance by the provided technical specification ID.

    :param tech_spec_id: The technical specification ID used to query the database.
    :param session: Optional database session, used for querying. If not provided, a
                    session will be created and managed within this function.
    :return: A CodeGeneration instance if found in the database, otherwise None.
    """
    with get_db_session(session) as session:
        code_gen = (session.query(CodeGeneration).filter(CodeGeneration.tech_spec_id == tech_spec_id).first())
        if code_gen and not session:
            session.expunge(code_gen)
        return code_gen


def get_code_gen_by_project_id_and_tech_spec_id(project_id: str, tech_spec_id: str,
                                                session: Optional[Session] = None) -> Optional[CodeGeneration]:
    """
    Fetches the code generation specification for the given project ID and
    technical specification ID. Optionally, a database session can be provided, otherwise a new
    session is created and used internally.

    :param project_id: The unique identifier for the project.
    :param tech_spec_id: The identifier of the code generation record.
    :param session: Optional database session object for querying the
                    database.
    """
    # Get code gen by tech spec ID and project ID.
    with get_db_session(session) as session:
        code_gen = (session.query(CodeGeneration).filter(CodeGeneration.project_id == project_id,
                                                         CodeGeneration.tech_spec_id == tech_spec_id).first())
        if code_gen and not session:
            session.expunge(code_gen)
        return code_gen
