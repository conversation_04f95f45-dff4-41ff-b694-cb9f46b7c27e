from typing import Optional

from common_models.db_client import get_db_session
from common_models.models import CodeGeneration
from sqlalchemy.orm import Session


def get_code_gen_by_id(code_gen_id: str, session: Optional[Session] = None) -> Optional[CodeGeneration]:
    """
    Retrieve a CodeGeneration record by its unique identifier.

    :param code_gen_id: The unique identifier of the CodeGeneration record.
    :type code_gen_id: str
    :param session: Optional database session to use for the query. If not provided, a new session will be created.
    :type session: Optional[Session]
    :return: The CodeGeneration record if found, otherwise None.
    :rtype: Optional[CodeGeneration]
    """
    with get_db_session(session) as session:
        code_gen = session.query(CodeGeneration).filter(CodeGeneration.id == code_gen_id).first()

        if code_gen and not session:
            session.expunge(code_gen)
        return code_gen
