from typing import Optional

from common_models.db_client import get_db_session
from common_models.models import CodeGeneration
from sqlalchemy.orm import Session


def get_code_gen_by_id(code_gen_id: str, session: Optional[Session] = None) -> Optional[CodeGeneration]:
    with get_db_session(session) as session:
        code_gen = (session.query(CodeGeneration).filter(CodeGeneration.id == code_gen_id).first())

        if code_gen and not session:
            session.expunge(code_gen)

        return code_gen


def update_code_gen_by_id(code_gen_id: str, update_payload: dict, session: Optional[Session] = None):
    with get_db_session(session) as session:
        code_gen_updated = session.query(CodeGeneration).filter(CodeGeneration.id == code_gen_id).update(
            update_payload)
        session.flush()
        return code_gen_updated
