from typing import Any, Dict, Optional

from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import SoftwareRequirement
from sqlalchemy.orm import Session

from src.error.errors import SoftwareRequirementUpdateError


def save_software_req(software_req: SoftwareRequirement, session: Optional[Session] = None) -> SoftwareRequirement:
    """
    Save software req to database.
    :param software_req: Software req object to save.
    :param session: Session if any.
    :return: software req object.
    """
    with get_db_session(session) as session:
        session.add(software_req)
        session.flush()
        return software_req


def update_software_req_by_project_id(project_id: str, update_payload: Dict[Any, Any],
                                      session: Optional[Session] = None):
    """
    Update software requirement metadata by project id.
    :param project_id: Project ID for filtering.
    :param update_payload: Update payload.
    :param session: Client session if any.
    """
    with get_db_session(session) as session:
        software_updated = session.query(SoftwareRequirement).filter(
            SoftwareRequirement.project_id == project_id,
        ).update(update_payload)

        if not software_updated:
            logger.error(f"Failed to update project {project_id}")
            raise SoftwareRequirementUpdateError(f"Failed to update software requirements for project {project_id}")


def get_software_req_by_project_id(project_id: str, session: Optional[Session] = None) -> Optional[SoftwareRequirement]:
    """
    Get software requirement by project id.
    :param project_id: Project ID.
    :param session: Session if any.
    :return: Software requirement if found.
    """
    with get_db_session(session) as session:
        software_req = (session.query(SoftwareRequirement)
                        .filter(SoftwareRequirement.project_id == project_id).first())
        if software_req and not session:
            session.expunge(software_req)
        return software_req
