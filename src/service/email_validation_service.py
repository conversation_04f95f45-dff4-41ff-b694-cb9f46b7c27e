from enum import Enum
from typing import Op<PERSON>, <PERSON><PERSON>, Union

import requests
from blitzy_utils.logger import logger
from email_validator import EmailNotValidError
from email_validator import validate_email as validate_email_syntax

from src.api.models import Status400
from src.consts import SENDGRID_API_KEY, SENDGRID_API_URL, is_dev_env
from src.error.base_error import BlitzyError


class EmailValidationError(Enum):
    INVALID_FORMAT = "INVALID_FORMAT"
    PERSONAL_EMAIL = "PERSONAL_EMAIL"
    DISPOSABLE_EMAIL = "DISPOSABLE_EMAIL"
    VALIDATION_FAILED = "VALIDATION_FAILED"


class EmailValidator:
    PERSONAL_EMAIL_DOMAINS = {
        'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
        'icloud.com', 'aol.com', 'protonmail.com', 'mail.com',
        'zoho.com', 'yandex.com'
    }

    DISPOSABLE_EMAIL_DOMAINS = {
        'temp-mail.org', 'tempmail.com', 'guerrillamail.com', '10minutemail.com',
        'mailinator.com', 'throwawaymail.com', 'yopmail.com', 'tempmail.net',
        'sharklasers.com', 'getairmail.com', 'temp-mail.io', 'fakeinbox.com',
        'trashmail.com', 'emailondeck.com', 'tempinbox.com', 'dispostable.com',
        'maildrop.cc', 'mailnesia.com', 'tempmailaddress.com', 'tempmail.dev'
    }

    def validate_email(self, email: str) -> Tuple[bool, Optional[EmailValidationError]]:
        """
        Validates an email address using email-validator package for syntax,
        domain check and SendGrid API for deliverability.

        Args:
            email: The email address to validate

        Returns:
            Tuple of (is_valid: bool, error_type: Optional[EmailValidationError])
        """
        # Step 1: Validate email syntax using email-validator
        try:
            # Validate and get normalized form of email
            validate_email_syntax(email, check_deliverability=False)
        except EmailNotValidError:
            return False, EmailValidationError.INVALID_FORMAT

        # Step 2: Check if it's a personal email domain
        if self._is_personal_email(email):
            return False, EmailValidationError.PERSONAL_EMAIL

        # Step 3: Check if it's a disposable email domain
        if not is_dev_env() and self._is_disposable_email(email):
            return False, EmailValidationError.DISPOSABLE_EMAIL

        # Step 4: Real-time validation with SendGrid
        if not self._validate_with_sendgrid(email):
            return False, EmailValidationError.VALIDATION_FAILED

        return True, None

    def _is_personal_email(self, email: str) -> bool:
        """Checks if the email domain is in the personal email blacklist."""
        domain = email.split('@')[-1].lower()
        return domain in self.PERSONAL_EMAIL_DOMAINS

    def _is_disposable_email(self, email: str) -> bool:
        """Checks if the email domain is in the disposable email blacklist."""
        domain = email.split('@')[-1].lower()
        return domain in self.DISPOSABLE_EMAIL_DOMAINS

    def _validate_with_sendgrid(self, email: str) -> bool:
        """
        Validates email using SendGrid's Email Validation API.

        Returns:
            bool: True if email is valid, False otherwise
        Raises:
            BlitzyError: If there's an error with the API call
        """
        try:
            headers = {
                'Authorization': f'Bearer {SENDGRID_API_KEY}',
                'Content-Type': 'application/json'
            }

            payload = {
                'email': email,
                'source': 'Registration'
            }

            response = requests.post(
                SENDGRID_API_URL,
                headers=headers,
                json=payload
            )

            if response.status_code != 200:
                logger.error(f"SendGrid validation failed: {response.text}")

            result = response.json().get('result', {})
            verdict = result.get('verdict', 'Valid')

            return verdict.lower() != 'invalid'

        except Exception as e:
            logger.error(f"Unexpected error during email validation: {str(e)}")
            raise BlitzyError(
                message="Email validation failed",
                status_code=500
            )


def validate_registration_email(email: str) -> Tuple[Union[Status400, None], Optional[int]]:
    """
    Validates registration email and returns appropriate Status400 response if invalid.

    Args:
        email: The email address to validate

    Returns:
        Tuple[Union[Status400, None], Optional[int]]: Returns (None, None) if valid,
        or (Status400 object, 400) if invalid
    """
    validator = EmailValidator()
    is_valid, error_type = validator.validate_email(email)

    if not is_valid:
        if error_type == EmailValidationError.INVALID_FORMAT:
            return Status400(
                message="Invalid email format",
            ), 400
        elif error_type == EmailValidationError.PERSONAL_EMAIL:
            return Status400(
                message="Please use your work email to register",
            ), 400
        elif error_type == EmailValidationError.DISPOSABLE_EMAIL:
            return Status400(
                message="Temporary email address is not allowed",
            ), 400
        elif error_type == EmailValidationError.VALIDATION_FAILED:
            return Status400(
                message="Please enter valid work email",
            ), 400

    return None, None
