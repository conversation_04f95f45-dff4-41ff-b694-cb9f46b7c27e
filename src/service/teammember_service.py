from typing import List, Optional

from common_models.db_client import get_db_session
from common_models.models import Team<PERSON>ember
from sqlalchemy.orm import Session


def get_team_ids_by_user_id(user_id: str, session: Optional[Session] = None) -> Optional[List[str]]:
    """
    Retrieve a list of team IDs associated with a specific user.

    :param user_id: The unique identifier of the user.
    :type user_id: str
    :param session: The database session to use, if provided.
    :type session: Optional[Session]
    :return: A list of team IDs associated with the user or None if no teams are found.
    :rtype: Optional[List[str]]
    """
    with get_db_session(session) as session:
        team_member_ids = session.query(TeamMember.team_id).filter(TeamMember.user_id == user_id).all()
        team_ids = [team_member_id[0] for team_member_id in team_member_ids]
        return team_ids
