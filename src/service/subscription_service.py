from typing import Optional

from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import Subscription, SubscriptionType, User
from sqlalchemy.orm import Session
from tenacity import (retry, retry_if_result, stop_after_attempt,
                      wait_exponential)


def is_none(value):
    return value is None


@retry(
    retry=retry_if_result(is_none),
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10)
)
def get_subscription_info_by_user_id(user_id: str, session: Optional[Session] = None) -> Optional[Subscription]:
    """
    Get subscription info by user id.
    :param user_id: User id.
    :param session: Session if any.
    :return: Subscription if exists.
    """
    with get_db_session(session) as session:
        subscription = session.query(Subscription).filter(Subscription.user_id == user_id).first()

        if subscription and not session:
            session.expunge(subscription)
        return subscription


def get_user_email_by_subscription(subscription: Subscription) -> Optional[str]:
    """
    Get user's email from subscription

    Args:
        subscription: Subscription object containing user relationship

    Returns:
        Optional[str]: User's email if found, None otherwise
    """
    try:
        with get_db_session() as session:
            user = session.query(User).filter(User.id == subscription.user_id).first()
            if user:
                return user.email
            return None
    except Exception as e:
        logger.error(f"Failed to get user email for subscription: {str(e)}")
        return None


@retry(
    retry=retry_if_result(is_none),
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10)
)
def get_subscription_info_by_customer_id(customer_id: str, session: Optional[Session] = None) -> Optional[Subscription]:
    """
    Get subscription info by Stripe customer id.
    :param customer_id: Customer id.
    :param session: Session if any.
    :return: Subscription if exists.
    """
    with get_db_session(session) as session:
        subscription = session.query(Subscription).filter(Subscription.stripe_customer_id == customer_id).first()

        if subscription and not session:
            session.expunge(subscription)
        return subscription


def save_subscription(subscription: Subscription, session: Optional[Session] = None) -> Subscription:
    """
    Save subscription to database.
    :param subscription: Subscription object.
    :param session: Session if any.
    :return: Subscription object.
    """
    with get_db_session(session) as session:
        session.add(subscription)
        session.flush()
        return subscription


def get_user_plan_by_id(user_id: str, session: Optional[Session] = None) -> Optional[SubscriptionType]:
    with get_db_session(session) as session:
        value = (session.query(Subscription.plan_name).filter(Subscription.user_id == user_id).first())
        return value[0]
