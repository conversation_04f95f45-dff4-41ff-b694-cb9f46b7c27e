from typing import Optional

from common_models.db_client import get_db_session
from common_models.models import Company, Project
from sqlalchemy.orm import Session


def get_company_id_by_project_id(project_id: str, session: Optional[Session] = None) -> Optional[str]:
    """
    Get the company ID associated with a given project ID.

    Searches for the project by the provided project ID and retrieves the
    associated company's ID if available.

    :param project_id: The ID of the project to search for.
    :param session: An optional database session for querying data.
    :return: The company ID associated with the provided project ID, or None
             if no matching project or company information is found.
    """
    with get_db_session(session) as session:
        project_info: Project = session.query(Project).filter(Project.id == project_id).first()

        if not project_info:
            return None

        company_info = project_info.user.company_info
        if company_info:
            return company_info.id
        return None


def get_company_by_id(company_id: str, session: Optional[Session] = None) -> Optional[Company]:
    with get_db_session(session) as session:
        company_info = session.query(Company).filter(Company.id == company_id).first()

        if company_info and not session:
            session.expunge(company_info)

        return company_info
