from typing import Optional

from common_models.db_client import get_db_session
from common_models.models import TeamMember
from sqlalchemy.orm import Session


def get_team_member_by_user_id(user_id: str, session: Optional[Session] = None) -> Optional[TeamMember]:
    """
    Get team member by user id.
    :param user_id: User id.
    :param session: Client session if any.
    :return: Team member info if found.
    """
    with get_db_session(session) as session:
        team_member = (
            session.query(TeamMember)
            .filter(TeamMember.user_id == user_id)
            .first()
        )
        if team_member and not session:
            session.expunge(team_member)
        return team_member
