from typing import Any, Dict, Optional

from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import PlatformMetrics
from sqlalchemy.orm import Session

from src.error.errors import (PlatformMetricsRecordAlreadyExistsError,
                              PlatformMetricsUpdateError)


def get_platform_metrics(session: Optional[Session] = None) -> Optional[PlatformMetrics]:
    """
    Get platform metrics.
    :param session: Session if any.
    :return: Platform metrics if exists.
    """
    with get_db_session(session) as session:
        metrics = session.query(PlatformMetrics).first()
        if not metrics:
            metrics = create_platform_metrics(session)
        if not session:
            session.expunge(metrics)
        return metrics


def update_platform_metrics(metrics_id: str, update_payload: Dict[Any, Any], session: Optional[Session] = None):
    """
    Update platform metrics.
    :param metrics_id: Metrics ID to update record.
    :param update_payload: Update payload.
    :param session: Session if any.
    :return: Updated result.
    """
    with get_db_session(session) as session:
        updated = session.query(PlatformMetrics).filter(PlatformMetrics.id == metrics_id).update(update_payload)

        if not updated:
            logger.error("Failed to update aggregated count in platform metrics.")
            raise PlatformMetricsUpdateError("Failed to update aggregated count.")
        return updated


def create_platform_metrics(session: Optional[Session] = None) -> PlatformMetrics:
    """
    Create platform metrics if not exists.
    :param session: Session if any.
    :return: Platform metrics object.
    """
    with get_db_session(session) as session:
        metrics = session.query(PlatformMetrics).first()
        if metrics:
            PlatformMetricsRecordAlreadyExistsError()
        metrics = PlatformMetrics()
        session.add(metrics)
        session.flush()
        return metrics
