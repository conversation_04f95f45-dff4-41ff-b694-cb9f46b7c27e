"""
Service for all git installation-related operations for all version control systems.
Replaces github_installation_service.py
"""

from typing import Optional

from common_models.db_client import get_db_session
from common_models.models import (AccessType, GithubInstallation,
                                  GitHubInstallationAccess,
                                  VersionControlSystem)
from sqlalchemy.orm import Session


def get_latest_git_integration_by_user_id_and_svc_type(
        user_id: str,
        svc_type: VersionControlSystem,
        session: Optional[Session] = None
) -> Optional[GithubInstallation]:
    """
    Get the latest active git installation info by user id.
    :param user_id: User ID.
    :param svc_type: Version control system we want to get installation info for, e.g. GitHub, GitLab, Bitbucket...
    :param session: Session if any.
    :return: GithubInstallation model if found or None.
    """
    with get_db_session(session) as session:
        github_installation_access = (session.query(GitHubInstallationAccess)
                                      .filter(GitHubInstallationAccess.entity_id == user_id,
                                              GitHubInstallationAccess.access_type == AccessType.USER)
                                      .join(GithubInstallation)
                                      .filter(GithubInstallation.svc_type == svc_type)
                                      .order_by(GitHubInstallationAccess.created_at.desc())
                                      .first())
        if not github_installation_access:
            return None

        github_installation = github_installation_access.installation
        if github_installation and not session:
            session.expunge(github_installation)
        return github_installation
