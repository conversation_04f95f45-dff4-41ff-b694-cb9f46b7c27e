from typing import Optional

from common_models.db_client import get_db_session
from common_models.models import TechnicalSpec
from sqlalchemy.orm import Session


def get_tech_spec_by_id(tech_spec_id: str, db_session: Optional[Session] = None) -> Optional[TechnicalSpec]:
    with get_db_session(db_session) as session:
        tech_spec = (session.query(TechnicalSpec).filter(TechnicalSpec.id == tech_spec_id).first())

        if tech_spec and not db_session:
            session.expunge(tech_spec)
        return tech_spec


def update_tech_spec_by_id(tech_spec_id: str, update_payload: dict, session: Optional[Session] = None):
    with get_db_session(session) as session:
        tech_spec_updated = session.query(TechnicalSpec).filter(TechnicalSpec.id == tech_spec_id).update(
            update_payload)
        session.flush()

        if not tech_spec_updated:
            raise Exception(f"Failed to update technical specification for the project {tech_spec_id}.")
