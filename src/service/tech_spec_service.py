from typing import Optional

from common_models.db_client import get_db_session
from common_models.models import TechnicalSpec
from sqlalchemy.orm import Session


def get_tech_spec_by_id(tech_spec_id: str, session: Optional[Session] = None) -> Optional[TechnicalSpec]:
    """
    Retrieve a TechnicalSpec object by its ID from the database.

    This function queries the database for a TechnicalSpec object matching
    the given ID. If no session is provided, it creates a temporary session
    to perform the query.

    :param tech_spec_id: The unique identifier of the TechnicalSpec.
    :type tech_spec_id: str
    :param session: An optional database session used for querying.
    :type session: Optional[Session]
    :return: The TechnicalSpec object if found, otherwise None.
    :rtype: Optional[TechnicalSpec]
    """
    with get_db_session(session) as session:
        tech_spec = session.query(TechnicalSpec).filter(TechnicalSpec.id == tech_spec_id).first()

        if tech_spec and not session:
            session.expunge(tech_spec)
        return tech_spec
