from typing import Any, Dict, Optional, Tuple

from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import (ReportStatus, Status, TechnicalSpec,
                                  TechSpecJobType)
from sqlalchemy.orm import Session

from src.error.errors import TechnicalSpecificationUpdateError


def save_tech_spec(tech_spec: TechnicalSpec, session: Optional[Session] = None) -> TechnicalSpec:
    """
    Save tech spec to database.
    :param tech_spec: Tech spec object to save.
    :param session: Session if any.
    :return: Tech spec object.
    """
    with get_db_session(session) as session:
        session.add(tech_spec)
        session.flush()
        return tech_spec


def update_tech_spec_by_project_id(project_id: str, update_payload: Dict[Any, Any], session: Optional[Session] = None):
    """
    Update tech spec by project id.
    :param project_id: Project ID.
    :param update_payload: Update payload.
    :param session: Session if any.
    """
    with get_db_session(session) as session:
        technical_spec_updated = session.query(TechnicalSpec).filter(
            TechnicalSpec.project_id == project_id,
        ).update(update_payload)

        if not technical_spec_updated:
            logger.error(f"Failed to update technical specification table for project {project_id}")
            raise TechnicalSpecificationUpdateError(
                f"Failed to update technical specification for project {project_id}")


def get_tech_spec_by_project_id(project_id: str, session: Optional[Session] = None) -> TechnicalSpec:
    with get_db_session(session) as session:
        tech_spec = (session.query(TechnicalSpec).filter(TechnicalSpec.project_id == project_id).first())
        if tech_spec and not session:
            session.expunge(tech_spec)
        return tech_spec


def update_tech_spec_pdf_report_status_by_project_id(project_id: str, report_status: ReportStatus,
                                                     session: Optional[Session] = None):
    with get_db_session(session) as session:
        update_payload = {
            TechnicalSpec.pdf_report_status: report_status
        }
        update_tech_spec_by_project_id(project_id, update_payload, session)


def get_in_progress_tech_spec_by_project_id_and_status(project_id: str,
                                                       session: Optional[Session] = None) -> Optional[TechnicalSpec]:
    """
    Retrieves a technical specification that is in progress or queued based on
    the provided project identifier. The method queries the database session
    for the relevant technical specification and ensures it is returned in
    cases where it's either queued or in progress. If no session is provided,
    a new one will be created and managed within this method.

    :param project_id: A unique identifier representing the project for which the technical
        specification is to be fetched.
    :param session: An optional SQLAlchemy session instance to use for the database operation.
        If not provided, a new session will be created and managed internally.
    :return: A technical specification object if one is found with the specified project identifier and
        status in progress or queued; otherwise, None.
    """
    with get_db_session(session) as session:
        tech_spec = (session.query(TechnicalSpec)
                     .filter(TechnicalSpec.project_id == project_id,
                             TechnicalSpec.status.in_([Status.QUEUED, Status.IN_PROGRESS]))
                     .first())
        if tech_spec and not session:
            session.expunge(tech_spec)
        return tech_spec


def get_all_tech_specs_by_project_id_order_by_created_at(project_id: str,
                                                         session: Optional[Session] = None) -> list[TechnicalSpec]:
    """
    Fetch all tech specs by project ID sorted by created_at in descending order.
    :param project_id: Project ID.
    :param session: Session if any.
    :return: List of TechnicalSpec objects sorted by created_at.
    """
    with get_db_session(session) as session:
        tech_specs = (session.query(TechnicalSpec)
                      .filter(TechnicalSpec.project_id == project_id)
                      .order_by(TechnicalSpec.created_at.desc())
                      .all())
        return tech_specs


def get_tech_spec_by_project_id_and_job_type(project_id: str, job_type: TechSpecJobType,
                                             session: Optional[Session] = None) -> Optional[TechnicalSpec]:
    """
    Fetch a tech spec by project_id and job_type 'NEW_PRODUCT'.
    :param project_id: Project ID.
    :param job_type: Job type.
    :param session: Session if any.
    :return: TechnicalSpec object if found, otherwise None.
    """
    with get_db_session(session) as session:
        tech_spec = (session.query(TechnicalSpec)
                     .filter(TechnicalSpec.project_id == project_id,
                             TechnicalSpec.job_type == job_type)
                     .first())

        if tech_spec and not session:
            session.expunge(tech_spec)
        return tech_spec


def get_in_progress_tech_spec_by_project_id_and_job_type(project_id: str, job_type: TechSpecJobType,
                                                         session: Optional[Session] = None) -> Optional[TechnicalSpec]:
    """
    Retrieves a technical specification that is in progress or queued for a specific job type
    based on the provided project identifier and job type. This ensures idempotency by
    preventing multiple jobs of the same type from running simultaneously.

    :param project_id: A unique identifier representing the project for which the technical
        specification is to be fetched.
    :param job_type: The specific job type to check for (ADD_FEATURE, REFACTOR_CODE, SYNC_TECH_SPEC,
        FIX_BUGS, FIX_CVES, ADD_TESTING, DOCUMENT_CODE, CUSTOM).
    :param session: An optional SQLAlchemy session instance to use for the database operation.
        If not provided, a new session will be created and managed internally.
    :return: A technical specification object if one is found with the specified project identifier,
        job type, and status in progress or queued; otherwise, None.
    """
    with get_db_session(session) as session:
        tech_spec = (session.query(TechnicalSpec)
                     .filter(TechnicalSpec.project_id == project_id,
                             TechnicalSpec.job_type == job_type,
                             TechnicalSpec.status.in_([Status.QUEUED, Status.IN_PROGRESS]))
                     .first())
        if tech_spec and not session:
            session.expunge(tech_spec)
        return tech_spec


def get_latest_tech_spec_by_project_id(project_id: str, session: Optional[Session] = None) -> Optional[TechnicalSpec]:
    """
    Fetch the first tech spec by project ID sorted by created_at in descending order.
    :param project_id: Project ID.
    :param session: Session if any.
    :return: The first TechnicalSpec object sorted by created_at.
    """
    with get_db_session(session) as session:
        tech_spec = (session.query(TechnicalSpec)
                     .filter(TechnicalSpec.project_id == project_id)
                     .order_by(TechnicalSpec.created_at.desc())
                     .first())
        if tech_spec and not session:
            session.expunge(tech_spec)
        return tech_spec


def get_tech_spec_by_id_and_project_id(project_id: str, tech_spec_id: str,
                                       session: Optional[Session] = None) -> Optional[TechnicalSpec]:
    """
    Get tech spec by id and project id.
    :param project_id: ID of the project.
    :param tech_spec_id: Tech spec ID.
    :param session: Session if any.
    :return: Technical spec object if found.
    """
    with get_db_session(session) as session:
        tech_spec = (session.query(TechnicalSpec)
                     .filter(TechnicalSpec.id == tech_spec_id, TechnicalSpec.project_id == project_id)
                     .first())
        if tech_spec and not session:
            session.expunge(tech_spec)
        return tech_spec


def get_latest_tech_spec(project_id: str, session: Optional[Session] = None) -> Optional[TechnicalSpec]:
    """
    Get latest tech spec version for particular project.
    :param project_id: Project ID.
    :param session: Client session if any.
    :return: Version number if found.
    """
    with get_db_session(session) as session:
        # Include deleted tech specs
        tech_spec = (session.query(TechnicalSpec)
                     .filter(TechnicalSpec.project_id == project_id)
                     .order_by(TechnicalSpec.created_at.desc())
                     .execution_options(include_deleted=True)
                     .first())

        if not tech_spec:
            return None

        if tech_spec and not session:
            session.expunge(tech_spec)
        return tech_spec


def get_latest_tech_spec_with_new_version(project_id: str, session: Session = None) -> Tuple[TechnicalSpec, int]:
    """
    Get the latest technical specification and increment the version.
    :param project_id: Project ID.
    :param session: Client session.
    :return: Updated version.
    """
    latest_technical_spec = get_latest_tech_spec(project_id, session)
    updated_version = 0
    if latest_technical_spec:
        updated_version = latest_technical_spec.version + 1
    return latest_technical_spec, updated_version


def get_tech_spec_by_id(tech_spec_id: str, session: Optional[Session] = None) -> Optional[TechnicalSpec]:
    """
    Fetches a TechnicalSpec object by its ID.

    :param tech_spec_id: The ID of the TechnicalSpec to fetch.
    :type tech_spec_id: str
    :param session: An optional database session used for querying.
    :type session: Optional[Session]
    :return: The TechnicalSpec object with the given ID or None if not found.
    :rtype: Optional[TechnicalSpec]
    """
    with get_db_session(session) as session:
        tech_spec = (session.query(TechnicalSpec).filter(TechnicalSpec.id == tech_spec_id).first())
        if tech_spec and not session:
            session.expunge(tech_spec)
        return tech_spec
