from typing import Any, Dict, Optional

from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import BlitzyCommit
from sqlalchemy.orm import Session

from src.error.errors import DatabaseUpdateError


def get_blitzy_commit_by_code_gen_id(code_gen_id: str, session: Optional[Session] = None) -> Optional[BlitzyCommit]:
    """
    Fetches a single BlitzyCommit object using the given code_gen_id. If a Session
    object is not provided, a new one will be created and used for the database query.
    The method ensures that if a BlitzyCommit object is retrieved and the session is
    not provided, the object is detached from the session.

    :param code_gen_id: The identifier used to uniquely fetch the BlitzyCommit object
        from the database.
    :param session: The optional database session to use for querying. If not provided,
        a session will be created automatically.
    :return: Returns a BlitzyCommit object matching the provided code_gen_id if it
        exists in the database; otherwise, returns None.
    """
    with get_db_session(session) as session:
        blitzy_commit = (
            session.query(BlitzyCommit)
            .filter(BlitzyCommit.code_gen_id == code_gen_id)
            .first()
        )
        if blitzy_commit and not session:
            session.expunge(blitzy_commit)
        return blitzy_commit


def update_blitzy_commit(update_payload: Dict[Any, Any], code_gen_id: str, session: Optional[Session] = None):
    """
    Updates a BlitzyCommit record in the database with the provided payload.

    This function updates an existing BlitzyCommit in the database by matching it
    on the given code_gen_id. If no matching record is found, it raises a
    DatabaseUpdateError. A session can optionally be provided; otherwise, a new
    session will be created for the operation.

    :param update_payload: A dictionary containing the fields and their values to
        update in a BlitzyCommit record.
    :param code_gen_id: The unique identifier for the BlitzyCommit record to update.
    :param session: An optional SQLAlchemy session. If not provided, a new session
        will be used.
    :return: None if the update is successful. Raises DatabaseUpdateError if no
        matching record is found.
    """
    with get_db_session(session) as session:
        updated = (
            session.query(BlitzyCommit)
            .filter(BlitzyCommit.code_gen_id == code_gen_id)
            .update(update_payload)
        )

        if not updated:
            logger.error(f"No blitzy commit found for code gen id: {code_gen_id}")
            raise DatabaseUpdateError("No blitzy commit found.")
        session.commit()
