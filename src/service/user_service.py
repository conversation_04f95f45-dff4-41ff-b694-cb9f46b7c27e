from typing import Optional

from common_models.db_client import get_db_session
from common_models.models import User
from sqlalchemy.orm import Session


def get_user_by_id(user_id: str, session: Optional[Session] = None) -> User:
    with get_db_session(session) as session:
        user = session.query(User).filter(User.id == user_id).first()
        if user and not session:
            session.expunge(user)
        return user
