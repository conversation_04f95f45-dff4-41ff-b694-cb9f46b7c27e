from datetime import datetime
from typing import Any, Dict, Optional

from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import (GeoLocation, Subscription, SubscriptionType,
                                  User, UserConfig)
from firebase_admin import auth
from firebase_admin.auth import UserRecord
from sqlalchemy.orm import Session, joinedload

from src.api.models import CreateUserInput
from src.error.errors import InvalidUserCredentials, UserUpdateError
from src.service.ip_service import (add_geolocation, get_client_ip,
                                    get_geolocation_from_ip)
from src.service.subscription_service import save_subscription
from src.service.user_config_service import save_config


def create_user_using_firebase_user_id(user_payload: CreateUserInput, session: Session) -> Dict[str, Any]:
    """
    Creates user with default subscription. Uses firebase to fetch information. User gets created with default
    subscription.
    :param session: User session.
    :param user_payload: User payload received via API.
    :return: User created in database.
    """

    user_info = fetch_user_info_from_auth_provider(user_payload.userId)
    if user_info.email != user_payload.email:
        raise InvalidUserCredentials(
            f"Invalid credentials provided. Got {user_payload.email} but expected {user_info.email}")

    user = User(
        user_id=user_payload.userId,
        email=user_payload.email,
        is_verified=True,
        registration_completed=False,
    )
    display_name = user_info.display_name
    if display_name:
        name_parts = display_name.split()
        first_name = name_parts[0] if name_parts else ""
        last_name = " ".join(name_parts[1:]) if len(name_parts) > 1 else ""
        user.first_name = first_name
        user.last_name = last_name

    session.add(user)
    session.flush()

    subscription = Subscription(
        user_id=user.id,
        plan_name=SubscriptionType.FREE,
        start_date=datetime.utcnow(),
    )
    save_subscription(subscription, session)

    config = UserConfig(
        user_id=user.id,
        tech_spec_notification_enabled=True,
        code_gen_notification_enabled=True,
        platform_config={}
    )
    save_config(config, session)
    return user.to_dict()


def create_user(user: User, session: Optional[Session] = None) -> User:
    """
    Create and persist a new user in the database.

    :param user: User object to be created and added to the database.
    :type user: User
    :param session: Optional database session to be reused. If not provided, a new
        session is created and managed internally.
    :type session: Optional[Session]
    :return: The newly created User object.
    :rtype: User
    """
    with get_db_session(session) as session:
        session.add(user)
        session.flush()
        return user


def fetch_user_info_from_auth_provider(user_id: str) -> UserRecord:
    """
    Fetches User from auth provider.
    :param user_id: User ID.
    :return: User ID fetched from auth provider.
    """
    user_info = auth.get_user(user_id)
    if not user_info:
        raise Exception(f"User with ID {user_id} does not exists in firebase.")
    return user_info


def get_user_by_id(user_id: str, session: Optional[Session] = None) -> Optional[User]:
    """
    Get user information by ID.
    :param user_id: User ID.
    :param session: Session if any.
    :return: User if found.
    """
    with get_db_session(session) as session:
        user = (session.query(User).options(joinedload(User.subscription),
                                            joinedload(User.user_config)).filter(User.id == user_id).first())

        if user and not session:
            session.expunge(user)
        return user


def update_user_by_id(user_id: str, update_payload: Dict[Any, Any], session: Optional[Session] = None):
    """
    Update user information by ID.
    :param user_id: User ID.
    :param update_payload: Update payload.
    :param session: Session if any.
    """
    with get_db_session(session) as session:
        user_updated = session.query(User).filter(
            User.id == user_id,
        ).update(update_payload)

        if not user_updated:
            logger.error(f"Failed to update user {user_id}")
            raise UserUpdateError(f"Failed to update user information for ID {user_id}")


def add_user(user: User, session: Optional[Session] = None) -> User:
    """
    Add user to the database.
    :param user: User model.
    :param session: Session if any.
    :return: Saved model.
    """
    with get_db_session(session) as session:
        session.add(user)
        session.flush()
        return user


def get_user_by_user_email(email: str, session: Optional[Session] = None) -> Optional[User]:
    """
    Get user information by email.
    :param email: Email ID.
    :param session: Session if any.
    :return: User object if found.
    """
    with get_db_session(session) as session:
        user = (session.query(User).options(joinedload(User.subscription)).filter(User.email == email).first())

        if user and not session:
            session.expunge(user)
        return user


def save_user_geolocation(user_id: str, session: Session) -> None:
    """
    Helper function to save user geolocation if it doesn't exist.
    Args:
        user_id: User ID from the database
        session: Active database session
    """
    existing_geolocation = session.query(GeoLocation).filter(
        GeoLocation.user_id == user_id
    ).first()
    if not existing_geolocation:
        logger.info(f"No existing geolocation found for user_id: {user_id}")
        ip_address = get_client_ip()
        if ip_address:
            geo_data = get_geolocation_from_ip(ip_address)
            if geo_data:
                geolocation = GeoLocation(
                    user_id=user_id,
                    ip_address=geo_data['ip'],
                    country_code=geo_data['country_code'],
                    city=geo_data['city'],
                    region=geo_data['region'],
                    latitude=geo_data['latitude'],
                    longitude=geo_data['longitude'],
                    timezone=geo_data['timezone']
                )
                add_geolocation(geolocation, session)
                session.commit()
            else:
                logger.warning(
                    message=f"Failed to get geolocation information for user_id: {user_id}"
                )
