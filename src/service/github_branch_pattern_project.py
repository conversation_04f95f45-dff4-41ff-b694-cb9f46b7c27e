from typing import Optional

from common_models.db_client import get_db_session
from common_models.models import GithubBranchPatternProject, UsageType
from sqlalchemy.orm import Session


def get_branch_pattern_by_project_id(project_id: str, usage_type: UsageType,
                                     session: Optional[Session] = None) -> Optional[GithubBranchPatternProject]:
    """
    Get github branch pattern by project id.
    :param project_id: Project id.
    :param usage_type: Udage type.
    :param session: Session if any.
    :return: Github branch pattern if found.
    """
    with get_db_session(session) as session:
        branch_pattern = session.query(GithubBranchPatternProject).filter(
            GithubBranchPatternProject.project_id == project_id,
            GithubBranchPatternProject.usage_type == usage_type
        ).first()

        if branch_pattern and not session:
            session.expunge(branch_pattern)
        return branch_pattern
