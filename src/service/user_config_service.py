from typing import Optional

from common_models.db_client import get_db_session
from common_models.models import UserConfig
from sqlalchemy.orm import Session


def get_user_config_by_user_id(user_id: str, session: Optional[Session] = None) -> Optional[UserConfig]:
    with get_db_session(session) as session:
        return (
            session.query(UserConfig)
            .filter(UserConfig.user_id == user_id)
            .first()
        )


def save_config(config: UserConfig, session: Optional[Session] = None):
    with get_db_session(session) as session:
        session.add(config)
        session.flush()
