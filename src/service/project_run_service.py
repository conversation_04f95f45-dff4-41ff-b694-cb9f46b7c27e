from typing import Optional

from common_models.db_client import get_db_session
from common_models.models import ProjectRun
from sqlalchemy.orm import Session


def update_project_run_by_tech_spec_id(tech_spec_id, project_run_update, session: Optional[Session] = None):
    with get_db_session(session) as session:
        project_run_updated = session.query(ProjectRun).filter(ProjectRun.tech_spec_id == tech_spec_id).update(
            project_run_update)
        session.flush()
        if not project_run_updated:
            raise Exception(f"Failed to update project run for the project {tech_spec_id}.")


def update_project_run_by_code_gen_id(code_gen_id, project_run_update, session: Optional[Session] = None):
    with get_db_session(session) as session:
        project_run_updated = session.query(ProjectRun).filter(ProjectRun.code_gen_id == code_gen_id).update(
            project_run_update)
        session.flush()
        if not project_run_updated:
            raise Exception(f"Failed to update project run for the project {code_gen_id}.")
