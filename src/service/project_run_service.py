from typing import Optional

from common_models.db_client import get_db_session
from common_models.models import ProjectRun
from sqlalchemy.orm import Session


def save_project_run(project_run: ProjectRun, session: Optional[Session] = None) -> ProjectRun:
    """
    Save project run to database.
    :param project_run: Job object to save.
    :param session: Session if any.
    :return: Project run object.
    """
    with get_db_session(session) as session:
        session.add(project_run)
        session.flush()
        return project_run


def update_project_run_with_tech_spec_id(tech_spec_id: str, project_run_id: str, session: Optional[Session] = None):
    with get_db_session(session) as session:
        update_payload = {
            ProjectRun.tech_spec_id: tech_spec_id
        }

        session.query(ProjectRun).filter(ProjectRun.id == project_run_id).update(update_payload)


def get_project_run_by_project_and_tech_spec_id(tech_spec_id: str, project_id: str,
                                                session: Optional[Session] = None) -> Optional[ProjectRun]:
    with get_db_session(session) as session:
        project_run = (
            session.query(ProjectRun).filter(ProjectRun.tech_spec_id == tech_spec_id,
                                             ProjectRun.project_id == project_id).first())
        if project_run and not session:
            session.expunge(project_run)
        return project_run


def get_project_run_by_code_gen_id(code_gen_id: str, session: Optional[Session] = None) -> Optional[ProjectRun]:
    with get_db_session(session) as session:
        project_run = (
            session.query(ProjectRun).filter(ProjectRun.code_gen_id == code_gen_id).first())
        if project_run and not session:
            session.expunge(project_run)
        return project_run
