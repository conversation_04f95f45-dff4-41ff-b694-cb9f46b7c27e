from common_models.db_client import get_db_session
from common_models.models import GitHubRepository


def get_github_repository_by_project_id(project_id):
    with get_db_session() as session:
        github_repository = (session.query(GitHubRepository)
                             .filter(GitHubRepository.project_id == project_id)
                             .first())
        if github_repository:
            session.expunge(github_repository)
        return github_repository
