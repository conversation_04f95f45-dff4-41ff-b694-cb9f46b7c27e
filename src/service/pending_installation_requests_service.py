from typing import Optional

from blitzy_utils.logger import logger
from blitzy_utils.sercert_manager import get_github_token_from_secret_manager
from common_models.db_client import get_db_session
from common_models.models import GithubInstallation, PendingInstallationRequest
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from src.api.routes.secret_manager import get_secret_value
from src.api.utils.secret_manager_utils import (create_or_update_github_secret,
                                                create_or_update_secret)


def get_pending_installation_requests_by_github_user_id(
        github_user_id: str,
        session: Optional[Session] = None) -> PendingInstallationRequest:
    with get_db_session(session) as session:
        with get_db_session(session) as session:
            pending_github_installation = (session.query(PendingInstallationRequest)
                                           .filter(PendingInstallationRequest.request_by == github_user_id)
                                           .first())
            if pending_github_installation and not session:
                session.expunge(pending_github_installation)
            return pending_github_installation


def create_pending_installation_record(
        github_installation: GithubInstallation,
        session: Optional[Session] = None
):
    try:
        pending_installation = PendingInstallationRequest()
        pending_installation.request_by = github_installation.requested_by
        session.add(pending_installation)
        session.commit()
    except IntegrityError as e:
        logger.info("Record for pending installation already exists. No need to create new record.")
        raise e


def handle_pending_installation_record_by_github_user_id(github_user_id: str, installation_id: str,
                                                         session: Optional[Session] = None):
    """
    Processes a pending installation record associated with a given GitHub user ID by performing
    a set of operations including updating user secrets if applicable and removing the pending
    installation record from the database.

    The function retrieves a pending installation record for a given GitHub user ID from the database
    using a provided or newly created session. If a pending installation is found, it attempts to update
    the user's secret using the provided installation ID. After completing this operation, the function
    deletes the pending installation record from the database.

    :param github_user_id: GitHub user ID associated with the pending installation record.
    :param installation_id: Installation ID to be used for updating user secrets if a pending
        installation is found.
    :param session: Session if any.
    """
    with get_db_session(session) as session:
        pending_installation = get_pending_installation_requests_by_github_user_id(github_user_id, session)
        if pending_installation:
            logger.info("Attempting to update secret for user with installation id")
            attempt_user_secret_update(pending_installation, installation_id)
        result = delete_pending_installation_record_by_github_user_id(github_user_id, session)
        return result


def delete_pending_installation_record_by_github_user_id(github_user_id: str,
                                                         session: Optional[Session] = None):
    """
    Deletes a pending installation record associated with a given GitHub user ID.

    This function interacts with the database to delete any pending installation
    records associated with the provided GitHub user ID. It utilizes a SQLAlchemy
    session to perform the deletion. If no session is provided, it automatically
    manages a new session. The deletion is carried out using a query filtering
    based on the `request_by` field of the `PendingInstallationRequest` model.

    :param github_user_id: The GitHub user ID for which the pending installation
        record needs to be deleted.
    :param session: Session if any.
    :return: The count of deleted rows corresponding to the pending installation
        record for the given GitHub user ID.
    """
    with get_db_session(session) as session:
        logger.info(f"Deleting pending installation record for github user id {github_user_id}")
        deleted = session.query(PendingInstallationRequest).filter(
            PendingInstallationRequest.request_by == github_user_id).delete()
        return deleted


def attempt_user_secret_update(pending_installation: PendingInstallationRequest, installation_id: str, ):
    """
    Searches for a GitHub secret associated with the provided user and updates the secret if found.
    If no secret is found for the user, the function logs that no secret exists.

    :param pending_installation: The installation request containing details about the user and
        pending repository connection.
    :param installation_id: The unique identifier for the GitHub installation.
    """
    logger.info("Searching for secret.")
    # secret = get_github_token_from_secret_manager(pending_installation.user_id)
    secret = get_secret_value(pending_installation.user_id, "latest")
    if secret:
        logger.info(f"Secret found for user with id {pending_installation.user_id}")
        logger.info("Replicating secret with appropriate data.")
        access_token = secret["access_token"]
        code = secret["code"]
        setup_action = secret["setup_action"]
        # Replicating this for a time being. This will be deprecated later on.
        # TODO (Chaitanya): Deprecate saving secret to user secret.
        create_or_update_secret(pending_installation.user_id, access_token, code, installation_id, setup_action)
        create_or_update_github_secret(access_token, code, installation_id, setup_action)
        logger.info("Secret updated successfully.")
        return
    logger.info(f"Secret not found for user {pending_installation.user_id}.")
