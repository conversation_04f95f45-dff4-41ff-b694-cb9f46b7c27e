from common_models.db_client import get_db_session
from common_models.models import PendingInstallationRequest


def get_pending_installation_requests_by_user_id(user_id, session):
    """
    Fetches the first pending installation request associated with the given
    user ID from the database session. If the session object is not active,
    the pending installation request is detached from the session.

    :param user_id: The ID of the user whose pending installation request
        is being queried.
    :param session: Session if any.
    :return: The first pending installation request for the given user ID,
        or None if no such request exists.
    """
    with get_db_session(session) as session:
        pending_installation_request = (session.query(PendingInstallationRequest).filter(
            PendingInstallationRequest.user_id == user_id).first())

        if pending_installation_request and not session:
            session.expunge(pending_installation_request)
        return pending_installation_request
