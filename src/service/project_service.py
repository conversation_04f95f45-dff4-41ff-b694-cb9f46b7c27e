from typing import Optional

from common_models.db_client import get_db_session
from common_models.models import Project
from sqlalchemy.orm import Session


def get_project_by_id(project_id: str, session: Optional[Session] = None) -> Optional[Project]:
    """
    Retrieve project details by its unique identifier.

    :param project_id: Unique identity key for the project.
    :type project_id: str
    :param session: Optional database session to be used. Defaults to None.
    :type session: Optional[Session]
    :return: The project if found, or None if no project matches the given ID.
    :rtype: Optional[Project]
    """
    with get_db_session(session) as session:
        project_info = session.query(Project).filter(Project.id == project_id).first()

        if project_info and not session:
            session.expunge(project_info)

        return project_info
