from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional

from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import (CodeGeneration, Project, SoftwareRequirement,
                                  Status, TechnicalSpec)
from sqlalchemy import Row, func
from sqlalchemy.orm import Session, joinedload

from src.consts import FREE_USER_LIMIT_RESETTING_TIME_HR
from src.error.errors import ProjectUpdateError


def save_project(project: Project, session: Optional[Session] = None) -> Project:
    """
    Save project to database.
    :param project: Project object to save.
    :param session: Session if any.
    :return: Job object.
    """
    with get_db_session(session) as session:
        session.add(project)
        session.flush()
        return project


def get_project_by_user_id_with_relations(user_id: str, project_id: str,
                                          session: Optional[Session] = None) -> Optional[Project]:
    """
    Get Project by user id and project ID. Retrieves complete project with all it's relations
    :param user_id: User ID.
    :param project_id: Project ID.
    :param session: Session if any.
    :param tech_spec_id: Technical Spec ID.
    :return: Project if found.
    """
    with get_db_session(session) as session:
        project = (session.query(Project)
                   .options(joinedload(Project.user),
                            joinedload(Project.software_requirement),
                            joinedload(Project.technical_spec),
                            joinedload(Project.code_generation))
                   .filter(Project.id == project_id,
                           Project.user_id == user_id).first())

        if project and not session:
            session.expunge(project)
        return project


def get_project_with_latest_spec(user_id: str, project_id: str,
                                 session: Optional[Session] = None) -> Optional[Dict[str, Any]]:
    """
    Fetches a project along with its latest technical specification and latest
    code generation details based on the user and project ID.
    If no project or specifications are found, returns None.

    :param user_id: Identifier for the user.
    :type user_id: str
    :param project_id: Identifier for the project.
    :type project_id: str
    :param session: Optional database session. If not provided, a new session is created.
    :type session: Optional[Session]
    :return: Dictionary containing project, latest technical specification, and
             latest code generation details, or None.
    :rtype: Optional[Dict[str, Any]]
    """
    with get_db_session(session) as session:
        project = session.query(Project).filter(Project.id == project_id, Project.user_id == user_id).first()

        if not project:
            return None

        latest_tech_spec = session.query(TechnicalSpec).filter(
            TechnicalSpec.project_id == project_id
        ).order_by(TechnicalSpec.created_at.desc()).first()

        # Fetch latest code generation
        latest_code_gen = session.query(CodeGeneration).filter(
            CodeGeneration.project_id == project_id,
        ).order_by(CodeGeneration.created_at.desc()).first()

        session.expunge(project)
        if latest_tech_spec:
            session.expunge(latest_tech_spec)
        if latest_code_gen:
            session.expunge(latest_code_gen)

        result = {
            "project": project,
            "latest_technical_spec": latest_tech_spec,
            "latest_code_generation": latest_code_gen if latest_tech_spec else None
        }
        return result


def get_project_by_user_id(user_id: str, project_id: str, session: Optional[Session] = None) -> Optional[Project]:
    """
    Get Project by user id and project ID. Retrieves only project.
    :param user_id: User ID.
    :param project_id: Project ID.
    :param session: Session if any.
    :return: Project if found.
    """
    with get_db_session(session) as session:
        project = (session.query(Project)
                   .filter(Project.id == project_id,
                           Project.user_id == user_id).first())
        if project and not session:
            session.expunge(project)
        return project


def update_project_filter_by_user_id_project_id(user_id: str, project_id: str, update_payload: Dict[Any, Any],
                                                session: Optional[Session] = None) -> Any:
    with get_db_session(session) as session:
        updated = session.query(Project).filter(
            Project.id == project_id,
            Project.user_id == user_id
        ).update(update_payload)

        if not updated:
            logger.error(f"Failed to update project {project_id}")
            raise ProjectUpdateError(f"Project {project_id} has not been submitted yet.")
        return updated


def get_all_projects_by_user_id(user_id: str, session: Optional[Session] = None) -> List[Row]:
    """
    Get all projects by user id and sorts by created at timestamp.
    :param user_id: User ID.
    :param session: Session if any.
    :return: List of projects.
    """
    with get_db_session(session) as session:
        projects = (session.query(
            Project.id,
            Project.name,
            Project.status,
            Project.prompt_status,
            Project.prompt_updated_at,
            Project.repo_prefix,
            Project.repo_url,
            Project.created_at,
            Project.updated_at,
            Project.is_disabled,
            Project.disable_reason
        ).filter(Project.user_id == user_id).order_by(Project.created_at.desc()).all())
        if projects and not session:
            session.expunge(projects)
        return projects


def get_project_by_id_user_id(user_id: str, project_id: str, session: Optional[Session] = None) -> Optional[Project]:
    """
    Get project by user id and project ID.
    :param user_id: User ID.
    :param project_id: Project ID.
    :param session: Session if any.
    :return: Project if found.
    """
    with get_db_session(session) as session:
        project = session.query(Project).filter(Project.id == project_id,
                                                Project.user_id == user_id).first()

        if project and not session:
            session.expunge(project)
        return project


def get_submission_count_last_24h(user_id: str, session: Optional[Session] = None) -> Optional[int]:
    with get_db_session(session) as session:
        time_24h_ago = (datetime.now(timezone.utc) - timedelta(hours=FREE_USER_LIMIT_RESETTING_TIME_HR))

        soft_req_count = (session.query(func.count(SoftwareRequirement.id))
                          .join(Project, Project.id == SoftwareRequirement.project_id)
                          .filter(
            Project.user_id == user_id,
            SoftwareRequirement.status.in_([Status.SUBMITTED, Status.DONE, Status.IN_PROGRESS]),
            SoftwareRequirement.updated_at >= time_24h_ago
        ).scalar())
        logger.info(f"Current total soft req count for user {user_id}: {soft_req_count}")

        tech_spec_count = (session.query(func.count(TechnicalSpec.id))
                           .join(Project, Project.id == TechnicalSpec.project_id)
                           .filter(
            Project.user_id == user_id,
            TechnicalSpec.status.in_([Status.SUBMITTED, Status.DONE, Status.IN_PROGRESS]),
            TechnicalSpec.updated_at >= time_24h_ago
        ).scalar())
        logger.info(f"Current total tech spec count for user {user_id}: {tech_spec_count}")

        return soft_req_count + tech_spec_count


def get_project_by_id(project_id: str, session: Optional[Session] = None) -> Optional[Project]:
    """
    Get Project by project ID. Retrieves only project.
    :param user_id: User ID.
    :param project_id: Project ID.
    :param session: Session if any.
    :return: Project if found.
    """
    with get_db_session(session) as session:
        project = (session.query(Project)
                   .filter(Project.id == project_id).first())
        if project and not session:
            session.expunge(project)
        return project


def update_project_by_id(project_id: str, update_payload: Dict[Any, Any], session: Optional[Session] = None):
    with get_db_session(session) as session:
        updated = session.query(Project).filter(
            Project.id == project_id,
        ).update(update_payload)

        if not updated:
            logger.error(f"Failed to update project {project_id}")
            raise ProjectUpdateError(f"Project {project_id} has not been submitted yet.")
        return updated


def update_project_timeline(project_id: str, session: Optional[Session] = None) -> Any:
    """
    Update project updated_at timestamp to current time.
    :param project_id: Project ID.
    :param session: Session if any.
    :return: Result of the update operation.
    """
    with get_db_session(session) as session:
        update_payload = {"updated_at": datetime.now(timezone.utc)}

        updated = session.query(Project).filter(
            Project.id == project_id
        ).update(update_payload)

        if not updated:
            logger.error(f"Failed to update project timeline for {project_id}")
            raise ProjectUpdateError(f"Project {project_id} not found or update failed.")

        return updated
