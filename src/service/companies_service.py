from typing import Optional

from common_models.db_client import get_db_session
from common_models.models import Company, TeamMember
from sqlalchemy.orm import Session


def get_company_by_user_id(user_id, session: Optional[Session] = None) -> Optional[Company]:
    """
    Get company by user id.
    :param user_id: User id.
    :param session: Client session if any.
    :return: Company info if found.
    """
    with get_db_session() as session:
        # find team first.
        team_member = session.query(TeamMember).filter(TeamMember.user_id == user_id).first()
        if not team_member:
            return None

        company = team_member.team.company

        if team_member and not session:
            session.expunge(team_member)
            session.expunge(company)

        return company
