from typing import Dict, Optional

import requests
from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import GeoLocation
from flask import request
from sqlalchemy.orm import Session

from src.consts import IPINFO_API_URL, IPINFO_TOKEN


def get_client_ip() -> Optional[str]:
    """Get the client's IP address from request headers."""
    # Check X-Forwarded-For header first (for clients behind proxy)
    if "X-Forwarded-For" in request.headers:
        return request.headers['X-Forwarded-For'].split(',')[0].strip()
    # Fall back to remote address
    return request.remote_addr


def get_geolocation_from_ip(ip_address: str) -> Optional[Dict]:
    """
    Get geolocation information from IP address using IPinfo API.

    Args:
        ip_address: IP address to look up

    Returns:
        Optional[Dict]: Dictionary containing geolocation data or None if lookup fails
        {
            'ip': str,
            'country_code': str,
            'city': str,
            'region': str,
            'loc': str (latitude,longitude),
            'timezone': str
        }
    """
    response = requests.get(
        f"{IPINFO_API_URL}/{ip_address}",
        params={"token": IPINFO_TOKEN},
        timeout=5
    )

    if response.status_code == 200:
        data = response.json()
        # Extract latitude and longitude from loc field
        lat, lon = data.get('loc', '0,0').split(',') if 'loc' in data else (None, None)

        return {
            'ip': data.get('ip'),
            'country_code': data.get('country'),
            'city': data.get('city'),
            'region': data.get('region'),
            'latitude': float(lat) if lat else None,
            'longitude': float(lon) if lon else None,
            'timezone': data.get('timezone')
        }

    logger.warning(f"IPinfo API returned status code {response.status_code}. Response body: {response.text}")

    return None


def add_geolocation(geolocation: GeoLocation, session: Optional[Session] = None) -> GeoLocation:
    """
    Add geolocation to the database.

    Args:
        geolocation: GeoLocation model
        session: Session if any

    Returns:
        GeoLocation: Saved geolocation model
    """
    with get_db_session(session) as session:
        session.add(geolocation)
        session.flush()
        return geolocation
