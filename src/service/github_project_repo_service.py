from typing import Any, Dict, Optional

from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import GitHubProjectRepo, UsageType, VersionControlSystem, GithubInstallation, GithubInstallationStatus
from sqlalchemy.orm import Session

from src.error.base_error import BlitzyError


def get_github_project_repo_by_repo_and_branch(repo_id: str, base_branch: str,
                                               session: Optional[Session] = None) -> GitHubProjectRepo:
    """
    Fetches a GitHub project repository by repository ID and branch name.

    :param repo_id: The identifier of the GitHub repository.
    :type repo_id: str
    :param base_branch: The branch name in the GitHub repository.
    :type base_branch: str
    :param session: An optional database session.
    :type session: Optional[Session]
    :return: The matching GitHubProjectRepo instance or None if not found.
    :rtype: Optional[GitHubProjectRepo]
    """
    with get_db_session(session) as session:
        project_repo = session.query(GitHubProjectRepo).filter_by(
            repo_id=repo_id,
            branch_name=base_branch,
            usage_type=UsageType.SOURCE
        ).first()

        if project_repo and not session:
            session.expunge(project_repo)
        return project_repo


def get_github_project_repo_by_repo_and_org(repo_id: str, org_name: str,
                                            session: Optional[Session] = None) -> GitHubProjectRepo:
    """
    Fetches a GitHub project repository by repository ID and org name.

    :param repo_id: The identifier of the GitHub repository.
    :type repo_id: str
    :param org_name: The org name in the GitHub project repository.
    :type org_name: str
    :param session: An optional database session.
    :type session: Optional[Session]
    :return: The matching GitHubProjectRepo instance or None if not found.
    :rtype: Optional[GitHubProjectRepo]
    """

    with get_db_session(session) as session:
        project_repo = session.query(GitHubProjectRepo).filter_by(
            repo_id=repo_id,
            org_name=org_name,
            usage_type=UsageType.SOURCE
        ).filter(
            GitHubProjectRepo.azure_project_id.isnot(None),
            GitHubProjectRepo.azure_project_id != ''
        ).first()

        if project_repo and not session:
            session.expunge(project_repo)
        return project_repo


def update_github_project_repo_by_id(github_project_repo_id: str, update_payload: Dict[Any, Any],
                                     session: Optional[Session] = None):
    """
    Updates a GitHub project repository by its ID with the provided payload.

    :param github_project_repo_id: The ID of the GitHub project repository to update.
    :param update_payload: The payload containing fields to update.
    :param session: An optional database session. A new session will be created if not provided.
    :return: None
    :raises BlitzyError: If no record is found for the provided repository ID.
    """
    with get_db_session(session) as session:
        updated = session.query(GitHubProjectRepo).filter(GitHubProjectRepo.id == github_project_repo_id).update(
            update_payload)

        if not updated:
            error_message = f"No record found for id {github_project_repo_id}"
            logger.error(error_message)
            raise BlitzyError(error_message)


def get_github_project_repo_by_repo_id(repo_id: str,
                                       session: Optional[Session] = None) -> Optional[GitHubProjectRepo]:
    with get_db_session(session) as session:
        project_repo = session.query(GitHubProjectRepo).filter(GitHubProjectRepo.repo_id == repo_id).first()

        if project_repo and not session:
            session.expunge(project_repo)

        return project_repo


def get_github_project_repo_by_project_repo_id(
        project_repo_id: str,
        session: Optional[Session] = None) -> Optional[GitHubProjectRepo]:
    """
    Fetches a GitHub project repository by its project repository ID.

    :param project_repo_id: The ID of the GitHub project repository.
    :param session: An optional database session.
    :return: The matching GitHubProjectRepo instance or None if not found.
    """
    with get_db_session(session) as session:
        project_repo = session.query(GitHubProjectRepo).filter(GitHubProjectRepo.id == project_repo_id).first()

        if project_repo and not session:
            session.expunge(project_repo)

        return project_repo
    

def get_svc_type_by_github_project_repo_id(github_project_repo_id: str,
                                           session: Optional[Session] = None) -> Optional[str]:
    """ 
    Get the svc_type (version control system type) for a repository by github_project_repo_id.
    
    This function first gets the org_id from GitHubProjectRepo table using github_project_repo_id,
    then gets the svc_type from GithubInstallation table using org_id.
    
    :param github_project_repo_id: The identifier of the GithubProjectRepo record.
    :type github_project_repo_id: str
    :param session: An optional database session.
    :type session: Optional[Session]
    :return: The svc_type (GITHUB or AZURE_DEVOPS) or None if not found.
    :rtype: Optional[str]
    """
    with get_db_session(session) as session:
        # First, get the project repo record to find org_name
        project_repo = session.query(GitHubProjectRepo).filter(
            GitHubProjectRepo.id == github_project_repo_id
        ).first()
        
        if not project_repo:
            logger.debug(f"No project repository found for github_project_repo_id {github_project_repo_id}")
            return None
            
        org_id = project_repo.org_id
        
        # Get the active installation for this org_name that the user has access to
        github_installation = session.query(GithubInstallation).filter(
            GithubInstallation.target_id == org_id,
            GithubInstallation.status == GithubInstallationStatus.ACTIVE,
        ).first()
        
        if not github_installation:
            logger.debug(f"No active installation found for org_id {org_id}")
            return None
            
        return github_installation.svc_type.value if github_installation.svc_type else None
