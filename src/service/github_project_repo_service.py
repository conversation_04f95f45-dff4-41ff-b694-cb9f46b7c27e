from typing import List, Optional

from common_models.db_client import get_db_session
from common_models.models import GitHubProjectRepo
from sqlalchemy.orm import Session


def get_github_project_repo_id_by_project_id(project_id: str, session: Optional[Session] = None) -> Optional[List[str]]:
    """
    Retrieve a list of unique GitHub repository IDs associated with a specified project.

    :param project_id: The unique identifier for the project.
    :param session: Optional existing database session.
    :return: A list of unique GitHub repository IDs related to the project.
    """
    with get_db_session(session) as session:
        github_project_repo_ids = session.query(GitHubProjectRepo.repo_id).filter(
            GitHubProjectRepo.project_id == project_id).all()

        # Convert the list of IDs into a set
        github_project_repo_id_set = {item[0] for item in github_project_repo_ids}

        return list(github_project_repo_id_set)
