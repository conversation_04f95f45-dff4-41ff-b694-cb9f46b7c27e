from typing import Dict, Optional

from blitzy_utils.enums import ProjectPhase
from common_models.db_client import get_db_session
from common_models.models import Job
from requests import Session


def save_job(job: Job, session: Optional[Session] = None) -> Job:
    """
    Save job to database.
    :param job: Job object to save.
    :param session: Session if any.
    :return: Job object.
    """
    with get_db_session(session) as session:
        session.add(job)
        session.flush()
        return job


def get_job_by_project_stage_type(project_id: str, stage_type: ProjectPhase, session: Optional[Session] = None) -> \
        Optional[Job]:
    """
    Get job by project id and stage type.
    :param project_id: Project ID.
    :param stage_type: Stage type.
    :param session: Session if any.
    :return: Job if found.
    """
    with get_db_session(session) as session:
        job = session.query(Job).filter(Job.project_id == project_id, Job.stage_type == stage_type.value).first()

        if job and not session:
            session.expunge(job)
        return job


def get_job_by_id(job_id: str, session: Optional[Session] = None) -> Optional[Dict]:
    """
    Get job by ID.
    :param job_id: Job ID.
    :param session: Session if any.
    :return: Job if found.
    """
    with get_db_session(session) as session:
        job = session.query(Job).with_entities(
            Job.job_metadata,
            Job.status,
            Job.stage_type,
            Job.created_at,
            Job.updated_at
        ).filter(Job.id == job_id).first()

        if job and not session:
            session.expunge(job)

        job_dict = {
            "jobMetadata": job[0],
            "status": job[1].value,
            "stageType": job[2],
            "createdAt": int(job[3].timestamp()),
            "updatedAt": int(job[4].timestamp())
        }
        return job_dict
