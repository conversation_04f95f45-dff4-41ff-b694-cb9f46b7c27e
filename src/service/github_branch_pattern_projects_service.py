from typing import List, Optional

from common_models.db_client import get_db_session
from common_models.models import GithubBranchPatternProject
from sqlalchemy.orm import Session


def get_branch_id_by_project_id(project_id: str, session: Optional[Session] = None) -> Optional[List[str]]:
    """
    Retrieve branch pattern IDs associated with a specific project.

    :param project_id: The ID of the project to retrieve branch pattern IDs for.
    :type project_id: str
    :param session: The database session to be used, or None to create a new one.
    :type session: Optional[Session]
    :return: A list of branch pattern IDs associated with the given project ID, or
        None if no IDs are found.
    :rtype: Optional[List[str]]
    """
    with get_db_session(session) as session:
        branch_pattern_ids = session.query(GithubBranchPatternProject.pattern_id).filter(
            GithubBranchPatternProject.project_id == project_id).all()

        pattern_ids = {item[0] for item in branch_pattern_ids}
        return list(pattern_ids)
