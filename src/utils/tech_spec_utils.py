from typing import Optional

from blitzy_utils.common import generate_technical_spec_document_path
from blitzy_utils.consts import TECH_SPECIFICATION_NAME
from blitzy_utils.logger import logger
from common_models.models import Project, TechnicalSpec, TechSpecJobType
from google.api_core import exceptions

from src.consts import BLOB_NAME, GCS_BUCKET_NAME, storage_client


def generate_technical_spec_document_url(repo_url: str, tech_spec_id: str) -> str:
    """
    Generates technical specification document URL.
    :param repo_url: Project repo URL.
    :param tech_spec_id: Tech Spec ID
    :return: File path.
    """
    file_path = f"{BLOB_NAME}/{repo_url}/blitzy/documentation/{TECH_SPECIFICATION_NAME}_{tech_spec_id}.md"
    return file_path


def get_tech_spec_markdown_path(tech_spec: TechnicalSpec, project_info: Project, company_id: str) -> Optional[str]:
    if tech_spec.job_type == TechSpecJobType.NEW_PRODUCT:
        if tech_spec.job_metadata:
            repo_name = tech_spec.job_metadata.get("repo_name")
            file_path = generate_technical_spec_document_url(repo_name, tech_spec.id)
            logger.debug(f"Got file_path {file_path} using repo_name {repo_name}")
        else:
            file_path = generate_technical_spec_document_url(project_info.repo_url, tech_spec.id)
            logger.debug(f"Got file_path {file_path} using repo_url {project_info.repo_url}")
    else:
        job_metadata = tech_spec.job_metadata
        if not job_metadata:
            logger.warning(
                f"Job metadata not found for the technical specification {tech_spec.id}. Skipping PDF generation.")
            return None

        file_path = generate_technical_spec_document_path(job_metadata, GCS_BUCKET_NAME)

    bucket = storage_client.bucket(GCS_BUCKET_NAME)
    blob = bucket.blob(file_path)
    if not blob.exists():
        logger.info("Created new bucket")
        bucket = storage_client.bucket(GCS_BUCKET_NAME + f"-{company_id}")
        blob = bucket.blob(file_path)
    try:
        if blob.exists():
            return file_path
    except exceptions.NotFound:
        logger.warning(f"File {file_path} not found.")
        return None
