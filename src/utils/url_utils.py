import urllib.parse


class URLUtils:

    @staticmethod
    def decode_file_path(encoded_path: str) -> str:
        return urllib.parse.unquote(encoded_path)

    @staticmethod
    def validate_file_path(file_path: str) -> str:
        if not file_path:
            raise ValueError('File path cannot be empty')

        # Remove leading/trailing slashes and whitespace
        cleaned = file_path.strip().strip('/')

        # Check for dangerous patterns
        dangerous_patterns = ['..', '//', '\\']
        for pattern in dangerous_patterns:
            if pattern in cleaned:
                raise ValueError(f'Invalid file path: contains {pattern}')

        return cleaned
