from typing import Optional

from common_models.db_client import get_db_session
from common_models.models import UserConfig, User
from sqlalchemy.orm import Session


def get_user_config_by_user_id(user_id: str, session: Optional[Session] = None) -> UserConfig:
    with get_db_session(session) as session:
        user_config = session.query(UserConfig).filter(UserConfig.user_id == user_id).first()
        return user_config

def get_user_info_by_user_id(user_id: str, session: Optional[Session] = None) -> User:
    with get_db_session(session) as session:
        user = session.query(User).filter(User.id == user_id).first()
        return user