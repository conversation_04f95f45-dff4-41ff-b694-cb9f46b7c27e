"""
Redis cache utility for distributed caching with Google Cloud Memorystore.
Provides a fallback to local cachetools.TTLCache when Redis is unavailable.
"""

import hashlib
import os
import logging
from typing import Op<PERSON>, <PERSON><PERSON>, Any
from cachetools import TTLCache

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

logger = logging.getLogger(__name__)


class DistributedCache:
    """
    Distributed cache implementation using Redis with local cache fallback.
    
    This class provides a unified interface for caching that can use either:
    1. Redis (Google Cloud Memorystore) for distributed caching across instances
    2. Local TTLCache as a fallback when Redis is unavailable
    """
    
    def __init__(self, default_ttl: int = 600, maxsize: int = 1000):
        """
        Initialize the distributed cache.
        
        Args:
            default_ttl: Default time-to-live for cache entries in seconds
            maxsize: Maximum size for local cache fallback
        """
        self.default_ttl = default_ttl
        self.maxsize = maxsize
        self.redis_client = None
        self.local_cache = TTLCache(maxsize=maxsize, ttl=default_ttl)
        
        # Try to initialize Redis connection
        self._init_redis()
    
    def _init_redis(self) -> None:
        """Initialize Redis connection if available and configured."""
        if not REDIS_AVAILABLE:
            logger.warning("Redis library not available, using local cache only")
            return
        
        try:
            redis_host = os.environ.get('REDIS_HOST')
            redis_port = int(os.environ.get('REDIS_PORT', '6379'))
            redis_password = os.environ.get('REDIS_PASSWORD')
            redis_ssl = os.environ.get('REDIS_SSL', 'false').lower() == 'true'
            redis_db = int(os.environ.get('REDIS_DB', '0'))
            
            if not redis_host:
                logger.info("REDIS_HOST not configured, using local cache only")
                return
            
            # Create Redis connection
            self.redis_client = redis.Redis(
                host=redis_host,
                port=redis_port,
                password=redis_password if redis_password else None,
                ssl=redis_ssl,
                db=redis_db,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # Test the connection
            self.redis_client.ping()
            logger.info(f"Successfully connected to Redis at {redis_host}:{redis_port}")
            
        except Exception as e:
            logger.warning(f"Failed to connect to Redis: {e}. Using local cache only.")
            self.redis_client = None
    
    def _generate_key(self, key_parts: Tuple[str, ...]) -> str:
        """
        Generate a cache key from multiple parts.
        
        Args:
            key_parts: Tuple of strings to combine into a cache key
            
        Returns:
            SHA256 hash of the combined key parts
        """
        combined = "_".join(key_parts)
        return hashlib.sha256(combined.encode()).hexdigest()
    
    def get(self, key_parts: Tuple[str, ...]) -> Optional[Any]:
        """
        Get a value from the cache.
        
        Args:
            key_parts: Tuple of strings to combine into a cache key
            
        Returns:
            Cached value if found, None otherwise
        """
        cache_key = self._generate_key(key_parts)
        
        # Try Redis first
        if self.redis_client:
            try:
                value = self.redis_client.get(cache_key)
                if value is not None:
                    logger.debug(f"Cache hit in Redis for key: {cache_key[:16]}...")
                    # For simplicity, we store strings in Redis
                    # In production, you might want to use JSON or pickle
                    return eval(value) if value.startswith('(') else value
            except Exception as e:
                logger.warning(f"Redis get failed: {e}. Falling back to local cache.")
        
        # Fallback to local cache
        value = self.local_cache.get(cache_key)
        if value is not None:
            logger.debug(f"Cache hit in local cache for key: {cache_key[:16]}...")
        
        return value
    
    def set(self, key_parts: Tuple[str, ...], value: Any, ttl: Optional[int] = None) -> None:
        """
        Set a value in the cache.
        
        Args:
            key_parts: Tuple of strings to combine into a cache key
            value: Value to cache
            ttl: Time-to-live in seconds (uses default if None)
        """
        cache_key = self._generate_key(key_parts)
        ttl = ttl or self.default_ttl
        
        # Try Redis first
        if self.redis_client:
            try:
                # Convert value to string for Redis storage
                redis_value = str(value) if not isinstance(value, str) else value
                self.redis_client.setex(cache_key, ttl, redis_value)
                logger.debug(f"Cached in Redis for key: {cache_key[:16]}... (TTL: {ttl}s)")
            except Exception as e:
                logger.warning(f"Redis set failed: {e}. Falling back to local cache.")
        
        # Always cache locally as well for faster access
        self.local_cache[cache_key] = value
        logger.debug(f"Cached locally for key: {cache_key[:16]}... (TTL: {ttl}s)")
    
    def delete(self, key_parts: Tuple[str, ...]) -> None:
        """
        Delete a value from the cache.
        
        Args:
            key_parts: Tuple of strings to combine into a cache key
        """
        cache_key = self._generate_key(key_parts)
        
        # Delete from Redis
        if self.redis_client:
            try:
                self.redis_client.delete(cache_key)
                logger.debug(f"Deleted from Redis for key: {cache_key[:16]}...")
            except Exception as e:
                logger.warning(f"Redis delete failed: {e}")
        
        # Delete from local cache
        self.local_cache.pop(cache_key, None)
        logger.debug(f"Deleted from local cache for key: {cache_key[:16]}...")
    
    def clear(self) -> None:
        """Clear all cache entries."""
        # Note: This only clears local cache, not Redis
        # In production, you might want to use Redis FLUSHDB with caution
        self.local_cache.clear()
        logger.debug("Cleared local cache")
    
    def health_check(self) -> dict:
        """
        Check the health of cache connections.
        
        Returns:
            Dictionary with health status information
        """
        status = {
            'local_cache': True,
            'local_cache_size': len(self.local_cache),
            'redis_available': False,
            'redis_connected': False
        }
        
        if self.redis_client:
            try:
                self.redis_client.ping()
                status['redis_available'] = True
                status['redis_connected'] = True
                
                # Get Redis info
                info = self.redis_client.info()
                status['redis_memory_used'] = info.get('used_memory_human', 'unknown')
                status['redis_connected_clients'] = info.get('connected_clients', 'unknown')
                
            except Exception as e:
                status['redis_error'] = str(e)
        
        return status


# Global cache instance for Azure token caching
# TTL of 600 seconds (10 minutes) to match Azure auth code validity
azure_distributed_cache = DistributedCache(default_ttl=600, maxsize=1000)


def get_cached_azure_token_exchange(auth_code: str, redirect_uri: str) -> Optional[Tuple[str, str]]:
    """
    Get cached Azure token exchange result using distributed cache.
    
    Args:
        auth_code: The authorization code
        redirect_uri: The redirect URI used in the exchange
        
    Returns:
        Tuple of (access_token, refresh_token) if cached, None otherwise
    """
    result = azure_distributed_cache.get(('azure_auth_code', auth_code, redirect_uri))
    return result


def cache_azure_token_exchange(auth_code: str, redirect_uri: str, 
                              access_token: str, refresh_token: str) -> None:
    """
    Cache Azure token exchange result using distributed cache.
    
    Args:
        auth_code: The authorization code that was exchanged
        redirect_uri: The redirect URI used in the exchange
        access_token: The access token received
        refresh_token: The refresh token received
    """
    azure_distributed_cache.set(
        ('azure_auth_code', auth_code, redirect_uri),
        (access_token, refresh_token)
    )
