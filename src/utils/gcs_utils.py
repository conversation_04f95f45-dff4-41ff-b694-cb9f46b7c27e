from blitzy_utils.logger import logger
from google.cloud import storage
from google.cloud.exceptions import Conflict, Forbidden, NotFound

from src.error.errors import ConflictingResourceError


def create_bucket(bucket_name, location='US'):
    client = storage.Client()

    try:
        # Create the bucket
        bucket = client.bucket(bucket_name)
        bucket.location = location
        bucket = client.create_bucket(bucket)

        logger.info(f"Bucket {bucket.name} created successfully in {bucket.location}")
        return bucket

    except Conflict:
        logger.warning(f"Bucket {bucket_name} already exists")
        raise ConflictingResourceError(f"Bucket {bucket_name} already exists")
    except Exception as e:
        logger.error(f"Error creating bucket: {e}")
        return None


def bucket_exists(bucket_name):
    client = storage.Client()

    try:
        client.get_bucket(bucket_name)
        return True
    except NotFound:
        return False
    except Forbidden:
        logger.warning(f"Access denied to bucket {bucket_name}. It may exist but you don't have permission.")
        raise ConflictingResourceError(
            f"Access denied to bucket {bucket_name}. It may exist but you don't have permission.")
    except Exception as e:
        logger.error(f"Error checking bucket: {e}")
        raise e
