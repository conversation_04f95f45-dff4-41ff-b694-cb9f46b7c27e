
from blitzy_utils.common import blitzy_exponential_retry
from blitzy_utils.logger import logger
from google.cloud.exceptions import NotFound
from src.consts import GCS_BUCKET_NAME, storage_client

@blitzy_exponential_retry()
def copy_gcs_file(source_path, destination_path):
    """
    Copies a file from one path to another in Google Cloud Storage (GCS).
    
    This function includes retry logic for handling rate limiting and transient errors,
    and checks for file existence before attempting to copy.

    :param source_path: The path of the source file in the bucket.
    :type source_path: str

    :param destination_path: The path where the file will be copied to in
        the bucket.
    :type destination_path: str

    :return: Returns True upon successful copy operation.
    :rtype: bool
    
    :raises NotFound: If the source file does not exist.
    :raises Exception: For other GCS operation errors.
    """
    bucket = storage_client.bucket(GCS_BUCKET_NAME)
    source_blob = bucket.blob(source_path)
    
    # Check if source file exists before attempting to copy
    if not source_blob.exists():
        logger.warning(f"Source file does not exist: {source_path}")
        raise NotFound(f"Source file not found: {source_path}")
    
    # Perform the copy operation
    bucket.copy_blob(source_blob, bucket, destination_path)
    
    logger.info(f"File {source_path} copied to {destination_path} in bucket {GCS_BUCKET_NAME}.")
    return True


def copy_gcs_file_if_exists(source_path, destination_path):
    """
    Copies a file from one path to another in Google Cloud Storage (GCS) if the source exists.
    
    This is a safe wrapper around copy_gcs_file that doesn't raise an exception
    if the source file doesn't exist.

    :param source_path: The path of the source file in the bucket.
    :type source_path: str

    :param destination_path: The path where the file will be copied to in
        the bucket.
    :type destination_path: str

    :return: Returns True if file was copied, False if source doesn't exist.
    :rtype: bool
    """
    try:
        return copy_gcs_file(source_path, destination_path)
    except NotFound:
        logger.warning(f"Source file does not exist, skipping copy: {source_path}")
        return False
    except Exception as e:
        logger.error(f"Error copying file from {source_path} to {destination_path}: {str(e)}")
        raise
