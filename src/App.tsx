import {AuthProvider, useAuthContext} from './context/auth-context';
import {useMemo} from 'react';
import {
    Navigate,
    createBrowser<PERSON><PERSON>er,
    RouterProvider,
    RouteObject,
} from 'react-router-dom';
import {SignInPanel} from './panel/home/<USER>';
import {Home} from './home';
import {Workspace} from './workspace';
import {WelcomePanel} from './panel/home/<USER>';
import {ProjectsPanel} from './panel/workspace/projects-panel';
import {SettingsPanel} from './panel/workspace/settings/settings-panel';
import {GettingStartedPanel} from './panel/workspace/getting-started-panel';
import {SignUpPanel} from './panel/home/<USER>';
import {ProjectDocumentPanel} from './panel/workspace/project-document-panel';
import {PendingCodePanel} from './panel/workspace/pending-code-panel';
import {ProjectStatusPanel} from './panel/workspace/project-status-panel';
import {ProjectProvider} from './context/project-context';
import {ForgotPasswordPanel} from './panel/home/<USER>';
import {ProjectPromptPanel} from './panel/workspace/project-prompt-panel';
import {AlertProvider} from './context/alert-context';
import {UpgradeToEnterprisePanel} from './panel/workspace/upgrade-to-enterprise-panel';
import {VerifyEmailPanel} from './panel/workspace/verify-email-panel';
import {PageNotFound} from './panel/home/<USER>';
import {ProjectTypePanel} from './panel/workspace/project-type-panel';
import {ProjectNamePanel} from './panel/workspace/project-name-panel';
import {GitConnectPanel} from './panel/workspace/back-prop/git-connect-panel';
import {GitLocationWrapper} from './panel/workspace/back-prop/git-location-wrapper';
import {CompleteRegistrationPanel} from './panel/workspace/complete-registration-panel';
import {SSO_SignInPanel} from './panel/home/<USER>';
import './App.css';

function ProtectedRoute({children}: {children: React.ReactNode}) {
    const {userProfile, isUserProfileLoading} = useAuthContext();

    if (isUserProfileLoading) {
        return null;
    }

    if (!userProfile) {
        return <Navigate to="/sign-in" replace />;
    }

    return <>{children}</>;
}

function Screens() {
    const {userProfile} = useAuthContext();
    const routes = useMemo(() => {
        const routes: RouteObject[] = [
            {
                path: '/',
                element: <Home />,
                children: [
                    {
                        index: true,
                        element: <SignInPanel />,
                    },
                    {
                        path: 'welcome',
                        element: <WelcomePanel />,
                    },
                    {
                        path: 'sign-up',
                        element: <SignUpPanel />,
                    },
                    {
                        path: 'sign-in',
                        element: <SignInPanel />,
                    },
                    {
                        path: 'sso-sign-in',
                        element: <SSO_SignInPanel />,
                    },
                    {
                        path: 'forgot-password',
                        element: <ForgotPasswordPanel />,
                    },
                    {
                        path: 'verify-email',
                        element: <VerifyEmailPanel />,
                    },
                    {
                        path: 'complete-registration',
                        element: <CompleteRegistrationPanel />,
                    },
                ],
            },
        ];

        routes.push({
            path: '/workspace',
            element: (
                <ProtectedRoute>
                    <Workspace userId={userProfile?.id} />
                </ProtectedRoute>
            ),
            children: [
                {
                    index: true,
                    element: <ProjectsPanel />,
                },
                {
                    path: 'getting-started',
                    element: <GettingStartedPanel />,
                },
                {
                    path: 'project-type',
                    element: <ProjectTypePanel />,
                },
                {
                    path: 'project-name',
                    element: <ProjectNamePanel />,
                },
                {
                    path: 'choose-project-type',
                    element: <GettingStartedPanel />,
                },
                {
                    path: 'projects',
                    element: <ProjectsPanel />,
                },
                {
                    path: 'settings',
                    children: [
                        {
                            path: 'profile',
                            element: <SettingsPanel initialTab="profile" />,
                        },
                        {
                            path: 'password',
                            element: <SettingsPanel initialTab="password" />,
                        },
                        {
                            path: 'integrations',
                            element: (
                                <SettingsPanel initialTab="integrations" />
                            ),
                        },
                        {
                            path: 'plan',
                            element: <SettingsPanel initialTab="plan" />,
                        },
                        {
                            path: 'notifications',
                            element: (
                                <SettingsPanel initialTab="notifications" />
                            ),
                        },
                    ],
                },
                {
                    path: 'project/:projectId',
                    element: <ProjectProvider />,
                    children: [
                        {
                            index: true,
                            element: <Navigate to="status" />,
                        },
                        {
                            path: 'prompt',
                            element: <ProjectPromptPanel />,
                        },
                        {
                            path: 'tech-spec',
                            element: (
                                <ProjectDocumentPanel
                                    key="tech_spec"
                                    docType="tech_spec"
                                />
                            ),
                        },
                        {
                            path: 'pending-code',
                            element: <PendingCodePanel />,
                        },
                        {
                            path: 'status',
                            element: <ProjectStatusPanel />,
                        },
                        {
                            path: 'back-prop',
                            children: [
                                {
                                    path: 'connect-git',
                                    element: <GitConnectPanel />,
                                },
                                {
                                    path: 'new-product',
                                    element: (
                                        <GitLocationWrapper type="new-product" />
                                    ),
                                },
                                {
                                    path: 'source-code',
                                    element: (
                                        <GitLocationWrapper type="onboard-code" />
                                    ),
                                },
                                {
                                    path: 'refactor-code',
                                    element: (
                                        <GitLocationWrapper type="refactor-code" />
                                    ),
                                },
                                {
                                    path: 'custom',
                                    element: (
                                        <GitLocationWrapper type="custom" />
                                    ),
                                },
                            ],
                        },
                        {
                            path: '*',
                            element: <Navigate to="status" />,
                        },
                    ],
                },
                {
                    path: 'enterprise',
                    element: <UpgradeToEnterprisePanel />,
                },
            ],
        });

        routes.push({
            path: '*',
            element: <PageNotFound />,
        });

        return routes;
    }, [userProfile]);

    const router = useMemo(() => createBrowserRouter(routes), [routes]);

    return <RouterProvider router={router} />;
}

function App() {
    return (
        <AlertProvider>
            <AuthProvider>
                <Screens />
            </AuthProvider>
        </AlertProvider>
    );
}

export default App;
