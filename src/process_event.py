import traceback
from typing import Any, Dict, Optional

from blitzy_utils.consts import DEFAULT_NAME
from blitzy_utils.enums import ProjectPhase, ProjectPhaseAction
from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import (CodeGeneration, Job, Project, ProjectRun,
                                  ProjectRunStatus, ReportStatus,
                                  SoftwareRequirement, Status, TechnicalSpec)
from sqlalchemy.orm import Session

from src.consts import PLATFORM_URL
from src.event.blitzy_commit_event import process_blitzy_commit_event
from src.event.code_graph_event import attempt_branch_lock_release
from src.event.tech_spec_context import process_code_download_event
from src.service.code_gen_service import (get_code_gen_by_id,
                                          update_code_gen_by_id)
from src.service.project_run_service import (
    update_project_run_by_code_gen_id, update_project_run_by_tech_spec_id)
from src.service.project_service import get_project_by_id
from src.service.tech_spec_service import (get_tech_spec_by_id,
                                           update_tech_spec_by_id)
from src.utils.email_utils import (
    send_code_generation_completion_notification,
    send_tech_spec_completion_notification)
from src.utils.pdf import convert_to_pdf
from src.utils.tech_spec_utils import get_tech_spec_markdown_path
from src.utils.user_utils import (get_user_config_by_user_id,
                                  get_user_info_by_user_id)


def process_payload(payload: Dict[str, Any]):
    """
    Process received payload.
    :param payload: Payload.
    :return:
    """
    project_phase = payload.get("phase")
    project_id = payload.get("projectId")
    job_id = payload.get("jobId")
    metadata = payload.get("metadata")

    if not project_id:
        logger.warning(f"Project ID not found in {payload}. Event won't be processed.")
        return

    job_status = payload.get("status")

    if job_status not in Status.__members__:
        raise Exception(f"Status {job_status} is not a valid status in project {project_id}")

    status = Status[job_status]

    project_info = get_project_by_id(project_id)
    if not project_info:
        logger.warning(f"Project {project_id} does not exist. Event won't be processed.")
        return

    logger.info(f"Starting Project {project_id} event processing for phase {project_phase}")

    if project_phase == ProjectPhase.SOFTWARE_REQUIREMENTS.value:
        process_software_requirement_event(project_info, job_id, status, metadata)
    elif project_phase == ProjectPhase.TECHNICAL_SPECIFICATION.value:
        process_technical_specification_event(project_info, job_id, status, metadata, payload)
    elif project_phase == ProjectPhase.CODE_GENERATION.value:
        process_code_generation_event(project_info, job_id, status, metadata, payload)
    elif project_phase == ProjectPhase.REVERSE_CODE_GENERATION.value:
        process_code_generation_event(project_info, job_id, status, metadata, payload)
    elif project_phase == ProjectPhase.CODE_DOWNLOAD.value:
        process_code_download_event(project_info, job_id, status, metadata, payload)
    else:
        logger.info("Event is not supported yet.")

    logger.info(f"Finished Project {project_id} event processing for phase {project_phase}")


def process_software_requirement_event(project_info: Project, job_id: str, status: Status,
                                       metadata: Dict[str, Any]):
    with get_db_session() as session:
        # Update software requirements table with job id.
        software_requirement_payload = {
            SoftwareRequirement.job_id: job_id,
            SoftwareRequirement.status: status,
            SoftwareRequirement.job_metadata: metadata
        }

        software_req_updated = session.query(SoftwareRequirement).filter(
            SoftwareRequirement.project_id == project_info.id,
        ).update(software_requirement_payload)

        if not software_req_updated:
            raise Exception(f"Failed to update software requirements for the project {project_info.id}.")

        job_payload = {
            Job.status: status,
            Job.job_metadata: metadata,
        }

        job_updated = session.query(Job).filter(Job.id == job_id).update(job_payload)
        if not job_updated:
            raise Exception(f"Failed to update software requirement job metadata for the project {project_info.id}.")

        session.commit()
        logger.info(f"Software requirement and job metadata updated for the project {project_info.id}")


def process_technical_specification_event(project_info: Project, job_id: str, status: ProjectRunStatus,
                                          metadata: Dict[str, Any],
                                          payload: Dict[str, Any]):
    if payload.get("action"):
        process_technical_specification_action(project_info, job_id, status, metadata, payload)
    else:
        operate_technical_specification_event(project_info, job_id, status, metadata, payload)


def process_code_generation_event(project_info: Project, job_id: str, status: Status,
                                  metadata: Dict[str, Any],
                                  payload: Dict[str, Any]):
    code_gen_id = payload.get("code_gen_id")
    if not code_gen_id:
        logger.warning(f"Code gen ID not found in {payload}. Event won't be processed.")
        return

    if status == Status.FAILED:
        logger.warning(
            f"Failed event will not be processed for project {project_info.id} with code gen id {code_gen_id}")
        return

    code_gen = get_code_gen_by_id(code_gen_id)
    if not code_gen:
        logger.warning(f"Code gen {code_gen_id} does not exist. Event won't be processed.")
        return

    user_config = get_user_config_by_user_id(project_info.user_id)
    logger.debug(f"User config for user {project_info.user_id}: {user_config}")

    with get_db_session() as session:
        # Update software requirements table with job id.
        project_run_status = ProjectRunStatus[payload["status"]]
        job_payload = {
            ProjectRun.status: project_run_status,
            ProjectRun.job_metadata: metadata,
        }

        update_project_run_by_code_gen_id(code_gen.id, job_payload, session=session)

        code_gen_payload = {
            CodeGeneration.status: status,
        }

        update_code_gen_by_id(code_gen_id, code_gen_payload, session=session)

        process_blitzy_commit_event(payload, session)
        session.commit()
        logger.info(f"Code generation and job metadata updated for the project {project_info.id}")

        # Send email notification for successful completion
        if status == Status.DONE and user_config.code_gen_notification_enabled:
            send_code_generation_email(project_info)


def operate_technical_specification_event(project_info: Project, job_id: str, status: Status, metadata: Dict[str, Any],
                                          payload: Dict[str, Any]):
    tech_spec_id = payload.get("tech_spec_id")
    if not tech_spec_id:
        logger.warning(f"Tech spec ID not found in {payload}. Event won't be processed.")
        return

    tech_spec = get_tech_spec_by_id(tech_spec_id)
    if not tech_spec:
        logger.warning(f"Tech spec {tech_spec_id} does not exist. Event won't be processed.")
        return

    user_config = get_user_config_by_user_id(project_info.user_id)
    logger.debug(f"User config for user {project_info.user_id}: {user_config}")

    with get_db_session() as session:
        # Update software requirements table with job id.
        tech_spec_payload = {
            TechnicalSpec.status: status,
        }

        update_tech_spec_by_id(tech_spec_id, tech_spec_payload, session=session)

        project_run_status = ProjectRunStatus[payload["status"]]
        job_payload = {
            ProjectRun.status: project_run_status,
            ProjectRun.job_metadata: metadata,
        }

        update_project_run_by_tech_spec_id(tech_spec.id, job_payload, session=session)

        if status == Status.DONE:
            logger.info("Technical Specification is ready, generating PDF and uploading to GCS")
            process_technical_spec_generate_pdf_action(project_info, payload, tech_spec, session)

            # Send email notification for successful completion
            if user_config and user_config.tech_spec_notification_enabled:
                send_tech_spec_email(project_info)

            # Release branch lock if it exists.
            branch_id = payload.get("branch_id")
            if branch_id:
                attempt_branch_lock_release(branch_id, session=session)
        session.commit()
        logger.info(f"Technical specification and job metadata updated for the project {project_info.id}")


def process_technical_specification_action(project_info: Project, job_id: str, status: Status, metadata: Dict[str, Any],
                                           payload: Dict[str, Any]):
    action = payload["action"]
    if action == ProjectPhaseAction.GENERATE_PDF.value:
        process_technical_spec_generate_pdf_action(project_info, payload)
        return

    logger.info(f"Action {action} is not supported yet.")


def process_technical_spec_generate_pdf_action(project_info: Project, payload: Dict[str, Any],
                                               tech_spec: Optional[TechnicalSpec] = None,
                                               session: Optional[Session] = None):
    generate_tech_spec_pdf(project_info, tech_spec, payload)
    with get_db_session(session) as session:
        update_tech_spec_pdf_report_status_by_project_id(project_info.id, ReportStatus.READY, session=session)
        session.commit()
        logger.info(f"Technical specification PDF generated for the project {project_info.id}")


def generate_tech_spec_pdf(project_info: Project, tech_spec: TechnicalSpec, payload: Dict[str, Any]):
    try:
        project_name = project_info.name
        tech_spec_id = payload.get("tech_spec_id")
        if not tech_spec_id:
            logger.warning(f"Tech spec ID not found in {payload}. Event won't be processed.")
            return

        if not tech_spec:
            logger.info(f"Will fetch tech spec details by ID {tech_spec_id}")
            tech_spec = get_tech_spec_by_id(tech_spec_id)

        user_info = get_user_info_by_user_id(project_info.user_id)
        company_id = user_info.company_id
        if not company_id:
            logger.info(f"User {user_info.id} has no company_id, setting empty value.")
            company_id = ""
        file_path = get_tech_spec_markdown_path(tech_spec, project_info, company_id)
        # Send request to PDF conversion service
        pdf_response = convert_to_pdf(
            project_name=project_name,
            file_path=file_path,
            company_id=company_id
        )

        if not pdf_response or pdf_response.status_code != 200:
            logger.error(f"Failed to convert markdown to PDF for project {project_info.id}")
        logger.info(f"Successfully converted markdown to PDF for project {project_info.id}")
    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error during PDF conversion for project {project_info.id}: {str(e)}")


def update_tech_spec_pdf_report_status_by_project_id(project_id: str, report_status: ReportStatus,
                                                     session: Optional[Session] = None):
    with get_db_session(session) as session:
        update_payload = {
            TechnicalSpec.pdf_report_status: report_status
        }
        technical_spec_updated = session.query(TechnicalSpec).filter(
            TechnicalSpec.project_id == project_id,
        ).update(update_payload)

        if not technical_spec_updated:
            logger.error(f"Failed to update technical specification table for project {project_id}")
            raise Exception(f"Failed to update technical specification for project {project_id}")


def send_tech_spec_email(project_info: Project):
    """
    Send tech spec completion email notification to the project owner.

    Args:
        project_info: Project information containing user details
    """
    try:
        if not project_info.user or not project_info.user.email:
            logger.warning(f"No user email found for project {project_info.id}. Cannot send notification.")
            return

        # Get username from user info
        username = project_info.user.first_name + " " + project_info.user.last_name

        # Generate project URL
        project_url = f"{PLATFORM_URL}/workspace/project/{project_info.id}"
        notification_preferences_url = f"{PLATFORM_URL}/workspace/settings/notifications"

        logger.debug(f"Tech spec email URLs - project_url: {project_url}, "
                     f"notification_preferences_url: {notification_preferences_url}")

        success = send_tech_spec_completion_notification(
            to_email=project_info.user.email,
            username=username,
            project_name=project_info.name,
            project_url=project_url,
            notification_preferences_url=notification_preferences_url
        )

        if success:
            logger.info("Tech spec completion email sent successfully")
        else:
            logger.error(f"Failed to send tech spec completion email for project {project_info.id}")

    except Exception as e:
        logger.error(f"Error sending tech spec completion email for project {project_info.id}: {str(e)}")


def send_code_generation_email(project_info: Project):
    """
    Send code generation completion email notification to the project owner.

    Args:
        project_info: Project information containing user details
    """
    try:
        if not project_info.user or not project_info.user.email:
            logger.warning(f"No user email found for project {project_info.id}. Cannot send notification.")
            return

        # Get username from user info
        username = project_info.user.first_name or project_info.user.email.split('@')[0]

        # Generate project URL
        project_url = f"{PLATFORM_URL}/workspace/project/{project_info.id}"
        notification_preferences_url = f"{PLATFORM_URL}/workspace/settings/notifications"

        logger.debug(f"Code generation email URLs - project_url: {project_url}, "
                     f"notification_preferences_url: {notification_preferences_url}")

        success = send_code_generation_completion_notification(
            to_email=project_info.user.email,
            username=username,
            project_name=project_info.name,
            project_url=project_url,
            notification_preferences_url=notification_preferences_url
        )

        if success:
            logger.info("Code generation completion email sent successfully")
        else:
            logger.error(f"Failed to send code generation completion email for project {project_info.id}")

    except Exception as e:
        logger.error(f"Error sending code generation completion email for project {project_info.id}: {str(e)}")
