// Data models for block-level markdown editing system

export interface Document {
    id: string;
    projectId: string;
    title: string;
    contentFull: string;
    createdAt: Date;
    updatedAt: Date;
    version: string;
    docType: 'tech_spec' | 'software_req' | 'prd';
}

export interface DocumentBlock {
    id: string;
    documentId: string;
    blockType: 'heading' | 'paragraph' | 'list' | 'code' | 'table' | 'blockquote' | 'image';
    headingLevel?: number; // 1-6 for headings
    headingText?: string;
    content: string;
    positionStart: number; // Character position in full document
    positionEnd: number;
    orderIndex: number; // Order within document
    parentBlockId?: string; // For nested structures
    createdAt: Date;
    updatedAt: Date;
    astNodeType: string; // AST node type from markdown parser
    astMetadata: Record<string, any>; // Additional AST data
}

export interface TableOfContents {
    id: string;
    documentId: string;
    tocStructure: TOCItem[];
    generatedAt: Date;
}

export interface TOCItem {
    id: string;
    tocId: string;
    blockId: string;
    title: string;
    level: number; // Heading level 1-6
    orderIndex: number;
    parentItemId?: string;
    anchorId: string; // For navigation
    children?: TOCItem[]; // Nested structure
}

export interface EditSession {
    id: string;
    userId: string;
    documentId: string;
    blockId: string;
    contentDraft: string;
    startedAt: Date;
    lastModified: Date;
    isActive: boolean;
    editorState: Record<string, any>; // TipTap editor state
}

export interface BlockVersion {
    id: string;
    blockId: string;
    content: string;
    userId: string;
    createdAt: Date;
    changeType: 'create' | 'update' | 'delete' | 'move';
    diffData: Record<string, any>; // Diff information
}

// Frontend-specific interfaces
export interface MarkdownEditorState {
    document: Document | null;
    blocks: DocumentBlock[];
    tableOfContents: TableOfContents | null;
    activeBlockId: string | null;
    editingSessions: Map<string, EditSession>;
    isLoading: boolean;
    error: string | null;
}

export interface BlockEditingProps {
    block: DocumentBlock;
    isEditing: boolean;
    onStartEdit: (blockId: string) => void;
    onSaveBlock: (blockId: string, content: string) => Promise<void>;
    onCancelEdit: (blockId: string) => void;
    onContentChange: (blockId: string, content: string) => void;
}

export interface TOCNavigationProps {
    tableOfContents: TableOfContents;
    activeBlockId: string | null;
    onNavigateToBlock: (blockId: string) => void;
    onToggleExpand: (itemId: string) => void;
    expandedItems: Set<string>;
}

// API Response types
export interface GetDocumentResponse {
    document: Document;
    blocks: DocumentBlock[];
    tableOfContents: TableOfContents;
}

export interface UpdateBlockRequest {
    blockId: string;
    content: string;
    editorState?: Record<string, any>;
}

export interface UpdateBlockResponse {
    block: DocumentBlock;
    updatedDocument: Document;
    newTableOfContents?: TableOfContents;
}

export interface CreateBlockRequest {
    documentId: string;
    blockType: DocumentBlock['blockType'];
    content: string;
    insertAfterBlockId?: string;
    parentBlockId?: string;
}

export interface DeleteBlockRequest {
    blockId: string;
}

// AST-related types
export interface MarkdownASTNode {
    type: string;
    position: {
        start: { line: number; column: number; offset: number };
        end: { line: number; column: number; offset: number };
    };
    children?: MarkdownASTNode[];
    value?: string;
    depth?: number; // For headings
    ordered?: boolean; // For lists
    lang?: string; // For code blocks
    meta?: string; // For code blocks
}

export interface ParsedMarkdownDocument {
    ast: MarkdownASTNode;
    blocks: DocumentBlock[];
    tableOfContents: TOCItem[];
}

// Editor configuration
export interface MarkdownEditorConfig {
    enableBlockEditing: boolean;
    enableTableOfContents: boolean;
    autoSaveInterval: number; // milliseconds
    maxBlockSize: number; // characters
    supportedBlockTypes: DocumentBlock['blockType'][];
}
