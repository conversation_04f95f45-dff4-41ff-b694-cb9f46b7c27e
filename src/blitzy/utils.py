from json import dumps

from blitzy_utils.consts import CORRELATION_ID_VAR_NAME, REQUEST_ID_VAR_NAME
from blitzy_utils.logger import logger
from flask import g


def publish_notification(publisher, notification_data, project_id, topic_id):
    topic_path = publisher.topic_path(project_id, topic_id)
    data_str = dumps(notification_data)
    data = data_str.encode('utf-8')

    # Add correlation_id and request_id to message attributes. We are trying to be
    # safe here and ensure event publishing isn't broken even if request_id or correlation_id are
    # not present
    tracking_attributes = {}
    if hasattr(g, 'correlation_id') and g.correlation_id:
        tracking_attributes[CORRELATION_ID_VAR_NAME] = g.correlation_id
    if hasattr(g, 'request_id') and g.request_id:
        tracking_attributes[REQUEST_ID_VAR_NAME] = g.request_id

    logger.info(f'Publishing message to topic {topic_id}: {data_str} with tracking attributes {tracking_attributes}')
    future = publisher.publish(topic_path, data, **tracking_attributes)

    future.add_done_callback(
        lambda p: logger.info(f'Published message to topic {topic_id}: {p.result()}')
    )
    logger.info(f'Result of publish to topic {topic_id}: {future.result()}')


def download_from_gcs(storage_client, bucket_name: str, filename: str, blob_name: str):
    bucket = storage_client.bucket(bucket_name)
    destination_blob_name = f"{blob_name}/{filename}"
    blob = bucket.blob(destination_blob_name)
    return blob.download_as_text()


def upload_to_gcs(storage_client, bucket_name: str, blob_name: str, filename: str, data: str,
                  content_type='text/plain'):
    bucket = storage_client.bucket(bucket_name)
    destination_blob_name = f"{blob_name}/{filename}"
    blob = bucket.blob(destination_blob_name)
    blob.upload_from_string(data, content_type=content_type)
    logger.info(f"Uploaded to: gs://{bucket_name}/{destination_blob_name}")
    return f"gs://{bucket_name}/{destination_blob_name}"


def gcs_bucket_walk(storage_client, bucket_name, prefix=""):
    """
    Walk through a GCS bucket, yielding tuples of (path, folders, files) similar to os.walk().

    :param storage_client: Initialized Google Cloud Storage client
    :param bucket_name: Name of the GCS bucket
    :param prefix: Optional prefix to start walking from a specific directory
    :yield: Tuples of (path, folders, files)
    """
    bucket = storage_client.get_bucket(bucket_name)

    # Use set to keep track of unique folders
    folders = set()

    # List all blobs in the bucket with the given prefix
    blobs = bucket.list_blobs(prefix=prefix)

    for blob in blobs:
        # Get the relative path of the blob
        path = blob.name

        # If it's a prefix (folder), add it to the folders set
        if blob.name.endswith('/'):
            folders.add(blob.name)
        else:
            # Extract the directory path
            dir_path = '/'.join(blob.name.split('/')[:-1]) + '/'
            if dir_path:
                folders.add(dir_path)

    # Sort folders to maintain a consistent order
    sorted_folders = sorted(folders)

    # Yield results for each folder
    for folder in sorted_folders:
        path = folder
        subfolders = [f for f in sorted_folders if f.startswith(folder) and f != folder]
        files = [b.name for b in bucket.list_blobs(prefix=folder, delimiter='/') if not b.name.endswith('/')]
        yield (path, subfolders, files)
