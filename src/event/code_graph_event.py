from datetime import datetime
from typing import Any, Dict, Optional

from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import BranchLock, Project, Status
from sqlalchemy.orm import Session

from src.service.branch_lock_service import (get_branch_lock_by_branch_id,
                                             update_branch_lock_by_id)


def process_code_graph_event(project_info: Project, job_id: str, status: Status, metadata: Dict[str, Any],
                             payload: Dict[str, Any]):
    """
    Processes a code graph event based on the given project and job details.

    :param project_info: Information about the project
    :type project_info: Project
    :param job_id: Identifier for the job
    :type job_id: str
    :param status: Current status of the project run
    :type status: ProjectRunStatus
    :param metadata: Additional information or metadata for the event
    :type metadata: Dict[str, Any]
    :param payload: Data payload containing details about the event
    :type payload: Dict[str, Any]
    :return: None
    """
    if status != Status.DONE:
        logger.info("Status is not done. Skipping event.")
        return

    branch_id = payload.get("branch_id")

    if not branch_id:
        logger.warning(f"Branch ID not found in {payload}. Event won't be processed.")
        return

    with get_db_session() as session:
        attempt_branch_lock_release(branch_id, session=session)
        session.commit()
        logger.info(f"Branch lock released for branch {branch_id}")


def attempt_branch_lock_release(branch_id: str, session: Optional[Session] = None):
    with get_db_session(session) as session:
        branch_lock = get_branch_lock_by_branch_id(branch_id)
        if not branch_lock:
            logger.warning(f"Branch lock not found for branch {branch_id}. Skipping further processing.")
            return

        update_payload = {
            BranchLock.is_active: False,
            BranchLock.released_at: datetime.utcnow(),
            BranchLock.updated_at: datetime.utcnow(),
        }
        update_branch_lock_by_id(branch_lock.id, update_payload, session=session)
        logger.info(f"Branch lock released for branch {branch_id}")
