import React from 'react';

interface AvatarProps {
    src?: string;
    name?: string;
    email?: string;
    size?: 'small' | 'large';
}

const Avatar: React.FC<AvatarProps> = ({src, name, email, size = 'large'}) => {
    // Function to get initials from name or email
    const getInitials = (name?: string, email?: string) => {
        if (name) {
            const names = name.trim().split(' ');
            return (names[0][0] + (names[1]?.[0] || '')).toUpperCase();
        }
        if (email) {
            const username = email.split('@')[0];
            return username[0].toUpperCase();
        }
        return '';
    };

    const sizeClasses =
        size === 'large' ? 'w-[32px] h-[32px]' : 'w-[24px] h-[24px]';

    const initialsStyles =
        size === 'large'
            ? 'text-white font-semibold text-[20px] leading-[48.1px] tracking-[-1.002px]'
            : 'text-white font-semibold text-[12px] leading-[16px] tracking-[-0.5px]';

    return (
        <div
            className={`flex items-center justify-center rounded-full border border-white overflow-hidden ${sizeClasses} ${
                src
                    ? 'bg-cover bg-center'
                    : 'bg-[var(--background-bg-brand,#5B39F3)]'
            }`}
            style={src ? {backgroundImage: `url(${src})`} : {}}>
            {!src && (
                <span className={initialsStyles}>
                    {getInitials(name, email)}
                </span>
            )}
        </div>
    );
};

export default Avatar;
