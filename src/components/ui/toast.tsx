import * as React from 'react';
import {Cross2Icon} from '@radix-ui/react-icons';
import * as ToastPrimitives from '@radix-ui/react-toast';
import {cva, type VariantProps} from 'class-variance-authority';
import ErrorCircle from '/images/error-circle.svg';
import CrossIcon from '/images/x-close.svg';
import CheckCircle from '@/panel/workspace/icons/tick.tsx';
import InfoCircle from '@/panel/workspace/icons/info-circle';
import {cn} from '@/lib/utils';

const ToastProvider = ToastPrimitives.Provider;

const ToastViewport = React.forwardRef<
    React.ElementRef<typeof ToastPrimitives.Viewport>,
    React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>
>(({className, ...props}, ref) => (
    <ToastPrimitives.Viewport
        ref={ref}
        className={cn(
            'fixed top-4 left-1/2 z-[100] flex max-h-screen w-full -translate-x-1/2 flex-col p-4 md:max-w-[420px]',
            className,
        )}
        {...props}
    />
));
ToastViewport.displayName = ToastPrimitives.Viewport.displayName;

const toastVariants = cva(
    'group pointer-events-auto relative flex items-start justify-between gap-3 rounded-[var(--spacing-spacing-3,12px)] border p-[var(--spacing-4,16px)] shadow-[1px_3px_12px_3px_rgba(0,0,0,0.10),0px_2px_4px_-2px_rgba(0,0,0,0.06)] transition-all',
    {
        variants: {
            variant: {
                default: 'border bg-background text-foreground',
                destructive:
                    'border-[var(--background-bg-error,#FFDFDF)] bg-[var(--background-bg-error,#FFDFDF)] text-[var(--text-text-error-200,#991010)]',
                success:
                    'border-[var(--background-bg-success,#DFF7DF)] bg-[var(--background-bg-success,#DFF7DF)] text-[var(--text-text-success,#107C10)]',
            },
        },
        defaultVariants: {
            variant: 'default',
        },
    },
);

const Toast = React.forwardRef<
    React.ElementRef<typeof ToastPrimitives.Root>,
    React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &
        VariantProps<typeof toastVariants> & {
            message?: string;
            icon?: React.ReactNode;
        }
>(
    (
        {className, variant = 'default', message, icon, children, ...props},
        ref,
    ) => {
        const getIcon = () => {
            if (variant === 'destructive') {
                return (
                    <img
                        src={ErrorCircle}
                        alt="Error Icon"
                        className="h-6 w-6 shrink-0"
                    />
                );
            }
            if (variant === 'success') {
                return <CheckCircle />;
            }
            if (variant === 'default') {
                return <InfoCircle color="#333" />;
            }
            return null;
        };

        return (
            <ToastPrimitives.Root
                ref={ref}
                className={cn(
                    toastVariants({variant}),
                    'flex items-center gap-3 p-4', // Flex layout with spacing and padding
                    className,
                )}
                {...props}>
                {/* Icon */}
                {icon || getIcon()}
                {/* Message */}
                <div className="flex-1">
                    <span
                        className={cn(
                            'font-inter text-sm font-semibold leading-[24px] tracking-[-0.04px]',
                            variant === 'destructive' &&
                                'text-[var(--text-text-error-200,#991010)]',
                            variant === 'success' &&
                                'text-[var(--text-text-success,#107C10)]',
                        )}>
                        {message || children}
                    </span>
                </div>
                {/* Close Button */}
                <ToastClose
                    className={cn(
                        'text-[var(--text-text-error-200,#991010)] hover:text-[var(--text-text-error-400,#FF0000)]',
                        variant === 'success' &&
                            'text-[var(--text-text-success,#107C10)] hover:text-[var(--text-text-success-dark,#004B00)]',
                    )}
                    aria-label="Close">
                    <img
                        src={CrossIcon}
                        alt="Close"
                        className="h-5 w-5" // Adjust size for alignment
                    />
                </ToastClose>
            </ToastPrimitives.Root>
        );
    },
);

Toast.displayName = ToastPrimitives.Root.displayName;

const ToastAction = React.forwardRef<
    React.ElementRef<typeof ToastPrimitives.Action>,
    React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>
>(({className, ...props}, ref) => (
    <ToastPrimitives.Action
        ref={ref}
        className={cn(
            'inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium transition-colors hover:bg-secondary focus:outline-none focus:ring-1 focus:ring-ring disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive',
            className,
        )}
        {...props}
    />
));
ToastAction.displayName = ToastPrimitives.Action.displayName;

const ToastClose = React.forwardRef<
    React.ElementRef<typeof ToastPrimitives.Close>,
    React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>
>(({className, ...props}, ref) => (
    <ToastPrimitives.Close
        ref={ref}
        className={cn(
            // 'absolute right-1 top-1 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600',
            'absolute right-1 top-1 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600 bg-transparent',
            className,
        )}
        toast-close=""
        {...props}>
        <Cross2Icon className="h-4 w-4" />
    </ToastPrimitives.Close>
));
ToastClose.displayName = ToastPrimitives.Close.displayName;

const ToastTitle = React.forwardRef<
    React.ElementRef<typeof ToastPrimitives.Title>,
    React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>
>(({className, ...props}, ref) => (
    <ToastPrimitives.Title
        ref={ref}
        className={cn('text-sm font-semibold [&+div]:text-xs', className)}
        {...props}
    />
));
ToastTitle.displayName = ToastPrimitives.Title.displayName;

const ToastDescription = React.forwardRef<
    React.ElementRef<typeof ToastPrimitives.Description>,
    React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>
>(({className, ...props}, ref) => (
    <ToastPrimitives.Description
        ref={ref}
        className={cn('text-sm opacity-90', className)}
        {...props}
    />
));
ToastDescription.displayName = ToastPrimitives.Description.displayName;

type ToastProps = React.ComponentPropsWithoutRef<typeof Toast>;

type ToastActionElement = React.ReactElement<typeof ToastAction>;

export {
    type ToastProps,
    type ToastActionElement,
    ToastProvider,
    ToastViewport,
    Toast,
    ToastTitle,
    ToastDescription,
    ToastClose,
    ToastAction,
};
