import * as React from 'react';
import showIcon from '../../panel/workspace/icons/show.svg';
import hideIcon from '../../panel/workspace/icons/hide.svg';

export interface InputProps
    extends React.InputHTMLAttributes<HTMLInputElement> {
    showPasswordToggle?: boolean;
}

const PasswordInput = React.forwardRef<HTMLInputElement, InputProps>(
    ({className, type, showPasswordToggle, ...props}, ref) => {
        const [showPassword, setShowPassword] = React.useState(false);

        return (
            <div className="relative w-full">
                <input
                    type={showPassword ? 'text' : type}
                    className={`w-full px-4 py-2 border focus:outline-none ${
                        showPasswordToggle ? 'pr-12' : ''
                    } ${className}`}
                    ref={ref}
                    {...props}
                />
                {showPasswordToggle && (
                    <span
                        className="absolute p-[12px] right-4 top-1/2 -translate-y-1/2 flex justify-center items-center cursor-pointer"
                        onClick={() => setShowPassword(!showPassword)}>
                        <img
                            src={showPassword ? showIcon : hideIcon}
                            alt="toggle password"
                            className=" w-[24px] h-[24px]"
                        />
                    </span>
                )}
            </div>
        );
    },
);

export {PasswordInput};
