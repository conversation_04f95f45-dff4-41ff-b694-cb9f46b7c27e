import CheckIcon from '@/panel/workspace/icons/check';

export default function PasswordValidationMessage({
    isValid,
    message,
}: {
    isValid?: boolean;
    message: string;
}) {
    return (
        <p className="flex items-center gap-[8px]">
            {isValid ? (
                <CheckIcon width={16} height={16} fill="#005335" />
            ) : (
                <div className="w-[16px] h-[16px] flex justify-center items-center">
                    <div className="w-[4px] h-[4px] bg-[#999] rounded-full"></div>
                </div>
            )}
            <span className="pb-[1px] text-[#999]">{message}</span>
        </p>
    );
}
