import ReactRouterPrompt from 'react-router-prompt';
import {
    Dialog,
    DialogTitle,
    DialogDescription,
    DialogHeader,
    DialogFooter,
    DialogContent,
} from '@/components/ui/dialog';

export function NavigationBlocker({when}: {when: boolean}) {
    return (
        <ReactRouterPrompt when={when}>
            {({isActive, onConfirm, onCancel}) =>
                isActive && (
                    <Dialog open={isActive}>
                        <DialogContent className="min-h-dvh sm:min-h-0 w-full sm:w-[608px] sm:h-[250px] sm:max-w-none sm:pt-[1.5rem] [&>button]:hidden">
                            <DialogHeader className="flex-grow">
                                <DialogTitle>
                                    You have unsaved changes
                                </DialogTitle>
                                <DialogDescription>
                                    If you navigate away, your changes will be
                                    lost.
                                </DialogDescription>
                            </DialogHeader>
                            <DialogFooter className="gap-[16px] sm:gap-0">
                                <div
                                    role="button"
                                    className="primary-button"
                                    onClick={onConfirm}>
                                    Discard changes
                                </div>
                                <div
                                    role="button"
                                    className="secondary-button"
                                    onClick={onCancel}>
                                    Cancel
                                </div>
                            </DialogFooter>
                        </DialogContent>
                    </Dialog>
                )
            }
        </ReactRouterPrompt>
    );
}
