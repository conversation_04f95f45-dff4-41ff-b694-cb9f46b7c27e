import {useEffect, useMemo, useState} from 'react';
import {Input} from '@/components/ui/input';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import SearchIcon from '@/panel/workspace/icons/search';

export interface SelectItem {
    value: string;
    label: string;
    disabled: boolean;
    badge?: any;
}

interface SelectWithFilterProps
    extends React.SelectHTMLAttributes<HTMLSelectElement> {
    onValueChange?: (value: string) => void;
    onOpenChange?: (open: boolean) => void;
    defaultValue?: string;
    value?: string;
    placeholder?: string;
    items: SelectItem[];
    itemsLoading?: boolean;
    errors?: {
        message?: string;
    };
}

export default function SelectWithFilter({
    onValueChange,
    onOpenChange,
    defaultValue = '',
    value,
    placeholder = 'Select...',
    items,
    itemsLoading = false,
    errors,
}: SelectWithFilterProps) {
    const [searchKey, setSearchKey] = useState('');
    const [reducedItems, setReducedItems] = useState(
        items.map(item => ({item, isIncluded: true})),
    );
    const [reducedItemsCount, setReducedItemsCount] = useState(0);
    const valueLabel = useMemo(
        () => items.find(item => item.value === value)?.label,
        [items, value],
    );

    useEffect(() => {
        setReducedItemsCount(0);
        const reducedItemsArray = items.reduce<typeof reducedItems>(
            (items, item) => {
                const isIncluded = item.label
                    .toLowerCase()
                    .includes(searchKey.toLowerCase());
                items.push({item, isIncluded});
                if (isIncluded) {
                    setReducedItemsCount(count => count + 1);
                }
                return items;
            },
            [],
        );
        setReducedItems(reducedItemsArray);
    }, [items, searchKey]);

    const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchKey(e.target.value);
    };

    return (
        <>
            <Select
                defaultValue={defaultValue}
                value={value}
                onValueChange={onValueChange}
                onOpenChange={onOpenChange}>
                <SelectTrigger
                    className={
                        value === defaultValue ? 'text-[#999]' : 'text-[#666]'
                    }>
                    <SelectValue placeholder={placeholder} />
                </SelectTrigger>
                <SelectContent>
                    {itemsLoading ? (
                        <div className="px-[20px] py-[12px]">
                            <span className="text-[16px] text-[#999] leading-[24px]">
                                Loading...
                            </span>
                        </div>
                    ) : (
                        <>
                            <div
                                className="w-full h-auto px-[20px] py-[12px] text-[16px] border-b border-[#D9D9D9] flex items-center gap-[8px]"
                                onKeyDown={e => e.stopPropagation()}>
                                <SearchIcon />
                                <Input
                                    className="w-full text-[16px] leading-[19px] focus:outline-none"
                                    placeholder="Search"
                                    type="text"
                                    value={searchKey}
                                    onChange={handleSearch}
                                />
                            </div>
                            {reducedItemsCount > 0 ? (
                                reducedItems.map(({item, isIncluded}) => (
                                    <SelectItem
                                        key={item.value}
                                        value={item.value}
                                        className={isIncluded ? '' : 'hidden'}
                                        disabled={item.disabled}>
                                        {item.label}
                                        {item?.badge ? item.badge : null}
                                    </SelectItem>
                                ))
                            ) : (
                                <div className="px-[20px] py-[12px]">
                                    <span className="text-[16px] text-[#999] leading-[24px]">
                                        No results found
                                    </span>
                                    <SelectItem
                                        key={value}
                                        value={value || 'fallback-value'}
                                        className="hidden">
                                        {valueLabel || value}
                                    </SelectItem>
                                </div>
                            )}
                        </>
                    )}
                </SelectContent>
            </Select>
            {errors?.message && (
                <p className="mt-[8px] text-[#EC3636]">{errors.message}</p>
            )}
        </>
    );
}
