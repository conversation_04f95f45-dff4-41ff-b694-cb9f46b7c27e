import {logGAEvent} from '@/lib/utils';
import {EXTERNAL_LINKS} from '@/assets/constants';

export function TermClause({srcPage}: {srcPage: string}) {
    return (
        <div className="w-full text-center foot-note">
            <p>
                By signing up, you agree to our{' '}
                <a
                    target="_blank"
                    href="https://policies.blitzy.com/terms-of-service"
                    onClick={() => {
                        logGAEvent('external_link_clicked', {
                            link_name: 'terms_of_use',
                            link_url:
                                'https://policies.blitzy.com/terms-of-service',
                            source: 'sign_in_page',
                        });
                    }}>
                    Terms of Use
                </a>{' '}
                and{' '}
                <a
                    target="_blank"
                    href="https://policies.blitzy.com/privacy"
                    onClick={() => {
                        logGAEvent('external_link_clicked', {
                            link_name: 'privacy_notice',
                            link_url: 'https://policies.blitzy.com/privacy',
                            source: 'sign_in_page',
                        });
                    }}>
                    Privacy Notice
                </a>
            </p>
            <p>
                <a
                    target="_blank"
                    href="https://blitzy.statuspage.io/"
                    onClick={() => {
                        logGAEvent('external_link_clicked', {
                            link_name: 'service_status',
                            link_url: 'https://blitzy.statuspage.io/',
                            source: srcPage,
                        });
                    }}>
                    Service Status
                </a>{' '}
                |{' '}
                <a
                    target="_blank"
                    href={'mailto:' + EXTERNAL_LINKS.SUPPORT}
                    onClick={() => {
                        logGAEvent('external_link_clicked', {
                            link_name: 'support',
                            link_url: 'mailto:' + EXTERNAL_LINKS.SUPPORT,
                            source: srcPage,
                        });
                    }}>
                    Help & Support
                </a>
            </p>
        </div>
    );
}
