import {useEffect, useState} from 'react';
import {cn} from '@/lib/utils';
import WarningIcon from '@/panel/workspace/icons/warning';

interface NetworkStatusBannerProps {
    className?: string;
}

export function NetworkStatusBanner({className}: NetworkStatusBannerProps) {
    const [isOnline, setIsOnline] = useState(navigator.onLine);

    useEffect(() => {
        const handleOnline = () => setIsOnline(true);
        const handleOffline = () => setIsOnline(false);

        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);

        return () => {
            window.removeEventListener('online', handleOnline);
            window.removeEventListener('offline', handleOffline);
        };
    }, []);

    if (isOnline) {
        return null;
    }

    return (
        <div
            className={cn(
                'fixed top-0 left-0 right-0 z-50 bg-red-600 text-white px-4 py-3 text-center font-medium',
                'flex items-center justify-center gap-2',
                className,
            )}>
            <WarningIcon width={20} height={20} color="white" />
            <span>
                No internet connection. Please check your network and try again.
            </span>
        </div>
    );
}
