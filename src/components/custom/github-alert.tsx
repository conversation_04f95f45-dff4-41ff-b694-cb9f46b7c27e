import InfoCircle from '../../panel/workspace/icons/info-circle';

interface GitHubAlertProps {
    onViewStatus: () => void;
    text: string;
}

const GitHubAlert = ({onViewStatus, text}: GitHubAlertProps) => {
    return (
        <div className="rounded-[12px] bg-[#F2F0FE] p-[16px] text-[#000] text-[16px] font-semibold leading-[150%] tracking-[-0.04px]">
            {/* Desktop Layout */}
            <div className="hidden sm:flex sm:justify-between sm:gap-[12px]">
                <div className="flex items-center gap-3">
                    <div className="flex justify-center items-center w-5 h-5">
                        <InfoCircle />
                    </div>
                    <span className="text-[14px] font-semibold leading-[150%] text-black">
                        {text}
                    </span>
                </div>
                <a
                    onClick={onViewStatus}
                    className="flex ml-[8px] text-[14px] font-semibold text-indigo-600 hover:text-indigo-700 cursor-pointer">
                    View status
                </a>
            </div>

            {/* Mobile Layout */}
            <div className="flex sm:hidden">
                <div className="flex justify-center items-start w-5 h-5 mt-1">
                    <InfoCircle />
                </div>
                <div className="flex flex-col ml-3">
                    <span className="text-[14px] font-semibold leading-[150%] text-black">
                        {text}
                    </span>
                    <a
                        onClick={onViewStatus}
                        className="text-[14px] font-semibold text-indigo-600 hover:text-indigo-700 cursor-pointer mt-2">
                        View status
                    </a>
                </div>
            </div>
        </div>
    );
};

export default GitHubAlert;
