import {useCallback} from 'react';
import {useNavigate} from 'react-router-dom';
import {useToast} from '@/hooks/use-toast';
import {logGAEvent} from '@/lib/utils';
import {CarKey} from '@/modal/icons/car-key';

interface SSOSignInButtonProps {
    className?: string;
    buttonText?: string;
}

export function SSOSignInButton({
    className = 'flex justify-center items-center gap-[8px] sm:gap-[12px] self-stretch rounded-[32px] bg-[var(--background-bg-tertiary,#F5F5F5)] px-[16px] sm:px-[20px] py-[10px] sm:py-[8px]',
    buttonText = 'Continue with SAML (SSO)',
}: SSOSignInButtonProps) {
    const {toast, dismiss} = useToast();
    const navigateTo = useNavigate();

    const handleSSOSignIn = useCallback(async () => {
        try {
            dismiss();
            logGAEvent('sign_in', {
                method: 'sso',
            });
            // Navigate to SSO sign-in page
            navigateTo('/sso-sign-in', {replace: true});
        } catch (error) {
            toast({
                variant: 'destructive',
                description:
                    'An error occurred during SSO sign in. Please try again. ' +
                    error,
            });
        }
    }, [navigateTo, toast, dismiss]);

    return (
        <div role="button" onClick={handleSSOSignIn} className={className}>
            <CarKey
                width={20}
                height={20}
                fill="var(--text-text-secondary,#333)"
            />
            <div className="text-[var(--text-text-secondary,#333)] text-[16px] font-semibold">
                {buttonText}
            </div>
        </div>
    );
}
