import {
    ChangeEvent,
    KeyboardEvent,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState,
} from 'react';
import {Virtuoso, VirtuosoHandle} from 'react-virtuoso';
import {Input} from '../ui/input';
import ArrowDown from '@/panel/workspace/icons/arrow-down';
import SearchIcon from '@/panel/workspace/icons/search';
import CheckIcon from '@/panel/workspace/icons/check';

const listItemHeight = 48;

interface IProps {
    readonly value: string;
    options: Array<{value: string; label: string; disabled?: boolean}>;
    onChange: (value: string) => void;
    placeholder: string;
    emptyMessage?: string;
    itemsLoading?: boolean;
    loadingMessage?: string;
    maxHeight?: number;
    disabled?: boolean;
}

export function ComboSelect(props: IProps) {
    const {
        value,
        options,
        onChange,
        emptyMessage = 'No options available',
        placeholder,
        maxHeight = 200,
        itemsLoading = false,
        loadingMessage = 'Loading...',
        disabled = false,
    } = props;
    const [open, setOpen] = useState<boolean>(false);
    const [selectedOption, setSelectedOption] = useState<string | null>(null);
    const [selectedOptionValue, setSelectedOptionValue] = useState<
        string | null
    >(null);
    const [searchQuery, setSearchQuery] = useState<string>();
    const ref = useRef<VirtuosoHandle>(null);
    const inputRef = useRef<HTMLInputElement>(null);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const [currentItemIndex, setCurrentItemIndex] = useState<number>(-1); // keyboard navigation highlight

    useEffect(() => {
        const handleClickOutside = () => {
            const isInputActive = document.activeElement === inputRef.current;
            setOpen(isInputActive);
        };
        document.body.addEventListener('click', handleClickOutside);
        return () => {
            document.body.removeEventListener('click', handleClickOutside);
        };
    }, []);

    // reset selected option when value is empty
    useEffect(() => {
        if (value === '') {
            setSelectedOption(null);
            setSelectedOptionValue(null);
        }
    }, [value]);

    // Close dropdown when disabled
    useEffect(() => {
        if (disabled && open) {
            setOpen(false);
        }
    }, [disabled, open]);

    // Focus input when dropdown opens
    useEffect(() => {
        if (open && !disabled) {
            if (inputRef.current) inputRef.current.focus();
            if (dropdownRef.current) {
                dropdownRef.current.classList.remove('bottom-full');
                dropdownRef.current.classList.add('top-full');
            }
        }
    }, [open, disabled]);

    const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
        if (disabled) return;
        setSearchQuery(e.target.value);
        setCurrentItemIndex(-1);
    };

    // Filter options based on search query
    const filteredOptions = useMemo(() => {
        let matches = options;
        if (searchQuery) {
            const text = searchQuery.toLowerCase();
            matches = options.filter(option => {
                const b = option.label.toLowerCase().includes(text);
                return b;
            });
        }
        return matches;
    }, [searchQuery, options]);

    const changeValue = useCallback(
        (index: number) => {
            if (disabled) return;
            if (index >= 0 && index < filteredOptions.length) {
                const selectedOption = filteredOptions[index];
                setSelectedOption(selectedOption.label);
                setSelectedOptionValue(selectedOption.value);
                onChange(selectedOption.value);
                setCurrentItemIndex(-1);
                setOpen(false);
            }
        },
        [filteredOptions, onChange, disabled],
    );

    const handleKeydown = useCallback(
        (e: KeyboardEvent) => {
            if (disabled) return;
            setOpen(true);
            let nextIndex: number = NaN;

            if (e.code === 'ArrowUp' || e.code === 'ArrowDown') {
                nextIndex =
                    e.code === 'ArrowUp'
                        ? Math.max(0, currentItemIndex - 1)
                        : Math.min(
                              filteredOptions.length - 1,
                              currentItemIndex + 1,
                          );

                if (ref.current && !isNaN(nextIndex)) {
                    ref.current.scrollIntoView({
                        index: nextIndex,
                        behavior: 'auto',
                        done: () => {
                            setCurrentItemIndex(nextIndex);
                        },
                    });
                    e.preventDefault();
                }
            } else if (e.code === 'Enter') {
                changeValue(currentItemIndex);
                e.preventDefault();
            } else if (e.code === 'Escape') {
                setSearchQuery('');
                setCurrentItemIndex(-1);
                setOpen(false);
                e.preventDefault();
            }
        },
        [changeValue, currentItemIndex, filteredOptions.length, disabled],
    );

    const handleToggleOpen = useCallback(() => {
        if (disabled) return;
        setOpen(open => !open);
    }, [disabled]);

    const handleKeyToggle = useCallback(
        (e: KeyboardEvent) => {
            if (disabled) return;
            if (e.key === 'Enter') {
                setOpen(open => !open);
            }
        },
        [disabled],
    );

    return (
        <div className="relative">
            <div
                className={`--mb-[8px] min-h-[48px] px-[12px] py-[4px] text-[16px] flex justify-between items-center gap-[8px] border border-[#999] rounded-[24px] transition-colors outline-none ${
                    disabled
                        ? 'opacity-50 cursor-not-allowed bg-gray-50'
                        : 'hover:border-[#D4CBFC] focus:border-[#D4CBFC] cursor-pointer'
                }`}
                onClick={handleToggleOpen}
                tabIndex={disabled ? -1 : 0}
                onKeyDown={handleKeyToggle}>
                <div
                    className={`${selectedOption ? 'text-[#000]' : 'text-[#999]'} line-clamp-1`}>
                    {selectedOption || placeholder}
                </div>
                <div className="w-[24px] h-[24px] flex justify-center items-center">
                    <ArrowDown />
                </div>
            </div>
            {open && !disabled && (
                <div className="absolute z-30 top-full left-0 right-0 my-[8px]">
                    <div
                        ref={dropdownRef}
                        className="border border-[#D9D9D9] rounded-[24px] shadow-md overflow-hidden">
                        <div
                            className={`h-[${listItemHeight}px] px-[20px] py-[4px] bg-[#FFF] flex items-center gap-[8px] border-b border-[#D9D9D9]`}
                            onClick={e => e.stopPropagation()}>
                            <SearchIcon />
                            <Input
                                ref={inputRef}
                                placeholder={placeholder}
                                value={searchQuery}
                                onChange={handleInputChange}
                                onKeyDown={handleKeydown}
                                className={`w-full h-[${listItemHeight - 8}px] text-[16px] text-[#000] placeholder:text-[#999] outline-none`}
                            />
                        </div>
                        {itemsLoading ? (
                            <div
                                className={`w-full min-h-[${listItemHeight}px] px-[12px] py-[4px] text-[16px] bg-[#FFF] text-[#999] flex items-center gap-[8px]`}>
                                <span className="w-[24px] h-[24px] flex justify-center items-center"></span>
                                <span>{loadingMessage}</span>
                            </div>
                        ) : filteredOptions.length === 0 ? (
                            <div
                                className={`w-full min-h-[${listItemHeight}px] px-[12px] py-[4px] text-[16px] bg-[#FFF] text-[#999] flex items-center gap-[8px]`}>
                                <span className="w-[24px] h-[24px] flex justify-center items-center"></span>
                                <span>{emptyMessage}</span>
                            </div>
                        ) : (
                            <div
                                className="bg-[#FFF]"
                                style={{
                                    height: Math.min(
                                        filteredOptions.length * listItemHeight,
                                        maxHeight,
                                    ),
                                }}>
                                <Virtuoso
                                    ref={ref}
                                    key={`virtuoso-${searchQuery}`}
                                    style={{height: '100%'}}
                                    totalCount={filteredOptions.length}
                                    itemContent={index => {
                                        const option = filteredOptions[index];
                                        return (
                                            <div
                                                className={`h-[${listItemHeight}px] px-[12px] py-[4px] text-[16px] ${index === currentItemIndex ? 'bg-[#F2F0FE]' : 'bg-[#FFF]'} flex items-center gap-[8px] cursor-pointer`}
                                                onClick={() =>
                                                    changeValue(index)
                                                }
                                                onMouseEnter={() =>
                                                    setCurrentItemIndex(index)
                                                }>
                                                <span className="w-[24px] h-[24px] flex justify-center items-center">
                                                    {selectedOption ===
                                                        option.label &&
                                                        selectedOptionValue ===
                                                            option.value && (
                                                            <CheckIcon fill="#000" />
                                                        )}
                                                </span>
                                                <span
                                                    className="line-clamp-1"
                                                    title={option.label}>
                                                    {option.label}
                                                </span>
                                            </div>
                                        );
                                    }}
                                />
                            </div>
                        )}
                    </div>
                    <div className="h-[24px]"></div>
                </div>
            )}
        </div>
    );
}
