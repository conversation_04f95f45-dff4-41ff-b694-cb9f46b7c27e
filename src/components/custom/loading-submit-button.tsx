import * as React from 'react';
import {Loader2} from 'lucide-react';

interface LoadingSubmitButtonProps
    extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    loading?: boolean;
    loadingText?: string;
}

const LoadingSubmitButton = ({
    className,
    loading = false,
    loadingText = 'loading...',
    children,
    disabled,
    ...props
}: LoadingSubmitButtonProps) => {
    return (
        <button
            type="submit"
            className={
                'flex justify-center items-center gap-[8px] ' + className
            }
            disabled={disabled || loading}
            {...props}>
            {loading && (
                <Loader2 className="inline h-[20px] w-[20px] animate-spin" />
            )}
            <span>{loading ? loadingText : children}</span>
        </button>
    );
};

LoadingSubmitButton.displayName = 'LoadingSubmitButton';

export {LoadingSubmitButton};
