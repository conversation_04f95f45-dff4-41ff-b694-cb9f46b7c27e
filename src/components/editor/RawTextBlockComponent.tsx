import React, {useState} from 'react';
import {NodeViewWrapper, NodeViewContent} from '@tiptap/react';
import {Copy, Type} from 'lucide-react';

interface RawTextBlockComponentProps {
    node: any;
    selected: boolean;
}

export const RawTextBlockComponent: React.FC<RawTextBlockComponentProps> = ({
    node,
    selected,
}) => {
    const [showCopyButton, setShowCopyButton] = useState(false);

    const handleCopy = async () => {
        const content = node.textContent || '';
        try {
            await navigator.clipboard.writeText(content);
            // Could add a toast notification here
        } catch (err) {
            console.error('Failed to copy text: ', err);
        }
    };

    return (
        <NodeViewWrapper
            className={`raw-text-block-wrapper ${selected ? 'selected' : ''}`}
            onMouseEnter={() => setShowCopyButton(true)}
            onMouseLeave={() => setShowCopyButton(false)}>
            <div className="raw-text-block-header">
                <div className="raw-text-block-label">
                    <Type size={14} />
                    <span>Raw Text</span>
                </div>

                {showCopyButton && (
                    <button
                        onClick={handleCopy}
                        className="raw-text-copy-button"
                        title="Copy raw text"
                        type="button">
                        <Copy size={14} />
                    </button>
                )}
            </div>

            <div className="raw-text-block-content">
                <NodeViewContent
                    as="pre"
                    className="raw-text-content-editable"
                    spellCheck={false}
                />
            </div>
        </NodeViewWrapper>
    );
};
