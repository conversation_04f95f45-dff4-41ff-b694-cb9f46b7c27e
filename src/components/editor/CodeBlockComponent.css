/* Code Block Component Styles */
.code-block-wrapper {
    position: relative;
    margin: 1rem 0;
    border-radius: 8px;
    overflow: hidden;
    background-color: #f8f9fa;
    border: 1px solid #e5e7eb;
}

.code-block-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    /* padding: 0.5rem 1rem; */
}

.language-selector {
    position: relative;
}

.language-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.375rem 0.75rem;
    background-color: white;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.language-button:hover {
    border-color: #9ca3af;
    background-color: #f9fafb;
}
.language-button.active,
.language-button:focus {
    outline: 2px solid #60a5fa;
    outline-offset: 2px;
}

.language-label {
    font-weight: 500;
    color: #374151;
}

.chevron {
    transition: transform 0.2s ease;
    color: #6b7280;
}

.chevron.open {
    transform: rotate(180deg);
}

/* Dropdown styles */
.language-dropdown {
    min-width: 280px;
    background-color: white;
    border-radius: 8px;
    box-shadow:
        0 10px 15px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    overflow: hidden;
}

.dropdown-header {
    padding: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
    background-color: #f9fafb;
}
.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    color: #9ca3af;
}

.search-input {
    width: 100%;
    padding: 0.5rem 2rem 0.5rem 2.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: #60a5fa;
}

.clear-search {
    position: absolute;
    right: 0.5rem;
    padding: 0.25rem;
    background: transparent;
    border: none;
    cursor: pointer;
    color: #6b7280;
    transition: color 0.2s ease;
}

.clear-search:hover {
    color: #374151;
}
.results-count {
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 0.5rem;
}

.language-list {
    max-height: 300px;
    overflow-y: auto;
    padding: 0.5rem 0;
}

.language-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 0.625rem 1rem;
    background: transparent;
    border: none;
    cursor: pointer;
    text-align: left;
    transition: background-color 0.15s ease;
}

.language-option:hover,
.language-option.highlighted {
    background-color: #f3f4f6;
}

.language-option.active {
    background-color: #dbeafe;
}

.language-option.active:hover,
.language-option.active.highlighted {
    background-color: #bfdbfe;
}

.language-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: #111827;
}

.language-code {
    font-size: 0.75rem;
    color: #6b7280;
    font-family: monospace;
}

.no-results {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2rem 1rem;
    color: #6b7280;
}

.no-results span {
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.no-results small {
    font-size: 0.75rem;
    color: #9ca3af;
}

.dropdown-footer {
    padding: 0.5rem 1rem;
    background-color: #f9fafb;
    border-top: 1px solid #e5e7eb;
    text-align: center;
}

.dropdown-footer small {
    font-size: 0.75rem;
    color: #6b7280;
}

/* Code block actions */
.code-block-actions {
    display: flex;
    gap: 0.5rem;
}

.mermaid-toolbar-button {
    padding: 0.375rem;
    background: transparent;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 6px;
}

.mermaid-toolbar-button:hover {
    background-color: #f3f4f6;
    border-color: #e5e7eb;
}

/* Code content */
.code-content {
    position: relative;
    overflow: auto;
}

.code-content pre {
    margin: 0;
    padding: 1rem;
    overflow: auto;
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.875rem;
    line-height: 1.5;
}

.code-content code {
    display: block;
    white-space: pre;
}

/* Scrollbar styles for dropdown */
.language-list::-webkit-scrollbar {
    width: 6px;
}

.language-list::-webkit-scrollbar-track {
    background: #f3f4f6;
}

.language-list::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
}

.language-list::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Code Suggestions Styles CodeBlockComponent.css*/
.code-suggestions {
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06);
    max-height: 200px;
    overflow-y: auto;
    min-width: 200px;
    font-size: 0.875rem;
}

.suggestion-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0.75rem;
    cursor: pointer;
    transition: background-color 0.15s ease;
}

.suggestion-item:hover,
.suggestion-item.selected {
    background-color: #f3f4f6;
}

.suggestion-text {
    color: #111827;
    font-family: 'Courier New', Courier, monospace;
}

.suggestion-type {
    color: #9ca3af;
    font-size: 0.75rem;
    margin-left: 1rem;
}

/* Scrollbar for suggestions */
.code-suggestions::-webkit-scrollbar {
    width: 6px;
}

.code-suggestions::-webkit-scrollbar-track {
    background: #f3f4f6;
}

.code-suggestions::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
}

.code-suggestions::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}
