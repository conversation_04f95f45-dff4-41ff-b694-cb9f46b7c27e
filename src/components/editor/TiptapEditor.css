/* Typography and Base Styles */
.tiptap-editor-wrapper {
    font-family:
        'Inter',
        -apple-system,
        BlinkMacSystemFont,
        'Segoe UI',
        Robot<PERSON>,
        'Helvetica Neue',
        Arial,
        sans-serif;
    background: transparent;
    border: none;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    border-radius: 12px;
    box-shadow: none;
    border: 1px solid #d9d9d9;
    padding: 12px;
}

/* Sticky Header Styles */
.tiptap-sticky-header {
    /* position: absolute; */
    /* top: 60px; Position below toolbar */
    /* left: 0; */
    /* right: 0; */
    z-index: 10;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(8px);
    border-bottom: 1px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    animation: stickyHeaderSlideIn 0.2s ease-out;
    border-radius: 4px;
    margin-top: 12px;
}

@keyframes stickyHeaderSlideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.sticky-header-content {
    padding: 12px 24px;
    font-weight: 600;
    color: #1f2328;
    font-family:
        'Inter',
        -apple-system,
        BlinkMacSystemFont,
        'Segoe UI',
        Roboto,
        sans-serif;
    line-height: 1.4;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
}

.sticky-header-h1,
.sticky-header-h2 {
    font-size: 1rem;
    border-left: 3px solid #5b39f3;
    padding-left: 16px;
    color: #1f2328;
    /* margin-left: 4px; */
}

/* Editor Content Wrapper */
.tiptap-editor-content-wrapper {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
    /* margin-top: 12px; */
}

.tiptap-editor-content {
    height: 100%;
    /* padding: 14px; */
    font-size: 16px;
    line-height: 1.6;
    color: #1f2328;
    /* min-height: 400px; Same minimum height as plain text editor */
    box-sizing: border-box;
    font-family:
        'Inter',
        -apple-system,
        BlinkMacSystemFont,
        'Segoe UI',
        Roboto,
        'Helvetica Neue',
        Arial,
        sans-serif;
    /* border: 1px solid #e2e8f0; */
    /* border-radius: 8px; */
    background: transparent;
}

.tiptap-editor-content .ProseMirror {
    min-height: 100%; /* Fill the available space */
    outline: none;
    height: auto; /* Allow content to determine height */
    padding: 0;
    margin: 0;
    font-family: inherit;
}

.tiptap-editor-content .ProseMirror:focus {
    outline: none;
}

/* Professional Typography - Headings */
.tiptap-heading {
    font-weight: 700;
    line-height: 1.3;
    margin-top: 0;
    margin-bottom: 0.75em;
    color: #1f2328;
    font-family:
        'Inter',
        -apple-system,
        BlinkMacSystemFont,
        'Segoe UI',
        Roboto,
        sans-serif;
}

h1.tiptap-heading {
    font-size: 2rem;
    margin-top: 24px;
    margin-bottom: 16px;
    padding-bottom: 9.6px;
    border-bottom: 1px solid #e2e8f0;
    font-weight: 600;
}

h2.tiptap-heading {
    font-size: 1.5rem;
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
    border-bottom: 1px solid #e2e8f0;
}

h3.tiptap-heading {
    font-size: 1.25rem;
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
}

h4.tiptap-heading {
    font-size: 1rem;
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
}

h5.tiptap-heading {
    font-size: 0.75rem;
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
}

h6.tiptap-heading {
    font-size: 0.5rem;
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
}

/* Enhanced Tables with Professional Styling */
.tiptap-table {
    border-collapse: collapse;
    width: 100%;
    margin: 1.5rem 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    overflow: hidden;
    background: #ffffff;
}

/* ProseMirror specific table styles - IMPORTANT for editing experience */
.ProseMirror table {
    border-collapse: collapse;
    table-layout: fixed;
    width: 100%;
    margin: 0;
    overflow: hidden;
}

.ProseMirror td,
.ProseMirror th {
    vertical-align: top;
    box-sizing: border-box;
    position: relative;
    min-width: 1em;
    border: 2px solid #ced4da;
    padding: 12px 16px;
    background-clip: padding-box;
}

/* Ensure borders are always visible during editing */
.ProseMirror table {
    border: 2px solid #e2e8f0;
}

.ProseMirror td,
.ProseMirror th {
    border: 1px solid #cbd5e0 !important;
}

/* Make empty cells more visible */
.ProseMirror td:empty,
.ProseMirror th:empty {
    min-height: 40px;
    background-color: #fafafa;
}

.ProseMirror td:empty::after,
.ProseMirror th:empty::after {
    content: '\00a0';
    color: transparent;
}

.ProseMirror th {
    background-color: #f7fafc;
    font-weight: 600;
    color: #2d3748;
    font-size: 0.875rem;
}

.ProseMirror tr {
    transition: background-color 0.2s ease;
}

.ProseMirror tbody tr:nth-child(even) {
    background-color: #f9fafb;
}

.ProseMirror tbody tr:hover {
    background-color: #edf2f7;
}

/* Selected cells styling */
.ProseMirror .selectedCell:after {
    z-index: 2;
    position: absolute;
    content: '';
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgba(200, 200, 255, 0.4);
    pointer-events: none;
}

/* Enhanced table wrapper styles */
.ProseMirror .tableWrapper {
    margin: 1.5rem 4px;
    overflow-x: auto;
    border-radius: 8px;
    box-shadow: 0 0 0 1px #e2e8f0;
}

/* Make sure tables have a consistent appearance */
.ProseMirror table {
    border-collapse: collapse;
    table-layout: fixed;
    width: 100%;
    margin: 0;
    overflow: hidden;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Style for table cells */
.ProseMirror td,
.ProseMirror th {
    vertical-align: top;
    box-sizing: border-box;
    position: relative;
    min-width: 100px;
    border: 1px solid #cbd5e0 !important;
    padding: 12px 16px;
    background-clip: padding-box;
}

/* Table headers */
.ProseMirror th {
    background-color: #f7fafc;
    font-weight: 600;
    color: #2d3748;
    font-size: 0.875rem;
    border-bottom: 2px solid #5b39f3 !important;
}

/* Zebra striping */
.ProseMirror tbody tr:nth-child(even) {
    background-color: #f9fafb;
}

/* Hover effects */
.ProseMirror tbody tr:hover {
    background-color: #edf2f7;
}

/* Cell hover effect */
.ProseMirror td:hover,
.ProseMirror th:hover {
    background-color: rgba(91, 57, 243, 0.1);
    border-color: #5b39f3 !important;
    cursor: text;
}

/* Make empty cells more visible during editing */
.ProseMirror td:empty,
.ProseMirror th:empty {
    min-height: 44px;
    background-color: #fafafa;
    position: relative;
}

.ProseMirror td:empty::before,
.ProseMirror th:empty::before {
    content: 'Click to edit';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #a0aec0;
    font-size: 0.75rem;
    pointer-events: none;
}

.ProseMirror td:empty:focus::before,
.ProseMirror th:empty:focus::before {
    display: none;
}

/* Selected cells styling */
.ProseMirror .selectedCell {
    background-color: rgba(91, 57, 243, 0.2) !important;
}

.ProseMirror .selectedCell:after {
    z-index: 2;
    position: absolute;
    content: '';
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgba(91, 57, 243, 0.3);
    pointer-events: none;
    border: 2px solid #5b39f3;
}

/* Column resize handle styling */
.ProseMirror .column-resize-handle {
    position: absolute;
    right: -2px;
    top: 0;
    bottom: -2px;
    width: 6px;
    z-index: 20;
    background-color: transparent;
    cursor: col-resize;
    transition: background-color 0.2s;
}

.ProseMirror .column-resize-handle:hover {
    background-color: #5b39f3;
}

.ProseMirror.resize-cursor {
    cursor: col-resize;
}

/* Focus styles for better editing experience */
.ProseMirror td:focus,
.ProseMirror th:focus {
    outline: 2px solid #4299e1;
    outline-offset: -2px;
    background-color: #f0f9ff !important;
    z-index: 10;
}

/* Code Blocks with Enhanced Language Selector */
.code-block-wrapper {
    position: relative;
    margin: 2rem 4px;
    border-radius: 12px;
    overflow: hidden;
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
    box-shadow: none;
    transition: all 0.3s ease;
    contain: layout style;
    padding: 12px;
}

.code-block-wrapper:focus-visible {
    outline: 1px solid #5b39f3; /* fix this */
}

.code-block-wrapper:hover {
    border-color: #b0b0b0;
}

.code-block-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    margin-bottom: 12px;
}

.language-selector {
    position: relative;
    z-index: 9998;
}

.language-button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    height: 44px;
    background: #ffffff;
    border: 1px solid #d9d9d9;
    border-radius: 32px;
    color: #333333;
    font-size: 16px;
    font-weight: 400;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 140px;
    user-select: none;
    font-family:
        'Inter',
        -apple-system,
        BlinkMacSystemFont,
        'Segoe UI',
        Roboto,
        sans-serif;
    box-sizing: border-box;
}

.language-button:hover {
    border-color: #b0b0b0;
}

.language-button:focus {
    outline: 1px solid #5b39f3;
}

.language-label {
    flex: 1;
    text-align: left;
    color: #333333;
}

.chevron {
    transition: transform 0.2s ease;
    flex-shrink: 0;
}

.chevron.open {
    transform: rotate(180deg);
}

/* Enhanced Language Dropdown */
.language-dropdown {
    width: 320px;
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    animation: dropdownFadeIn 0.2s ease-out;
    transform: translateZ(0);
    will-change: transform, opacity;
}

@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(-8px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-header {
    padding: 16px;
    border-bottom: 1px solid #f1f3f4;
    background: #fafbfc;
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.search-container > input:focus-visible {
    border: 1px solid #5b39f3;
    box-shadow: none;
}

.search-icon {
    position: absolute;
    left: 12px;
    color: #6b7280;
    z-index: 10001;
}

.search-input {
    width: 100%;
    padding: 10px 12px 10px 40px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    color: #1f2937;
    transition: all 0.2s ease;
    position: relative;
    z-index: 10000;
}

.search-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input::placeholder {
    color: #9ca3af;
}

.clear-search {
    position: absolute;
    right: 8px;
    padding: 4px;
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.clear-search:hover {
    background: #f3f4f6;
    color: #374151;
}

.results-count {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
}

.language-list {
    max-height: 280px;
    overflow-y: auto;
    padding: 8px 0;
}

.language-list::-webkit-scrollbar {
    width: 6px;
}

.language-list::-webkit-scrollbar-track {
    background: #f1f3f4;
}

.language-list::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
}

.language-list::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

.language-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 12px 16px;
    background: none;
    border: none;
    color: #1f2937;
    font-size: 14px;
    text-align: left;
    cursor: pointer;
    transition: all 0.15s ease;
}

.language-option:hover,
.language-option.highlighted {
    background: #f8fafc;
}

.language-option.active {
    background: #eff6ff;
    color: #5b39f3;
}

.language-option.active .language-code {
    color: #5b39f3;
}

.language-name {
    font-weight: 500;
    flex: 1;
}

.language-code {
    font-size: 12px;
    color: #6b7280;
    font-family:
        'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas,
        'Courier New', monospace;
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 4px;
}

.no-results {
    padding: 24px 16px;
    text-align: center;
    color: #6b7280;
}

.no-results span {
    display: block;
    font-weight: 500;
    margin-bottom: 4px;
}

.no-results small {
    font-size: 12px;
    color: #9ca3af;
}

.dropdown-footer {
    padding: 12px 16px;
    background: #f9fafb;
    border-top: 1px solid #f1f3f4;
    text-align: center;
}

.dropdown-footer small {
    color: #6b7280;
    font-size: 11px;
}

.code-block-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.copy-button,
.delete-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px;
    background: #ffffff;
    border: 1px solid #d9d9d9;
    border-radius: 24px;
    color: #666666;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
    width: 48px;
    height: 48px;
}

.copy-button:hover,
.delete-button:hover {
    border-color: #b0b0b0;
    background: #f9f9f9;
}

.delete-button:hover {
    border-color: #ff6b6b;
    background: #fff5f5;
    color: #ff6b6b;
}

.delete-button:hover svg {
    color: #ff6b6b;
}

.copy-text {
    font-weight: 500;
}

.code-block-wrapper .code-content {
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    overflow: hidden;
}

.code-block-wrapper .code-content pre {
    position: relative;
}

.code-block-wrapper .code-content code {
    outline: none;
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
}

.code-block-wrapper pre {
    margin: 0;
    padding: 12px;
    overflow-x: auto;
    background: transparent;
    min-height: 120px;
    position: relative;
}

.code-block-wrapper code {
    display: block;
    background: transparent;
    color: #5b39f3;
    font-family:
        'Roboto Mono', 'SF Mono', Monaco, 'Cascadia Code', 'Courier New',
        monospace;
    font-size: 16px;
    line-height: 1.5;
    tab-size: 4;
    white-space: pre;
    font-weight: 400;
    letter-spacing: -0.01875em;
}

/* Empty code block placeholder */
.code-block-wrapper code:empty::before {
    content: '// Start typing your code here...';
    color: #999999;
    font-style: italic;
    opacity: 0.6;
}

/* Code block focus state */
.code-block-wrapper:focus-within {
    box-shadow:
        0 12px 40px rgba(59, 130, 246, 0.15),
        0 4px 16px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.08);
    border-color: #5b39f3;
    transform: translateY(-2px);
}

/* Enhanced syntax highlighting */
.code-block-wrapper .hljs {
    background: transparent !important;
    color: #e2e8f0;
}

.code-block-wrapper .hljs-keyword {
    color: #c792ea;
    font-weight: 500;
}

.code-block-wrapper .hljs-string {
    color: #c3e88d;
}

.code-block-wrapper .hljs-comment {
    color: #546e7a;
    font-style: italic;
}

.code-block-wrapper .hljs-number {
    color: #f78c6c;
}

.code-block-wrapper .hljs-function {
    color: #82aaff;
}

/* .code-block-wrapper .hljs-variable {
    color: #eeffff;
} */

.code-block-wrapper .hljs-title {
    color: #82aaff;
    font-weight: 600;
}

.code-block-wrapper .hljs-attr {
    color: #ffcb6b;
}

.code-block-wrapper .hljs-built_in {
    color: #decb6b;
}

/* Line numbers styling (if needed) */
.code-block-wrapper .hljs-ln-numbers {
    color: #4a5568;
    border-right: 1px solid #2d3748;
    padding-right: 12px;
    margin-right: 12px;
    user-select: none;
}

/* Legacy code block styles for backward compatibility */
.tiptap-code-block {
    background: #1e293b;
    color: #e2e8f0;
    border-radius: 6px;
    padding: 1rem;
    margin: 1rem 0;
    overflow-x: auto;
    font-family: 'Fira Code', 'Monaco', 'Consolas', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.6;
    position: relative;
    tab-size: 2;
    white-space: pre;
}

.tiptap-code-block pre {
    margin: 0;
    padding: 0;
    background: transparent;
    overflow: visible;
    white-space: pre;
    tab-size: 2;
}

.tiptap-code-block code {
    background: transparent;
    padding: 0;
    font-size: inherit;
    color: inherit;
    white-space: pre;
    tab-size: 2;
}

/* Syntax highlighting colors */
.hljs-comment,
.hljs-quote {
    color: #64748b;
    font-style: italic;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-addition {
    color: #c084fc;
}

.hljs-number,
.hljs-string,
.hljs-meta .hljs-meta-string,
.hljs-literal,
.hljs-doctag,
.hljs-regexp {
    color: #34d399;
}

.hljs-title,
.hljs-section,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class {
    color: #60a5fa;
}

.hljs-attribute,
.hljs-attr,
.hljs-variable,
.hljs-template-variable,
.hljs-class .hljs-title,
.hljs-type {
    color: #fbbf24;
}

.hljs-symbol,
.hljs-bullet,
.hljs-subst,
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-selector-attr,
.hljs-selector-pseudo,
.hljs-link {
    color: #f472b6;
}

.hljs-built_in,
.hljs-deletion {
    color: #ef4444;
}

.hljs-formula {
    background: #334155;
}

.hljs-emphasis {
    font-style: italic;
}

.hljs-strong {
    font-weight: bold;
}

/* Better cursor visibility in code blocks */
.ProseMirror .code-block-wrapper .ProseMirror-focused {
    outline: none;
}

.ProseMirror .code-block-wrapper code:focus {
    outline: none;
}

/* Ensure proper editing experience in code blocks */
.ProseMirror pre {
    white-space: pre;
    word-wrap: normal;
    overflow-x: auto;
}

.ProseMirror pre code {
    display: block;
    white-space: pre;
    tab-size: 2;
}

/* Fix cursor positioning in code blocks */
.ProseMirror .code-block-wrapper {
    position: relative;
}

.ProseMirror .code-block-wrapper pre {
    position: relative;
    z-index: 1;
}

/* Ensure tab characters are visible */
.ProseMirror pre .tab {
    display: inline-block;
    width: 2ch;
}

/* Line numbers (optional, can be enabled later) */
.code-block-wrapper.show-line-numbers code {
    counter-reset: line;
}

.code-block-wrapper.show-line-numbers code > span {
    counter-increment: line;
}

.code-block-wrapper.show-line-numbers code > span::before {
    content: counter(line);
    display: inline-block;
    width: 2em;
    margin-right: 1em;
    color: #64748b;
    text-align: right;
}

/* Inline code */
.tiptap-editor-content code:not(.hljs) {
    /* background-color: #f1f5f9; */
    /* color: #e11d48; */
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-size: 0.875em;
    font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
}

/* Professional Blockquotes */
.tiptap-editor-content blockquote {
    border-left: 4px solid #4299e1;
    padding-left: 1.5rem;
    margin: 1.5rem 0;
    color: #4a5568;
    font-style: italic;
    background: #f7fafc;
    padding: 1rem 1.5rem;
    border-radius: 0 6px 6px 0;
    position: relative;
}

.tiptap-editor-content blockquote::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: 10px;
    font-size: 3rem;
    color: #cbd5e0;
    font-family: Georgia, serif;
}

/* Lists with Proper Indentation */
.tiptap-editor-content ul,
.tiptap-editor-content ol {
    padding-left: 2rem;
    margin: 1rem 0;
}

.tiptap-editor-content ul li,
.tiptap-editor-content ol li {
    margin: 0.25rem 0 0 0;
    line-height: 24px;
}

.tiptap-editor-content ul ul,
.tiptap-editor-content ol ol,
.tiptap-editor-content ul ol,
.tiptap-editor-content ol ul {
    margin: 0.25rem 0 0.25rem 0;
}

.tiptap-editor-content ul {
    list-style-type: disc;
}

.tiptap-editor-content ul ul {
    list-style-type: circle;
}

.tiptap-editor-content ul ul ul {
    list-style-type: square;
}

.tiptap-editor-content ol {
    list-style-type: decimal;
}

/* Links */
.tiptap-link {
    color: #4299e1;
    text-decoration: underline;
    cursor: pointer;
    transition: color 0.2s;
}

.tiptap-link:hover {
    color: #2b6cb0;
}

/* Images */
.tiptap-image {
    max-width: 100%;
    height: auto;
    margin: 1rem 0;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* HTML Formatting Extensions */

/* Text highlighting */
.tiptap-highlight {
    background-color: #fbbf24;
    color: #1f2937;
    padding: 0.1em 0.2em;
    border-radius: 0.125rem;
}

/* Underline styling */
.tiptap-underline {
    text-decoration: underline;
    text-decoration-color: #6366f1;
    text-decoration-thickness: 1px;
    text-underline-offset: 2px;
}

/* Subscript and superscript */
.tiptap-subscript {
    vertical-align: sub;
    font-size: 0.8em;
    line-height: 1;
}

.tiptap-superscript {
    vertical-align: super;
    font-size: 0.8em;
    line-height: 1;
}

/* Enhanced list styling */
.tiptap-bullet-list {
    margin: 1rem 0;
    padding-left: 1.5rem;
}

.tiptap-ordered-list {
    margin: 1rem 0;
    padding-left: 1.5rem;
}

.tiptap-list-item {
    margin: 0.25rem 0;
    line-height: 1.6;
}

.tiptap-list-item p {
    margin: 0;
}

/* Task list styling */
.tiptap-task-list {
    list-style: none;
    margin: 1rem 0;
    padding-left: 0;
}

.tiptap-task-item {
    display: flex;
    align-items: flex-start;
    margin: 0.5rem 0;
    line-height: 1.6;
}

.tiptap-task-item > label {
    display: flex;
    align-items: center;
    margin-right: 0.5rem;
    user-select: none;
    cursor: pointer;
}

.tiptap-task-item > label > input[type='checkbox'] {
    width: 1rem;
    height: 1rem;
    margin: 0;
    cursor: pointer;
    accent-color: #6366f1;
}

.tiptap-task-item > div {
    flex: 1;
    min-width: 0;
}

.tiptap-task-item[data-checked='true'] > div {
    text-decoration: line-through;
    opacity: 0.6;
}

/* Enhanced blockquote styling */
.tiptap-blockquote {
    border-left: 4px solid #6366f1;
    background: #f8fafc;
    margin: 1.5rem 0;
    padding: 1rem 1.5rem;
    border-radius: 0 6px 6px 0;
    font-style: italic;
    color: #475569;
    position: relative;
}

.tiptap-blockquote::before {
    content: '"';
    font-size: 3rem;
    color: #6366f1;
    position: absolute;
    top: -0.5rem;
    left: 0.5rem;
    opacity: 0.3;
    font-family: serif;
}

.tiptap-blockquote p {
    margin: 0;
    position: relative;
    z-index: 1;
}

.tiptap-blockquote p:not(:last-child) {
    margin-bottom: 0.5rem;
}

/* Color text styling (for Color extension) */
.tiptap-editor-content [data-color] {
    color: inherit;
}

/* Drop cursor styling */
.ProseMirror-dropcursor {
    background-color: #6366f1 !important;
    width: 2px !important;
    height: 1.2em !important;
    pointer-events: none;
    position: relative;
}

/* Gap cursor styling */
.ProseMirror-gapcursor {
    display: none;
    pointer-events: none;
    position: absolute;
}

.ProseMirror-gapcursor:after {
    content: '';
    display: block;
    position: absolute;
    top: -2px;
    width: 20px;
    border-top: 1px solid #6366f1;
    animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}

@keyframes ProseMirror-cursor-blink {
    to {
        visibility: hidden;
    }
}

.ProseMirror-focused .ProseMirror-gapcursor {
    display: block;
}

/* Enhanced selection styling for better visual feedback */
.ProseMirror ::selection {
    background-color: #c7d2fe;
}

/* Improved focus states */
.ProseMirror:focus-visible {
    outline: 2px solid #6366f1;
    outline-offset: 2px;
    border-radius: 4px;
}

/* Horizontal Rule */
.tiptap-editor-content hr,
.tiptap-horizontal-rule {
    border: none;
    border-top: 2px solid #e2e8f0;
    margin: 2rem 0;
    width: 100%;
}

/* Toolbar Styles - Clean minimalist design */
.tiptap-toolbar {
    display: flex;
    align-items: center;
    gap: 0.125rem;
    /* padding: 12px 16px; */
    background: transparent;
    border: none;
    border-bottom: none;
    flex-wrap: wrap;
    flex-shrink: 0; /* Prevent toolbar from shrinking */
    min-height: 32px;
}

.toolbar-group {
    display: flex;
    gap: 0.125rem;
    position: relative;
}

.toolbar-divider {
    width: 1px;
    height: 32px;
    background: #d1d5db;
    margin: 0 8px;
}

.tiptap-toolbar button {
    padding: 0;
    border: none;
    background: transparent;
    border-radius: 4px;
    cursor: pointer;
    color: #666666;
    transition: all 0.15s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    font-size: 16px;
    position: relative;
}

.tiptap-toolbar button:hover:not(:disabled):not(.active) {
    background: #f5f5f5;
    color: #666666;
}

.tiptap-toolbar button:active {
    transform: translateY(0);
}

.tiptap-toolbar button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.tiptap-toolbar button.active {
    background: #f2f0fe;
    color: #5b39f3;
}

.tiptap-toolbar button.active:hover {
    background: #f2f0fe;
    color: #5b39f3;
}

.tiptap-toolbar button.active svg,
.tiptap-toolbar button.active * {
    color: #5b39f3 !important;
    fill: #5b39f3 !important;
    /* stroke: #5b39f3 !important; */
}
.tiptap-toolbar button:focus {
    outline: 4px auto#5b39f3;
}
/* Table Dropdown Picker */
.table-dropdown {
    position: relative;
}

.table-picker {
    position: absolute;
    top: 100%;
    left: 0;
    margin-top: 4px;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    padding: 0.5rem;
    width: 150px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.table-picker-header {
    font-size: 0.75rem;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.5rem;
    text-align: center;
}

.table-grid {
    display: grid;
    grid-template-rows: repeat(6, 1fr);
    gap: 2px;
    margin-bottom: 0.5rem;
}

.table-row {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 2px;
}

.table-cell {
    width: 20px;
    height: 20px;
    border: 1px solid #e2e8f0;
    background: #ffffff;
    cursor: pointer;
    transition: all 0.1s;
}

.table-cell:hover {
    background: #5b39f3;
    border-color: #5b39f3;
}

.table-cell.highlighted {
    background: #f2f0fe;
    border-color: #5b39f3;
}

.table-picker-info {
    font-size: 0.75rem;
    color: #4a5568;
    text-align: center;
}

.size-display {
    font-weight: 600;
    color: #2d3748;
}

/* Code Language Dropdown */
.code-dropdown {
    position: relative;
}

.code-language-picker {
    position: absolute;
    top: 100%;
    left: 0;
    margin-top: 4px;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    padding: 0.5rem;
    min-width: 180px;
}

.code-picker-header {
    font-size: 0.75rem;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.5rem;
    text-align: center;
}

.language-list {
    max-height: 300px;
    overflow-y: auto;
}

.language-item {
    display: block;
    width: 100%;
    padding: 0.5rem 0.75rem;
    background: none;
    border: none;
    color: #4a5568;
    text-align: left;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.875rem;
    border-radius: 4px;
}

.language-item:hover {
    background: #f7fafc;
    color: #2d3748;
}

/* Table Controls */
.table-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 8px 16px;
    background: transparent;
    border-bottom: 1px solid #e5e7eb;
    flex-shrink: 0; /* Prevent table controls from shrinking */
}

.table-control-btn {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.375rem 0.75rem;
    border: none;
    background: #ffffff;
    border-radius: 4px;
    cursor: pointer;
    color: #4a5568;
    font-size: 0.875rem;
    transition: all 0.2s;
    border: 1px solid #e2e8f0;
}

.table-control-btn:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-control-btn:active {
    transform: translateY(0);
    box-shadow: none;
}

.table-control-delete {
    color: #e53e3e;
}

.table-control-delete:hover {
    background: #fff5f5;
    border-color: #fc8181;
    color: #c53030;
}

/* Mermaid Diagram Styles */
.mermaid-block {
    margin: 1.5rem 0;
    border: 1px solid #d9d9d9;
    border-radius: 12px;
    overflow: hidden;
    background-color: #f5f5f5;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.2s;
    padding: 12px;
}

/* .mermaid-block.selected {
    border-color: #5b39f3;
} */

.mermaid-block.fullscreen {
    position: fixed;
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    z-index: 1000;
    margin: 0;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.mermaid-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 12px;
}

.mermaid-toolbar-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.toolbar-error {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #e53e3e;
    font-size: 0.875rem;
}

.mermaid-toolbar-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    cursor: pointer;
    transition: all 0.2s;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    padding: 0;
    border: 0;
    background-color: transparent;
}

.mermaid-toolbar-button:hover {
    background: #f9f9f9;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.mermaid-toolbar-button:focus-visible,
.mermaid-toolbar-button:focus {
    outline: 1px solid #5b39f3; /* fix this */
}

/* Mermaid toolbar actions - similar to code block actions */
.mermaid-toolbar-actions {
    display: flex;
    gap: 0.5rem;
}

/* Edit Mode Styles */
.mermaid-edit-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 400px;
    overflow: auto;
    padding: 12px;
    background-color: #ffffff;
    border-radius: 12px;
    border: 1px solid #d9d9d9;
    column-gap: 12px;
}

.mermaid-editor-section {
    display: flex;
    flex-direction: column;
    /* margin-left: 12px; */
    /* margin-bottom: 12px; */
}

.editor-header,
.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0px;
}

.preview-header {
    margin: 9px 0px;
}

.editor-header h4,
.preview-header h4 {
    margin: 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: #2d3748;
}

.template-selector {
    position: relative;
    display: flex;
    align-items: center;
    border-radius: 32px;
    border: 1px solid #d9d9d9;
    background: #ffffff;
    cursor: pointer;
    transition: border-color 0.2s ease;
    overflow: hidden;
    height: 44px;
    box-sizing: border-box;
}

.template-selector:hover {
    border-color: #ac9bc2;
}

.template-selector:focus-within {
    border-color: #ac9bc2;
    box-shadow: 0 0 0 3px rgba(172, 155, 194, 0.1);
}

.template-dropdown {
    border: none;
    background: transparent;
    font-family:
        'Inter',
        -apple-system,
        BlinkMacSystemFont,
        'Segoe UI',
        Roboto,
        sans-serif;
    font-size: 16px;
    font-weight: 400;
    color: #333333;
    cursor: pointer;
    outline: none;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    padding: 12px 12px 12px 12px;
    min-width: 180px;
    border-radius: 32px;
    box-sizing: border-box;
    flex: 1;
}

.template-dropdown:focus {
    outline: none;
}

.template-dropdown option {
    background: #ffffff;
    color: #333333;
    font-family:
        'Inter',
        -apple-system,
        BlinkMacSystemFont,
        'Segoe UI',
        Roboto,
        sans-serif;
    font-size: 16px;
    font-weight: 400;
    padding: 8px 12px;
}

.template-dropdown option:hover {
    background: #eceefd;
}

.template-dropdown option:checked,
.template-dropdown option:selected {
    background: #eceefd;
    color: #333333;
}

.template-chevron {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    flex-shrink: 0;
}

.mermaid-textarea {
    flex: 1;
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d9d9d9;
    font-weight: 400;
    border-radius: 8px;
    font-family: 'Fira Code', 'Monaco', monospace;
    font-size: 16px;
    resize: none;
    background: #f5f5f5;
    color: #000000;
    line-height: 1.5;
    margin-top: 12px;
}

.mermaid-textarea:focus {
    outline: none;
    border-color: #4299e1;
    background: #ffffff;
}

.mermaid-preview-section {
    /* padding: 1rem; */
    display: flex;
    flex-direction: column;
    background: #ffffff;
    gap: 12px;
}

.preview-error {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #e53e3e;
    font-size: 0.75rem;
}

.mermaid-preview-content {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: auto;
    padding: 1rem;
    background: white;
    border-radius: 12px;
    border: 1px solid #d9d9d9;
    min-height: 300px;
}

.mermaid-preview-content-container {
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    overflow: auto;
}

.mermaid-preview-content svg {
    max-width: 100%;
    max-height: 500px;
    height: auto;
    /* min-height: 200px; */
}

/* View Mode Styles */
.mermaid-preview {
    padding: 24px 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    overflow: auto;
    background: #f5f5f5;
}

.mermaid-preview svg {
    max-width: 100%;
    max-height: 500px;
    height: auto;
    /* min-height: 200px; */
}

/* Fullscreen mode specific */
.mermaid-block.fullscreen .mermaid-edit-container {
    height: calc(100vh - 120px);
}

.mermaid-block.fullscreen .mermaid-preview {
    height: calc(100vh - 80px);
}

/* Error Styles */
.mermaid-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 2rem;
    text-align: center;
}

.error-icon {
    font-size: 2rem;
}

.error-message {
    font-size: 0.875rem;
    line-height: 1.5;
    max-width: 400px;
}

.error-message strong {
    display: block;
    margin-bottom: 0.5rem;
}

.mermaid-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    margin-top: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .mermaid-edit-container {
        grid-template-columns: 1fr;
    }

    .mermaid-editor-section {
        border-right: none;
        border-bottom: 1px solid #e2e8f0;
    }

    .mermaid-preview-section {
        min-height: 300px;
    }

    .mermaid-block.fullscreen {
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 0;
    }
}

/* Mermaid SVG Styling */
.mermaid-preview .node rect,
.mermaid-preview-content .node rect,
.mermaid-preview-content .node polygon {
    /* fill: #eceefd !important;
    stroke: #ac9bc2 !important; */
    fill: #f2f0fe !important;
    stroke: #5b39f3 !important;
    stroke-width: 1px !important;
}

.mermaid-preview .node text,
.mermaid-preview-content .node text {
    fill: #2d3748 !important;
}

.mermaid-preview .edgePath .path,
.mermaid-preview-content .edgePath .path {
    stroke: #4a5568 !important;
    stroke-width: 2px !important;
}

.mermaid-preview .edgeLabel,
.mermaid-preview-content .edgeLabel {
    background-color: transparent !important;
    padding: 2px 4px !important;
    border-radius: 0px !important;
    border: none !important;
    color: #2d3748 !important;
    font-weight: 500 !important;
    font-size: 12px !important;
    font-family:
        'Inter',
        -apple-system,
        BlinkMacSystemFont,
        'Segoe UI',
        Roboto,
        sans-serif !important;
    box-shadow: none !important;
    line-height: 1.2 !important;
    text-align: center !important;
    white-space: nowrap !important;
    z-index: 10 !important;
    position: relative !important;
}

.mermaid-preview .edgeLabel foreignObject,
.mermaid-preview-content .edgeLabel foreignObject {
    overflow: visible !important;
}

.mermaid-preview .edgeLabel div,
.mermaid-preview-content .edgeLabel div {
    background-color: transparent !important;
    padding: 2px 4px !important;
    border-radius: 0px !important;
    border: none !important;
    color: #2d3748 !important;
    font-weight: 500 !important;
    font-size: 12px !important;
    font-family:
        'Inter',
        -apple-system,
        BlinkMacSystemFont,
        'Segoe UI',
        Roboto,
        sans-serif !important;
    box-shadow: none !important;
    line-height: 1.2 !important;
    text-align: center !important;
    white-space: nowrap !important;
    display: inline-block !important;
}

/* Button Styles */
.btn-primary {
    background: #4299e1;
    color: white;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.2s;
    font-size: 0.875rem;
}

.btn-primary:hover {
    background: #3182ce;
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.2s;
    font-size: 0.875rem;
}

.btn-secondary:hover {
    background: #cbd5e0;
}

/* Text Alignment */
.tiptap-editor-content .ProseMirror p {
    margin-bottom: 16px;
    font-size: 16px;
}

.tiptap-editor-content .ProseMirror p > strong {
    font-weight: 600;
}

.tiptap-editor-content .ProseMirror p:first-child {
    margin-top: 0;
}

.tiptap-editor-content .ProseMirror p:last-child {
    margin-bottom: 0;
}

[style*='text-align: left'] {
    text-align: left !important;
}

[style*='text-align: center'] {
    text-align: center !important;
}

[style*='text-align: right'] {
    text-align: right !important;
}

[style*='text-align: justify'] {
    text-align: justify !important;
}

/* Print Styles */
@media print {
    .tiptap-toolbar,
    .mermaid-toolbar,
    .table-controls {
        display: none !important;
    }

    .tiptap-editor-wrapper {
        border: none;
        box-shadow: none;
    }

    .tiptap-editor-content {
        padding: 0;
    }

    .tiptap-table {
        box-shadow: none;
        page-break-inside: avoid;
    }

    .mermaid-block {
        box-shadow: none;
        border: 1px solid #ccc;
        page-break-inside: avoid;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        page-break-after: avoid;
    }

    .tiptap-code-block {
        page-break-inside: avoid;
    }

    blockquote {
        page-break-inside: avoid;
    }
}

/* Dark mode code highlighting adjustments */
/* @media (prefers-color-scheme: dark) {
    .tiptap-code-block {
        background: #1a202c;
    }

    .tiptap-sticky-header {
        background: rgba(26, 32, 44, 0.95);
        border-bottom-color: #4a5568;
    }

    .sticky-header-content {
        color: #e2e8f0;
    }

    .sticky-header-h1 {
        border-left-color: #5b39f3;
    }

    .sticky-header-h2 {
        border-left-color: #5b39f3;
        color: #cbd5e0;
    }
} */

/* Ensure content is readable */
.tiptap-editor-content p,
.tiptap-editor-content li {
    max-width: 100%;
    font-family:
        'Inter',
        -apple-system,
        BlinkMacSystemFont,
        'Segoe UI',
        Roboto,
        sans-serif;
}

.tiptap-editor-content h1,
.tiptap-editor-content h2,
.tiptap-editor-content h3,
.tiptap-editor-content h4,
.tiptap-editor-content h5,
.tiptap-editor-content h6 {
    max-width: 100%;
}

/* Focus styles for accessibility */
/* .tiptap-editor-content .ProseMirror:focus-visible {
    outline: 2px solid #4299e1;
    outline-offset: 2px;
} */

/* Selection styles */
.tiptap-editor-content .ProseMirror ::selection {
    background: #bee3f8;
    color: #1f2328;
}

/* Placeholder */
.tiptap-editor-content .ProseMirror p.is-editor-empty:first-child::before {
    content: attr(data-placeholder);
    float: left;
    color: #cbd5e0;
    pointer-events: none;
    height: 0;
}

/* Responsive adjustments */
/* Mobile Responsiveness */
@media (max-width: 768px) {
    /* .tiptap-editor-wrapper {
        min-height: 400px;
    } */

    .tiptap-editor-content {
        /* padding: 16px 18px; */
        font-size: 15px;
        border-radius: 6px;
    }

    .tiptap-toolbar {
        padding: 8px 12px;
        gap: 0.0625rem;
        min-height: 48px;
        justify-content: flex-start;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .tiptap-toolbar::-webkit-scrollbar {
        display: none;
    }

    .tiptap-toolbar button {
        width: 32px;
        height: 32px;
        padding: 6px;
        font-size: 14px;
        flex-shrink: 0;
    }

    .toolbar-divider {
        height: 16px;
        margin: 0 6px;
    }

    .table-controls {
        padding: 6px 12px;
        gap: 0.375rem;
    }

    .table-control-btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    /* Mobile table picker adjustments */
    .table-picker {
        width: 120px;
        padding: 0.375rem;
    }

    .table-cell {
        width: 16px;
        height: 16px;
    }

    /* Mobile code dropdown adjustments */
    .code-language-picker {
        min-width: 150px;
        padding: 0.375rem;
    }

    .language-item {
        padding: 0.375rem 0.5rem;
        font-size: 0.8125rem;
    }

    /* Mobile sticky header adjustments */
    .tiptap-sticky-header {
        top: 48px; /* Adjust for mobile toolbar height */
    }

    .sticky-header-content {
        padding: 10px 18px;
        font-size: 0.875rem;
    }

    .sticky-header-h1 {
        font-size: 1rem;
        padding-left: 12px;
    }

    .sticky-header-h2 {
        font-size: 0.875rem;
        padding-left: 10px;
    }
}

@media (max-width: 480px) {
    .tiptap-editor-content {
        /* padding: 12px 16px; */
        font-size: 14px;
    }

    .tiptap-toolbar {
        padding: 6px 8px;
        min-height: 44px;
    }

    .tiptap-toolbar button {
        width: 28px;
        height: 28px;
        padding: 4px;
        font-size: 13px;
    }

    .toolbar-divider {
        height: 14px;
        margin: 0 4px;
    }

    h1.tiptap-heading {
        font-size: 1.625rem;
    }

    h2.tiptap-heading {
        font-size: 1.375rem;
    }

    h3.tiptap-heading {
        font-size: 1.125rem;
    }

    /* Smaller mobile sticky header adjustments */
    .tiptap-sticky-header {
        top: 44px; /* Adjust for smaller mobile toolbar height */
    }

    .sticky-header-content {
        padding: 8px 16px;
        font-size: 0.8125rem;
    }

    .sticky-header-h1 {
        font-size: 0.9375rem;
        padding-left: 10px;
    }

    .sticky-header-h2 {
        font-size: 0.8125rem;
        padding-left: 8px;
    }
}

/* Plain Text Editor Styles */
.tiptap-plain-text-editor {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 0; /* Important for flex children */
}

.tiptap-plain-text-textarea {
    flex: 1;
    width: 100%;
    /* min-height: 400px; Minimum height for better UX */
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    outline: none;
    resize: vertical; /* Allow vertical resizing */
    padding: 20px 24px;
    font-family:
        'Inter',
        -apple-system,
        BlinkMacSystemFont,
        'Segoe UI',
        Roboto,
        sans-serif;
    font-size: 15px;
    line-height: 1.6;
    background: transparent;
    box-sizing: border-box;
    overflow-y: auto; /* Enable scrolling when content overflows */
}

.tiptap-plain-text-textarea:focus {
    outline: none;
    background-color: #ffffff;
}

.tiptap-plain-text-textarea:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.tiptap-plain-text-textarea::placeholder {
    color: #a0aec0;
    font-style: italic;
}

/* Dark mode support for plain text editor */
@media (prefers-color-scheme: dark) {
    .tiptap-plain-text-textarea {
        background-color: #2d3748;
        color: #e2e8f0;
    }

    .tiptap-plain-text-textarea:focus {
        background-color: #1a202c;
    }

    .tiptap-plain-text-textarea::placeholder {
        color: #4a5568;
    }
}

/* Raw Text Block Styles */
.raw-text-block-wrapper {
    position: relative;
    margin: 1rem 0;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: #f8fafc;
    font-family:
        'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro',
        monospace;
    overflow: hidden;
    transition: all 0.2s ease;
}

.raw-text-block-wrapper:hover {
    border-color: #cbd5e0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.raw-text-block-wrapper.selected {
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.raw-text-block-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #edf2f7;
    border-bottom: 1px solid #e2e8f0;
    font-size: 12px;
    font-weight: 500;
}

.raw-text-block-label {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #4a5568;
    font-family:
        -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.raw-text-copy-button {
    display: flex;
    align-items: center;
    padding: 4px 6px;
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 11px;
}

.raw-text-copy-button:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
    color: #374151;
}

.raw-text-block-content {
    position: relative;
}

.raw-text-content-editable {
    padding: 16px;
    margin: 0;
    background: transparent;
    border: none;
    outline: none;
    font-size: 14px;
    line-height: 1.5;
    color: #1a202c;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: inherit;
    min-height: 60px;
    resize: none;
}

.raw-text-content-editable:focus {
    outline: none;
}

.raw-text-content-editable::before {
    content: attr(data-placeholder);
    color: #a0aec0;
    font-style: italic;
    pointer-events: none;
    position: absolute;
}

.raw-text-content-editable:not(:empty)::before {
    display: none;
}

/* Dark mode support for raw text blocks */
@media (prefers-color-scheme: dark) {
    .raw-text-block-wrapper {
        background: #2d3748;
        border-color: #4a5568;
    }

    .raw-text-block-wrapper:hover {
        border-color: #718096;
    }

    .raw-text-block-header {
        background: #1a202c;
        border-color: #4a5568;
    }

    .raw-text-block-label {
        color: #a0aec0;
    }

    .raw-text-copy-button {
        background: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }

    .raw-text-copy-button:hover {
        background: #718096;
        color: #ffffff;
    }

    .raw-text-content-editable {
        color: #e2e8f0;
    }

    .raw-text-content-editable::before {
        color: #718096;
    }
}

/* Gantt Chart Specific Styles */
.mermaid-preview .gantt,
.mermaid-preview-content .gantt,
.mermaid-preview-content-container .gantt {
    width: 100% !important;
    height: auto !important;
    min-height: 300px !important;
}

/* Gantt chart task bars */
.mermaid-preview .task,
.mermaid-preview-content .task {
    fill: #5b39f3 !important;
    stroke: #4a2fb8 !important;
    stroke-width: 1px !important;
}

/* Gantt chart active tasks */
.mermaid-preview .task.active,
.mermaid-preview-content .task.active {
    fill: #7c5cff !important;
    stroke: #5b39f3 !important;
}

/* Gantt chart done tasks */
.mermaid-preview .task.done,
.mermaid-preview-content .task.done {
    fill: #d4ccff !important;
    stroke: #ac9bc2 !important;
}

/* Gantt chart text */
.mermaid-preview .taskText,
.mermaid-preview-content .taskText,
.mermaid-preview .taskTextOutsideRight,
.mermaid-preview-content .taskTextOutsideRight,
.mermaid-preview .taskTextOutsideLeft,
.mermaid-preview-content .taskTextOutsideLeft {
    fill: #2d3748 !important;
    font-size: 11px !important;
    font-family:
        'Inter',
        -apple-system,
        BlinkMacSystemFont,
        'Segoe UI',
        Roboto,
        sans-serif !important;
}

/* Gantt chart grid lines */
.mermaid-preview .grid,
.mermaid-preview-content .grid {
    stroke: #e2e8f0 !important;
    stroke-width: 0.5px !important;
}

/* Gantt chart today marker */
.mermaid-preview .today,
.mermaid-preview-content .today {
    stroke: #ff6b6b !important;
    stroke-width: 2px !important;
}

/* Ensure Gantt charts are visible in preview containers */
.mermaid-preview-content > svg[id^='mermaid'],
.mermaid-preview > svg[id^='mermaid'] {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Fix for Gantt chart container overflow */
.mermaid-preview-container {
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
}

.mermaid-preview {
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
}

/* Ensure Gantt charts have proper scrolling */
.mermaid-preview:has(.gantt),
.mermaid-preview-content:has(.gantt) {
    overflow-x: auto !important;
    overflow-y: hidden !important;
    padding-bottom: 10px; /* Space for scrollbar */
}

/* Custom scrollbar for Gantt charts */
.mermaid-preview::-webkit-scrollbar,
.mermaid-preview-content::-webkit-scrollbar {
    height: 8px;
}

.mermaid-preview::-webkit-scrollbar-track,
.mermaid-preview-content::-webkit-scrollbar-track {
    background: #f1f3f4;
    border-radius: 4px;
}

.mermaid-preview::-webkit-scrollbar-thumb,
.mermaid-preview-content::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 4px;
}

.mermaid-preview::-webkit-scrollbar-thumb:hover,
.mermaid-preview-content::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}
