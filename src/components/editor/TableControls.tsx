import {Editor} from '@tiptap/core';
import {Minus, Plus} from 'lucide-react';
import React from 'react';
import {Trash} from './icons';

interface TableControlsProps {
    editor: Editor;
}

export const TableControls: React.FC<TableControlsProps> = ({editor}) => {
    if (!editor || !editor.isActive('table')) {
        return null;
    }

    return (
        <div className="table-controls">
            <button
                onClick={() => editor.chain().focus().addColumnAfter().run()}
                title="Add Column After"
                className="table-control-btn">
                <Plus size={16} />
                <span>Column</span>
            </button>
            <button
                onClick={() => editor.chain().focus().deleteColumn().run()}
                title="Delete Column"
                className="table-control-btn">
                <Minus size={16} />
                <span>Column</span>
            </button>
            <button
                onClick={() => editor.chain().focus().addRowAfter().run()}
                title="Add Row After"
                className="table-control-btn">
                <Plus size={16} />
                <span>Row</span>
            </button>
            <button
                onClick={() => editor.chain().focus().deleteRow().run()}
                title="Delete Row"
                className="table-control-btn">
                <Minus size={16} />
                <span>Row</span>
            </button>
            <button
                onClick={() => editor.chain().focus().deleteTable().run()}
                title="Delete Table"
                className="table-control-btn table-control-delete">
                <Trash width={16} height={16} color="#e53e3e" />
                <span>Table</span>
            </button>
        </div>
    );
};
