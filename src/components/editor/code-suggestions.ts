// Basic language keywords and snippets
export const LANGUAGE_SUGGESTIONS: Record<string, string[]> = {
    javascript: [
        // Keywords & Statements
        'const',
        'let',
        'var',
        'function',
        'return',
        'if',
        'else',
        'for',
        'while',
        'class',
        'import',
        'export',
        'async',
        'await',
        'try',
        'catch',
        'finally',
        'new',
        'this',
        'super',
        'extends',
        'typeof',
        'instanceof',

        // Common Functions & Snippets
        'console.log()',
        'setTimeout()',
        'setInterval()',
        'JSON.stringify()',
        'JSON.parse()',
        'Promise',

        // Common Web APIs (Browser)
        'document.getElementById()',
        'document.querySelector()',
        'document.querySelectorAll()',
        'document.createElement()',
        'document.addEventListener()',
        'window.fetch()',
        'localStorage',
        'sessionStorage',

        // Common Array Methods
        'map()',
        'filter()',
        'reduce()',
        'forEach()',
        'find()',
        'findIndex()',
        'includes()',
        'push()',
        'pop()',
        'slice()',
        'splice()',
        'join()',
        'sort()',

        // Built-in Objects
        'Object',
        'Array',
        'String',
        'Number',
        'Boolean',
        'Date',
        'Math',
    ],
    typescript: [
        // ---- Keywords (TypeScript & JavaScript) ----
        'const',
        'let',
        'var',
        'function',
        'class',
        'interface',
        'type',
        'enum',
        'implements',
        'extends',
        'return',
        'if',
        'else',
        'for',
        'while',
        'switch',
        'case',
        'break',
        'continue',
        'import',
        'export',
        'default',
        'from',
        'async',
        'await',
        'new',
        'this',
        'super',
        'try',
        'catch',
        'finally',
        'throw',
        'in',
        'of',

        // ---- Types & Type Keywords ----
        'string',
        'number',
        'boolean',
        'any',
        'void',
        'null',
        'undefined',
        'unknown',
        'never',
        'object',
        'bigint',
        'symbol',
        'as',
        'is',
        'typeof',
        'keyof',

        // ---- Utility Types ----
        'Partial<T>',
        'Required<T>',
        'Readonly<T>',
        'Record<K, T>',
        'Pick<T, K>',
        'Omit<T, K>',
        'Exclude<T, U>',
        'Extract<T, U>',
        'NonNullable<T>',
        'ReturnType<T>',
        'Parameters<T>',
        'Awaited<T>',

        // ---- Class Members ----
        'public',
        'private',
        'protected',
        'readonly',
        'static',
        'constructor()',
        'get',
        'set',

        // ---- Common Functions & Snippets ----
        'console.log()',
        '() => {}', // Arrow function
        'new Promise()',
        'setTimeout()',
        'setInterval()',
        'JSON.stringify()',
        'JSON.parse()',

        // ---- Common Array Methods ----
        'map()',
        'filter()',
        'reduce()',
        'forEach()',
        'find()',
        'findIndex()',
        'some()',
        'every()',
        'includes()',
        'push()',
        'pop()',
        'shift()',
        'unshift()',
        'slice()',
        'splice()',
        'join()',
        'sort()',
        'flat()',
        'flatMap()',

        // ---- Common String Methods ----
        'slice()',
        'substring()',
        'split()',
        'trim()',
        'toUpperCase()',
        'toLowerCase()',
        'startsWith()',
        'endsWith()',
        'replace()',
        'replaceAll()',
        'match()',

        // ---- Web APIs (Browser) ----
        'document.getElementById()',
        'document.querySelector()',
        'document.querySelectorAll()',
        'document.createElement()',
        'document.addEventListener()',
        'window.fetch()',
        'localStorage.setItem()',
        'localStorage.getItem()',
        'sessionStorage',
        'alert()',
        'confirm()',
    ],
    python: [
        // ---- Keywords & Statements ----
        'def',
        'class',
        'if',
        'elif',
        'else',
        'for',
        'in',
        'while',
        'break',
        'continue',
        'return',
        'yield',
        'lambda',
        'import',
        'from',
        'as',
        'try',
        'except',
        'finally',
        'with',
        'pass',
        'assert',
        'del',
        'global',
        'nonlocal',
        'is',
        'and',
        'or',
        'not',
        'if __name__ == "__main__":',

        // ---- Built-in Constants & Types ----
        'True',
        'False',
        'None',
        'str',
        'int',
        'float',
        'list',
        'dict',
        'set',
        'tuple',
        'bool',

        // ---- Built-in Functions ----
        'print()',
        'len()',
        'range()',
        'type()',
        'input()',
        'open()',
        'super()',
        'max()',
        'min()',
        'sum()',
        'sorted()',
        'enumerate()',
        'zip()',
        'isinstance()',
        'hasattr()',
        'getattr()',

        // ---- Common Dunder Methods ----
        '__init__()',
        '__str__()',
        '__repr__()',
        '__len__()',
        '__eq__()',
        '__name__',
        '__main__',

        // ---- Common Standard Library Modules ----
        'os',
        'sys',
        'json',
        'datetime',
        'math',
        're',
        'collections',
        'pathlib',
        'requests', // Not standard, but extremely common
        'unittest',
        'argparse',

        // ---- Common String Methods ----
        'strip()',
        'lstrip()',
        'rstrip()',
        'split()',
        'join()',
        'format()',
        'startswith()',
        'endswith()',
        'upper()',
        'lower()',
        'replace()',
        'find()',

        // ---- Common List Methods ----
        'append()',
        'extend()',
        'insert()',
        'remove()',
        'pop()',
        'clear()',
        'index()',
        'count()',
        'sort()',
        'reverse()',

        // ---- Common Dictionary Methods ----
        'keys()',
        'values()',
        'items()',
        'get()',
        'pop()',
        'update()',
        'clear()',
    ],
    css: [
        // ---- Layout & Positioning ----
        'display: block;',
        'display: inline;',
        'display: inline-block;',
        'display: flex;',
        'display: grid;',
        'display: none;',
        'position: static;',
        'position: relative;',
        'position: absolute;',
        'position: fixed;',
        'position: sticky;',
        'top:',
        'bottom:',
        'left:',
        'right:',
        'z-index:',
        'float: left;',
        'clear: both;',
        'overflow: hidden;',
        'overflow: auto;',
        'overflow-x:',
        'overflow-y:',

        // ---- Box Model ----
        'width:',
        'height:',
        'min-width:',
        'max-width:',
        'min-height:',
        'max-height:',
        'margin:',
        'margin-top:',
        'margin-right:',
        'margin-bottom:',
        'margin-left:',
        'padding:',
        'padding-top:',
        'padding-right:',
        'padding-bottom:',
        'padding-left:',
        'border:',
        'border-width:',
        'border-style:',
        'border-color:',
        'border-radius:',
        'box-sizing: border-box;',
        'box-shadow:',

        // ---- Flexbox ----
        'flex-direction: row;',
        'flex-direction: column;',
        'flex-wrap: wrap;',
        'justify-content: center;',
        'justify-content: space-between;',
        'justify-content: flex-start;',
        'justify-content: flex-end;',
        'align-items: center;',
        'align-content:',
        'flex-grow:',
        'flex-shrink:',
        'flex-basis:',
        'gap:',

        // ---- Grid ----
        'grid-template-columns:',
        'grid-template-rows:',
        'grid-gap:',
        'grid-column:',
        'grid-row:',

        // ---- Typography ----
        'color:',
        'font-family:',
        'font-size:',
        'font-weight: bold;',
        'font-style: italic;',
        'line-height:',
        'text-align: left;',
        'text-align: center;',
        'text-align: right;',
        'text-decoration: none;',
        'text-transform: uppercase;',
        'letter-spacing:',
        'word-spacing:',
        'white-space: nowrap;',

        // ---- Background & Color ----
        'background:',
        'background-color:',
        'background-image: url();',
        'background-repeat: no-repeat;',
        'background-position: center;',
        'background-size: cover;',
        'opacity:',

        // ---- Transitions & Animations ----
        'transition:',
        'transition-property:',
        'transition-duration:',
        'transition-timing-function: ease-in-out;',
        'animation:',
        'animation-name:',
        'animation-duration:',
        '@keyframes',

        // ---- Selectors & Pseudo-classes ----
        ':hover',
        ':focus',
        ':active',
        ':first-child',
        ':last-child',
        ':nth-child()',
        '::before',
        '::after',

        // ---- Units & Values ----
        'px',
        'rem',
        'em',
        '%',
        'vh',
        'vw',
        'auto',
        'inherit',
        'initial',
        'transparent',
        'calc()',
        'var()',
    ],
    java: [
        // ---- Access Modifiers & Keywords ----
        'public',
        'private',
        'protected',
        'static',
        'final',
        'abstract',
        'synchronized',
        'volatile',
        'transient',

        // ---- Class & Interface Keywords ----
        'class',
        'interface',
        'enum',
        'extends',
        'implements',
        'new',
        'this',
        'super',
        'instanceof',

        // ---- Primitive Types ----
        'int',
        'double',
        'boolean',
        'char',
        'long',
        'float',
        'byte',
        'short',

        // ---- Control Flow ----
        'if',
        'else',
        'for',
        'while',
        'do',
        'switch',
        'case',
        'break',
        'continue',
        'return',

        // ---- Exception Handling ----
        'try',
        'catch',
        'finally',
        'throw',
        'throws',

        // ---- Core Language Classes (java.lang) ----
        'String',
        'Object',
        'System',
        'Math',
        'Integer',
        'Double',
        'Boolean',
        'Character',
        'Thread',
        'Runnable',
        'Exception',
        'Override',

        // ---- Common Utility Classes (java.util) ----
        'ArrayList',
        'LinkedList',
        'HashMap',
        'HashSet',
        'List',
        'Map',
        'Set',
        'Scanner',
        'Date',
        'Random',
        'Optional',
        'Arrays',
        'Collections',

        // ---- Common Methods & Snippets ----
        'System.out.println()',
        'main(String[] args)',
        'toString()',
        'equals()',
        'hashCode()',
        'length()',
        'substring()',
        'charAt()',
        'toUpperCase()',

        // ---- Import Statement ----
        'import',
    ],
    html: [
        // ---- Document Structure ----
        '<!DOCTYPE html>',
        '<html>',
        '<head>',
        '<body>',
        '<title>',
        '<meta>',
        '<link>',
        '<style>',
        '<script>',

        // ---- Sectioning & Semantic HTML ----
        '<header>',
        '<footer>',
        '<main>',
        '<section>',
        '<article>',
        '<nav>',
        '<aside>',
        '<div>',

        // ---- Text Content ----
        '<h1>',
        '<h2>',
        '<h3>',
        '<h4>',
        '<h5>',
        '<h6>',
        '<p>',
        '<span>',
        '<a>',
        '<strong>',
        '<em>',
        '<b>',
        '<i>',
        '<u>',
        '<blockquote>',
        '<br>',
        '<hr>',
        '<code>',
        '<pre>',

        // ---- Lists ----
        '<ul>',
        '<ol>',
        '<li>',
        '<dl>',
        '<dt>',
        '<dd>',

        // ---- Tables ----
        '<table>',
        '<thead>',
        '<tbody>',
        '<tfoot>',
        '<tr>',
        '<th>',
        '<td>',
        '<caption>',

        // ---- Forms ----
        '<form>',
        '<input>',
        '<textarea>',
        '<button>',
        '<label>',
        '<select>',
        '<option>',
        '<fieldset>',
        '<legend>',

        // ---- Embedded Content ----
        '<img>',
        '<video>',
        '<audio>',
        '<iframe>',
        '<source>',
        '<canvas>',
        '<svg>',

        // ---- Global Attributes ----
        'id=""',
        'class=""',
        'style=""',
        'title=""',
        'lang=""',
        'data-*=""',

        // ---- Common Specific Attributes ----
        'href=""',
        'src=""',
        'alt=""',
        'type=""',
        'action=""',
        'method=""',
        'for=""',
        'name=""',
        'value=""',
        'placeholder=""',
        'required',
        'disabled',
        'width=""',
        'height=""',
        'rel="stylesheet"',
        'target="_blank"',
    ],
    php: [
        // ---- Keywords & Constructs ----
        'echo',
        'print',
        'require',
        'require_once',
        'include',
        'include_once',
        'if',
        'elseif',
        'else',
        'endif',
        'for',
        'foreach',
        'while',
        'endwhile',
        'switch',
        'case',
        'break',
        'continue',
        'return',
        'function',
        'class',
        'interface',
        'trait',
        'extends',
        'implements',
        'new',
        'use',
        'namespace',
        'try',
        'catch',
        'finally',
        'throw',
        'global',

        // ---- OOP Keywords ----
        'public',
        'private',
        'protected',
        'static',
        'final',
        'abstract',

        // ---- Superglobals ----
        '$_SERVER',
        '$_GET',
        '$_POST',
        '$_FILES',
        '$_COOKIE',
        '$_SESSION',
        '$_REQUEST',
        '$_ENV',
        '$GLOBALS',

        // ---- Variable & Type Functions ----
        'isset()',
        'empty()',
        'unset()',
        'is_null()',
        'is_string()',
        'is_int()',
        'is_bool()',
        'is_array()',
        'is_object()',
        'var_dump()',
        'print_r()',
        'gettype()',

        // ---- String Functions ----
        'strlen()',
        'str_replace()',
        'strpos()',
        'substr()',
        'strtolower()',
        'strtoupper()',
        'ucfirst()',
        'trim()',
        'explode()',
        'implode()',
        'htmlspecialchars()',
        'html_entity_decode()',
        'sprintf()',

        // ---- Array Functions ----
        'count()',
        'array_push()',
        'array_pop()',
        'array_shift()',
        'array_unshift()',
        'array_keys()',
        'array_values()',
        'in_array()',
        'array_merge()',
        'array_search()',
        'sort()',
        'rsort()',
        'asort()',
        'ksort()',

        // ---- Math & Date Functions ----
        'rand()',
        'round()',
        'ceil()',
        'floor()',
        'date()',
        'time()',
        'strtotime()',

        // ---- File System Functions ----
        'file_get_contents()',
        'file_put_contents()',
        'file_exists()',
        'is_dir()',
        'is_file()',
        'unlink()',

        // ---- JSON Functions ----
        'json_encode()',
        'json_decode()',

        // ---- Magic Methods & Constants ----
        '__construct()',
        '__destruct()',
        '__toString()',
        '__DIR__',
        '__FILE__',
        '__FUNCTION__',
        '__CLASS__',
        '__METHOD__',

        // ---- Other Common Functions ----
        'header()',
        'die()',
        'exit()',
    ],
    // Add more languages as needed s
};
