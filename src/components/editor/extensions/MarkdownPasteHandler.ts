import {Extension} from '@tiptap/core';
import {Plugin, Plugin<PERSON><PERSON>} from '@tiptap/pm/state';

export const MarkdownPasteHandler = Extension.create({
    name: 'markdownPasteHandler',

    addProseMirrorPlugins() {
        return [
            new Plugin({
                key: new PluginKey('markdownPasteHandler'),
                props: {
                    handlePaste: (view, event) => {
                        // Only process in WYSIWYG mode (not plain text)
                        if (!view.editable) return false;

                        // Check if we're inside a code block - if so, let default paste behavior work
                        const {state} = view;
                        const {$from} = state.selection;
                        if ($from.parent?.type.name === 'codeBlock') {
                            return false; // Let default paste behavior handle it
                        }

                        const clipboardData = event.clipboardData;
                        if (!clipboardData) return false;

                        // Get the plain text from clipboard
                        const text = clipboardData.getData('text/plain');

                        if (!text || text.length === 0) return false;
                        // Check if the text looks like markdown
                        const markdownPatterns = [
                            /^#{1,6}\s+.+/m, // Headers
                            /\*\*[^*]+\*\*/, // Bold
                            /\*[^*]+\*/, // Italic
                            /`[^`]+`/, // Inline code
                            /^\s*[-*+]\s+/m, // Unordered lists
                            /^\s*\d+\.\s+/m, // Ordered lists
                            /^\s*>\s+/m, // Blockquotes
                            /```[\s\S]*?```/, // Code blocks
                            /^\s*\|.+\|/m, // Tables
                            /\[.+\]\(.+\)/, // Links
                            /!\[.*\]\(.+\)/, // Images
                            /^---+$/m, // Horizontal rules
                        ];

                        const looksLikeMarkdown = markdownPatterns.some(
                            pattern => pattern.test(text),
                        );

                        try {
                            // Prevent the default paste behavior
                            // Show loading state during markdown processing
                            const setIsContentLoading =
                                this.editor.storage.setIsContentLoading;

                            if (!looksLikeMarkdown) {
                                return false;
                            }
                            if (setIsContentLoading) {
                                setIsContentLoading(true);
                            }

                            // It looks like markdown, so handle it.
                            event.preventDefault();

                            try {
                                setTimeout(() => {
                                    this.editor
                                        .chain()
                                        .focus()
                                        .insertContentAt(
                                            this.editor.state.selection.head,
                                            text,
                                        )
                                        .run();

                                    if (setIsContentLoading) {
                                        setIsContentLoading(false);
                                    }
                                }, 0);
                            } catch (e) {
                                console.error('Error inserting markdown:', e);
                                // Ensure loading state is always cleared, even if insertion fails
                                if (setIsContentLoading) {
                                    setTimeout(() => {
                                        setIsContentLoading(false);
                                    }, 50); // Small delay to ensure loading indicator is visible
                                }
                            }
                            if (setIsContentLoading) {
                                setTimeout(() => {
                                    setIsContentLoading(false);
                                }, 50); // Small delay to ensure loading indicator is visible
                            }
                            return true;
                        } catch (error) {
                            console.warn(
                                'Failed to parse markdown content:',
                                error,
                            );
                            // Fall back to default paste behavior
                            return false;
                        }

                        // Not markdown-like content, use default behavior
                        return false;
                    },
                },
            }),
        ];
    },
});
