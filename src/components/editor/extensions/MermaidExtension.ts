import {Node, mergeAttributes} from '@tiptap/core';
import {ReactNodeViewRenderer} from '@tiptap/react';
import {TextSelection} from '@tiptap/pm/state';
import {Plugin, PluginKey} from '@tiptap/pm/state';
import {MermaidBlock} from '../MermaidBlock';

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        mermaid: {
            insertMermaid: (content?: string) => ReturnType;
        };
    }
}

export const MermaidExtension = Node.create({
    name: 'mermaid',

    group: 'block',

    content: '',

    marks: '',

    code: true,

    defining: true,

    atom: true,

    selectable: false,

    draggable: false,

    addAttributes() {
        return {
            content: {
                default: 'graph TD\n  A[Start] --> B[End]',
                parseHTML: element => {
                    return (
                        element.getAttribute('data-content') ||
                        element.textContent ||
                        ''
                    );
                },
                renderHTML: attributes => {
                    return {
                        'data-content': attributes.content,
                    };
                },
            },
        };
    },

    parseHTML() {
        return [
            {
                tag: 'div[data-type="mermaid"]',
                getAttrs: dom => {
                    const content =
                        dom.getAttribute('data-content') ||
                        dom.textContent ||
                        '';
                    return {content};
                },
            },
            {
                tag: 'pre[data-mermaid]',
                getAttrs: node => {
                    const content = (node as HTMLElement).textContent || '';
                    return {content};
                },
            },
        ];
    },

    renderHTML({HTMLAttributes}) {
        return [
            'div',
            mergeAttributes(
                {
                    'data-type': 'mermaid',
                    'data-content': HTMLAttributes.content || '',
                },
                HTMLAttributes,
            ),
        ];
    },

    addNodeView() {
        return ReactNodeViewRenderer(MermaidBlock);
    },

    addKeyboardShortcuts() {
        return {
            // Enter - handle mermaid selection properly
            Enter: () => {
                const {state, dispatch} = this.editor.view;
                const {selection} = state;

                // Check if we have a non-empty selection (node selection)
                if (selection.empty) {
                    return false; // Let default behavior handle empty selections
                }

                // For node selections, check if it's a mermaid
                const {$from, from, to} = selection;
                const selectedNode = $from.nodeAfter;

                // If we have a selected mermaid node
                if (selectedNode && selectedNode.type.name === this.name) {
                    // Make sure the selection spans the entire node
                    if (to - from === selectedNode.nodeSize) {
                        // Create exactly ONE paragraph after the mermaid
                        const afterPos = to;
                        const tr = state.tr.insert(
                            afterPos,
                            state.schema.nodes.paragraph.create(),
                        );
                        tr.setSelection(
                            TextSelection.near(tr.doc.resolve(afterPos + 1), 1),
                        );
                        dispatch(tr);

                        // Return true to prevent other handlers from running
                        return true;
                    }
                }

                // Not a mermaid selection, let default behavior handle it
                return false;
            },
            // Arrow Down - only when mermaid is selected (atom node)
            ArrowDown: () => {
                const {state, dispatch} = this.editor.view;
                const {selection, doc} = state;

                // For atom nodes, check if the selection is on the mermaid node
                const selectedNode = doc.nodeAt(selection.from);
                const isAtMermaid = selectedNode?.type.name === this.name;

                if (!isAtMermaid) {
                    return false; // Let other handlers deal with it
                }

                const nodePos = selection.from;
                const afterPos = nodePos + selectedNode.nodeSize;

                if (afterPos <= doc.content.size) {
                    // Try to move to existing content after mermaid
                    try {
                        const tr = state.tr.setSelection(
                            TextSelection.near(doc.resolve(afterPos), 1),
                        );
                        dispatch(tr);
                        return true;
                    } catch {
                        // If no content after, create paragraph
                        const tr = state.tr.insert(
                            afterPos,
                            state.schema.nodes.paragraph.create(),
                        );
                        tr.setSelection(
                            TextSelection.near(tr.doc.resolve(afterPos + 1), 1),
                        );
                        dispatch(tr);
                        return true;
                    }
                }

                return false;
            },

            // Arrow Up - only when mermaid is selected (atom node)
            ArrowUp: () => {
                const {state, dispatch} = this.editor.view;
                const {selection, doc} = state;

                // For atom nodes, check if the selection is on the mermaid node
                const selectedNode = doc.nodeAt(selection.from);
                const isAtMermaid = selectedNode?.type.name === this.name;

                if (!isAtMermaid) {
                    return false; // Let other handlers deal with it
                }

                const nodePos = selection.from;

                if (nodePos > 0) {
                    try {
                        const tr = state.tr.setSelection(
                            TextSelection.near(doc.resolve(nodePos), -1),
                        );
                        dispatch(tr);
                        return true;
                    } catch {
                        return false;
                    }
                }

                return false;
            },

            // Escape - exit mermaid selection and move cursor after
            Escape: () => {
                const {state, dispatch} = this.editor.view;
                const {selection, doc} = state;

                // Only handle escape if a mermaid node is selected
                if (selection.empty) {
                    return false;
                }

                const {$from, from, to} = selection;
                const selectedNode = $from.nodeAfter;

                if (!selectedNode || selectedNode.type.name !== this.name) {
                    return false;
                }

                // Make sure we're selecting the entire mermaid node
                if (to - from !== selectedNode.nodeSize) {
                    return false;
                }

                // Move cursor to after the mermaid block
                const afterPos = to;

                // Try to move to existing content, or create a paragraph if needed
                try {
                    const tr = state.tr.setSelection(
                        TextSelection.near(doc.resolve(afterPos), 1),
                    );
                    dispatch(tr);
                    return true;
                } catch {
                    // No content after, create paragraph
                    const tr = state.tr.insert(
                        afterPos,
                        state.schema.nodes.paragraph.create(),
                    );
                    tr.setSelection(
                        TextSelection.near(tr.doc.resolve(afterPos + 1), 1),
                    );
                    dispatch(tr);
                    return true;
                }
            },
        };
    },

    addCommands() {
        return {
            insertMermaid:
                (content = 'graph TD\n  A[Start] --> B[End]') =>
                ({commands}) => {
                    return commands.insertContent({
                        type: this.name,
                        attrs: {content},
                    });
                },
        };
    },

    addStorage() {
        return {
            markdown: {
                serialize(state: any, node: any) {
                    // Serialize mermaid as a code block with mermaid language
                    const content = node.attrs.content || '';
                    state.write('```mermaid\n');
                    state.text(content, false);
                    state.write('\n```');
                    state.closeBlock(node);
                },
            },
        };
    },

    // Add ProseMirror plugin to handle conversion of mermaid code blocks to mermaid nodes
    addProseMirrorPlugins() {
        return [
            new Plugin({
                key: new PluginKey('mermaidMarkdownParser'),
                appendTransaction: (transactions, _oldState, newState) => {
                    // Only process if the document has changed
                    const docChanged = transactions.some(tr => tr.docChanged);
                    if (!docChanged) return;

                    let tr = newState.tr;
                    let modified = false;

                    // Collect all mermaid code blocks first to avoid position invalidation
                    const mermaidBlocks: Array<{
                        pos: number;
                        node: any;
                        nodeSize: number;
                    }> = [];

                    newState.doc.descendants((node, pos) => {
                        if (
                            node.type.name === 'codeBlock' &&
                            node.attrs.language === 'mermaid'
                        ) {
                            mermaidBlocks.push({
                                pos,
                                node,
                                nodeSize: node.nodeSize,
                            });
                        }
                    });

                    // Replace in reverse order to maintain position validity
                    for (let i = mermaidBlocks.length - 1; i >= 0; i--) {
                        const {pos, node, nodeSize} = mermaidBlocks[i];

                        // Create mermaid node with the code block's content
                        const mermaidNode =
                            newState.schema.nodes.mermaid.create({
                                content: node.textContent,
                            });

                        // Replace the code block with the mermaid node
                        tr = tr.replaceWith(pos, pos + nodeSize, mermaidNode);
                        modified = true;
                    }

                    return modified ? tr : null;
                },
            }),
        ];
    },
});
