import {Code<PERSON>lockLowlight} from '@tiptap/extension-code-block-lowlight';
import {TextSelection, AllSelection} from '@tiptap/pm/state';
import {ReactNodeViewRenderer} from '@tiptap/react';
import {CodeBlockComponent} from '../CodeBlockComponent';

export const CustomCodeBlock = CodeBlockLowlight.extend({
    addAttributes() {
        return {
            ...this.parent?.(),
            language: {
                default: 'plaintext',
                parseHTML: element => {
                    const classNames = [
                        ...(element.firstElementChild?.classList || []),
                    ];
                    const languages = classNames
                        .filter(className => className.startsWith('language-'))
                        .map(className => className.replace('language-', ''));
                    const detectedLanguage = languages[0];

                    return detectedLanguage || null;
                },
                rendered: false,
            },
        };
    },
    addKeyboardShortcuts() {
        return {
            // Handle Tab key
            Tab: () => {
                const {state, dispatch} = this.editor.view;
                const {selection, tr} = state;

                // Check if we're inside a code block
                const {$from, $to} = selection;
                const node = $from.node();

                if (node && node.type.name === 'codeBlock') {
                    // Insert 2 spaces for tab
                    const tabSpaces = '  ';

                    if (
                        selection instanceof TextSelection ||
                        selection instanceof AllSelection
                    ) {
                        // If there's a selection, indent all selected lines
                        if (!selection.empty) {
                            const from = $from.start();
                            const to = $to.end();
                            const content = node.textContent;
                            const lines = content.split('\n');

                            // Find which lines are selected
                            let currentPos = from;
                            let startLine = 0;
                            let endLine = 0;

                            for (let i = 0; i < lines.length; i++) {
                                const lineLength = lines[i].length + 1; // +1 for newline
                                if (
                                    currentPos <= selection.from &&
                                    selection.from < currentPos + lineLength
                                ) {
                                    startLine = i;
                                }
                                if (
                                    currentPos <= selection.to &&
                                    selection.to <= currentPos + lineLength
                                ) {
                                    endLine = i;
                                    break;
                                }
                                currentPos += lineLength;
                            }

                            // Indent selected lines
                            for (let i = startLine; i <= endLine; i++) {
                                lines[i] = tabSpaces + lines[i];
                            }

                            // Replace content
                            const newContent = lines.join('\n');
                            tr.replaceWith(
                                from,
                                to,
                                this.editor.schema.text(newContent),
                            );

                            // Adjust selection
                            const newFrom = selection.from + tabSpaces.length;
                            const newTo =
                                selection.to +
                                tabSpaces.length * (endLine - startLine + 1);
                            tr.setSelection(
                                TextSelection.create(tr.doc, newFrom, newTo),
                            );
                        } else {
                            // No selection, just insert tab at cursor
                            tr.insertText(tabSpaces);
                        }

                        dispatch(tr);
                        return true;
                    }
                }

                return false;
            },

            // Handle Shift+Tab (outdent)
            'Shift-Tab': () => {
                const {state, dispatch} = this.editor.view;
                const {selection, tr} = state;

                // Check if we're inside a code block
                const {$from, $to} = selection;
                const node = $from.node();

                if (node && node.type.name === 'codeBlock') {
                    const tabSpaces = '  ';

                    if (
                        selection instanceof TextSelection ||
                        selection instanceof AllSelection
                    ) {
                        const from = $from.start();
                        const to = $to.end();
                        const content = node.textContent;
                        const lines = content.split('\n');

                        // Find which lines are selected
                        let currentPos = from;
                        let startLine = 0;
                        let endLine = 0;

                        for (let i = 0; i < lines.length; i++) {
                            const lineLength = lines[i].length + 1; // +1 for newline
                            if (
                                currentPos <= selection.from &&
                                selection.from < currentPos + lineLength
                            ) {
                                startLine = i;
                            }
                            if (
                                currentPos <= selection.to &&
                                selection.to <= currentPos + lineLength
                            ) {
                                endLine = i;
                                break;
                            }
                            currentPos += lineLength;
                        }

                        // Outdent selected lines
                        let removedChars = 0;
                        for (let i = startLine; i <= endLine; i++) {
                            if (lines[i].startsWith(tabSpaces)) {
                                lines[i] = lines[i].substring(tabSpaces.length);
                                removedChars += tabSpaces.length;
                            } else if (lines[i].startsWith(' ')) {
                                // Remove single space if no full tab
                                lines[i] = lines[i].substring(1);
                                removedChars += 1;
                            }
                        }

                        // Replace content
                        const newContent = lines.join('\n');
                        tr.replaceWith(
                            from,
                            to,
                            this.editor.schema.text(newContent),
                        );

                        // Adjust selection
                        const newFrom = Math.max(
                            from,
                            selection.from - tabSpaces.length,
                        );
                        const newTo = Math.max(
                            from,
                            selection.to - removedChars,
                        );
                        tr.setSelection(
                            TextSelection.create(tr.doc, newFrom, newTo),
                        );

                        dispatch(tr);
                        return true;
                    }
                }

                return false;
            },

            // Handle Enter key for auto-indentation
            Enter: () => {
                const {state, dispatch} = this.editor.view;
                const {selection, tr} = state;

                // Check if we're inside a code block
                const {$from} = selection;
                const node = $from.node();

                if (node && node.type.name === 'codeBlock') {
                    // Get the current line
                    const text = node.textContent;
                    const lines = text.split('\n');

                    // Find current line
                    let currentPos = $from.start();
                    let currentLine = '';

                    for (let i = 0; i < lines.length; i++) {
                        const lineStart = currentPos;
                        const lineEnd = currentPos + lines[i].length;

                        if (
                            selection.from >= lineStart &&
                            selection.from <= lineEnd
                        ) {
                            currentLine = lines[i];
                            break;
                        }

                        currentPos = lineEnd + 1; // +1 for newline
                    }

                    // Calculate indentation from current line
                    const match = currentLine.match(/^(\s*)/);
                    const indentation = match ? match[1] : '';

                    // Check if the line ends with a bracket that should increase indentation
                    const trimmedLine = currentLine.trim();
                    const shouldIncreaseIndent =
                        trimmedLine.endsWith('{') ||
                        trimmedLine.endsWith('[') ||
                        trimmedLine.endsWith('(') ||
                        trimmedLine.endsWith(':');

                    const newIndentation = shouldIncreaseIndent
                        ? indentation + '  '
                        : indentation;

                    // Insert newline with indentation
                    tr.insertText('\n' + newIndentation);
                    dispatch(tr);

                    return true;
                }

                return false;
            },

            // Handle Cmd+A (Select All) within code blocks
            'Mod-a': () => {
                const {state, dispatch} = this.editor.view;
                const {selection} = state;
                const {$from} = selection;

                // Check if we're inside a code block
                if ($from.parent?.type.name === 'codeBlock') {
                    const node = $from.parent;
                    const start = $from.start();
                    const end = start + node.content.size;

                    // Select all content within the code block only
                    const tr = state.tr.setSelection(
                        TextSelection.create(state.doc, start, end),
                    );
                    dispatch(tr);
                    return true;
                }

                return false; // Let default behavior handle it for other contexts
            },

            // Arrow Down at end of code block - exit to next paragraph
            ArrowDown: () => {
                const {state, dispatch} = this.editor.view;
                const {selection, doc} = state;
                const {$from} = selection;

                // Check if we're at the end of a code block
                if ($from.parent?.type.name === 'codeBlock') {
                    const node = $from.parent;
                    const nodeStart = $from.start() - 1;
                    const nodeEnd = nodeStart + node.nodeSize;

                    // Check if cursor is at the very end of the code block
                    if (selection.from === nodeEnd - 1) {
                        const pos = nodeEnd;

                        // Try to find or create a paragraph after the code block
                        if (pos < doc.content.size) {
                            const tr = state.tr.setSelection(
                                TextSelection.near(doc.resolve(pos), 1),
                            );
                            dispatch(tr);
                            return true;
                        } else {
                            // Create a new paragraph at the end
                            const tr = state.tr.insert(
                                pos,
                                state.schema.nodes.paragraph.create(),
                            );
                            tr.setSelection(
                                TextSelection.near(tr.doc.resolve(pos + 1), 1),
                            );
                            dispatch(tr);
                            return true;
                        }
                    }
                }

                return false;
            },

            // Arrow Up at beginning of code block - exit to previous paragraph
            ArrowUp: () => {
                const {state, dispatch} = this.editor.view;
                const {selection, doc} = state;
                const {$from} = selection;

                // Check if we're at the beginning of a code block
                if ($from.parent?.type.name === 'codeBlock') {
                    const nodeStart = $from.start() - 1;

                    // Check if cursor is at the very beginning of the code block content
                    if (selection.from === $from.start()) {
                        const pos = nodeStart;

                        // Try to move to previous node
                        if (pos > 0) {
                            const tr = state.tr.setSelection(
                                TextSelection.near(doc.resolve(pos), -1),
                            );
                            dispatch(tr);
                            return true;
                        }
                    }
                }

                return false;
            },

            // Escape key - exit code block
            Escape: () => {
                const {state, dispatch} = this.editor.view;
                const {selection, doc} = state;
                const {$from} = selection;

                if ($from.parent?.type.name === 'codeBlock') {
                    const node = $from.parent;
                    const nodeStart = $from.start() - 1;
                    const nodeEnd = nodeStart + node.nodeSize;
                    const pos = nodeEnd;

                    // Create a new paragraph after the code block
                    if (pos <= doc.content.size) {
                        const tr = state.tr;

                        // Insert paragraph if none exists
                        if (
                            pos === doc.content.size ||
                            doc.resolve(pos).parent.type.name !== 'paragraph'
                        ) {
                            tr.insert(
                                pos,
                                state.schema.nodes.paragraph.create(),
                            );
                        }

                        tr.setSelection(
                            TextSelection.near(tr.doc.resolve(pos + 1), 1),
                        );
                        dispatch(tr);
                        return true;
                    }
                }

                return false;
            },
        };
    },

    addCommands() {
        return {
            ...this.parent?.(),
            setCodeBlock:
                attributes =>
                ({commands}) => {
                    return commands.setNode(this.name, attributes);
                },
            toggleCodeBlock:
                attributes =>
                ({commands}) => {
                    return commands.toggleNode(
                        this.name,
                        'paragraph',
                        attributes,
                    );
                },
        };
    },

    addNodeView() {
        return ReactNodeViewRenderer(CodeBlockComponent);
    },

    addProseMirrorPlugins() {
        return [...(this.parent?.() || [])];
    },
});
