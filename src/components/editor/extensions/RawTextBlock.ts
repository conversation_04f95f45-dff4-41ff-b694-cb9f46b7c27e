import {Node} from '@tiptap/core';
import {ReactNodeViewRenderer} from '@tiptap/react';
import {Plugin, PluginKey} from '@tiptap/pm/state';
import {RawTextBlockComponent} from '../RawTextBlockComponent';

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        rawTextBlock: {
            insertRawTextBlock: (content?: string) => ReturnType;
            toggleRawTextBlock: () => ReturnType;
        };
    }
}

export const RawTextBlock = Node.create({
    name: 'rawTextBlock',

    group: 'block',

    content: 'text*',

    marks: '', // No marks allowed in raw text blocks

    defining: true,

    isolating: true,

    addAttributes() {
        return {
            content: {
                default: '',
                parseHTML: element => element.textContent || '',
                renderHTML: () => {
                    return {};
                },
            },
        };
    },

    parseHTML() {
        return [
            {
                tag: 'div[data-type="raw-text-block"]',
                getAttrs: element => ({
                    content: (element as HTMLElement).textContent || '',
                }),
            },
        ];
    },

    renderHTML({HTMLAttributes, node}) {
        return [
            'div',
            {
                ...HTMLAttributes,
                'data-type': 'raw-text-block',
                class: 'raw-text-block',
            },
            ['pre', {class: 'raw-text-content'}, node.textContent || ''],
        ];
    },

    addNodeView() {
        return ReactNodeViewRenderer(RawTextBlockComponent);
    },

    addCommands() {
        return {
            insertRawTextBlock:
                (content = '') =>
                ({commands}) => {
                    return commands.insertContent({
                        type: this.name,
                        content: content ? [{type: 'text', text: content}] : [],
                    });
                },

            toggleRawTextBlock:
                () =>
                ({commands}) => {
                    return commands.toggleNode(this.name, 'paragraph');
                },
        };
    },

    addKeyboardShortcuts() {
        return {
            'Mod-Shift-r': () => this.editor.commands.toggleRawTextBlock(),
        };
    },

    addStorage() {
        return {
            markdown: {
                serialize(state: any, node: any) {
                    // Serialize as a fenced code block with special marker
                    const content = node.textContent || '';
                    state.write('```raw-text\n');
                    state.text(content, false);
                    state.write('\n```');
                    state.closeBlock(node);
                },
            },
        };
    },

    addProseMirrorPlugins() {
        return [
            new Plugin({
                key: new PluginKey('rawTextBlockParser'),
                appendTransaction: (transactions, _oldState, newState) => {
                    // Only process if the document has changed
                    const docChanged = transactions.some(tr => tr.docChanged);
                    if (!docChanged) return;

                    const tr = newState.tr;
                    let modified = false;

                    // Walk through the document to find code blocks with 'raw-text' language
                    newState.doc.descendants((node, pos) => {
                        if (
                            node.type.name === 'codeBlock' &&
                            node.attrs.language === 'raw-text'
                        ) {
                            // Replace the code block with a raw text block
                            const content = node.textContent;
                            const rawTextNode =
                                newState.schema.nodes.rawTextBlock.create(
                                    {},
                                    content
                                        ? [newState.schema.text(content)]
                                        : [],
                                );

                            tr.replaceWith(
                                pos,
                                pos + node.nodeSize,
                                rawTextNode,
                            );
                            modified = true;
                        }
                    });

                    return modified ? tr : null;
                },
            }),
        ];
    },
});
