// CodeSuggestions

import React, {useEffect, useRef, useState} from 'react';
import {createPortal} from 'react-dom';
import {LANGUAGE_SUGGESTIONS} from './code-suggestions';

interface CodeSuggestionsProps {
    language: string;
    cursorPosition: {top: number; left: number};
    currentWord: string;
    onSelect: (suggestion: string) => void;
    onClose: () => void;
    isVisible: boolean;
}

export const CodeSuggestions: React.FC<CodeSuggestionsProps> = ({
    language,
    cursorPosition,
    currentWord,
    onSelect,
    onClose,
    isVisible,
}) => {
    const [selectedIndex, setSelectedIndex] = useState(0);
    const suggestionsRef = useRef<HTMLDivElement>(null);

    const suggestions = LANGUAGE_SUGGESTIONS[language] || [];
    const filteredSuggestions = currentWord
        ? suggestions.filter(s =>
              s.toLowerCase().startsWith(currentWord.toLowerCase()),
          )
        : suggestions.slice(0, 10); // Show first 10 if no current word

    if (document.querySelector('.code-suggestions')) {
        (
            document.querySelector(
                '.tiptap-editor-content-wrapper',
            ) as HTMLElement
        ).style.overflowY = 'hidden';
    } else {
        (
            document.querySelector(
                '.tiptap-editor-content-wrapper',
            ) as HTMLElement
        ).style.overflowY = 'auto';
    }

    useEffect(() => {
        setSelectedIndex(0);
    }, [currentWord]);

    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (!isVisible) return;
            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    setSelectedIndex(prev =>
                        prev < filteredSuggestions.length - 1 ? prev + 1 : 0,
                    );
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    setSelectedIndex(prev =>
                        prev > 0 ? prev - 1 : filteredSuggestions.length - 1,
                    );
                    break;
                case 'Enter':
                case 'Tab':
                    if (document.querySelector('.code-suggestions')) {
                        e.preventDefault();
                        e.stopPropagation();
                        if (filteredSuggestions[selectedIndex]) {
                            onSelect(filteredSuggestions[selectedIndex]);
                        }
                    }
                    break;
                case 'Escape':
                    e.preventDefault();
                    onClose();
                    break;
            }
        };

        if (isVisible) {
            document.addEventListener('keydown', handleKeyDown, true);
        }

        return () => {
            document.removeEventListener('keydown', handleKeyDown, true);
        };
    }, [isVisible, selectedIndex, filteredSuggestions, onSelect, onClose]);

    useEffect(() => {
        // Function to handle clicks outside the component
        const handleClickOutside = (event: MouseEvent) => {
            // If the suggestions div exists and the click is outside of it
            if (
                suggestionsRef.current &&
                !suggestionsRef.current.contains(event.target as Node)
            ) {
                onClose(); // Call the onClose prop
            }
        };

        // Add the event listener when the suggestions are visible
        if (isVisible) {
            // Use 'mousedown' to catch the event before a blur event on an input field
            document.addEventListener('mousedown', handleClickOutside);
        }

        // Cleanup function to remove the event listener
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isVisible, onClose]); // Dependencies for the effect

    if (!isVisible || filteredSuggestions.length === 0) return null;

    return createPortal(
        <div
            ref={suggestionsRef}
            className="code-suggestions"
            style={{
                position: 'fixed',
                top: cursorPosition.top,
                left: cursorPosition.left,
                zIndex: 10000,
            }}>
            {filteredSuggestions.map((suggestion, index) => (
                <div
                    key={index}
                    className={`suggestion-item ${index === selectedIndex ? 'selected' : ''}`}
                    onClick={() => onSelect(suggestion)}
                    onMouseEnter={() => setSelectedIndex(index)}>
                    <span className="suggestion-text">{suggestion}</span>
                    <span className="suggestion-type">{language}</span>
                </div>
            ))}
        </div>,
        document.body,
    );
};
