interface BoldProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function Bold({
    width = 32,
    height = 32,
    color = 'black',
}: BoldProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g transform="translate(3.00, 3.00) scale(1.125)">
                <path
                    d="M10.1598 0C12.5457 1.10887e-07 14.4798 1.95367 14.4798 4.36364C14.4798 5.5567 14.0048 6.63698 13.2367 7.42472C14.4247 8.28205 15.1998 9.68552 15.1998 11.2727C15.1998 13.8835 13.1045 16 10.5198 16H0.799805V0H10.1598ZM2.9598 13.8182H10.5198C11.9116 13.8182 13.0398 12.6785 13.0398 11.2727C13.0398 9.86691 11.9116 8.72727 10.5198 8.72727H2.9598V13.8182ZM2.9598 2.18182V6.54545H10.1598C11.3527 6.54545 12.3198 5.56862 12.3198 4.36364C12.3198 3.15865 11.3527 2.18182 10.1598 2.18182H2.9598Z"
                    fill={color}
                />
            </g>
        </svg>
    );
}
