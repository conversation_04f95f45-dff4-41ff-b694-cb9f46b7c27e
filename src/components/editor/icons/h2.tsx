interface H2Props {
    width?: number;
    height?: number;
    color?: string;
}

export default function H2({
    width = 25,
    height = 25,
    color = 'black',
}: H2Props) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 20 10"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
                d="M2 6H7V10H9V0H7V4H2V0H0V10H2V6ZM11 2H18V4H13C11.9 4 11 4.9 11 6V8C11 9.1 11.9 10 13 10H20V8H13V6H18C19.1 6 20 5.1 20 4V2C20 0.9 19.1 0 18 0H11V2Z"
                fill={color}
            />
        </svg>
    );
}
