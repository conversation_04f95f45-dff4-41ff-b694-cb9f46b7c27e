interface ImageProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function Image({
    width = 32,
    height = 32,
    color = 'black',
}: ImageProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g transform="translate(2.20, 2.20) scale(1.100)">
                <path
                    d="M2 18H16C17.1 18 18 17.1 18 16V2C18 0.9 17.1 0 16 0H2C0.9 0 0 0.9 0 2V16C0 17.1 0.9 18 2 18ZM2 16V14.41L5 11.41L6.29 12.7C6.68 13.09 7.31 13.09 7.7 12.7L12.99 7.41L15.99 10.41V16H1.99H2ZM16 2V7.59L13.71 5.3C13.6175 5.2073 13.5076 5.13375 13.3866 5.08357C13.2656 5.03339 13.136 5.00756 13.005 5.00756C12.874 5.00756 12.7443 5.03339 12.6234 5.08357C12.5024 5.13375 12.3925 5.2073 12.3 5.3L7.01 10.59L5.72 9.3C5.62749 9.2073 5.5176 9.13375 5.39662 9.08357C5.27565 9.03339 5.14597 9.00756 5.015 9.00756C4.88403 9.00756 4.75435 9.03339 4.63338 9.08357C4.5124 9.13375 4.40251 9.2073 4.31 9.3L2.02 11.59V2H16.02H16Z"
                    fill={color}
                />
                <path
                    d="M5.5 4C5.10218 4 4.72064 4.15804 4.43934 4.43934C4.15804 4.72064 4 5.10218 4 5.5C4 5.89782 4.15804 6.27936 4.43934 6.56066C4.72064 6.84196 5.10218 7 5.5 7C5.89782 7 6.27936 6.84196 6.56066 6.56066C6.84196 6.27936 7 5.89782 7 5.5C7 5.10218 6.84196 4.72064 6.56066 4.43934C6.27936 4.15804 5.89782 4 5.5 4Z"
                    fill={color}
                />
            </g>
        </svg>
    );
}
