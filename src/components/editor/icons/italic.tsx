interface ItalicProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function Italic({
    width = 32,
    height = 32,
    color = 'black',
}: ItalicProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g transform="translate(3.00, 3.00) scale(1.125)">
                <path
                    d="M15.1109 0H4.95216V2H8.68042L5.20613 14H0.888672V16H11.0474V14H7.31915L10.7934 2H15.1109V0Z"
                    fill={color}
                />
            </g>
        </svg>
    );
}
