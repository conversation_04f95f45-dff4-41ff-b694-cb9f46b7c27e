interface H3Props {
    width?: number;
    height?: number;
    color?: string;
}

export default function H3({
    width = 25,
    height = 25,
    color = 'black',
}: H3Props) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 20 10"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
                d="M2 6H7V10H9V0H7V4H2V0H0V10H2V6ZM20 2C20 0.9 19.1 0 18 0H11V2H18V4H13V6H18V8H11V10H18C19.1 10 20 9.1 20 8V6C20 5.63 19.89 5.3 19.72 5C19.89 4.7 20 4.37 20 4V2Z"
                fill={color}
            />
        </svg>
    );
}
