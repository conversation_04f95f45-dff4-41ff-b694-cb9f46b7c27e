interface H1Props {
    width?: number;
    height?: number;
    color?: string;
}

export default function H1({
    width = 25,
    height = 25,
    color = 'black',
}: H1Props) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 20 10"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
                d="M2 6H7V10H9V0H7V4H2V0H0V10H2V6ZM20 8H17V0H15.59L12.29 3.29L13.71 4.71L15 3.41V8H12V10H20V8Z"
                fill={color}
            />
        </svg>
    );
}
