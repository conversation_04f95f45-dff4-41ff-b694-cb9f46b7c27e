interface RedoProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function Redo({
    width = 32,
    height = 32,
    color = 'black',
}: RedoProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g transform="translate(2.20, 7.00) scale(1.100)">
                <path
                    d="M16.2 4.136C14.517 1.868 11.889 0.5 9 0.5C4.041 0.5 0 4.541 0 9.5H1.8C1.8 5.531 5.031 2.3 9 2.3C11.592 2.3 13.923 3.677 15.21 5.9H11.7V7.7H18V1.4H16.2V4.136Z"
                    fill={color}
                />
            </g>
        </svg>
    );
}
