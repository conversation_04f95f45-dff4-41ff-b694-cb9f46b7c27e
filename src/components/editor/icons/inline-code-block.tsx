interface CodeBlockProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function CodeBlock({
    width = 32,
    height = 32,
    color = 'black',
}: CodeBlockProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g transform="translate(2.00, 5.00) scale(1.000)">
                <path
                    d="M7.56642 11.5591L2.99681 7.00014L7.56642 2.44116L6.05739 0.932129L0 7.00014L6.05739 13.0682L7.56642 11.5591ZM13.9426 13.0682L20 7.00014L13.9426 0.932129L12.4336 2.44116L17.0032 7.00014L12.4336 11.5591L13.9426 13.0682Z"
                    fill={color}
                />
            </g>
        </svg>
    );
}
