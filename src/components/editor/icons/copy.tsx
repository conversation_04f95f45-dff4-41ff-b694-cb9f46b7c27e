interface CopyProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function Copy({
    width = 32,
    height = 32,
    color = 'black',
}: CopyProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g transform="scale(1)">
                <path
                    d="M20 2H10C8.897 2 8 2.897 8 4V8H4C2.897 8 2 8.897 2 10V20C2 21.103 2.897 22 4 22H14C15.103 22 16 21.103 16 20V16H20C21.103 16 22 15.103 22 14V4C22 2.897 21.103 2 20 2ZM4 20V10H14L14.002 20H4ZM20 14H16V10C16 8.897 15.103 8 14 8H10V4H20V14Z"
                    fill={color}
                />
            </g>
        </svg>
    );
}
