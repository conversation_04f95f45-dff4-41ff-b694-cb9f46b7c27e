interface FigmaProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function Figma({
    width = 32,
    height = 32,
    color = 'black',
}: FigmaProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g transform="translate(4.00, 2.00) scale(1.000)">
                <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M6.21352 1.55642H3.89328C2.61185 1.55642 1.57304 2.58425 1.57304 3.85214C1.57304 5.12003 2.61185 6.14785 3.89328 6.14785H6.21352V1.55642ZM6.21352 0H7.78656H10.1068C12.2571 0 14 1.72465 14 3.85214C14 5.10761 13.3931 6.22281 12.4535 6.92606C13.3931 7.62933 14 8.74454 14 10C14 12.1275 12.2571 13.8521 10.1068 13.8521C9.23739 13.8521 8.43454 13.5702 7.78656 13.0936V13.8521V16.1479C7.78656 18.2753 6.04347 20 3.89328 20C1.74307 20 0 18.2753 0 16.1479C0 14.8923 0.607006 13.7772 1.54649 13.0739C0.607006 12.3707 0 11.2555 0 10C0 8.74454 0.607021 7.62933 1.54652 6.92608C0.607021 6.22281 0 5.10761 0 3.85214C0 1.72465 1.74307 0 3.89328 0H6.21352ZM7.78656 1.55642V6.14785H10.1068C11.3883 6.14785 12.427 5.12003 12.427 3.85214C12.427 2.58425 11.3883 1.55642 10.1068 1.55642H7.78656ZM3.89328 12.2957H6.21352V10.0059V10V9.99409V7.70429H3.89328C2.61185 7.70429 1.57304 8.73212 1.57304 10C1.57304 11.2649 2.60688 12.2908 3.88408 12.2957L3.89328 12.2957ZM1.57304 16.1479C1.57304 14.883 2.60688 13.857 3.88408 13.8521L3.89328 13.8521H6.21352V16.1479C6.21352 17.4157 5.17471 18.4436 3.89328 18.4436C2.61185 18.4436 1.57304 17.4157 1.57304 16.1479ZM7.78656 9.99543C7.78906 8.72964 8.82691 7.70429 10.1068 7.70429C11.3883 7.70429 12.427 8.73212 12.427 10C12.427 11.2679 11.3883 12.2957 10.1068 12.2957C8.82691 12.2957 7.78906 11.2704 7.78656 10.0046V9.99543Z"
                    fill={color}
                />
            </g>
        </svg>
    );
}
