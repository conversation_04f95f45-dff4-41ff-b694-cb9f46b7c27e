interface CodeFileProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function CodeFile({
    width = 32,
    height = 32,
    color = 'black',
}: CodeFileProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g transform="translate(2.80, 2.20) scale(1.100)">
                <path
                    d="M6.23547 8.35156L3.35547 11.2406L6.23547 14.1296L7.51347 12.8516L5.89347 11.2406L7.51347 9.62956L6.23547 8.35156ZM8.48547 9.62956L10.1055 11.2406L8.48547 12.8516L9.76347 14.1296L12.6435 11.2406L9.76347 8.35156L8.48547 9.62956Z"
                    fill={color}
                />
                <path
                    d="M15.1458 5.103L15.1188 5.022C15.0789 4.92271 15.0175 4.83354 14.9388 4.761L10.4388 0.261C10.3578 0.18 10.2678 0.126 10.1778 0.0809999L10.0968 0.0539999C10.0218 0.0261072 9.94276 0.010899 9.8628 0.00899999C9.8448 0.00899999 9.82681 0 9.80881 0H2.5998C1.6098 0 0.799805 0.81 0.799805 1.8V16.2C0.799805 17.19 1.6098 18 2.5998 18H13.3998C14.3898 18 15.1998 17.19 15.1998 16.2V5.4C15.1998 5.4 15.1908 5.364 15.1908 5.346C15.1908 5.265 15.1728 5.193 15.1458 5.112V5.103ZM2.5998 16.191V1.791H8.89981V5.391C8.89981 5.886 9.3048 6.291 9.7998 6.291H13.3998V16.191H2.5998Z"
                    fill={color}
                />
            </g>
        </svg>
    );
}
