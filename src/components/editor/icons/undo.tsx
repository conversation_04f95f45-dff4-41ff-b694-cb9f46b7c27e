interface UndoProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function Undo({
    width = 32,
    height = 32,
    color = 'black',
}: UndoProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g transform="translate(2.20, 7.00) scale(1.100)">
                <path
                    d="M9 0.5C6.111 0.5 3.483 1.868 1.8 4.136V1.4H0V7.7H6.3V5.9H2.79C4.077 3.677 6.408 2.3 9 2.3C12.969 2.3 16.2 5.531 16.2 9.5H18C18 4.541 13.959 0.5 9 0.5Z"
                    fill={color}
                />
            </g>
        </svg>
    );
}
