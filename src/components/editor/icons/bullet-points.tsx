interface BulletPointsProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function BulletPoints({
    width = 32,
    height = 32,
    color = 'black',
}: BulletPointsProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g transform="translate(2.20, 2.80) scale(1.100)">
                <path
                    d="M5.625 6.89211H18V9.1075H5.625V6.89211ZM5.625 1.35365H18V3.56904H5.625V1.35365ZM5.625 12.4306H18V14.646H5.625V12.4306ZM0 0.799805H3.375V4.12288H0V0.799805ZM0 6.33827H3.375V9.66134H0V6.33827ZM0 11.8767H3.375V15.1998H0V11.8767Z"
                    fill={color}
                />
            </g>
        </svg>
    );
}
