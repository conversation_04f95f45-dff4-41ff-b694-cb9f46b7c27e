interface NumberedBulletPointsProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function NumberedBulletPoints({
    width = 32,
    height = 32,
    color = 'black',
}: NumberedBulletPointsProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g transform="translate(2.20, 2.80) scale(1.100)">
                <path
                    d="M3.375 8.5625V5.75H0V6.875H2.25V7.4375H0V10.25H3.375V9.125H1.125V8.5625H3.375ZM3.375 0.125H1.125V1.25H2.25V4.625H3.375V0.125ZM0 14.75V15.875H3.375V11.375H0V12.5H2.25V13.0625H0V14.1875H2.25V14.75H0ZM5.625 6.875H18V9.125H5.625V6.875ZM5.625 1.25H18V3.5H5.625V1.25ZM5.625 12.5H18V14.75H5.625V12.5Z"
                    fill={color}
                />
            </g>
        </svg>
    );
}
