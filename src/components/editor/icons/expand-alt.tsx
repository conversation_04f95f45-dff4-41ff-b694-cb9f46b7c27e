interface ExpandAltProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function ExpandAlt({
    width = 32,
    height = 32,
    color = 'black',
}: ExpandAltProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g>
                <path
                    d="M5 12H3V21H12V19H5V12ZM12 5H19V12H21V3H12V5Z"
                    fill={color}
                />
            </g>
        </svg>
    );
}
