interface TableProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function Table({
    width = 32,
    height = 32,
    color = 'black',
}: TableProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g transform="translate(2.20, 2.80) scale(1.100)">
                <path
                    d="M16.2 0H1.8C0.81 0 0 0.8 0 1.77778V14.2222C0 15.2 0.81 16 1.8 16H16.2C17.19 16 18 15.2 18 14.2222V1.77778C18 0.8 17.19 0 16.2 0ZM16.2 7.11111H9.9V1.77778H16.2V7.11111ZM8.1 1.77778V7.11111H1.8V1.77778H8.1ZM1.8 14.2222V8.88889H8.1V14.2222H1.8ZM9.9 14.2222V8.88889H16.2V14.2222H9.9Z"
                    fill={color}
                />
            </g>
        </svg>
    );
}
