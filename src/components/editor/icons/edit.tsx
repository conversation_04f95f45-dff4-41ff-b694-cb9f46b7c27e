interface EditProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function Edit({
    width = 32,
    height = 32,
    color = 'black',
}: EditProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g>
                <path
                    d="M7 17.0129L11.413 16.9979L21.045 7.4579C21.423 7.0799 21.631 6.5779 21.631 6.0439C21.631 5.5099 21.423 5.0079 21.045 4.6299L19.459 3.0439C18.703 2.2879 17.384 2.2919 16.634 3.0409L7 12.5829V17.0129ZM18.045 4.4579L19.634 6.0409L18.037 7.6229L16.451 6.0379L18.045 4.4579ZM9 13.4169L15.03 7.4439L16.616 9.0299L10.587 15.0009L9 15.0059V13.4169Z"
                    fill={color}
                />
                <path
                    d="M5 20.9999H19C20.103 20.9999 21 20.1029 21 18.9999V10.3319L19 12.3319V18.9999H8.158C8.132 18.9999 8.105 19.0099 8.079 19.0099C8.046 19.0099 8.013 19.0009 7.979 18.9999H5V4.99988H11.847L13.847 2.99988H5C3.897 2.99988 3 3.89688 3 4.99988V18.9999C3 20.1029 3.897 20.9999 5 20.9999Z"
                    fill={color}
                />
            </g>
        </svg>
    );
}
