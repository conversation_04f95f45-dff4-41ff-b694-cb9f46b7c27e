interface LinkProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function Link({
    width = 32,
    height = 32,
    color = 'black',
}: LinkProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g transform="translate(2.20, 2.20) scale(1.100)">
                <path
                    d="M6.93686 15.1967C6.38935 15.7436 5.64717 16.0507 4.87335 16.0507C4.09952 16.0507 3.35734 15.7436 2.80983 15.1967C2.26299 14.6492 1.95584 13.907 1.95584 13.1332C1.95584 12.3594 2.26299 11.6172 2.80983 11.0697L5.56443 8.31509L4.192 6.94266L1.43739 9.69726C0.525726 10.6103 0.0136719 11.8478 0.0136719 13.1381C0.0136719 14.4283 0.525726 15.6659 1.43739 16.5789C2.39128 17.5231 3.62745 18 4.88308 18C6.13871 18 7.37488 17.5231 8.32877 16.5789L11.0834 13.8243L9.71093 12.4519L6.95633 15.2065L6.93686 15.1967ZM9.69147 1.43346L6.93686 4.18806L8.3093 5.56049L11.0639 2.80589C11.6114 2.25906 12.3536 1.9519 13.1274 1.9519C13.9012 1.9519 14.6434 2.25906 15.1909 2.80589C15.7378 3.3534 16.0449 4.09559 16.0449 4.86941C16.0449 5.64323 15.7378 6.38541 15.1909 6.93292L12.4363 9.68753L13.8088 11.06L16.5634 8.30536C17.475 7.39232 17.9871 6.1548 17.9871 4.86454C17.9871 3.57428 17.475 2.33676 16.5634 1.42372C15.6503 0.512054 14.4128 0 13.1226 0C11.8323 0 10.5948 0.512054 9.68173 1.42372L9.69147 1.43346Z"
                    fill={color}
                />
                <path
                    d="M13.8189 5.55993L13.1278 4.87858L12.4464 4.1875L8.30965 8.31454L4.18262 12.4513L4.8737 13.1327L5.55505 13.8237L9.69182 9.6967L13.8189 5.55993Z"
                    fill={color}
                />
            </g>
        </svg>
    );
}
