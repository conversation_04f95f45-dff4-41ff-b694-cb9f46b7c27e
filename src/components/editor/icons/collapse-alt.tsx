interface CollapseAltProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function CollapseAlt({
    width = 32,
    height = 32,
    color = 'black',
}: CollapseAltProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g transform="scale(1.00)">
                <path
                    d="M2 15H9V22H11V13H2V15ZM15 2H13V11H22V9H15V2Z"
                    fill={color}
                />
            </g>
        </svg>
    );
}
