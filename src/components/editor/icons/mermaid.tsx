interface MermaidProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function Mermaid({
    width = 32,
    height = 32,
    color = 'black',
}: MermaidProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g transform="translate(2.20, 2.20) scale(1.100)">
                <path
                    d="M10.3848 0C11.5318 2.30553e-05 12.4618 0.930197 12.4619 2.07715V4.84668C12.4618 5.99361 11.5318 6.92381 10.3848 6.92383H9.69238V8.30762H13.1543C14.3013 8.30774 15.2314 9.23785 15.2314 10.3848V11.0771H15.9238C17.0707 11.0774 18 12.0074 18 13.1543V15.9229C18 17.0697 17.0707 17.9998 15.9238 18H13.1543C12.0072 18 11.0771 17.0699 11.0771 15.9229V13.1543C11.0771 12.0073 12.0072 11.0771 13.1543 11.0771H13.8467V10.3848C13.8466 10.0025 13.5365 9.69251 13.1543 9.69238H4.8457C4.46352 9.69256 4.15338 10.0026 4.15332 10.3848V11.0771H4.8457C5.9928 11.0771 6.92285 12.0073 6.92285 13.1543V15.9229C6.92285 17.0699 5.9928 18 4.8457 18H2.07715C0.930049 18 0 17.0699 0 15.9229V13.1543C0 12.0073 0.930048 11.0771 2.07715 11.0771H2.76855V10.3848C2.76861 9.23789 3.69879 8.3078 4.8457 8.30762H8.30762V6.92383H7.61523C6.46826 6.92375 5.53819 5.99358 5.53809 4.84668V2.07715C5.53817 0.93023 6.46825 7.77747e-05 7.61523 0H10.3848ZM2.38477 12.4619C1.83248 12.4619 1.38477 12.9096 1.38477 13.4619V15.6152C1.38477 16.1675 1.83248 16.6152 2.38477 16.6152H4.53809C5.09037 16.6152 5.53809 16.1675 5.53809 15.6152V13.4619C5.53809 12.9096 5.09037 12.4619 4.53809 12.4619H2.38477ZM13.4619 12.4619C12.9096 12.4619 12.4619 12.9096 12.4619 13.4619V15.6152C12.4619 16.1675 12.9096 16.6152 13.4619 16.6152H15.6162C16.1683 16.615 16.6162 16.1674 16.6162 15.6152V13.4619C16.6162 12.9098 16.1683 12.4621 15.6162 12.4619H13.4619ZM7.92285 1.38574C7.37068 1.38587 6.92285 1.83354 6.92285 2.38574V4.53906C6.92285 5.09127 7.37068 5.53893 7.92285 5.53906H10.0771C10.6294 5.53904 11.0771 5.09133 11.0771 4.53906V2.38574C11.0771 1.83347 10.6294 1.38576 10.0771 1.38574H7.92285Z"
                    fill={color}
                />
            </g>
        </svg>
    );
}
