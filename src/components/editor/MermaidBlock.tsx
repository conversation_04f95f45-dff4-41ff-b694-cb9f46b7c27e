import {NodeViewProps, NodeViewWrapper} from '@tiptap/react';
import {Check, Eye, Loader2} from 'lucide-react';
import mermaid from 'mermaid';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {CollapseAlt, Copy, Edit, ExpandAlt, Trash} from './icons';

// Initialize mermaid with a unique ID generator
let mermaidIdCounter = 0;

export const MermaidBlock: React.FC<NodeViewProps> = ({
    node,
    updateAttributes,
    selected,
    deleteNode,
}) => {
    const [isEditing, setIsEditing] = useState(false);
    const [tempContent, setTempContent] = useState(node.attrs.content || '');
    const [isPreviewLoading, setIsPreviewLoading] = useState(false);
    const [, setError] = useState<string | null>(null);
    const [copied, setCopied] = useState(false);
    const [isFullscreen, setIsFullscreen] = useState(false);
    const [renderedSvg, setRenderedSvg] = useState<string>('');
    const [previewSvg, setPreviewSvg] = useState<string>('');
    const [selectedTemplate, setSelectedTemplate] = useState<string>('');
    const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
    const isRenderingRef = useRef(false);
    const hasInitiallyRendered = useRef(false);
    const mermaidIdRef = useRef<string>(
        `mermaid-${Date.now()}-${mermaidIdCounter++}`,
    );

    // Initialize mermaid once
    useEffect(() => {
        try {
            mermaid.initialize({
                startOnLoad: false,
                theme: 'default',
                securityLevel: 'strict',
                fontFamily: 'Inter, system-ui, sans-serif',
                logLevel: 'fatal',
                flowchart: {
                    useMaxWidth: true,
                    htmlLabels: true,
                    curve: 'basis',
                    padding: 15,
                },
                sequence: {
                    diagramMarginX: 50,
                    diagramMarginY: 10,
                    actorMargin: 50,
                    width: 150,
                    height: 65,
                    boxMargin: 10,
                    boxTextMargin: 5,
                    noteMargin: 10,
                    messageMargin: 35,
                    mirrorActors: true,
                },
                gantt: {
                    titleTopMargin: 25,
                    barHeight: 20,
                    barGap: 4,
                    topPadding: 50,
                    leftPadding: 75,
                    gridLineStartPadding: 35,
                    fontSize: 11,
                    sectionFontSize: 11,
                    numberSectionStyles: 4,
                    axisFormat: '%Y-%m-%d',
                    useMaxWidth: true,
                    useWidth: 1200,
                },
                pie: {
                    useMaxWidth: true,
                    textPosition: 0.75,
                },
                requirement: {
                    useMaxWidth: true,
                },
            });
        } catch (error) {
            console.error('Mermaid initialization error:', error);
        }
    }, []);

    // Render mermaid diagram to SVG string
    const renderMermaidToSvg = useCallback(
        async (content: string, isPreview: boolean = false) => {
            if (!content.trim() || isRenderingRef.current) {
                return;
            }

            isRenderingRef.current = true;

            try {
                setError(null);

                // Generate a new unique ID for each render
                const renderElementId = `${mermaidIdRef.current}-render-${Date.now()}`;

                // Parse and render the diagram
                await mermaid.parse(content);
                const {svg} = await mermaid.render(renderElementId, content);

                // Process SVG to make it responsive - special handling for Gantt charts
                const isGanttChart = content.trim().startsWith('gantt');

                // For Gantt charts, we need different sizing to ensure visibility
                let svgStyle = 'max-width: 100%; height: auto;';
                if (isGanttChart) {
                    svgStyle = 'width: 100%; height: auto; min-height: 300px;';
                }

                // Process the SVG
                let processedSvg = svg;

                // Remove any conflicting width/height attributes and add our style
                processedSvg = processedSvg.replace(
                    /<svg([^>]*)(?:width="[^"]*")?([^>]*)(?:height="[^"]*")?([^>]*)>/,
                    `<svg$1$2$3 style="${svgStyle}" preserveAspectRatio="xMidYMid meet">`,
                );

                // For Gantt charts, ensure the viewBox is properly set
                if (isGanttChart && !processedSvg.includes('viewBox')) {
                    processedSvg = processedSvg.replace(
                        /<svg([^>]*)>/,
                        '<svg$1 viewBox="0 0 1200 600">',
                    );
                }

                // Update the appropriate state
                if (isPreview) {
                    setPreviewSvg(processedSvg);
                } else {
                    setRenderedSvg(processedSvg);
                }

                // Small delay for Gantt charts to ensure proper rendering
                if (isGanttChart) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            } catch (err) {
                const errorMessage =
                    err instanceof Error ? err.message : 'Unknown error';
                setError(errorMessage);

                // console.error('Mermaid render error:', err);

                const errorHtml = `
          <div class="mermaid-error">
                <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M31.8753 5.3335C17.2407 5.3335 5.33398 17.2962 5.33398 32.0002C5.33398 46.7042 17.2967 58.6668 32.0007 58.6668C46.7047 58.6668 58.6673 46.7042 58.6673 32.0002C58.6673 17.2962 46.6487 5.3335 31.8753 5.3335ZM32.0007 53.3335C20.238 53.3335 10.6673 43.7628 10.6673 32.0002C10.6673 20.2375 20.1793 10.6668 31.8753 10.6668C43.71 10.6668 53.334 20.2375 53.334 32.0002C53.334 43.7628 43.7633 53.3335 32.0007 53.3335Z" fill="#D9D9D9"/>
                    <path d="M29.334 18.6668H34.6673V37.3335H29.334V18.6668ZM29.334 40.0002H34.6673V45.3335H29.334V40.0002Z" fill="#D9D9D9"/>
                </svg>
              <div class="text-[24px] font-semibold text-[#333]">Syntax error in diagram</div>
              <div class="text-[16px] text-[#999] font-normal">${errorMessage}</div>
          </div>
        `;

                if (isPreview) {
                    setPreviewSvg(errorHtml);
                } else {
                    setRenderedSvg(errorHtml);
                }
            } finally {
                isRenderingRef.current = false;
                setIsPreviewLoading(false);
            }
        },
        [],
    );

    // Render diagram when content changes and not editing
    useEffect(() => {
        if (!isEditing && node.attrs.content) {
            setIsPreviewLoading(true);
            renderMermaidToSvg(node.attrs.content, false);
        } else if (!isEditing && !node.attrs.content) {
            // Clear rendered SVG when no content
            setRenderedSvg('');
            setError(null);
        }
    }, [node.attrs.content, isEditing, renderMermaidToSvg]);

    // Initial preview when entering edit mode
    useEffect(() => {
        if (isEditing && tempContent.trim() && !hasInitiallyRendered.current) {
            // Show initial loading and render
            setIsPreviewLoading(true);
            renderMermaidToSvg(tempContent, true);
            hasInitiallyRendered.current = true;
        } else if (!isEditing) {
            // Reset when exiting edit mode
            hasInitiallyRendered.current = false;
            setSelectedTemplate('');
        }
    }, [isEditing, tempContent, renderMermaidToSvg]);

    // Live preview in edit mode with improved debouncing
    useEffect(() => {
        if (isEditing) {
            // Clear any existing debounce timer
            if (debounceTimerRef.current) {
                clearTimeout(debounceTimerRef.current);
            }

            if (tempContent.trim()) {
                // Debounce the preview rendering with a longer delay to prevent UI hanging
                debounceTimerRef.current = setTimeout(() => {
                    if (!isRenderingRef.current) {
                        // Set loading state only when actually rendering
                        setIsPreviewLoading(true);
                        renderMermaidToSvg(tempContent, true);
                    }
                }, 1000); // Increased debounce time to prevent UI hanging
            } else {
                // Clear preview when no content
                setPreviewSvg('');
                setIsPreviewLoading(false);
                setError(null);
            }

            return () => {
                if (debounceTimerRef.current) {
                    clearTimeout(debounceTimerRef.current);
                }
            };
        }
    }, [tempContent, isEditing, renderMermaidToSvg]);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            if (debounceTimerRef.current) {
                clearTimeout(debounceTimerRef.current);
            }
            isRenderingRef.current = false;
        };
    }, []);

    const handleSave = () => {
        updateAttributes({content: tempContent});
        setIsEditing(false);
        setIsPreviewLoading(false);
        // Clear any pending debounce timer
        if (debounceTimerRef.current) {
            clearTimeout(debounceTimerRef.current);
        }
    };

    const handleCancel = () => {
        setTempContent(node.attrs.content || '');
        setIsEditing(false);
        setError(null);
        setIsPreviewLoading(false);
        // Clear any pending debounce timer
        if (debounceTimerRef.current) {
            clearTimeout(debounceTimerRef.current);
        }
    };

    const handleCopy = () => {
        navigator.clipboard.writeText(node.attrs.content || tempContent);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
    };

    const handleDelete = () => {
        if (deleteNode) {
            deleteNode();
        }
    };

    const toggleFullscreen = () => {
        setIsFullscreen(!isFullscreen);
    };

    // Example templates
    const templates = {
        Flowchart: `graph TD
    A[Start] --> B{Is it?}
    B -->|Yes| C[OK]
    B -->|No| D[End]`,
        Sequence: `sequenceDiagram
    Alice->>John: Hello John
    John-->>Alice: Hi Alice!`,
        Gantt: `gantt
    title A Gantt Diagram
    dateFormat YYYY-MM-DD
    section Section
    Task 1           :a1, 2024-01-01, 30d
    Task 2           :after a1, 20d
    section Another
    Task 3           :2024-01-12, 12d
    Task 4           :24d`,
        'Class Diagram': `classDiagram
    class Animal {
        +String name
        +int age
        +makeSound()
    }
    class Dog {
        +String breed
        +bark()
    }
    Animal <|-- Dog`,
    };

    const templateTypes = Object.keys(templates);

    return (
        <NodeViewWrapper
            className={`mermaid-block ${selected ? 'selected' : ''} ${isFullscreen ? 'fullscreen' : ''} flex flex-col`}>
            <div className="mermaid-toolbar">
                <div className="mermaid-toolbar-buttons">
                    <button
                        onClick={() => {
                            if (!isEditing) {
                                setTempContent(node.attrs.content || '');
                            } else {
                                // When exiting edit mode, clear any pending operations
                                if (debounceTimerRef.current) {
                                    clearTimeout(debounceTimerRef.current);
                                }
                                setIsPreviewLoading(false);
                            }
                            setIsEditing(!isEditing);
                        }}
                        contentEditable={false}
                        className="mermaid-toolbar-button"
                        title={isEditing ? 'Preview' : 'Edit'}
                        type="button">
                        {isEditing ? (
                            <Eye size={24} color="#666666" />
                        ) : (
                            <Edit width={24} height={24} color="#666666" />
                        )}
                    </button>

                    {!isEditing && !isFullscreen && (
                        <button
                            onClick={toggleFullscreen}
                            className="mermaid-toolbar-button"
                            title="Toggle fullscreen"
                            contentEditable={false}
                            type="button">
                            <ExpandAlt width={24} height={24} color="#666666" />
                        </button>
                    )}
                    {!isEditing && isFullscreen && (
                        <button
                            onClick={() => {
                                setIsFullscreen(false);
                            }}
                            className="mermaid-toolbar-button"
                            title="Close"
                            contentEditable={false}
                            type="button">
                            <CollapseAlt
                                width={24}
                                height={24}
                                color="#666666"
                            />
                        </button>
                    )}
                </div>
                {!isEditing && (
                    <div className="mermaid-toolbar-actions">
                        <button
                            className="mermaid-toolbar-button"
                            onClick={handleCopy}
                            title="Copy diagram code"
                            contentEditable={false}
                            type="button">
                            {copied ? (
                                <Check size={24} />
                            ) : (
                                <Copy height={24} width={24} color="#666666" />
                            )}
                        </button>
                        <button
                            className="mermaid-toolbar-button"
                            onClick={handleDelete}
                            title="Delete mermaid block"
                            contentEditable={false}
                            type="button">
                            <Trash width={24} height={24} color="#666666" />
                        </button>
                    </div>
                )}
            </div>

            {isEditing ? (
                <div className="mermaid-edit-container">
                    <div className="mermaid-editor-section">
                        <div className="mermaid-preview-section">
                            <div className="preview-header">
                                <h4 className="!text-[16px]">
                                    Current saved version
                                </h4>
                            </div>
                            <div
                                className="mermaid-preview-content-container"
                                style={{
                                    minHeight: '300px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                }}>
                                <div
                                    className="mermaid-preview-content"
                                    dangerouslySetInnerHTML={{
                                        __html: renderedSvg,
                                    }}
                                />
                            </div>

                            <div className="preview-header">
                                <h4 className="!text-[16px]">Live preview</h4>
                            </div>
                            <div
                                className="mermaid-preview-content-container"
                                style={{
                                    minHeight: '300px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                }}>
                                {isPreviewLoading ? (
                                    <div className="mermaid-preview-loading flex flex-col items-center justify-center">
                                        <Loader2 className="w-8 h-8 mb-2 animate-spin text-[#5B39F3]" />
                                        <span className="ml-2 text-sm text-gray-600">
                                            Rendering preview...
                                        </span>
                                    </div>
                                ) : (
                                    <div
                                        className="mermaid-preview-content"
                                        dangerouslySetInnerHTML={{
                                            __html: previewSvg,
                                        }}
                                    />
                                )}
                            </div>
                        </div>
                    </div>
                    <div>
                        <div className="editor-header">
                            <h4 className="!text-[16px]">Mermaid code</h4>
                            <div className="template-selector">
                                <select
                                    value={selectedTemplate}
                                    onChange={e => {
                                        if (e.target.value) {
                                            setTempContent(
                                                templates[e.target.value],
                                            );
                                            setSelectedTemplate(e.target.value);
                                        }
                                    }}
                                    className="template-dropdown">
                                    <option value="">Select template</option>
                                    {templateTypes.map((t, i) => (
                                        <option key={i} value={t}>
                                            {t}
                                        </option>
                                    ))}
                                </select>
                                <svg
                                    className="template-chevron"
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M6.29004 8.29004L12 14L17.71 8.29004"
                                        stroke="#999999"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    />
                                </svg>
                            </div>
                        </div>
                        <textarea
                            value={tempContent}
                            onChange={e => setTempContent(e.target.value)}
                            className="mermaid-textarea"
                            rows={12}
                            placeholder="Enter Mermaid diagram code..."
                            spellCheck={false}
                        />
                        <div className="mermaid-actions">
                            <button
                                onClick={handleCancel}
                                contentEditable={false}
                                className="bg-white h-[44px] rounded-[2rem] border-[1px] border-[#5B39F3] text-[#5B39F3] text-[18px] font-semibold px-[20px] py-[8px]">
                                Cancel
                            </button>
                            <button
                                onClick={handleSave}
                                contentEditable={false}
                                className="bg-[#5B39F3] h-[44px] rounded-[2rem] border-[1px] border-[#5B39F3] text-white text-[18px] font-semibold px-[20px] py-[8px]">
                                Save changes
                            </button>
                        </div>
                    </div>
                </div>
            ) : (
                <div className="mermaid-preview-container flex-1">
                    {isPreviewLoading ? (
                        <div className="mermaid-preview-loading flex flex-col items-center justify-center h-full">
                            <Loader2 className="w-8 h-8 mb-2 animate-spin text-[#5B39F3]" />
                            <span className="ml-2 text-sm text-gray-600">
                                Loading diagram...
                            </span>
                        </div>
                    ) : (
                        <div
                            className="mermaid-preview"
                            style={{
                                minHeight: node.attrs.content
                                    ?.trim()
                                    .startsWith('gantt')
                                    ? '400px'
                                    : '200px',
                            }}
                            dangerouslySetInnerHTML={{__html: renderedSvg}}
                        />
                    )}
                </div>
            )}
        </NodeViewWrapper>
    );
};
