import {Editor} from '@tiptap/core';
import React, {useEffect, useRef, useState} from 'react';

// Import available icons from the icons folder
import {
    Bold,
    BulletPoints,
    CodeFile,
    H1,
    H2,
    H3,
    InlineCodeBlock,
    Italic,
    Mermaid,
    NumberedBulletPoints,
    Redo,
    Table,
    Undo,
} from './icons';

interface EditorToolbarProps {
    editor: Editor;
}

export const EditorToolbar: React.FC<EditorToolbarProps> = ({editor}) => {
    const [showTablePicker, setShowTablePicker] = useState(false);
    const [hoveredRows, setHoveredRows] = useState(0);
    const [hoveredCols, setHoveredCols] = useState(0);
    const tablePickerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (
                tablePickerRef.current &&
                !tablePickerRef.current.contains(event.target as Node)
            ) {
                setShowTablePicker(false);
                setHoveredRows(0);
                setHoveredCols(0);
            }
        };

        if (showTablePicker) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [showTablePicker]);

    if (!editor) {
        return null;
    }

    // const addImage = () => {
    //     const url = window.prompt('Enter image URL');
    //     if (url) {
    //         editor.chain().focus().setImage({src: url}).run();
    //     }
    // };

    // const addLink = () => {
    //     const previousUrl = editor.getAttributes('link').href;
    //     const url = window.prompt('URL', previousUrl);

    //     if (url === null) {
    //         return;
    //     }

    //     if (url === '') {
    //         editor.chain().focus().extendMarkRange('link').unsetLink().run();
    //         return;
    //     }

    //     editor
    //         .chain()
    //         .focus()
    //         .extendMarkRange('link')
    //         .setLink({href: url})
    //         .run();
    // };

    const insertMermaidDiagram = () => {
        const defaultDiagram = `graph TD
    A[Start] --> B{Is it?}
    B -->|Yes| C[OK]
    C --> D[Rethink]
    D --> B
    B ---->|No| E[End]`;

        editor.chain().focus().insertMermaid(defaultDiagram).run();
    };

    const insertCodeBlock = () => {
        editor.chain().focus().toggleCodeBlock().run();
    };

    const insertTable = (rows: number, cols: number) => {
        editor
            .chain()
            .focus()
            .insertTable({rows, cols, withHeaderRow: true})
            .run();
        setShowTablePicker(false);
        setHoveredRows(0);
        setHoveredCols(0);
    };

    return (
        <div className="tiptap-toolbar">
            {/* Attachments Section */}
            {/* <div className="toolbar-group">
                <button title="Add Figma Design">
                    <Figma width={18} height={18} color="#666666" />
                </button>
                <button title="Add File">
                    <File width={18} height={18} color="#666666" />
                </button>
                <button onClick={addImage} title="Add Image">
                    <Image width={18} height={18} color="#666666" />
                </button>
                <button onClick={addLink} title="Add Link">
                    <Link width={18} height={18} color="#666666" />
                </button>
            </div>

            <div className="toolbar-divider" /> */}

            {/* Text Editor Section */}
            <div className="toolbar-group">
                <button
                    onClick={() => editor.chain().focus().toggleBold().run()}
                    className={editor.isActive('bold') ? 'active' : ''}
                    title="Bold">
                    <Bold
                        width={18}
                        height={18}
                        color={editor.isActive('bold') ? '#5B39F3' : '#666666'}
                    />
                </button>
                <button
                    onClick={() => editor.chain().focus().toggleItalic().run()}
                    className={editor.isActive('italic') ? 'active' : ''}
                    title="Italic">
                    <Italic
                        width={18}
                        height={18}
                        color={
                            editor.isActive('italic') ? '#5B39F3' : '#666666'
                        }
                    />
                </button>
            </div>

            <div className="toolbar-divider" />

            {/* Hierarchy Section */}
            <div className="toolbar-group">
                <button
                    onClick={() =>
                        editor.chain().focus().toggleHeading({level: 1}).run()
                    }
                    className={
                        editor.isActive('heading', {level: 1}) ? 'active' : ''
                    }
                    title="Heading 1">
                    <H1
                        width={18}
                        height={18}
                        color={
                            editor.isActive('heading', {level: 1})
                                ? '#5B39F3'
                                : '#666666'
                        }
                    />
                </button>
                <button
                    onClick={() =>
                        editor.chain().focus().toggleHeading({level: 2}).run()
                    }
                    className={
                        editor.isActive('heading', {level: 2}) ? 'active' : ''
                    }
                    title="Heading 2">
                    <H2
                        width={18}
                        height={18}
                        color={
                            editor.isActive('heading', {level: 2})
                                ? '#5B39F3'
                                : '#666666'
                        }
                    />
                </button>
                <button
                    onClick={() =>
                        editor.chain().focus().toggleHeading({level: 3}).run()
                    }
                    className={
                        editor.isActive('heading', {level: 3}) ? 'active' : ''
                    }
                    title="Heading 3">
                    <H3
                        width={18}
                        height={18}
                        color={
                            editor.isActive('heading', {level: 3})
                                ? '#5B39F3'
                                : '#666666'
                        }
                    />
                </button>
            </div>

            <div className="toolbar-divider" />

            {/* Structure Section */}
            <div className="toolbar-group">
                <button
                    onClick={() =>
                        editor.chain().focus().toggleBulletList().run()
                    }
                    className={editor.isActive('bulletList') ? 'active' : ''}
                    title="Bullet List">
                    <BulletPoints
                        width={18}
                        height={18}
                        color={
                            editor.isActive('bulletList')
                                ? '#5B39F3'
                                : '#666666'
                        }
                    />
                </button>
                <button
                    onClick={() =>
                        editor.chain().focus().toggleOrderedList().run()
                    }
                    className={editor.isActive('orderedList') ? 'active' : ''}
                    title="Ordered List">
                    <NumberedBulletPoints
                        width={18}
                        height={18}
                        color={
                            editor.isActive('orderedList')
                                ? '#5B39F3'
                                : '#666666'
                        }
                    />
                </button>
                <div className="table-dropdown" ref={tablePickerRef}>
                    <button
                        onClick={() => setShowTablePicker(!showTablePicker)}
                        className={editor.isActive('table') ? 'active' : ''}
                        title="Insert Table">
                        <Table
                            width={18}
                            height={18}
                            color={
                                editor.isActive('table') ? '#5B39F3' : '#666666'
                            }
                        />
                    </button>
                    {showTablePicker && (
                        <div className="table-picker">
                            <div className="table-picker-header">
                                Insert Table
                            </div>
                            <div
                                className="table-grid"
                                onMouseLeave={() => {
                                    setHoveredRows(0);
                                    setHoveredCols(0);
                                }}>
                                {Array.from({length: 6}, (_, row) => (
                                    <div key={row} className="table-row">
                                        {Array.from({length: 6}, (_, col) => (
                                            <div
                                                key={col}
                                                className={`table-cell ${
                                                    row < hoveredRows &&
                                                    col < hoveredCols
                                                        ? 'highlighted'
                                                        : ''
                                                }`}
                                                onClick={() =>
                                                    insertTable(
                                                        row + 1,
                                                        col + 1,
                                                    )
                                                }
                                                onMouseEnter={() => {
                                                    setHoveredRows(row + 1);
                                                    setHoveredCols(col + 1);
                                                }}
                                            />
                                        ))}
                                    </div>
                                ))}
                            </div>
                            <div className="table-picker-info">
                                <span className="size-display">
                                    {hoveredRows > 0 && hoveredCols > 0
                                        ? `${hoveredRows} × ${hoveredCols}`
                                        : 'Select size'}
                                </span>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            <div className="toolbar-divider" />

            {/* Code Section */}
            <div className="toolbar-group">
                <button
                    onClick={() => editor.chain().focus().toggleCode().run()}
                    className={editor.isActive('code') ? 'active' : ''}
                    title="Inline Code">
                    <InlineCodeBlock
                        width={20}
                        height={20}
                        color={editor.isActive('code') ? '#5B39F3' : '#666666'}
                    />
                </button>
                <button
                    onClick={insertCodeBlock}
                    className={editor.isActive('codeBlock') ? 'active' : ''}
                    title="Code Block">
                    <CodeFile
                        width={18}
                        height={18}
                        color={
                            editor.isActive('codeBlock') ? '#5B39F3' : '#666666'
                        }
                    />
                </button>
                <button
                    onClick={insertMermaidDiagram}
                    title="Insert Mermaid Diagram">
                    <Mermaid width={18} height={18} color="#666666" />
                </button>
            </div>

            <div className="toolbar-divider" />

            {/* Actions Section */}
            <div className="toolbar-group">
                <button
                    onClick={() => editor.chain().focus().undo().run()}
                    disabled={!editor.can().undo()}
                    title="Undo">
                    <Undo width={18} height={18} color="#666666" />
                </button>
                <button
                    onClick={() => editor.chain().focus().redo().run()}
                    disabled={!editor.can().redo()}
                    title="Redo">
                    <Redo width={18} height={18} color="#666666" />
                </button>
            </div>
        </div>
    );
};
