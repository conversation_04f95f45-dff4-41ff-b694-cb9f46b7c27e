import {<PERSON>de<PERSON><PERSON>w<PERSON>ontent, NodeViewProps, NodeViewWrapper} from '@tiptap/react';
import {Check, ChevronDown, Search, X} from 'lucide-react';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {createPortal} from 'react-dom';
import {Copy, Trash} from './icons';
import './CodeBlockComponent.css';
import {CodeSuggestions} from './CodeSuggestions';

const LANGUAGE_OPTIONS = [
    // Web Technologies
    {value: 'javascript', label: 'JavaScript'},
    {value: 'typescript', label: 'TypeScript'},
    {value: 'html', label: 'HTML'},
    {value: 'css', label: 'CSS'},
    {value: 'scss', label: 'SCSS'},
    {value: 'sass', label: 'Sass'},
    {value: 'less', label: 'Less'},
    {value: 'php', label: 'PHP'},
    {value: 'vue', label: 'Vue'},
    {value: 'svelte', label: '<PERSON>vel<PERSON>'},
    {value: 'jsx', label: 'JSX'},
    {value: 'tsx', label: 'TSX'},

    // Popular Programming Languages
    {value: 'python', label: 'Python'},
    {value: 'java', label: 'Java'},
    {value: 'csharp', label: 'C#'},
    {value: 'cpp', label: 'C++'},
    {value: 'c', label: 'C'},
    {value: 'go', label: 'Go'},
    {value: 'rust', label: 'Rust'},
    {value: 'ruby', label: 'Ruby'},
    {value: 'swift', label: 'Swift'},
    {value: 'kotlin', label: 'Kotlin'},
    {value: 'scala', label: 'Scala'},
    {value: 'dart', label: 'Dart'},
    {value: 'elixir', label: 'Elixir'},
    {value: 'erlang', label: 'Erlang'},
    {value: 'haskell', label: 'Haskell'},
    {value: 'clojure', label: 'Clojure'},
    {value: 'fsharp', label: 'F#'},
    {value: 'objectivec', label: 'Objective-C'},
    {value: 'perl', label: 'Perl'},
    {value: 'r', label: 'R'},
    {value: 'matlab', label: 'MATLAB'},
    {value: 'julia', label: 'Julia'},
    {value: 'lua', label: 'Lua'},
    {value: 'vim', label: 'Vim Script'},

    // Mobile Development
    {value: 'flutter', label: 'Flutter'},
    {value: 'reactnative', label: 'React Native'},

    // Shell & Scripting
    {value: 'bash', label: 'Bash'},
    {value: 'shell', label: 'Shell'},
    {value: 'powershell', label: 'PowerShell'},
    {value: 'batch', label: 'Batch'},
    {value: 'fish', label: 'Fish'},
    {value: 'zsh', label: 'Zsh'},

    // Databases & Query Languages
    {value: 'sql', label: 'SQL'},
    {value: 'mysql', label: 'MySQL'},
    {value: 'postgresql', label: 'PostgreSQL'},
    {value: 'sqlite', label: 'SQLite'},
    {value: 'plsql', label: 'PL/SQL'},
    {value: 'mongodb', label: 'MongoDB'},
    {value: 'redis', label: 'Redis'},
    {value: 'graphql', label: 'GraphQL'},

    // Data & Configuration
    {value: 'json', label: 'JSON'},
    {value: 'yaml', label: 'YAML'},
    {value: 'xml', label: 'XML'},
    {value: 'toml', label: 'TOML'},
    {value: 'ini', label: 'INI'},
    {value: 'csv', label: 'CSV'},
    {value: 'properties', label: 'Properties'},
    {value: 'env', label: 'Environment'},

    // DevOps & Infrastructure
    {value: 'dockerfile', label: 'Dockerfile'},
    {value: 'docker-compose', label: 'Docker Compose'},
    {value: 'kubernetes', label: 'Kubernetes'},
    {value: 'terraform', label: 'Terraform'},
    {value: 'ansible', label: 'Ansible'},
    {value: 'vagrant', label: 'Vagrant'},
    {value: 'nginx', label: 'Nginx'},
    {value: 'apache', label: 'Apache'},

    // Cloud & CI/CD
    {value: 'github-actions', label: 'GitHub Actions'},
    {value: 'gitlab-ci', label: 'GitLab CI'},
    {value: 'jenkins', label: 'Jenkins'},
    {value: 'aws', label: 'AWS'},
    {value: 'gcp', label: 'Google Cloud'},
    {value: 'azure', label: 'Azure'},

    // Documentation & Markup
    {value: 'markdown', label: 'Markdown'},
    {value: 'latex', label: 'LaTeX'},
    {value: 'asciidoc', label: 'AsciiDoc'},
    {value: 'restructuredtext', label: 'reStructuredText'},
    {value: 'textile', label: 'Textile'},

    // Assembly & Low Level
    {value: 'assembly', label: 'Assembly'},
    {value: 'nasm', label: 'NASM'},
    {value: 'arm', label: 'ARM Assembly'},
    {value: 'x86asm', label: 'x86 Assembly'},

    // Functional & Logic
    {value: 'lisp', label: 'Lisp'},
    {value: 'scheme', label: 'Scheme'},
    {value: 'prolog', label: 'Prolog'},
    {value: 'ocaml', label: 'OCaml'},
    {value: 'reason', label: 'ReasonML'},
    {value: 'elm', label: 'Elm'},
    {value: 'purescript', label: 'PureScript'},

    // Game Development
    {value: 'glsl', label: 'GLSL'},
    {value: 'hlsl', label: 'HLSL'},
    {value: 'gdscript', label: 'GDScript'},
    {value: 'unrealscript', label: 'UnrealScript'},

    // Scientific & Mathematical
    {value: 'mathematica', label: 'Mathematica'},
    {value: 'octave', label: 'Octave'},
    {value: 'sas', label: 'SAS'},
    {value: 'stata', label: 'Stata'},
    {value: 'spss', label: 'SPSS'},

    // Web Frameworks & Libraries
    {value: 'django', label: 'Django'},
    {value: 'flask', label: 'Flask'},
    {value: 'rails', label: 'Ruby on Rails'},
    {value: 'laravel', label: 'Laravel'},
    {value: 'symfony', label: 'Symfony'},
    {value: 'express', label: 'Express.js'},
    {value: 'fastapi', label: 'FastAPI'},
    {value: 'spring', label: 'Spring'},

    // Other Notable Languages
    {value: 'cobol', label: 'COBOL'},
    {value: 'fortran', label: 'Fortran'},
    {value: 'pascal', label: 'Pascal'},
    {value: 'delphi', label: 'Delphi'},
    {value: 'vbnet', label: 'VB.NET'},
    {value: 'visual-basic', label: 'Visual Basic'},
    {value: 'ada', label: 'Ada'},
    {value: 'smalltalk', label: 'Smalltalk'},
    {value: 'tcl', label: 'Tcl'},
    {value: 'awk', label: 'AWK'},
    {value: 'sed', label: 'Sed'},
    {value: 'makefile', label: 'Makefile'},
    {value: 'cmake', label: 'CMake'},
    {value: 'gradle', label: 'Gradle'},
    {value: 'maven', label: 'Maven'},

    // Specialized
    {value: 'regex', label: 'Regular Expression'},
    {value: 'log', label: 'Log Files'},
    {value: 'diff', label: 'Diff'},
    {value: 'patch', label: 'Patch'},
    {value: 'gitignore', label: 'Git Ignore'},
    {value: 'gitconfig', label: 'Git Config'},
    {value: 'editorconfig', label: 'EditorConfig'},
    {value: 'browserslist', label: 'Browserslist'},

    // Default
    {value: 'plaintext', label: 'Plain Text'},
];

export const CodeBlockComponent: React.FC<NodeViewProps> = ({
    node,
    updateAttributes,
    deleteNode,
    editor,
}) => {
    const [showLanguageMenu, setShowLanguageMenu] = useState(false);
    const [copied, setCopied] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [highlightedIndex, setHighlightedIndex] = useState(0);
    const [dropdownPosition, setDropdownPosition] = useState({top: 0, left: 0});

    // Code suggestions state
    const [showSuggestions, setShowSuggestions] = useState(false);
    const [suggestionPosition, setSuggestionPosition] = useState({
        top: 0,
        left: 0,
    });
    const [currentWord, setCurrentWord] = useState('');

    const dropdownRef = useRef<HTMLDivElement>(null);
    const searchInputRef = useRef<HTMLInputElement>(null);
    const optionsListRef = useRef<HTMLDivElement>(null);
    const languageButtonRef = useRef<HTMLButtonElement>(null);

    const language = node.attrs.language || 'plaintext';
    const currentLanguage =
        LANGUAGE_OPTIONS.find(opt => opt.value === language) ||
        LANGUAGE_OPTIONS[0];

    // Filter languages based on search term
    const filteredLanguages = LANGUAGE_OPTIONS.filter(
        lang =>
            lang.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
            lang.value.toLowerCase().includes(searchTerm.toLowerCase()),
    );

    const handleLanguageChange = useCallback(
        (newLanguage: string) => {
            updateAttributes({language: newLanguage});
            setShowLanguageMenu(false);
            setSearchTerm('');
            setHighlightedIndex(0);
        },
        [updateAttributes],
    );

    // Close dropdown when clicking outside and handle scroll lock
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            const target = event.target as Node;

            // Check if clicked outside the language button
            if (
                dropdownRef.current &&
                !dropdownRef.current.contains(event.target as Node) &&
                languageButtonRef.current &&
                !languageButtonRef.current.contains(event.target as Node)
            ) {
                // Check if clicked outside the dropdown (which is now in a portal)
                const dropdownElement =
                    document.querySelector('.language-dropdown');
                if (!dropdownElement || !dropdownElement.contains(target)) {
                    setShowLanguageMenu(false);
                    setSearchTerm('');
                    setHighlightedIndex(0);
                }
            }
        };

        // Lock scroll when dropdown is open
        let scrollPosition = 0;

        if (showLanguageMenu) {
            // Save current scroll position
            scrollPosition = window.pageYOffset;

            // Lock body scroll
            document.body.style.overflow = 'hidden';
            document.body.style.position = 'fixed';
            document.body.style.top = `-${scrollPosition}px`;
            document.body.style.width = '100%';

            document.addEventListener('mousedown', handleClickOutside);
        }

        return () => {
            // Restore scroll
            if (showLanguageMenu) {
                document.body.style.overflow = '';
                document.body.style.position = '';
                document.body.style.top = '';
                document.body.style.width = '';
                window.scrollTo(0, scrollPosition);
            }

            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [showLanguageMenu]);

    // Focus search input when dropdown opens
    useEffect(() => {
        if (showLanguageMenu && searchInputRef.current) {
            searchInputRef.current.focus();
        }
    }, [showLanguageMenu]);

    // Reset highlighted index when search term changes
    useEffect(() => {
        setHighlightedIndex(0);
    }, [searchTerm]);

    // Handle keyboard navigation
    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (!showLanguageMenu) return;
            // event.stopPropagation();
            switch (event.key) {
                case 'ArrowDown':
                    event.preventDefault();
                    setHighlightedIndex(prev =>
                        prev < filteredLanguages.length - 1 ? prev + 1 : 0,
                    );
                    break;
                case 'ArrowUp':
                    event.preventDefault();
                    setHighlightedIndex(prev =>
                        prev > 0 ? prev - 1 : filteredLanguages.length - 1,
                    );
                    break;
                case 'Escape':
                    event.preventDefault();
                    setShowLanguageMenu(false);
                    setSearchTerm('');
                    setHighlightedIndex(0);
                    break;
                case 'Enter':
                    event.preventDefault();
                    if (filteredLanguages[highlightedIndex]) {
                        handleLanguageChange(
                            filteredLanguages[highlightedIndex].value,
                        );
                    }
                    break;
            }
        };

        if (showLanguageMenu) {
            document.addEventListener('keydown', handleKeyDown, true);
        }

        return () => {
            document.removeEventListener('keydown', handleKeyDown, true);
        };
    }, [
        showLanguageMenu,
        highlightedIndex,
        filteredLanguages,
        handleLanguageChange,
    ]);

    // Scroll highlighted option into view
    useEffect(() => {
        if (optionsListRef.current && showLanguageMenu) {
            const highlightedElement = optionsListRef.current.children[
                highlightedIndex
            ] as HTMLElement;
            if (highlightedElement) {
                highlightedElement.scrollIntoView({
                    block: 'nearest',
                    behavior: 'smooth',
                });
            }
        }
    }, [highlightedIndex, showLanguageMenu]);

    const handleCopy = () => {
        const codeText = node.textContent;
        navigator.clipboard.writeText(codeText);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
    };

    const handleDelete = () => {
        if (deleteNode) {
            deleteNode();
        }
    };

    const calculateDropdownPosition = () => {
        if (languageButtonRef.current) {
            const rect = languageButtonRef.current.getBoundingClientRect();
            setDropdownPosition({
                top: rect.bottom + 4,
                left: rect.left,
            });
        }
    };

    // Handle code input for suggestions
    const handleCodeInput = useCallback(() => {
        if (!editor) return;

        const selection = editor.state.selection;
        const pos = selection.$from;

        // Get the current word being typed
        const textBefore = pos.nodeBefore?.text || '';
        const match = textBefore.match(/(\w+)$/); // Use a single backslash
        const word = match ? match[1] : '';

        if (word.length > 0) {
            // Get cursor position
            const coords = editor.view.coordsAtPos(pos.pos);
            setSuggestionPosition({
                top: coords.bottom + 5,
                left: coords.left,
            });
            setCurrentWord(word);
            setShowSuggestions(true);
        } else {
            setShowSuggestions(false);
        }
    }, [editor]);

    // Handle suggestion selection
    const handleSuggestionSelect = useCallback(
        (suggestion: string) => {
            if (!editor) return;

            const selection = editor.state.selection;
            const pos = selection.$from;

            // Calculate the position to replace the current word
            const textBefore = pos.nodeBefore?.text || '';
            const match = textBefore.match(/(\w+)$/);

            if (match) {
                const wordStart = pos.pos - match[1].length;
                const wordEnd = pos.pos;

                // Replace the current word with the suggestion
                editor
                    .chain()
                    .focus()
                    .deleteRange({from: wordStart, to: wordEnd})
                    .insertContent(suggestion)
                    .run();
            } else {
                // Just insert the suggestion
                editor.chain().focus().insertContent(suggestion).run();
            }

            setShowSuggestions(false);
            setCurrentWord('');
        },
        [editor],
    );

    // Close suggestions
    const closeSuggestions = useCallback(() => {
        setShowSuggestions(false);
        setCurrentWord('');
    }, []);

    // Listen for input events on the editor
    useEffect(() => {
        if (!editor) return;

        const handleUpdate = () => {
            // Simple trigger on any update - you can make this more sophisticated
            if (editor.isFocused) {
                const {from} = editor.state.selection;
                const pos = editor.state.doc.resolve(from);

                // Check if we're in a code block
                if (pos.parent.type.name === 'codeBlock') {
                    handleCodeInput();
                }
            }
        };

        editor.on('update', handleUpdate);

        return () => {
            editor.off('update', handleUpdate);
        };
    }, [editor, handleCodeInput]);

    const clearSearch = () => {
        setSearchTerm('');
        setHighlightedIndex(0);
        if (searchInputRef.current) {
            searchInputRef.current.focus();
        }
    };

    return (
        <NodeViewWrapper className="code-block-wrapper">
            <div className="code-block-header">
                <div className="language-selector" ref={dropdownRef}>
                    <button
                        ref={languageButtonRef}
                        className="language-button"
                        onClick={() => {
                            if (!showLanguageMenu) {
                                calculateDropdownPosition();
                            }
                            setShowLanguageMenu(!showLanguageMenu);
                        }}
                        contentEditable={false}
                        type="button">
                        <span className="language-label">
                            {currentLanguage.label}
                        </span>
                        <ChevronDown
                            size={14}
                            className={`chevron ${showLanguageMenu ? 'open' : ''}`}
                        />
                    </button>
                    {showLanguageMenu &&
                        createPortal(
                            <>
                                {/* Backdrop overlay */}
                                <div
                                    className="language-dropdown-backdrop"
                                    style={{
                                        position: 'fixed',
                                        top: 0,
                                        left: 0,
                                        right: 0,
                                        bottom: 0,
                                        backgroundColor: 'rgba(0, 0, 0, 0.1)',
                                        zIndex: 9998,
                                    }}
                                    onClick={() => {
                                        setShowLanguageMenu(false);
                                        setSearchTerm('');
                                        setHighlightedIndex(0);
                                    }}
                                />
                                <div
                                    className="language-dropdown"
                                    style={{
                                        position: 'fixed',
                                        top: dropdownPosition.top,
                                        left: dropdownPosition.left,
                                        zIndex: 9999,
                                        maxHeight: '250px',
                                        overflowY: 'auto',
                                        backgroundColor: 'white',
                                        boxShadow:
                                            '0 4px 12px rgba(0, 0, 0, 0.15)',
                                        borderRadius: '8px',
                                        border: '1px solid #e5e7eb',
                                    }}>
                                    <div className="dropdown-header">
                                        <div className="search-container">
                                            <Search
                                                size={16}
                                                className="search-icon"
                                            />
                                            <input
                                                ref={searchInputRef}
                                                type="text"
                                                placeholder="Search languages..."
                                                value={searchTerm}
                                                onChange={e =>
                                                    setSearchTerm(
                                                        e.target.value,
                                                    )
                                                }
                                                className="search-input"
                                                contentEditable={false}
                                            />
                                            {searchTerm && (
                                                <button
                                                    onClick={clearSearch}
                                                    className="clear-search"
                                                    contentEditable={false}
                                                    type="button">
                                                    <X size={14} />
                                                </button>
                                            )}
                                        </div>
                                        <div className="results-count">
                                            {filteredLanguages.length} language
                                            {filteredLanguages.length !== 1
                                                ? 's'
                                                : ''}
                                        </div>
                                    </div>
                                    <div
                                        className="language-list"
                                        ref={optionsListRef}>
                                        {filteredLanguages.length > 0 ? (
                                            filteredLanguages.map(
                                                (option, index) => (
                                                    <button
                                                        key={option.value}
                                                        className={`language-option ${
                                                            option.value ===
                                                            language
                                                                ? 'active'
                                                                : ''
                                                        } ${index === highlightedIndex ? 'highlighted' : ''}`}
                                                        onClick={() =>
                                                            handleLanguageChange(
                                                                option.value,
                                                            )
                                                        }
                                                        onMouseEnter={() =>
                                                            setHighlightedIndex(
                                                                index,
                                                            )
                                                        }
                                                        contentEditable={false}
                                                        type="button">
                                                        <span className="language-name">
                                                            {option.label}
                                                        </span>
                                                        <span className="language-code">
                                                            {option.value}
                                                        </span>
                                                    </button>
                                                ),
                                            )
                                        ) : (
                                            <div className="no-results">
                                                <span>No languages found</span>
                                                <small>
                                                    Try a different search term
                                                </small>
                                            </div>
                                        )}
                                    </div>
                                    <div className="dropdown-footer">
                                        <small>
                                            Use ↑↓ to navigate, Enter to select,
                                            Esc to close
                                        </small>
                                    </div>
                                </div>
                            </>,
                            document.body,
                        )}
                </div>
                <div className="code-block-actions">
                    <button
                        className="mermaid-toolbar-button"
                        onClick={handleCopy}
                        title="Copy code"
                        contentEditable={false}
                        type="button">
                        {copied ? (
                            <Check size={24} />
                        ) : (
                            <Copy height={24} width={24} color="#666666" />
                        )}
                    </button>
                    <button
                        className="mermaid-toolbar-button"
                        onClick={handleDelete}
                        title="Delete code block"
                        contentEditable={false}
                        type="button">
                        <Trash width={24} height={24} color="#666666" />
                    </button>
                </div>
            </div>
            <div className="code-content">
                <pre>
                    <NodeViewContent as="code" />
                </pre>
            </div>
            <CodeSuggestions
                language={language}
                cursorPosition={suggestionPosition}
                currentWord={currentWord}
                onSelect={handleSuggestionSelect}
                onClose={closeSuggestions}
                isVisible={showSuggestions}
            />
        </NodeViewWrapper>
    );
};
