import React, {
    createContext,
    useContext,
    useCallback,
    useEffect,
    useState,
} from 'react';
import {getGithubInstallationStatus} from '@/lib/backend';
import {GithubInstallationStatus} from '@/lib/entity-types';

interface GitInstallationsContextType {
    installations: GithubInstallationStatus[];
    isLoading: boolean;
    error: string | null;
    checkGitInstallations: () => Promise<void>;
    hasActiveInstallation: boolean;
    hasActiveGithubInstallation: boolean;
    hasActiveAzureDevOpsInstallation: boolean;
    overallStatus: string;
    githubStatus: string;
    adoStatus: string;
}

const GitInstallationsContext = createContext<
    GitInstallationsContextType | undefined
>(undefined);

export function GitInstallationsProvider({
    children,
}: {
    children: React.ReactNode;
}) {
    const [installations, setInstallations] = useState<
        GithubInstallationStatus[]
    >([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const checkGitInstallations = useCallback(async () => {
        setIsLoading(true);
        setError(null);

        try {
            const response = await getGithubInstallationStatus();
            if (response.results && Array.isArray(response.results)) {
                setInstallations(
                    response.results as GithubInstallationStatus[],
                );
            } else {
                setInstallations([]);
            }
        } catch (err) {
            console.error('Failed to fetch git installations:', err);
            setError(
                err instanceof Error
                    ? err.message
                    : 'Failed to fetch git installations',
            );
            setInstallations([]);
        } finally {
            setIsLoading(false);
        }
    }, []);

    // Load data on mount
    useEffect(() => {
        checkGitInstallations();
    }, [checkGitInstallations]);

    // Check if any installation is active
    const hasActiveInstallation = installations.some(
        installation => installation.status === 'ACTIVE',
    );

    // Check if GitHub installation is active
    const hasActiveGithubInstallation = installations.some(
        installation =>
            installation.svc_type === 'GITHUB' &&
            installation.status === 'ACTIVE',
    );

    // Check if Azure DevOps installation is active
    const hasActiveAzureDevOpsInstallation = installations.some(
        installation =>
            installation.svc_type === 'AZURE_DEVOPS' &&
            installation.status === 'ACTIVE',
    );

    // Get overall status
    const overallStatus = hasActiveInstallation ? 'ACTIVE' : 'INACTIVE';

    // Get GitHub status
    const githubStatus =
        installations.find(installation => installation.svc_type === 'GITHUB')
            ?.status || '404';

    // Get Azure DevOps status
    const adoStatus =
        installations.find(
            installation => installation.svc_type === 'AZURE_DEVOPS',
        )?.status || '404';

    const value = {
        installations,
        isLoading,
        error,
        checkGitInstallations,
        hasActiveInstallation,
        hasActiveGithubInstallation,
        hasActiveAzureDevOpsInstallation,
        overallStatus,
        githubStatus,
        adoStatus,
    };

    return (
        <GitInstallationsContext.Provider value={value}>
            {children}
        </GitInstallationsContext.Provider>
    );
}

export function useGitInstallationsContext() {
    const context = useContext(GitInstallationsContext);
    if (context === undefined) {
        throw new Error(
            'useGitInstallationsContext must be used within a GitInstallationsProvider',
        );
    }
    return context;
}
