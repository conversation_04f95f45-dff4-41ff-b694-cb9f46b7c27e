import {
    createContext,
    ReactNode,
    useCallback,
    useContext,
    useEffect,
    useState,
} from 'react';
import {fetchUsage} from '@/lib/backend';
import {Usage} from '@/lib/entity-types';

interface UsageContextType {
    usage?: Usage;
    refreshUsage?: () => Promise<void>;
}

const UsageContext = createContext<UsageContextType>({});

export const useUsageContext = (): UsageContextType => {
    return useContext(UsageContext);
};

export const UsageProvider = ({children}: {children: ReactNode}) => {
    const [usage, setUsage] = useState<Usage>();

    const refreshUsage = useCallback(async () => {
        try {
            const usage = await fetchUsage();
            setUsage(usage);
        } catch (error) {
            console.error('Error fetching usage data:', error);
        }
    }, []);

    useEffect(() => {
        // TODO: Uncomment this when we have a way to fetch usage
        // refreshUsage();
    }, [refreshUsage]);

    return (
        <UsageContext.Provider value={{usage, refreshUsage}}>
            {children}
        </UsageContext.Provider>
    );
};
