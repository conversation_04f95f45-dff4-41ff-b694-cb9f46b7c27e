import {
    createContext,
    ReactN<PERSON>,
    useCallback,
    useContext,
    useEffect,
    useState,
} from 'react';
import {
    signInWithPopup,
    signInWithEmailAndPassword,
    signOut,
    User,
    GoogleAuthProvider,
    getAuth,
    OAuthProvider,
} from 'firebase/auth';
import {FirebaseError} from 'firebase/app';
import {auth} from '@/firebase';
import {
    emailVerificationStatus,
    refreshAPIAcessToken,
    retrieveUserProfile,
    updateUserProfile,
} from '@/lib/backend';
import {UserProfile} from '@/lib/entity-types';
import {useAlertContext} from './alert-context';
import {isValidUserProfile} from '@/lib/utils';
import {logGAEvent} from '@/lib/utils';

interface AuthContextType {
    userProfile?: UserProfile;
    isEmailVerified?: boolean;
    isUserProfileLoading?: boolean;
    isAuthInitialized?: boolean;
    checkEmailVerificationStatus?: () => Promise<boolean>;
    apiAccessToken?: string;
    googleLogin?: () => void;
    microsoftLogin?: () => Promise<void>;
    login?: (
        email: string,
        password: string,
    ) => Promise<{errorMsg: string} | {user_id: string}>;
    logout?: () => void;
    changeUserProfile?: (newProfile: UserProfile) => Promise<void>;
    refreshUserProfile?: (options?: {
        refreshInBackground?: boolean;
    }) => Promise<UserProfile | undefined>;
}

const googleProvider = new GoogleAuthProvider();

// Create the Microsoft auth provider using OAuthProvider
const microsoftProvider = new OAuthProvider('microsoft.com');

// Configure additional scopes or parameters if needed
microsoftProvider.setCustomParameters({
    //   tenant: "YOUR-TENANT-ID" // Optional: for specific tenant
});

const AuthContext = createContext<AuthContextType>({});

export const useAuthContext = (): AuthContextType => {
    return useContext(AuthContext);
};

function cachedEmailVerified(): {
    cacheKey?: string;
    emailVerified: true | null;
} {
    const uid = getAuth().currentUser?.uid;
    if (uid) {
        const cacheKey = `${uid}:email_verified`;
        const saved = localStorage.getItem(cacheKey);
        return {cacheKey, emailVerified: saved === 'true' ? true : null};
    } else {
        return {emailVerified: null};
    }
}

// Helper function to get current provider ID
function getCurrentProviderId(): string | undefined {
    const user = getAuth().currentUser;
    return user?.providerData[0]?.providerId;
}

export const AuthProvider = ({children}: {children: ReactNode}) => {
    const {showAlert} = useAlertContext();
    const [userProfile, setUserProfile] = useState<UserProfile | undefined>();
    const [isEmailVerified, setEmailVerified] = useState<boolean>(true);
    const [isUserProfileLoading, setIsUserProfileLoading] =
        useState<boolean>(true);
    const [isAuthInitialized, setIsAuthInitialized] = useState<boolean>(false);

    const refreshUserProfile = useCallback(
        async (options?: {refreshInBackground?: boolean}) => {
            const {refreshInBackground = false} = options ?? {};
            try {
                if (!refreshInBackground) {
                    setIsUserProfileLoading(true);
                }
                const userProfile = await retrieveUserProfile();
                if (userProfile && isValidUserProfile(userProfile)) {
                    // Always ensure providerId is set from current Firebase auth
                    const providerId = getCurrentProviderId();
                    const updatedProfile = {
                        ...userProfile,
                        providerId: providerId || userProfile.providerId,
                    };
                    setUserProfile(updatedProfile);
                    return updatedProfile;
                }
            } catch (error) {
                console.error(error);
                showAlert?.({
                    type: 'HttpError',
                    message: `Failed to retrieve user profile`,
                });
            } finally {
                if (!refreshInBackground) {
                    setIsUserProfileLoading(false);
                }
            }
        },
        [showAlert],
    );

    const checkEmailVerificationStatus =
        useCallback(async (): Promise<boolean> => {
            const {cacheKey, emailVerified: savedEmailVerified} =
                cachedEmailVerified();
            const emailVerified =
                savedEmailVerified === null
                    ? await emailVerificationStatus()
                    : savedEmailVerified;
            if (cacheKey) {
                if (emailVerified) {
                    if (savedEmailVerified === null)
                        localStorage.setItem(cacheKey, 'true');
                } else {
                    if (savedEmailVerified !== null)
                        localStorage.removeItem(cacheKey);
                }
            }
            setEmailVerified(emailVerified);
            return emailVerified;
        }, []);

    useEffect(() => {
        const unsubscribe = auth.onAuthStateChanged(
            async (user: User | null) => {
                if (user) {
                    // Keep loading state true during the entire auth process
                    setIsUserProfileLoading(true);
                    setIsAuthInitialized(false);

                    try {
                        await refreshAPIAcessToken(user);
                        await checkEmailVerificationStatus();
                        await refreshUserProfile();
                    } catch (error) {
                        console.error(error);
                    } finally {
                        // Only set initialized to true after everything is complete
                        setIsAuthInitialized(true);
                        setIsUserProfileLoading(false);
                    }
                } else {
                    setUserProfile(undefined);
                    setIsUserProfileLoading(false);
                    setIsAuthInitialized(true); // Auth is initialized even without user
                }
            },
        );

        return () => unsubscribe(); // Cleanup subscription
    }, [checkEmailVerificationStatus, refreshUserProfile, showAlert]);

    const login = useCallback(
        async (
            email: string,
            password: string,
        ): Promise<{errorMsg: string} | {user_id: string}> => {
            try {
                const response = await signInWithEmailAndPassword(
                    auth,
                    email,
                    password,
                );
                window.dataLayer = window.dataLayer || [];
                window.dataLayer.push({
                    user_id: response.user.uid,
                });
                logGAEvent('login_success', {
                    email: email,
                    user_id: response.user.uid,
                });
                return {user_id: response.user.uid};
            } catch (error) {
                if (error instanceof FirebaseError) {
                    const e = error as FirebaseError;
                    console.error(e.code, e.message);
                    if (e.code === 'auth/invalid-credential') {
                        return {errorMsg: 'Invalid email or password'};
                    } else {
                        return {errorMsg: e.message};
                    }
                } else if (error instanceof Error) {
                    const e = error as Error;
                    console.error(e.message);
                    return {errorMsg: e.message};
                }

                return {errorMsg: 'unknown error'};
            }
        },
        [],
    );

    const logout = useCallback(async () => {
        await signOut(auth);
        setUserProfile(undefined);
    }, []);

    const googleLogin = useCallback(async () => {
        try {
            await signInWithPopup(auth, googleProvider);
        } catch (error) {
            console.error(error);
            throw error;
        }
    }, []);

    const microsoftLogin = useCallback(async () => {
        try {
            const result = await signInWithPopup(auth, microsoftProvider);

            // This gives you a Microsoft Access Token.
            const credential = OAuthProvider.credentialFromResult(result);
            if (credential) {
                // Microsoft signed in successfully
            }
        } catch (error) {
            console.error(error);
            throw error;
        }
    }, []);

    const changeUserProfile = useCallback(
        async (newProfile: UserProfile) => {
            // Preserve the providerId when updating profile
            const providerId = getCurrentProviderId();
            const updatedProfile = {
                ...newProfile,
                providerId: providerId || userProfile?.providerId,
            };
            await updateUserProfile(updatedProfile);
            setUserProfile(updatedProfile);
        },
        [userProfile],
    );

    return (
        <AuthContext.Provider
            value={{
                login,
                logout,
                googleLogin,
                microsoftLogin,
                userProfile,
                isEmailVerified,
                isUserProfileLoading,
                isAuthInitialized,
                checkEmailVerificationStatus,
                changeUserProfile,
                refreshUserProfile,
            }}>
            {children}
        </AuthContext.Provider>
    );
};
