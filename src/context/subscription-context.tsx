import {
    createContext,
    ReactNode,
    useCallback,
    useContext,
    useEffect,
    useState,
} from 'react';
import {useSearchParams} from 'react-router-dom';
import dayjs from 'dayjs';
import {SubscriptionPlan} from '@/lib/entity-types';
import {
    fetchUserSubscriptionStatus,
    getStripeManageSubscriptionLink,
    getStripePaymentLink,
} from '@/lib/backend';
import {checkSubscriptionStatus} from '@/lib/utils';

interface SubscriptionContextType {
    plan?: SubscriptionPlan;
    setPlan?: (plan: SubscriptionPlan) => void;
    refreshSubscription?: () => Promise<void>;
    isExpired?: boolean;
    isStarted?: boolean;
    trialRemainingInterval?: number;
    trialEndDate?: string;
    subscriptionStartDate?: string;
    subscriptionEndDate?: string;
    subscriptionStatus?: string;
    isTrialing?: boolean;
    gotoPaymentLink?: (projectId?: string) => Promise<void>;
    goToManageSubscriptionLink?: () => Promise<void>;
}

const SubscriptionContext = createContext<SubscriptionContextType>({});

export const useSubscriptionContext = (): SubscriptionContextType => {
    return useContext(SubscriptionContext);
};

export const SubscriptionProvider = ({
    userId,
    children,
}: {
    userId: string | undefined;
    children: ReactNode;
}) => {
    const [plan, setPlan] = useState<SubscriptionPlan>();
    const [isExpired, setIsExpired] = useState<boolean>(false);
    const [isStarted, setIsStarted] = useState<boolean>(false);
    const [trialRemainingInterval, setTrialRemainingInterval] =
        useState<number>(0);
    const [trialEndDate, setTrialEndDate] = useState<string>('');
    const [subscriptionStartDate, setSubscriptionStartDate] =
        useState<string>('');
    const [subscriptionEndDate, setSubscriptionEndDate] = useState<string>('');
    const [subscriptionStatus, setSubscriptionStatus] = useState<string>('');
    const [isTrialing, setIsTrialing] = useState<boolean>(false);
    const [searchParams] = useSearchParams();
    const sessionId = searchParams.get('session_id') || undefined;

    const refreshSubscription = useCallback(async () => {
        const subscription = await fetchUserSubscriptionStatus();
        const {
            plan,
            isExpired,
            isStarted,
            trialRemainingInterval,
            trialEndDate: fetchedTrialEndDate,
            subscriptionStartDate: fetchedSubscriptionStartDate,
            subscriptionEndDate: fetchedSubscriptionEndDate,
            status,
            isTrialing,
        } = checkSubscriptionStatus(subscription);

        setPlan(plan);
        setIsExpired(isExpired);
        setIsStarted(isStarted);
        setTrialRemainingInterval(trialRemainingInterval);
        setSubscriptionStatus(status ?? '');
        setIsTrialing(isTrialing);

        // Format the trialEndDate to "Month day, Year" format using dayjs
        const formattedTrialEndDate = fetchedTrialEndDate
            ? dayjs(fetchedTrialEndDate * 1000).format('MMMM D, YYYY')
            : '';

        setTrialEndDate(formattedTrialEndDate);

        // Format the subscriptionStartDate to "Month day, Year" format using dayjs
        const formattedSubscriptionStartDate = fetchedSubscriptionStartDate
            ? dayjs(fetchedSubscriptionStartDate * 1000).format('MMMM D, YYYY')
            : '';

        // Format the subscriptionEndDate to "Month day, Year" format using dayjs
        const formattedSubscriptionEndDate = fetchedSubscriptionEndDate
            ? dayjs(fetchedSubscriptionEndDate * 1000).format('MMMM D, YYYY')
            : '';

        setSubscriptionStartDate(formattedSubscriptionStartDate);
        setSubscriptionEndDate(formattedSubscriptionEndDate);
    }, []);

    useEffect(() => {
        if (userId) {
            refreshSubscription();

            // Start polling if session_id is present
            if (sessionId) {
                let pollCount = 0;
                const maxPolls = 3; // 10 attempts * 2 seconds = 20 seconds total

                const pollInterval = setInterval(async () => {
                    await refreshSubscription();
                    pollCount++;

                    if (pollCount >= maxPolls) {
                        clearInterval(pollInterval);
                    }
                }, 10000);

                return () => clearInterval(pollInterval);
            }
        }
    }, [refreshSubscription, sessionId, userId]);

    const gotoPaymentLink = useCallback(async (projectId?: string) => {
        // const paymentLink = `${import.meta.env.VITE_STRIPE_PAYMENT_LINK}?prefilled_email=${encodeURIComponent(userProfile.email)}&client_reference_id=${encodeURIComponent(userProfile?.id)}`;
        const paymentLink = await getStripePaymentLink(projectId);
        if (paymentLink) {
            window.location.assign(paymentLink.url);
        }
    }, []);

    const goToManageSubscriptionLink = useCallback(async () => {
        const manageSubscriptionLink = await getStripeManageSubscriptionLink();
        if (manageSubscriptionLink) {
            window.location.assign(manageSubscriptionLink.url);
        }
    }, []);

    return (
        <SubscriptionContext.Provider
            value={{
                plan,
                refreshSubscription,
                isExpired,
                isStarted,
                trialRemainingInterval,
                gotoPaymentLink,
                trialEndDate,
                subscriptionStartDate,
                subscriptionEndDate,
                subscriptionStatus,
                isTrialing,
                goToManageSubscriptionLink,
            }}>
            {children}
        </SubscriptionContext.Provider>
    );
};
