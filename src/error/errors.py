from flask_utils.base_error import BaseError


class FlaskFileNotFound(BaseError):
    """Flask file not found error."""

    def __init__(self, message: str = None, status_code: int = 400):
        super().__init__(message=message, status_code=status_code)


class TooManyAttemptsError(BaseError):
    """Raised when too many attempts are made."""

    def __init__(self, message: str = "Too many attempts", status_code: int = 429):
        super().__init__(message=message, status_code=status_code)


class ResourceNotFound(BaseError):
    """Raised when a resource is not found."""

    def __init__(self, message: str, status_code: int = 404):
        super().__init__(message=message, status_code=status_code)


class InvalidRequest(BaseError):
    """Raised when a request is invalid."""

    def __init__(self, message: str, status_code: int = 400):
        super().__init__(message=message, status_code=status_code)


class ConflictingResourceError(BaseError):
    """Raised when a resource is already present."""

    def __init__(self, message: str, status_code: int = 409):
        super().__init__(message=message, status_code=status_code)
