import {createContext, useContext, useState} from 'react';
import {Navigate, Outlet} from 'react-router-dom';
import logo from '/images/Logo.svg';
import purpleBlob1 from '/images/purple-blob1.png';
import gradientBlob from '/images/gradient.png';
import {useAuthContext} from './context/auth-context';
import AuthenticatingUserModal from './modal/authenticating-user-modal';

interface HomeContextType {
    signUpEmail?: string;
    setSignUpEmail?: (email: string) => void;
}

const HomeContext = createContext<HomeContextType>({});

export const useHomeContext = (): HomeContextType => {
    return useContext(HomeContext);
};

export function Home() {
    const [signUpEmail, setSignUpEmail] = useState<string | undefined>();
    const {isUserProfileLoading, userProfile, isEmailVerified} =
        useAuthContext();
    if (
        userProfile?.id &&
        isEmailVerified &&
        userProfile.registrationCompleted
    ) {
        return <Navigate to="/workspace" replace />;
    }

    return (
        <HomeContext.Provider value={{signUpEmail, setSignUpEmail}}>
            <div className="relative h-dvh w-screen overflow-hidden">
                <div className="flex absolute w-full h-full top-1/2 -translate-y-1/2 m-0 px-3 py-3 flex-row items-center sm:gap-[88px]">
                    <div className="flex-1 basis-1/2 h-full relative hidden sm:block rounded-[24px] px-6 sm:px-[105px] py-8 sm:py-[132px] bg-[#010101] overflow-hidden">
                        {/* Purple Blob at top */}
                        <img
                            src={purpleBlob1}
                            className="absolute top-0 left-0 w-full object-cover"
                            alt="purple-blob"
                        />
                        {/* Gradient Blob at bottom */}
                        <img
                            src={gradientBlob}
                            className="absolute bottom-0 left-0 w-full object-cover"
                            alt="gradient-blob"
                        />

                        <div className="relative w-full max-w-[463px] flex flex-col justify-center h-full gap-[100px] xl:gap-[200px] text-white text-left">
                            <a href="/" className="cursor-pointer">
                                <img
                                    src={logo}
                                    className="w-[100px] sm:w-[150px] h-auto sm:h-[56px]"
                                    alt="logo"
                                />
                            </a>
                            <div className="text-[32px] sm:text-[42px] lg:text-[62px]  leading-[85%] tracking-[-2.511px] font-normal">
                                Build Enterprise
                                <br />
                                Software in{' '}
                                <div className="inline-block">
                                    <span className="bg-gradient-to-r from-[#5B39F3] via-[#94FAD5] to-[#07FF97] text-transparent bg-clip-text">
                                        Days,
                                    </span>
                                </div>
                                <br />
                                Not Months
                            </div>
                            <div className="text-base sm:text-xl lg:text-[29.96px] leading-[1.4em] tracking-[-0.28px]">
                                Blitzy.com
                            </div>
                        </div>
                    </div>
                    <div className="flex-1 basis-1/2 flex justify-center">
                        <div className="max-w-[720px] w-full max-h-dvh sm:max-h-none px-4 sm:px-6 lg:px-[76px] py-8 sm:py-12 lg:py-[310px] overflow-scroll sm:overflow-auto">
                            <Outlet />
                        </div>
                    </div>
                </div>
            </div>
            {isUserProfileLoading && <AuthenticatingUserModal />}
        </HomeContext.Provider>
    );
}
