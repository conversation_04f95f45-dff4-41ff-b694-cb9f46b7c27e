import {useCallback, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {CheckBox} from '../../modal/icons/checkbox';
import {UnCheckBox} from '../../modal/icons/un-checkbox';
import {LayerPlus} from '../../modal/icons/layer-plus';
import {CodeAlt} from '../../modal/icons/code-alt';
import {logGAEvent} from '@/lib/utils';
import {ProjectType} from '@/lib/entity-types';

export function ProjectTypePanel() {
    const navigateTo = useNavigate();
    const [projectType, setProjectType] = useState<null | ProjectType>(null);

    const handleNewProject = useCallback(
        (projectType: ProjectType) => {
            navigateTo(
                {
                    pathname: `/workspace/project-name/${projectType}`,
                },
                {replace: true},
            );
        },
        [navigateTo],
    );

    const handleCreateProject = useCallback(() => {
        if (projectType !== null) {
            logGAEvent('create_new_project', {projectType});
            handleNewProject(projectType);
        }
    }, [projectType, handleNewProject]);

    return (
        <div className="max-w-[1440px] mx-auto px-[24px] py-[16px] sm:p-[32px]">
            <div className="w-full flex flex-col gap-12 h-full">
                <div className="flex flex-col items-start">
                    <div className="text-[#000] font-inter text-3xl font-semibold leading-[130%] my-[48px]">
                        Create a Project
                    </div>
                    <div className="flex flex-col gap-2 mb-4">
                        <div className="text-[#333] font-inter text-lg font-semibold leading-7">
                            What kind of project are you working on?
                        </div>
                        <div className="text-[#333] font-inter text-sm font-normal leading-[150%]">
                            Select the best fit.
                        </div>
                    </div>
                    <div className="flex flex-col gap-6 w-full">
                        <div
                            className={`p-6 border w-full ${
                                projectType === 'NEW_PRODUCT'
                                    ? 'border-brand-purple bg-[#F2F0FE]'
                                    : 'border-[#999]'
                            } rounded-3xl flex justify-normal items-start gap-4 cursor-pointer`}
                            onClick={() => setProjectType('NEW_PRODUCT')}>
                            <div
                                className={`p-3 bg-[#F2F0FE] ${
                                    projectType === 'NEW_PRODUCT' && 'bg-white'
                                } rounded-2xl`}>
                                <LayerPlus />
                            </div>
                            <div className="flex-grow">
                                <span className="mb-2 px-2 py-1 font-semibold bg-secondary text-secondary-foreground rounded-full inline-block">
                                    Free
                                </span>
                                <span className="mb-2 text-lg font-semibold block">
                                    New product
                                </span>
                                <span className="block text-sm">
                                    Create a brand new software solution
                                    tailored to your business needs.
                                </span>
                            </div>
                            {projectType === 'NEW_PRODUCT' ? (
                                <CheckBox />
                            ) : (
                                <UnCheckBox />
                            )}
                        </div>
                        <div
                            className={`p-6 border w-full ${
                                projectType === 'EXISTING_PRODUCT'
                                    ? 'border-brand-purple bg-[#F2F0FE]'
                                    : 'border-[#999]'
                            } rounded-3xl flex justify-normal items-start gap-4 cursor-pointer`}
                            onClick={() => setProjectType('EXISTING_PRODUCT')}>
                            <div
                                className={`p-3 bg-[#F2F0FE] ${
                                    projectType === 'EXISTING_PRODUCT' &&
                                    'bg-white'
                                } rounded-2xl`}>
                                <CodeAlt />
                            </div>
                            <div className="flex-grow">
                                <span className="mb-2 px-2 py-1 font-semibold bg-brand-purple text-primary-foreground rounded-full inline-block">
                                    Enterprise
                                </span>
                                <span className="mb-2 text-lg font-semibold block">
                                    Existing product
                                </span>
                                <span className="block text-sm">
                                    Add features to your existing products or
                                    refactor your codebase.
                                </span>
                            </div>
                            {projectType === 'EXISTING_PRODUCT' ? (
                                <CheckBox />
                            ) : (
                                <UnCheckBox />
                            )}
                        </div>
                    </div>
                </div>
                <div className="self-end align-bottom my-[48px] bottom-0">
                    <button
                        role="button"
                        className="primary-button"
                        onClick={handleCreateProject}>
                        Create Project
                    </button>
                </div>
            </div>
        </div>
    );
}
