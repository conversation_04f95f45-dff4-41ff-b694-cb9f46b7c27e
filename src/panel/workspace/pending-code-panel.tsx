import {Navigate} from 'react-router-dom';
import <PERSON><PERSON> from 'lottie-react';
import {useProjectContext} from '@/context/project-context';
import animationData from '@/assets/Orb-Loader-textfree.json';
import {ProjectPanelContent} from './project-panel-content.tsx';
// import {ProjectHeader} from './header/project-header.tsx';

export function PendingCodePanel() {
    const {project, projectState} = useProjectContext();

    if (!project?.id) return null;

    if (projectState?.codeGeneration?.status === 'DONE') {
        return (
            <Navigate replace to={`/workspace/project/${project.id}/status`} />
        );
    }

    // if (isFetchingProject === true) return <FullScreenLoading />;

    return (
        <ProjectPanelContent>
            <div className="flex items-center h-full w-full justify-center">
                <div className="relative sm:w-[450px] sm:h-[450px]">
                    <div className="font-bold  absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-10 text-text-tertiary text-center max-w-[280px]">
                        <span className="font-extrabold text-[16px] ">
                            Deep in thought...
                        </span>{' '}
                        <br />
                        <br />
                        System 2 AI building your codebase. Check back in ~24h
                        for your repo.
                    </div>
                    <Lottie
                        animationData={animationData}
                        loop={true}
                        className="max-w-full max-h-full"
                    />
                </div>
            </div>
        </ProjectPanelContent>
    );
}
