import {useCallback, useEffect, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import dayjs from 'dayjs';
import {fetchProjects} from '@/lib/backend';
import {ProjectWithStatus} from '@/lib/entity-types';
import iconClock from '/images/clock.svg';
import {projectLastUpdatedAt} from '@/lib/utils';
import {ProjectPanelContent} from './project-panel-content';
import {ProjectListHeader} from './header/project-list-header';
import iconEmptyFolder from '/images/empty-state-folder.svg';
import {useAlertContext} from '@/context/alert-context';
import GitHubAlert from '@/components/custom/github-alert';
import {useGitInstallationsContext} from '@/context/git-installations-context';
import FullScreenLoading from '@/components/custom/full-screen-loading';
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from '@/components/ui/tooltip';

export function ProjectsPanel() {
    const {installations, checkGitInstallations} = useGitInstallationsContext();

    const [isFetchingProjects, setIsFetchingProjects] = useState(true);
    const [projects, setProjects] = useState<ProjectWithStatus[]>([]);

    const {showAlert} = useAlertContext();
    // const location = useLocation();
    // const searchParams = new URLSearchParams(location.search);

    const navigateTo = useNavigate();

    const retrieveProjects = useCallback(async () => {
        try {
            setIsFetchingProjects(true);
            const projs = (await fetchProjects()).reverse();
            // setProjectsJobStatus(
            //     await Promise.all(
            //         projs.map(project => fetchProjectJobStatus(project.id)),
            //     ),
            // );
            projs.sort((a, b) => b.updatedAt - a.updatedAt);
            setProjects(projs);
            // Check Git installations status when retrieving projects
            await checkGitInstallations();
        } catch (error) {
            console.error(error);
            if (error instanceof Error) {
                showAlert?.({
                    type: 'HttpError',
                    message: `Failed to retrieve projects: ${error.message}`,
                });
            }
        } finally {
            setIsFetchingProjects(false);
        }
    }, [showAlert, checkGitInstallations]);

    useEffect(() => {
        retrieveProjects();
    }, [retrieveProjects]);

    const handleProjectClick = useCallback(
        (projectId: string) => () => {
            navigateTo(`/workspace/project/${projectId}/status`, {
                replace: false,
            });
        },
        [navigateTo],
    );

    if (isFetchingProjects)
        return <FullScreenLoading message="Retrieving projects..." />;

    // Check if any installation is pending or uninstalled
    const hasPendingInstallation = installations.some(
        installation => installation.status === 'PENDING',
    );
    const hasUninstalledInstallation = installations.some(
        installation => installation.status === 'UNINSTALLED',
    );

    const shouldShowGitAlert =
        hasPendingInstallation || hasUninstalledInstallation;
    const gitAlertText = hasPendingInstallation
        ? 'Git platform connection awaiting admin approval. Refresh status to check for updates.'
        : 'Git platform connection unsuccessful. Please try connecting again to generate code.';

    return (
        <ProjectPanelContent header={<ProjectListHeader />}>
            {shouldShowGitAlert && (
                <div className="mb-[16px] sm:mb-[32px]">
                    <GitHubAlert
                        onViewStatus={() =>
                            navigateTo('/workspace/settings/integrations')
                        }
                        text={gitAlertText}
                    />
                </div>
            )}
            {projects.length > 0 ? (
                <div className="mx-[0px] my-[0px] sm:mx-0 flex flex-col items-start sm:flex-row sm:flex-wrap gap-[40px] pb-[60px] sm:pb-[100px]">
                    {projects.map((project, index) => (
                        <div
                            role="button"
                            onClick={handleProjectClick(project.id)}
                            key={`project-${index}`}
                            className="w-full sm:w-[428px] flex flex-col gap-[24px]">
                            <div className="project-tile-image relative rounded-[24px] w-full h-[276px] flex justify-center items-center overflow-hidden">
                                <img
                                    className={`absolute top-0 left-0 w-full h-full object-cover ${project?.status !== 'DONE' && 'opacity-50'}`}
                                    src="/images/project-card-image.png"
                                    alt="project card image"
                                />
                                {project?.status !== 'DONE' && (
                                    <img
                                        src={iconClock}
                                        className="w-[48px] h-[48px] z-10"
                                        alt="clock"
                                    />
                                )}
                            </div>
                            <div className="w-full flex flex-row">
                                <div className="w-full flex flex-col gap-[8px] items-start">
                                    <TooltipProvider delayDuration={100}>
                                        <Tooltip>
                                            <TooltipTrigger asChild>
                                                <div className="max-w-full text-[#333] text-[20px] font-bold leading-[1.2em] tracking-[-0.5px] truncate">
                                                    {project.name}
                                                </div>
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p className="text-[12px]">
                                                    {project.name}
                                                </p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                    <div className="text-[#33333380] text-[14px] font-bold leading-[1.2em] tracking-[-0.5px]">
                                        Last modified{' '}
                                        {dayjs
                                            .unix(projectLastUpdatedAt(project))
                                            .fromNow()}
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            ) : (
                <div className="flex flex-col items-center justify-center text-center h-full pb-[64px]">
                    <img
                        src={iconEmptyFolder}
                        alt="empty folder icon"
                        className="w-[100px] h-[100px] mb-4"
                    />
                    <div className="text-[var(--text-text-secondary,#333)] text-center font-inter text-[24px] font-semibold leading-[130%]">
                        No projects
                    </div>
                    <div className="text-[var(--text-text-tertiary,#999)] text-center font-inter text-[16px] font-normal leading-[150%] tracking-[-0.3px] mt-2">
                        You have not created a project yet. Tap the button above
                        to create one.
                    </div>
                </div>
            )}
        </ProjectPanelContent>
    );
}
