import {useMemo} from 'react';
import {useLocation} from 'react-router-dom';
import {ProjectState, ProjectType, PromptType} from '@/lib/entity-types';
import CheckCircle from '../icons/check-circle';
import LoaderIcon from '../icons/LoaderIcon';
import GitPullRequest from '../icons/git-pull-request';
import InfoCircle from '../icons/info-circle';
import BoxIcon from '../icons/box.svg';
import Edit from '../icons/edit';
import CodeCurlyAdd from '../icons/code-curly-add';
import Task from '../icons/task';
import Revision from '../icons/revision';
import Wrench from '../icons/wrench';
import Bug from '../icons/bug';
import CheckShield from '../icons/check-shield';
import CommentAdd from '../icons/comment-add';
import {AiSparkle} from '@/modal/icons/ai-sparkle';

interface IProps {
    readonly isLegacyProject: boolean;
    readonly projectState?: ProjectState;
    readonly projectType: ProjectType;
    readonly promptType?: PromptType;
    readonly isEditing?: boolean;
}

type StatusType =
    | 'IN_PROGRESS'
    | 'NEW_PRODUCT'
    | 'DONE'
    | 'FAILED'
    | 'REVIEW'
    | 'EDITING'
    | 'LEGACY'
    | 'ADD_FEATURE'
    | 'ADD_TESTING'
    | 'REFACTOR_CODE'
    | 'CUSTOM'
    | 'FIX_BUGS'
    | 'FIX_CVES'
    | 'DOCUMENT_CODE'
    | null;

const STATUS_CONFIG = {
    IN_PROGRESS: {
        className: 'bg-[#F0D8FC] text-[#B23AF2]',
        icon: ({color = '#B23AF2', width = 14, height = 14}) => (
            <div className="animate-spin">
                <LoaderIcon color={color} width={width} height={height} />
            </div>
        ),
    },
    NEW_PRODUCT: {
        className: 'bg-[#F2F0FE] text-[#5B39F3]',
        icon: ({fill = '#5B39F3', width = 14, height = 14}) => (
            <AiSparkle fill={fill} width={width} height={height} />
        ),
    },
    EDITING: {
        className: 'bg-[#F2F0FE] text-[#5B39F3]',
        icon: ({color = '#5B39F3', width = 14, height = 14}) => (
            <Edit color={color} width={width} height={height} />
        ),
    },
    ADD_FEATURE: {
        className: 'bg-[#F2F0FE] text-[#5B39F3]',
        icon: ({color = '#5B39F3', width = 14, height = 14}) => (
            <CodeCurlyAdd color={color} width={width} height={height} />
        ),
    },
    ADD_TESTING: {
        className: 'bg-[#F2F0FE] text-[#5B39F3]',
        icon: ({color = '#5B39F3', width = 14, height = 14}) => (
            <Task color={color} width={width} height={height} />
        ),
    },
    REFACTOR_CODE: {
        className: 'bg-[#F2F0FE] text-[#5B39F3]',
        icon: ({color = '#5B39F3', width = 14, height = 14}) => (
            <Revision color={color} width={width} height={height} />
        ),
    },
    CUSTOM: {
        className: 'bg-[#F2F0FE] text-[#5B39F3]',
        icon: ({color = '#5B39F3', width = 14, height = 14}) => (
            <Wrench color={color} width={width} height={height} />
        ),
    },
    FIX_BUGS: {
        className: 'bg-[#F2F0FE] text-[#5B39F3]',
        icon: ({color = '#5B39F3', width = 14, height = 14}) => (
            <Bug color={color} width={width} height={height} />
        ),
    },
    FIX_CVES: {
        className: 'bg-[#F2F0FE] text-[#5B39F3]',
        icon: ({color = '#5B39F3', width = 14, height = 14}) => (
            <CheckShield color={color} width={width} height={height} />
        ),
    },
    DOCUMENT_CODE: {
        className: 'bg-[#F2F0FE] text-[#5B39F3]',
        icon: ({color = '#5B39F3', width = 14, height = 14}) => (
            <CommentAdd color={color} width={width} height={height} />
        ),
    },
    DONE: {
        className: 'bg-[#C9FCEA] text-[#005335]',
        icon: ({color = '#005335', width = 14, height = 14}) => (
            <CheckCircle color={color} width={width} height={height} />
        ),
    },
    REVIEW: {
        className: 'bg-[#F2F0FE] text-[#5B39F3]',
        icon: ({color = '#5B39F3', width = 14, height = 14}) => (
            <GitPullRequest color={color} width={width} height={height} />
        ),
    },
    FAILED: {
        className: 'bg-[#FFDFDF] text-[#991010]',
        icon: ({color = '#991010', width = 14, height = 14}) => (
            <InfoCircle color={color} width={width} height={height} />
        ),
    },
    LEGACY: {
        className: 'bg-[#F5F5F5] text-[#333]',
        icon: () => <img src={BoxIcon} />,
    },
} as const;

const TechSpecJobTypeLabel = {
    NEW_PRODUCT: 'New product',
    ADD_FEATURE: 'Add feature',
    REFACTOR_CODE: 'Refactor',
    FIX_BUGS: 'Fix bugs',
    FIX_CVES: 'Fix security',
    ADD_TESTING: 'Add testing',
    DOCUMENT_CODE: 'Document code',
    CUSTOM: 'Custom',
} as const;

const CodeGeneratingJobTypeLabel = {
    NEW_PRODUCT: 'Building new product',
    ADD_FEATURE: 'Adding Feature',
    REFACTOR_CODE: 'Refactoring codebase',
    FIX_BUGS: 'Fixing bugs',
    FIX_CVES: 'Fixing security',
    ADD_TESTING: 'Adding Testing',
    DOCUMENT_CODE: 'Documenting code',
    CUSTOM: 'Generating code',
} as const;

const PRReadyJobTypeLabel = {
    NEW_PRODUCT: 'New product PR ready',
    ADD_FEATURE: 'Feature PR ready',
    REFACTOR_CODE: 'Refactor PR ready',
    FIX_BUGS: 'Bug fix PR ready',
    FIX_CVES: 'Security PR ready',
    ADD_TESTING: 'Testing PR ready',
    DOCUMENT_CODE: 'Documentation PR ready',
    CUSTOM: 'Custom PR ready',
} as const;

const CodeReadyJobTypeLabel = {
    NEW_PRODUCT: 'New product ready',
    ADD_FEATURE: 'Feature added',
    REFACTOR_CODE: 'Codebase refactored',
    FIX_BUGS: 'Bugs fixed',
    FIX_CVES: 'Security fixed',
    ADD_TESTING: 'Testing added',
    DOCUMENT_CODE: 'Code documented',
    CUSTOM: 'Code generated',
} as const;

const BuildButtonJobTypes = [
    'ADD_FEATURE',
    'REFACTOR_CODE',
    'FIX_BUGS',
    'FIX_CVES',
    'ADD_TESTING',
    'DOCUMENT_CODE',
    'CUSTOM',
    'NEW_PRODUCT',
];

const BASE_STATUS_CLASSES =
    'inline-flex text-[14px] px-[12px] py-[4px] items-center gap-[4px] rounded-full font-semibold h-fit';

export function CommandStatus({
    projectState,
    // projectType,
    promptType,
    isEditing,
    isLegacyProject,
}: IProps) {
    const location = useLocation();
    const {technicalSpec, codeGeneration, isTechSpecInSync} =
        projectState ?? {};
    const {status: techSpecStatus, jobType: techSpecType} = technicalSpec ?? {};
    const {status: codeStatus, commitStatus} = codeGeneration ?? {};

    const status = useMemo((): {text: string; type: StatusType} => {
        if (isLegacyProject) {
            return {text: 'Legacy mode', type: 'LEGACY'};
        } else if (isEditing) {
            return {text: 'Editing tech spec', type: 'EDITING'};
        } else if (!isTechSpecInSync && techSpecStatus === 'DONE') {
            return {text: 'Tech spec out-of-sync', type: 'FAILED'};
        } else if (promptType === 'ADD_FEATURE') {
            return {text: 'Add feature', type: 'ADD_FEATURE'};
        } else if (
            promptType === 'NEW_PRODUCT' ||
            location.pathname.includes('/back-prop/new-product')
        ) {
            return {text: 'New product', type: 'NEW_PRODUCT'};
        } else if (promptType === 'ADD_TESTING') {
            return {text: 'Add testing', type: 'ADD_TESTING'};
        } else if (
            promptType === 'REFACTOR_CODE' ||
            location.pathname.includes('/back-prop/refactor-code')
        ) {
            return {text: 'Refactor', type: 'REFACTOR_CODE'};
        } else if (
            promptType === 'CUSTOM' ||
            location.pathname.includes('/back-prop/custom')
        ) {
            return {text: 'Custom', type: 'CUSTOM'};
        } else if (promptType === 'FIX_BUGS') {
            return {text: 'Fix bugs', type: 'FIX_BUGS'};
        } else if (promptType === 'FIX_CVES') {
            return {text: 'Fix security', type: 'FIX_CVES'};
        } else if (promptType === 'DOCUMENT_CODE') {
            return {text: 'Document code', type: 'DOCUMENT_CODE'};
        } else if (
            techSpecStatus === 'QUEUED' ||
            techSpecStatus === 'IN_PROGRESS'
        ) {
            if (techSpecType === 'NEW_PRODUCT') {
                return {
                    text: 'New product • Building tech spec',
                    type: 'IN_PROGRESS',
                };
            } else if (
                techSpecType &&
                BuildButtonJobTypes.includes(techSpecType)
            ) {
                const text = techSpecType
                    ? `${TechSpecJobTypeLabel[techSpecType ?? '']} • Updating tech spec`
                    : 'Updating tech spec';
                return {text, type: 'IN_PROGRESS'};
            } else if (techSpecType === 'SYNC_TECH_SPEC') {
                return {text: 'Resyncing tech spec', type: 'IN_PROGRESS'};
            } else {
                return {text: 'Building tech spec', type: 'IN_PROGRESS'};
            }
        } else if (
            techSpecStatus === 'DONE' &&
            techSpecType === 'SYNC_TECH_SPEC'
        ) {
            return {text: 'Tech spec up-to-date', type: 'DONE'};
        } else if (
            techSpecStatus === 'DONE' &&
            (codeStatus === undefined || codeStatus === 'TODO')
        ) {
            if (techSpecType === 'NEW_PRODUCT') {
                return {
                    text: 'New product • Tech spec ready',
                    type: 'DONE',
                };
            } else if (
                techSpecType &&
                BuildButtonJobTypes.includes(techSpecType)
            ) {
                const text = techSpecType
                    ? `${TechSpecJobTypeLabel[techSpecType ?? '']} • Tech spec updated`
                    : 'Tech spec updated';
                return {
                    text,
                    type: 'DONE',
                };
            } else {
                return {
                    text: 'Tech spec ready',
                    type: 'DONE',
                };
            }
        } else if (codeStatus === 'DONE' && commitStatus === 'MERGED') {
            if (techSpecType && BuildButtonJobTypes.includes(techSpecType)) {
                const text = techSpecType
                    ? CodeReadyJobTypeLabel[techSpecType]
                    : 'Codebase ready';
                return {text, type: 'DONE'};
            } else {
                return {text: 'Codebase ready', type: 'DONE'};
            }
        } else if (codeStatus === 'DONE' && commitStatus === 'CLOSED') {
            return {text: 'Tech spec ready', type: 'DONE'};
        } else if (['SUBMITTED', 'DONE'].includes(techSpecStatus ?? '')) {
            if (codeStatus === 'QUEUED' || codeStatus === 'IN_PROGRESS') {
                if (
                    techSpecType &&
                    BuildButtonJobTypes.includes(techSpecType)
                ) {
                    const text = techSpecType
                        ? CodeGeneratingJobTypeLabel[techSpecType]
                        : 'Building codebase';
                    return {text, type: 'IN_PROGRESS'};
                } else {
                    return {text: 'Building codebase', type: 'IN_PROGRESS'};
                }
            } else if (codeStatus === 'DONE') {
                if (
                    techSpecType &&
                    BuildButtonJobTypes.includes(techSpecType)
                ) {
                    const text = techSpecType
                        ? PRReadyJobTypeLabel[techSpecType]
                        : 'PR ready';
                    return {text, type: 'REVIEW'};
                } else {
                    return {text: 'PR ready', type: 'REVIEW'};
                }
            }
        }

        return {text: '', type: null};
    }, [
        isTechSpecInSync,
        techSpecStatus,
        techSpecType,
        codeStatus,
        commitStatus,
        isEditing,
        isLegacyProject,
        promptType,
        location.pathname,
    ]);

    if (!isLegacyProject) {
        if (
            !(
                location.pathname.includes('/tech-spec') ||
                location.pathname.includes('/prompt') ||
                location.pathname.includes('/back-prop/refactor-code') ||
                location.pathname.includes('/back-prop/custom') ||
                location.pathname.includes('/back-prop/new-product')
            )
        )
            return null;
        if (
            location.pathname.includes('/tech-spec') &&
            techSpecStatus === 'TODO'
        )
            return null;
        if (!status.text) return null;
        if (promptType === 'ONBOARD_CODE') return null;
    }

    const config = status.type ? STATUS_CONFIG[status.type] : null;
    const Icon = config?.icon;

    return (
        <div
            className={`min-w-max ${BASE_STATUS_CLASSES} ${config?.className || ''}`}>
            {Icon && <Icon />}
            {status.text}
        </div>
    );
}
