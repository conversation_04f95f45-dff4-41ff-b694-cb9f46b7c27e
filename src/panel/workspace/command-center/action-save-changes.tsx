import {ProjectState} from '@/lib/entity-types';
import {useCallback, useState} from 'react';
import SmallLoader from '../icons/small-loader';
import {logGAEvent} from '@/lib/utils';
import {useProjectContext} from '@/context/project-context';
interface IProps {
    readonly jobStatus: ProjectState | undefined;
    readonly isEditing?: boolean;
    readonly onSaveEdits?: () => void;
    readonly isContentDirty?: boolean;
    readonly projectId: string;
}

function shouldHide(
    jobStatus: ProjectState | undefined,
    isEditing: boolean = false,
) {
    if (!jobStatus?.technicalSpec?.status) return true;
    // Show only when both techStatus is DONE and isEditing is true
    return !(jobStatus.technicalSpec.status === 'DONE' && isEditing);
}

export function ActionSaveChanges({
    jobStatus,
    onSaveEdits,
    isEditing,
    isContentDirty,
    projectId,
}: IProps) {
    const [isSaving, setIsSaving] = useState(false);
    const {refreshProjectState} = useProjectContext();
    const handleSaveEdits = useCallback(async () => {
        logGAEvent('save_and_exit_clicked', {
            event_category: 'Project',
            event_label: jobStatus?.technicalSpec?.jobType || 'unknown',
            tech_spec_status: jobStatus?.technicalSpec?.status,
            project_id: projectId,
        });
        if (!onSaveEdits) return;
        setIsSaving(true);
        try {
            await onSaveEdits();
            refreshProjectState?.();
        } finally {
            setIsSaving(false);
        }
    }, [onSaveEdits, jobStatus, projectId, refreshProjectState]);

    if (shouldHide(jobStatus, isEditing)) return null;

    return (
        <div
            role="button"
            onClick={handleSaveEdits}
            className={`primary-button w-[136px] flex justify-center items-center gap-1 ${isSaving && 'pointer-events-none'} ${!isContentDirty && 'opacity-50 pointer-events-none'}`}>
            {isSaving && (
                <div className="w-[22px] h-[22px] animate-spin">
                    <SmallLoader height={22} width={22} />
                </div>
            )}
            {isSaving ? 'Saving...' : 'Save & Exit'}
        </div>
    );
}
