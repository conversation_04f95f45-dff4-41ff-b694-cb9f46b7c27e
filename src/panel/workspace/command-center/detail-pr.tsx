import {ProjectState, ProjectType} from '@/lib/entity-types';
import {useProjectContext} from '@/context/project-context';
import {useCommitStatus} from '../hooks/commit-status-hook';
import './detail-human-tasks.css';

interface IProps {
    projectState: ProjectState | undefined;
    projectType: ProjectType | undefined;
}

function shouldHide(projectState: ProjectState | undefined) {
    const isCodeGenerated = projectState?.codeGeneration?.status === 'DONE';
    return (
        !location.pathname.includes('tech-spec') ||
        !(
            isCodeGenerated &&
            ['PENDING', 'MERGED'].includes(
                projectState?.codeGeneration?.commitStatus ?? '',
            )
        )
    );
}

export function DetailPR({projectState, projectType}: IProps) {
    const {project} = useProjectContext();
    const {status: commitStatus} = useCommitStatus(
        project?.id ?? '',
        projectState?.codeGeneration?.id ?? '',
    );

    if (!projectType || !commitStatus || shouldHide(projectState)) return null;

    return (
        <div className="flex items-center gap-[12px]">
            <div>
                <h3 className="text-[16px] font-semibold">
                    {commitStatus.status === 'MERGED'
                        ? 'Blitzy AI Commit'
                        : 'Blitzy AI PR'}
                </h3>
                <a
                    className="text-[16px]"
                    href={
                        commitStatus.status === 'MERGED'
                            ? commitStatus.blitzyCommitUrl
                            : commitStatus.prLink
                    }
                    target="_blank">
                    {commitStatus.blitzyCommitHash?.substring(0, 5)} ↗
                </a>
            </div>
        </div>
    );
}
