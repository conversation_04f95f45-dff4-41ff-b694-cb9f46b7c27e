import {useCallback} from 'react';
import {useLocation, useNavigate} from 'react-router-dom';
import {ProjectState, ProjectType} from '@/lib/entity-types';
import File from '../icons/file';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import {LoadingSubmitButton} from '@/components/custom/loading-submit-button';
import SmallLoader from '../icons/small-loader';
import {useProjectContext} from '@/context/project-context';
import {logGAEvent} from '@/lib/utils';
interface IProps {
    projectId: string;
    projectState?: ProjectState;
    jobStatus: ProjectState | undefined;
    projectType: ProjectType;
    isPromptValid?: boolean;
    isLegacyProject: boolean;
    isReadOnly?: boolean;
    handleSubmit?: () => Promise<boolean>;
    confirmContinue?: boolean;
    setContinueConfirm?: (value: boolean) => void;
}

function shouldHide(pathname: string) {
    if (pathname?.includes('prompt')) {
        return false;
    } else {
        return true;
    }
}

export function ActionWriteTechSpec({
    projectId,
    projectType,
    projectState: {technicalSpec} = {},
    isLegacyProject,
    isPromptValid,
    isReadOnly,
    handleSubmit,
    confirmContinue,
    setContinueConfirm,
}: IProps) {
    const navigateTo = useNavigate();
    const {pathname} = useLocation();
    const {isPromptSubmitting, promptType} = useProjectContext();

    const handleSubmitPrompt = useCallback(async () => {
        logGAEvent('build_tech_spec_confirm_clicked', {
            event_category: 'Project',
            event_label: projectType,
            project_id: projectId,
            is_legacy_project: isLegacyProject,
        });
        let success: boolean | undefined = false;
        try {
            success = await handleSubmit?.();
            if (success) {
                navigateTo(`/workspace/project/${projectId}/tech-spec`, {
                    replace: true,
                });
                return;
            }
        } catch (error) {
            console.error('Error submitting prompt:', error);
        }

        if (!success) {
            setContinueConfirm?.(false);
        }
    }, [
        handleSubmit,
        navigateTo,
        projectId,
        setContinueConfirm,
        projectType,
        isLegacyProject,
    ]);

    const handleButtonPress = useCallback(() => {
        logGAEvent('build_tech_spec_button_clicked', {
            event_category: 'Project',
            event_label: projectType,
            project_id: projectId,
            action:
                projectType === 'NEW_PRODUCT' || !technicalSpec
                    ? 'build'
                    : 'update',
            is_legacy_project: isLegacyProject,
        });
        if (isLegacyProject) {
            handleSubmitPrompt();
        } else {
            setContinueConfirm?.(true);
        }
    }, [
        projectType,
        projectId,
        technicalSpec,
        isLegacyProject,
        handleSubmitPrompt,
        setContinueConfirm,
    ]);

    if (shouldHide(pathname)) return null;

    return (
        <div className="flex-none">
            <LoadingSubmitButton
                loading={isPromptSubmitting}
                loadingText={
                    promptType === 'REFACTOR_CODE' ||
                    promptType === 'ADD_FEATURE' ||
                    promptType === 'CUSTOM' ||
                    promptType === 'FIX_BUGS' ||
                    promptType === 'FIX_CVES' ||
                    promptType === 'ADD_TESTING' ||
                    promptType === 'DOCUMENT_CODE' ||
                    technicalSpec
                        ? 'Update tech spec'
                        : 'Build tech spec'
                }
                role="button"
                onClick={handleButtonPress}
                className={`primary-button flex justify-center items-center gap-[8px]`}>
                <span className="flex justify-center items-center gap-[8px]">
                    <File width={20} height={19} color="white" />
                    <div>
                        {promptType === 'REFACTOR_CODE' ||
                        promptType === 'ADD_FEATURE' ||
                        promptType === 'CUSTOM' ||
                        promptType === 'FIX_BUGS' ||
                        promptType === 'FIX_CVES' ||
                        promptType === 'ADD_TESTING' ||
                        promptType === 'DOCUMENT_CODE' ||
                        technicalSpec
                            ? 'Update'
                            : 'Build'}{' '}
                        tech spec
                    </div>
                </span>
            </LoadingSubmitButton>
            {!isReadOnly && (
                <Dialog
                    open={confirmContinue}
                    onOpenChange={open => {
                        if (!open) {
                            logGAEvent('build_tech_spec_cancel_clicked', {
                                event_category: 'Project',
                                event_label: projectType,
                                project_id: projectId,
                                is_legacy_project: isLegacyProject,
                            });
                        }
                    }}>
                    <DialogContent
                        className="min-h-dvh sm:min-h-0 w-full  sm:max-w-md sm:pt-[1.5rem] [&>button]:hidden"
                        onInteractOutside={event => {
                            // Prevent closing on outside click
                            event.preventDefault();
                        }}>
                        <DialogHeader className="flex-grow">
                            <DialogTitle>
                                {isPromptValid
                                    ? 'Approve prompt'
                                    : 'Prompt too short'}
                            </DialogTitle>
                            <DialogDescription>
                                {isPromptValid
                                    ? 'Given prompt will be saved and you will not be able to change the prompt after approving.'
                                    : 'Your prompt must be at least 100 non-space characters - Please add more detail.'}
                            </DialogDescription>
                        </DialogHeader>
                        <DialogFooter className="gap-[16px] sm:gap-0">
                            {!isPromptSubmitting && (
                                <div
                                    role="button"
                                    className={`secondary-button`}
                                    onClick={() => {
                                        logGAEvent(
                                            'build_tech_spec_cancel_clicked',
                                            {
                                                event_category: 'Project',
                                                event_label: projectType,
                                                project_id: projectId,
                                                is_legacy_project:
                                                    isLegacyProject,
                                            },
                                        );
                                        setContinueConfirm?.(false);
                                    }}>
                                    {isPromptValid ? 'Cancel' : 'Got it'}
                                </div>
                            )}
                            {isPromptValid && (
                                // <div
                                //     role="button"
                                //     className="primary-button"
                                //     onClick={handleSubmitPrompt}>
                                //     Continue
                                // </div>
                                <div
                                    role="button"
                                    onClick={handleSubmitPrompt}
                                    className={`primary-button ${isPromptSubmitting && 'flex gap-[8px] justify-center items-center pointer-events-none'}`}>
                                    {isPromptSubmitting && (
                                        <div className="w-[22px] h-[22px] animate-spin">
                                            <SmallLoader
                                                height={22}
                                                width={22}
                                            />
                                        </div>
                                    )}
                                    {isPromptSubmitting
                                        ? 'Submitting...'
                                        : 'Continue'}
                                </div>
                            )}
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            )}
        </div>
    );
}
