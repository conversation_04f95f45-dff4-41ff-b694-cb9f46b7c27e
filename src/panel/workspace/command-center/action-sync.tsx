import {useCallback, useState} from 'react';
import Refresh from '../icons/refresh';
import {useProjectContext} from '@/context/project-context';
import {useLocation} from 'react-router-dom';
import {toast} from '@/hooks/use-toast';
import {logGAEvent} from '@/lib/utils';
import {useAlertContext} from '@/context/alert-context';
import {
    Tooltip,
    TooltipTrigger,
    TooltipContent,
    TooltipProvider,
} from '@/components/ui/tooltip';

interface ActionSyncProps {
    isMobile?: boolean;
}

export function ActionSync({isMobile}: ActionSyncProps) {
    const {project, projectState, refreshProjectState, fetchIsTechSpecInSync} =
        useProjectContext();
    const location = useLocation();
    const {showAlert} = useAlertContext();
    const [isLoading, setIsLoading] = useState(false);

    const handleTechSpecSyncStatus = useCallback(async () => {
        logGAEvent('check_sync_status_clicked', {
            event_category: 'Project',
            event_label: projectState?.technicalSpec?.jobType || 'unknown',
            project_id: project?.id,
        });
        if (!project) return;

        try {
            setIsLoading(true);
            const isTechSpecInSync = await fetchIsTechSpecInSync?.();
            if (!isTechSpecInSync && refreshProjectState) {
                refreshProjectState();
            } else {
                toast({
                    variant: 'success',
                    title: 'Tech spec is in-sync with code!',
                });
            }
        } catch (err) {
            console.error('Failed to check tech spec sync status:', err);
            if (err instanceof Error) {
                showAlert?.({
                    type: 'HttpError',
                    message: `Failed to retrieve project state ${err.message}`,
                });
            }
        } finally {
            setIsLoading(false);
        }
    }, [
        project,
        projectState?.technicalSpec?.jobType,
        refreshProjectState,
        fetchIsTechSpecInSync,
        showAlert,
    ]);

    if (
        !location.pathname.includes('/tech-spec') ||
        (projectState?.technicalSpec?.jobType !== 'NEW_PRODUCT' &&
            ['QUEUED', 'IN_PROGRESS'].includes(
                projectState?.codeGeneration?.status ?? '',
            )) ||
        (projectState?.technicalSpec?.jobType === 'NEW_PRODUCT' &&
            projectState?.codeGeneration?.status !== 'DONE') ||
        projectState?.codeGeneration?.commitStatus === 'PENDING'
    )
        return null;

    return (
        <TooltipProvider delayDuration={100}>
            <Tooltip>
                <TooltipTrigger asChild>
                    <div className="flex-none">
                        <div
                            role="button"
                            className={`flex items-center justify-center gap-2 border hover:border-[#5B39F3] rounded-full transition-all ${
                                isMobile
                                    ? 'secondary-button'
                                    : 'h-[40px] w-[40px] tertiary-button p-2 border border-transparent'
                            } ${!isMobile && isLoading && 'animate-spin-reverse'}`}
                            onClick={
                                isLoading ? undefined : handleTechSpecSyncStatus
                            }>
                            <div
                                className={`${isMobile && isLoading ? 'animate-spin-reverse' : ''}`}>
                                <Refresh
                                    color="#5B39F3"
                                    width={24}
                                    height={24}
                                />
                            </div>
                            {isMobile && 'Check spec status'}
                        </div>
                    </div>
                </TooltipTrigger>
                <TooltipContent>
                    <p className="text-[12px]">
                        {isLoading
                            ? 'Checking spec sync status...'
                            : 'Check spec sync status'}
                    </p>
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>
    );
}
