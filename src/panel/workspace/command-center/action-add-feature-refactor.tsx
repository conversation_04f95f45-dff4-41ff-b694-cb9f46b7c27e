import {useCallback, useState} from 'react';
import {ProjectState} from '@/lib/entity-types';
import {useLocation, useNavigate} from 'react-router-dom';
import CodeCurlyAdd from '../icons/code-curly-add';
import Code from '../icons/code';
import {useProjectContext} from '@/context/project-context';
import {logGAEvent} from '@/lib/utils';
import {LoadingSubmitButton} from '@/components/custom/loading-submit-button';

interface IProps {
    readonly action: 'ADD_FEATURE' | 'REFACTOR_CODE';
    readonly projectId: string;
    readonly projectState: ProjectState | undefined;
    readonly isEditing: boolean;
}

function shouldHide(
    projectState: ProjectState | undefined,
    isEditing: boolean,
) {
    const {
        codeGeneration: {status: codeGenStatus, commitStatus} = {},
        technicalSpec: {jobType: techSpecType, status: techSpecStatus} = {},
        isTechSpecInSync,
    } = projectState ?? {};

    if (isEditing || !isTechSpecInSync) return true;

    if (techSpecType === 'NEW_PRODUCT') {
        return !(
            codeGenStatus === 'DONE' &&
            (commitStatus === 'MERGED' || commitStatus === 'CLOSED')
        );
    } else if (techSpecType === 'EXISTING_PRODUCT') {
        return !['SUBMITTED', 'DONE'].includes(techSpecStatus ?? '');
    } else if (techSpecType === 'SYNC_TECH_SPEC') {
        return !['SUBMITTED', 'DONE'].includes(techSpecStatus ?? '');
    } else {
        // 'ADD_FEATURE' | 'REFACTOR_CODE'
        return (
            !['SUBMITTED', 'DONE'].includes(techSpecStatus ?? '') ||
            commitStatus === undefined ||
            commitStatus === 'PENDING'
        );
    }
}

export const ActionAddFeatureRefactor = ({
    action,
    projectId,
    projectState,
    isEditing,
}: IProps) => {
    const [isLoading, setIsLoading] = useState(false);
    const navigateTo = useNavigate();
    const {pathname} = useLocation();
    const {fetchIsTechSpecInSync} = useProjectContext();

    const handleContinue = useCallback(async () => {
        setIsLoading(true);
        try {
            logGAEvent(`${action.toLowerCase()}_button_clicked`, {
                event_category: 'Project',
                event_label: projectState?.technicalSpec?.jobType || 'unknown',
                project_id: projectId,
                tech_spec_status: projectState?.technicalSpec?.status,
            });
            const isTechSpecInSync = await fetchIsTechSpecInSync?.();
            if (!isTechSpecInSync) {
                logGAEvent(`${action.toLowerCase()}_blocked_not_in_sync`, {
                    event_category: 'Project',
                    event_label:
                        projectState?.technicalSpec?.jobType || 'unknown',
                    project_id: projectId,
                    tech_spec_status: projectState?.technicalSpec?.status,
                });
                return;
            } else {
                if (projectId) {
                    navigateTo(
                        `/workspace/project/${projectId}/${action === 'ADD_FEATURE' ? 'prompt?action=ADD_FEATURE' : 'prompt?action=REFACTOR_CODE'}`,
                    );
                }
            }
        } finally {
            setIsLoading(false);
        }
    }, [navigateTo, projectId, action, fetchIsTechSpecInSync, projectState]);

    if (!pathname.includes('tech-spec') || shouldHide(projectState, isEditing))
        return null;

    return (
        <div className="flex-none">
            <LoadingSubmitButton
                onClick={handleContinue}
                loading={isLoading}
                loadingText={
                    action === 'ADD_FEATURE'
                        ? 'Add feature'
                        : 'Refactor codebase'
                }
                className={`${action === 'ADD_FEATURE' ? 'primary-button' : 'secondary-button'} flex justify-center items-center gap-[8px] `}>
                <span className="flex justify-center items-center gap-[8px]">
                    {action === 'ADD_FEATURE' ? <CodeCurlyAdd /> : <Code />}
                    <div>
                        {action === 'ADD_FEATURE'
                            ? 'Add feature'
                            : 'Refactor codebase'}
                    </div>
                </span>
            </LoadingSubmitButton>
        </div>
    );
};
