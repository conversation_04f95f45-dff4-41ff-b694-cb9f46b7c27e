import {useCallback, useState} from 'react';
import {useLocation, useNavigate} from 'react-router-dom';
import {ProjectState, ProjectType} from '@/lib/entity-types';
import Skip from '../icons/skip';
import {useProjectContext} from '@/context/project-context';
import {LoadingSubmitButton} from '@/components/custom/loading-submit-button';
interface IProps {
    readonly projectId: string;
    readonly projectState?: ProjectState;
    readonly projectType: ProjectType;
    disableNavigationBlocker?: () => void;
}

function shouldHide(
    pathname: string,
    search: string,
    projectType: ProjectType,
) {
    return !(
        projectType === 'EXISTING_PRODUCT' &&
        pathname?.includes('prompt') &&
        search?.includes('action=ONBOARD_CODE')
    );
}

// onboard code repo WITHOUT contextual prompt
export function ActionOnboardCode({
    projectId,
    projectState,
    projectType,
    disableNavigationBlocker,
}: IProps) {
    const navigateTo = useNavigate();
    const {pathname, search} = useLocation();
    const [isLoading, setIsLoading] = useState(false);
    const {updatePrompt, refreshProjectState} = useProjectContext();

    const handleClick = useCallback(async () => {
        setIsLoading(true);
        disableNavigationBlocker?.();
        if (updatePrompt) {
            await updatePrompt('', 'ONBOARD_CODE', false);
            refreshProjectState?.();
            navigateTo(`/workspace/project/${projectId}/tech-spec`, {
                replace: true,
            });
        }
        setIsLoading(false);
    }, [
        navigateTo,
        projectId,
        updatePrompt,
        refreshProjectState,
        disableNavigationBlocker,
    ]);

    if (!projectState || shouldHide(pathname, search, projectType)) return null;
    return (
        <LoadingSubmitButton
            role="button"
            onClick={handleClick}
            loading={isLoading}
            loadingText="Skip & build"
            className="secondary-button flex items-center justify-center gap-[8px]">
            <span className="flex items-center justify-center gap-[8px] whitespace-nowrap ">
                <Skip height={10} width={11} color="#5B39F3" />
                <div>Skip & build</div>
            </span>
        </LoadingSubmitButton>
    );
}
