import {ProjectState} from '@/lib/entity-types';
import iconGradientLoader from '/images/gradient-loader.png';

interface IProps {
    jobStatus: ProjectState | undefined;
}

function shouldHide(jobStatus: ProjectState | undefined) {
    const isTechSpecReady = ['SUBMITTED', 'DONE'].includes(
        jobStatus?.technicalSpec?.status ?? '',
    );
    const isCodeGenerating =
        jobStatus?.codeGeneration?.status === 'IN_PROGRESS' ||
        jobStatus?.codeGeneration?.status === 'QUEUED';

    return !(isTechSpecReady && isCodeGenerating);
}

export function DetailDeepInThought({jobStatus}: IProps) {
    if (shouldHide(jobStatus)) return null;

    return (
        <div className="flex flex-row items-start gap-[12px] sm:gap-[16px]">
            <div className="min-w-[52px] min-h-[52px] max-w-[52px] max-h-[52px] animate-spin">
                <img
                    src={iconGradientLoader}
                    alt="Gradient Loader"
                    className="w-full h-full object-contain"
                />
            </div>
            <div className="flex flex-col gap-[4px]">
                <div className="text-[16px] font-semibold">Deep in thought</div>
                <div className="text-[14px] sm:text-[16px]">
                    Our System 2 AI is meticulously crafting your code - Most
                    projects finish in 2-3 days, but large codebases may take up
                    to a week. Code is typically processed at ~5 minutes per
                    file.
                </div>
            </div>
        </div>
    );
}
