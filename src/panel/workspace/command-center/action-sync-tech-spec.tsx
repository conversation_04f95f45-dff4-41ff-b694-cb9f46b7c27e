import {useState} from 'react';
import {ProjectState} from '@/lib/entity-types';
import File from '../icons/file';
import {logGAEvent} from '@/lib/utils';
import {LoadingSubmitButton} from '@/components/custom/loading-submit-button';

interface IProps {
    readonly projectState?: ProjectState;
    handleSync: () => void;
    readonly projectId: string;
}

function shouldHide(projectState: ProjectState | undefined) {
    const {isTechSpecInSync, technicalSpec: {status} = {}} = projectState ?? {};
    return isTechSpecInSync || ['QUEUED', 'IN_PROGRESS'].includes(status ?? '');
}

export function ActionSyncTechSpec({
    handleSync,
    projectState,
    projectId,
}: IProps) {
    const [isLoading, setIsLoading] = useState(false);

    async function handleSyncClick() {
        if (isLoading) return;
        setIsLoading(true);
        try {
            logGAEvent('sync_tech_spec_clicked', {
                event_category: 'Project',
                event_label: projectState?.technicalSpec?.jobType || 'unknown',
                tech_spec_status: projectState?.technicalSpec?.status,
                is_tech_spec_in_sync: projectState?.isTechSpecInSync || false,
                project_id: projectId,
            });
            await handleSync();
        } finally {
            setIsLoading(false);
        }
    }
    if (!projectState || shouldHide(projectState)) return null;

    return (
        <div className="flex-none">
            <LoadingSubmitButton
                onClick={handleSyncClick}
                disabled={isLoading}
                loading={isLoading}
                loadingText="Sync tech spec"
                className="primary-button">
                <div className="flex items-center justify-center gap-[8px]">
                    <File color="white" height={20} width={20} />
                    <div>Sync tech spec</div>
                </div>
            </LoadingSubmitButton>
        </div>
    );
}
