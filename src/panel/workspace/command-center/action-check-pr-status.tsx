import {useCallback, useState} from 'react';
import Refresh from '../icons/refresh';
import {useProjectContext} from '@/context/project-context';
import {useLocation, useNavigate} from 'react-router-dom';
import {useAlertContext} from '@/context/alert-context';
import {
    Tooltip,
    TooltipTrigger,
    TooltipContent,
    TooltipProvider,
} from '@/components/ui/tooltip';
import {fetchPRStatus} from '@/lib/backend';
import {useToast} from '@/hooks/use-toast';

interface ActionCheckPRStatusProps {
    isMobile?: boolean;
}

export function ActionCheckPRStatus({isMobile}: ActionCheckPRStatusProps) {
    const {project, projectState, refreshProjectState} = useProjectContext();
    const location = useLocation();
    const {showAlert} = useAlertContext();
    const [isLoading, setIsLoading] = useState(false);
    const navigate = useNavigate();
    const {toast} = useToast();

    const handleCheckPRStatus = useCallback(async () => {
        if (!project || !projectState?.codeGeneration?.id) return;

        try {
            setIsLoading(true);
            const pullRequestStatus = await fetchPRStatus(
                project.id,
                projectState.codeGeneration.id,
            );
            if (pullRequestStatus === 'PENDING') {
                toast({
                    title: 'Pull request is in pending state.',
                    variant: 'default',
                });
                return;
            } else if (
                pullRequestStatus === 'MERGED' ||
                pullRequestStatus === 'CLOSED' ||
                pullRequestStatus === 'REJECTED'
            ) {
                await refreshProjectState?.({skipTechSpecSyncCheck: true});
                toast({
                    title: `Pull request was ${pullRequestStatus.toLowerCase()}.`,
                    variant: 'default',
                });
            }
            navigate(`/workspace/project/${project.id}/status`, {
                replace: true,
            });
        } catch (err) {
            console.error('Failed to check PR status:', err);
            if (err instanceof Error) {
                showAlert?.({
                    type: 'HttpError',
                    message: err.message,
                });
            }
        } finally {
            setIsLoading(false);
        }
    }, [
        project,
        projectState?.codeGeneration?.id,
        toast,
        showAlert,
        navigate,
        refreshProjectState,
    ]);

    if (
        !location.pathname.includes('/tech-spec') ||
        projectState?.codeGeneration?.commitStatus !== 'PENDING'
    )
        return null;

    return (
        <TooltipProvider delayDuration={100}>
            <Tooltip>
                <TooltipTrigger asChild>
                    <div className="flex-none">
                        <div
                            role="button"
                            className={`flex items-center justify-center gap-2 border hover:border-[#5B39F3] rounded-full transition-all ${
                                isMobile
                                    ? 'secondary-button'
                                    : 'h-[40px] w-[40px] tertiary-button p-2 border border-transparent'
                            } ${!isMobile && isLoading && 'animate-spin-reverse'}`}
                            onClick={
                                isLoading ? undefined : handleCheckPRStatus
                            }>
                            <div
                                className={`${isMobile && isLoading ? 'animate-spin-reverse' : ''}`}>
                                <Refresh
                                    color="#5B39F3"
                                    width={24}
                                    height={24}
                                />
                            </div>
                            {isMobile && 'Check PR status'}
                        </div>
                    </div>
                </TooltipTrigger>
                <TooltipContent>
                    <p className="text-[12px]">
                        {isLoading
                            ? 'Checking PR status...'
                            : 'Check PR status'}
                    </p>
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>
    );
}
