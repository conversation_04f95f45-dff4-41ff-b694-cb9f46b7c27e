import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ontent,
    <PERSON><PERSON><PERSON>eader,
    <PERSON><PERSON><PERSON><PERSON>le,
    DialogFooter,
    DialogDescription,
} from '@/components/ui/dialog';
import {XClose} from '@/modal/icons/x-close';
import {TechSpecJobType} from '@/lib/entity-types';

interface DiscardDialogProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
    techSpecType: TechSpecJobType;
}

export function DiscardDialog({
    isOpen,
    onClose,
    onConfirm,
    techSpecType,
}: DiscardDialogProps) {
    const getDialogContent = () => {
        switch (techSpecType) {
            case 'NEW_PRODUCT':
            case 'EXISTING_PRODUCT':
                return {
                    title: 'Discard technical spec',
                    buttonText: 'Discard tech spec',
                    description:
                        'This will discard the technical spec and return to prompting. This action cannot be undone.',
                };

            case 'ADD_FEATURE':
                return {
                    title: 'Discard feature and revert tech spec',
                    buttonText: 'Discard feature',
                    description:
                        'This will discard all proposed changes and revert the technical spec to its previous state. This action cannot be undone.',
                };
            case 'REFACTOR_CODE':
                return {
                    title: 'Cancel refactor and revert tech spec',
                    buttonText: 'Cancel refactor',
                    description:
                        'This will discard all proposed changes and revert the technical spec to its previous state. This action cannot be undone.',
                };
            case 'FIX_BUGS':
                return {
                    title: 'Discard bug fix and revert tech spec',
                    buttonText: 'Discard bug fix',
                    description:
                        'This will discard all proposed changes and revert the technical spec to its previous state. This action cannot be undone.',
                };
            case 'ADD_TESTING':
                return {
                    title: 'Discard testing and revert tech spec',
                    buttonText: 'Discard testing',
                    description:
                        'This will discard all proposed changes and revert the technical spec to its previous state. This action cannot be undone.',
                };
            case 'DOCUMENT_CODE':
                return {
                    title: 'Discard code documentation and revert tech spec',
                    buttonText: 'Discard code documentation',
                    description:
                        'This will discard all proposed changes and revert the technical spec to its previous state. This action cannot be undone.',
                };
            case 'FIX_CVES':
                return {
                    title: 'Discard security vulnerabilities and revert tech spec',
                    buttonText: 'Discard security vulnerabilities',
                    description:
                        'This will discard all proposed changes and revert the technical spec to its previous state. This action cannot be undone.',
                };
            case 'CUSTOM':
                return {
                    title: 'Discard custom task and revert tech spec',
                    buttonText: 'Discard custom task',
                    description:
                        'This will discard all proposed changes and revert the technical spec to its previous state. This action cannot be undone.',
                };
            default:
                return {
                    title: 'Discard product',
                    buttonText: 'Discard product',
                    description:
                        'This will permanently delete this project. This action cannot be undone.',
                };
        }
    };

    const dialogContent = getDialogContent();

    const handleConfirm = () => {
        onConfirm();
        onClose();
    };
    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="min-h-dvh sm:min-h-0 w-full p-[24px] gap-[48px] sm:max-w-[512px] sm:rounded-[24px] bg-white [&>button]:hidden">
                <DialogHeader className="flex-grow gap-[12px]">
                    <DialogTitle className="text-[24px] font-semibold">
                        {dialogContent.title}
                    </DialogTitle>
                    <DialogDescription className="text-[16px]">
                        {dialogContent.description}
                    </DialogDescription>
                </DialogHeader>
                <DialogFooter>
                    <div className="flex flex-col gap-[12px] w-full sm:justify-end sm:flex-row">
                        <div
                            role="button"
                            className="justify-center sm:justify-start secondary-button text-[#EC3636] border-[#EC3636] flex items-center gap-[8px] order-last sm:order-first"
                            onClick={handleConfirm}>
                            {<XClose width={20} height={19} fill="#EC3636" />}
                            {dialogContent.buttonText}
                        </div>
                        <div
                            role="button"
                            className="inline-flex font-semibold text-[16px] h-[40px] px-[20px] py-[8px] justify-center items-center gap-[8px] rounded-[32px] bg-[#F5F5F5] order-first sm:order-last"
                            onClick={onClose}>
                            <span className="text-semibold">Cancel</span>
                        </div>
                    </div>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
