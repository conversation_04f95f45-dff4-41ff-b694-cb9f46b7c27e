import {ProjectState} from '@/lib/entity-types';
import {useCallback} from 'react';
import {logGAEvent} from '@/lib/utils';
interface IProps {
    readonly jobStatus: ProjectState | undefined;
    readonly isEditing?: boolean;
    readonly onCancelEditing?: () => void;
    readonly projectId: string;
}

function shouldHide(
    jobStatus: ProjectState | undefined,
    isEditing: boolean = false,
) {
    if (!jobStatus?.technicalSpec?.status) return true;
    // Show only when techStatus is DONE and isEditing is true
    return jobStatus.technicalSpec.status !== 'DONE' || !isEditing;
}

export function ActionExitEditing({
    jobStatus,
    onCancelEditing,
    isEditing,
    projectId,
}: IProps) {
    const techStatus = jobStatus?.technicalSpec?.status;

    const handleCancelEditing = useCallback(() => {
        logGAEvent('exit_editing_without_save', {
            event_category: 'Project',
            event_label: jobStatus?.technicalSpec?.jobType || 'unknown',
            tech_spec_status: jobStatus?.technicalSpec?.status,
            project_id: projectId,
        });
        onCancelEditing?.();
    }, [onCancelEditing, jobStatus, projectId]);

    if (shouldHide(jobStatus, isEditing)) return null;

    return techStatus === 'DONE' ? (
        <div className="flex-none">
            <div
                role="button"
                className="secondary-button"
                onClick={handleCancelEditing}>
                Exit without saving
            </div>
        </div>
    ) : null;
}
