import iconGradientLoader from '/images/gradient-loader.png';
import {ProjectState} from '@/lib/entity-types';

interface IProps {
    jobStatus: ProjectState | undefined;
}

export function ThinkingLoader(props: IProps) {
    const techStatus = props.jobStatus?.technicalSpec?.status;
    const currentIndex =
        props.jobStatus?.technicalSpec?.jobMetadata?.currentIndex;

    return techStatus === 'IN_PROGRESS' &&
        currentIndex !== null &&
        currentIndex !== undefined ? (
        <div className="flex items-center justify-center">
            <div className="GradientLoader w-[40px] h-[40px] relative animate-spin">
                <img
                    src={iconGradientLoader}
                    alt="Gradient Loader"
                    className="w-full h-full object-contain"
                />
            </div>
            <div className=" sm:flex justify-center items-center gap-[8px] px-[8px] py-[8px] rounded-[32px] text-black text-[18px] font-bold">
                Thinking
            </div>
        </div>
    ) : null;
}
