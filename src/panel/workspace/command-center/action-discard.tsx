import {ProjectState} from '@/lib/entity-types';
import {XClose} from '@/modal/icons/x-close';
import {useState} from 'react';
import {DiscardDialog} from './discard-dialog';
import {PlatformError} from '@/lib/platform-error';
import {toast} from '@/hooks/use-toast';
import {revertTechSpec} from '@/lib/backend';
import {useProjectContext} from '@/context/project-context';
import {useNavigate} from 'react-router-dom';
interface ActionDiscardProps {
    projectId: string;
    projectState: ProjectState;
    isMobile?: boolean;
    setIsMenuOpen?: (isOpen: boolean) => void;
}

export function shouldHideActionDiscard(
    projectState: ProjectState | undefined,
) {
    const {codeGeneration, technicalSpec} = projectState ?? {};
    const {status: codeGenStatus} = codeGeneration ?? {};
    const {status: techSpecStatus, jobType: techSpecJobType} =
        technicalSpec ?? {};

    if (!location.pathname.includes('tech-spec')) {
        return true;
    }

    if (
        techSpecJobType === 'EXISTING_PRODUCT' ||
        techSpecJobType === 'SYNC_TECH_SPEC'
    ) {
        return true;
    } else {
        return !(
            techSpecStatus === 'DONE' &&
            (codeGenStatus === undefined ||
                codeGenStatus === null ||
                codeGenStatus === 'TODO')
        );
    }
}

export default function ActionDiscard({
    isMobile,
    projectId,
    projectState,
    setIsMenuOpen,
}: ActionDiscardProps) {
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const {refreshProject, refreshProjectState, project} = useProjectContext();
    const navigateTo = useNavigate();

    if (shouldHideActionDiscard(projectState)) {
        return null;
    }

    function getDiscardText(projectState: ProjectState | undefined) {
        switch (projectState?.technicalSpec?.jobType) {
            case 'NEW_PRODUCT':
                return 'Discard tech spec';
            case 'EXISTING_PRODUCT':
                return 'Discard tech spec';
            case 'ADD_FEATURE':
                return 'Discard feature';
            case 'REFACTOR_CODE':
                return 'Cancel refactor';
            case 'FIX_BUGS':
                return 'Discard bug fixes';
            case 'ADD_TESTING':
                return 'Discard testing';
            case 'DOCUMENT_CODE':
                return 'Discard code documentation';
            case 'FIX_CVES':
                return 'Discard security vulnerabilities';
            case 'CUSTOM':
                return 'Discard custom task';
            default:
                return 'Discard tech spec';
        }
    }

    const handleDiscard = () => {
        setIsDialogOpen(true);
    };

    const handleConfirmDiscard = async () => {
        setIsDialogOpen(false);
        try {
            await revertTechSpec(projectId);
            if (refreshProject) {
                const proj = await refreshProject();
                if (refreshProjectState) {
                    refreshProjectState({
                        project: proj,
                        skipTechSpecSyncCheck: true,
                    });
                }
            }
            if (project?.type === 'NEW_PRODUCT') {
                navigateTo(`/workspace/project/${projectId}/status`, {
                    replace: true,
                });
            }
            if (project?.type === 'NEW_PRODUCT') {
                toast({
                    title: 'Tech spec discarded',
                    description:
                        'The technical specification has been discarded',
                });
            } else {
                toast({
                    title: 'Tech spec discarded',
                    description:
                        'The technical specification has been reverted to its previous version.',
                });
            }
            setIsMenuOpen?.(false);
        } catch (error) {
            if (
                error instanceof PlatformError &&
                error.code === 'HttpStatus409'
            ) {
                toast({
                    title: 'Cannot discard',
                    description: 'There is no previous version to revert to.',
                    variant: 'destructive',
                });
            } else {
                toast({
                    title: 'Error',
                    description:
                        'Failed to discard the technical specification. Please try again.',
                    variant: 'destructive',
                });
            }
        }
    };
    return (
        <div className="flex-none">
            <div
                className={
                    isMobile
                        ? 'min-w-max secondary-button flex items-center gap-[8px] border border-[#EC3636]'
                        : 'px-[12px] py-[12px] flex gap-[8px] bg-[#FFF] hover:bg-[#F2F0FE] items-center cursor-pointer'
                }
                onClick={handleDiscard}>
                <XClose width={22} height={22} fill="#EC3636" />
                <span className="text-[16px] text-[#EC3636] font-semibold">
                    {getDiscardText(projectState)}
                </span>
            </div>

            <DiscardDialog
                isOpen={isDialogOpen}
                onClose={() => setIsDialogOpen(false)}
                onConfirm={handleConfirmDiscard}
                techSpecType={
                    projectState?.technicalSpec?.jobType ?? 'NEW_PRODUCT'
                }
            />
        </div>
    );
}
