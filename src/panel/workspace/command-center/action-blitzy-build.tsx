import {useRef, useState} from 'react';
import {useLocation, useNavigate} from 'react-router-dom';
import {Popover, PopoverContent, PopoverTrigger} from '@/components/ui/popover';
import {
    Dialog,
    DialogContent,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';
import {useToast} from '@/hooks/use-toast';
import {ProjectState, PromptType} from '@/lib/entity-types';
import BlitzyIcon from '../icons/blitzy-icon';
import ChevronUp from '../icons/chevron-up';
import Wrench from '../icons/wrench';
import CodeCurlyAdd from '../icons/code-curly-add';
import Revision from '../icons/revision';
import Bug from '../icons/bug';
import CheckShield from '../icons/check-shield';
import Task from '../icons/task';
import CommentAdd from '../icons/comment-add';
import {XClose} from '@/modal/icons/x-close';
import {useProjectContext} from '@/context/project-context';
import {Loader2} from 'lucide-react';

interface IProps {
    readonly projectId: string;
    readonly projectState: ProjectState | undefined;
    readonly isEditing: boolean;
}

type BuildType = Exclude<PromptType, 'NEW_PRODUCT' | 'ONBOARD_CODE'>;

function shouldHide(
    projectState: ProjectState | undefined,
    isEditing: boolean,
) {
    const {
        codeGeneration: {status: codeGenStatus, commitStatus} = {},
        technicalSpec: {jobType: techSpecType, status: techSpecStatus} = {},
        isTechSpecInSync,
    } = projectState ?? {};

    if (isEditing || !isTechSpecInSync) return true;

    if (techSpecType === 'NEW_PRODUCT') {
        return !(
            codeGenStatus === 'DONE' &&
            (commitStatus === 'MERGED' || commitStatus === 'CLOSED')
        );
    } else if (techSpecType === 'EXISTING_PRODUCT') {
        return !['SUBMITTED', 'DONE'].includes(techSpecStatus ?? '');
    } else if (techSpecType === 'SYNC_TECH_SPEC') {
        return !['SUBMITTED', 'DONE'].includes(techSpecStatus ?? '');
    } else {
        // techSpecType = 'ADD_FEATURE' | 'REFACTOR_CODE'
        return (
            !['SUBMITTED', 'DONE'].includes(techSpecStatus ?? '') ||
            commitStatus === undefined ||
            commitStatus === 'PENDING'
        );
    }
}

export default function ActionBlitzyBuild({
    projectId,
    projectState,
    isEditing,
}: IProps) {
    const [isOpen, setIsOpen] = useState(false);
    const [isOpenMobile, setIsOpenMobile] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [isAtTop, setIsAtTop] = useState(false);
    const panelRef = useRef<HTMLDivElement>(null);
    const {pathname} = useLocation();
    const navigate = useNavigate();
    const {fetchIsTechSpecInSync} = useProjectContext();
    const {toast} = useToast();

    const handleToggle = () => {
        setIsOpen(isOpen => !isOpen);
    };

    const handleToggleMobile = () => {
        setIsOpenMobile(isOpenMobile => !isOpenMobile);
        setIsAtTop(false);
    };

    const handleMobileDialogScroll = () => {
        if (panelRef.current) {
            const rect = panelRef.current.getBoundingClientRect();
            setIsAtTop(rect.top <= 0);
        }
    };

    const handleBuild = async (buildType: BuildType) => {
        if (isLoading) return;
        console.log('handle build with', buildType);
        try {
            setIsLoading(true);
            setIsOpen(false);
            setIsOpenMobile(false);
            const isTechSpecInSync = await fetchIsTechSpecInSync?.();
            if (!isTechSpecInSync) {
                return;
            } else {
                navigate(
                    `/workspace/project/${projectId}/prompt?action=${buildType}`,
                );
            }
        } catch (error) {
            console.error('Error checking tech spec in sync', error);
            const errorMessage =
                error instanceof Error
                    ? error.message
                    : 'Something went wrong while initiating the build';
            toast({
                title: errorMessage,
                variant: 'destructive',
            });
        } finally {
            setIsLoading(false);
        }
    };

    if (!pathname.includes('tech-spec') || shouldHide(projectState, isEditing))
        return null;

    return (
        <>
            {/* For non mobile screen */}
            <div className="hidden sm:block">
                <Popover open={isOpen} onOpenChange={setIsOpen}>
                    <PopoverTrigger asChild>
                        <div>
                            <BuildButton
                                isOpen={isOpen}
                                isLoading={isLoading}
                                handleToggle={handleToggle}
                            />
                        </div>
                    </PopoverTrigger>
                    <PopoverContent
                        sideOffset={7}
                        className="max-w-[100vw] sm:max-w-[926px] mr-[32px] p-[24px] border border-[#D9D9D9] rounded-[24px] shadow-md">
                        <BuildContent handleBuild={handleBuild} />
                    </PopoverContent>
                </Popover>
            </div>
            {/* For mobile screen */}
            <div className="block sm:hidden">
                <Dialog open={isOpenMobile} onOpenChange={setIsOpenMobile}>
                    <DialogTrigger asChild>
                        <div>
                            <BuildButton
                                isOpen={isOpenMobile}
                                isLoading={isLoading}
                                handleToggle={handleToggleMobile}
                            />
                        </div>
                    </DialogTrigger>
                    <DialogContent
                        className="w-full h-dvh overflow-scroll p-[0px] bg-transparent [&>button]:hidden"
                        onScroll={handleMobileDialogScroll}>
                        <DialogTitle className="sr-only">
                            Blitzy build options
                        </DialogTitle>
                        <div
                            ref={panelRef}
                            className={`mt-[194px] p-[24px] pt-0 bg-white flex flex-col gap-[0px] ${isAtTop ? '' : 'rounded-t-[24px]'}`}>
                            <div className="py-[24px] flex justify-between items-center sticky top-[0px] bg-white">
                                <h4 className="text-[16px] font-semibold leading-[150%]">
                                    What would you like to do?
                                </h4>
                                <div
                                    className="w-[24px] h-[24px] cursor-pointer"
                                    onClick={() => setIsOpenMobile(false)}>
                                    <XClose
                                        width={24}
                                        height={24}
                                        fill="#666"
                                    />
                                </div>
                            </div>
                            <BuildContent handleBuild={handleBuild} />
                        </div>
                    </DialogContent>
                </Dialog>
            </div>
        </>
    );
}

function BuildButton({
    isOpen,
    isLoading,
    handleToggle,
}: {
    isOpen: boolean;
    isLoading: boolean;
    handleToggle: () => void;
}) {
    return (
        <button
            className="primary-button w-fit flex items-center gap-[8px]"
            disabled={isLoading}
            onClick={handleToggle}>
            {isLoading ? (
                <Loader2 className="inline h-[20px] w-[20px] animate-spin" />
            ) : (
                <BlitzyIcon width={20} height={20} />
            )}
            <span>Build</span>
            <ChevronUp
                width={21}
                height={20}
                style={{
                    transform: isOpen ? 'rotate(-180deg)' : 'rotate(0deg)',
                    transition: 'transform 0.2s ease-in-out',
                }}
            />
        </button>
    );
}

function BuildContent({
    handleBuild,
}: {
    handleBuild: (buildType: BuildType) => void;
}) {
    return (
        <div className="space-y-[12px] sm:space-y-[24px]">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-[12px]">
                <div className="space-y-[12px]">
                    <h5 className="sm:mb-[24px] text-[12px] text-[#999] leading-[140%]">
                        Development & modernization
                    </h5>
                    <div
                        className="p-[12px] flex gap-[12px] rounded-[12px] hover:bg-[#F2F0FE] cursor-pointer"
                        onClick={() => handleBuild('ADD_FEATURE')}>
                        <div className="min-w-[24px] h-[24px] bg-[#F2F0FE] flex justify-center items-center rounded-[6px]">
                            <CodeCurlyAdd
                                width={16}
                                height={16}
                                color="#5B39F3"
                            />
                        </div>
                        <div className="space-y-[4px]">
                            <h6 className="text-[16px] font-semibold leading-[150%]">
                                Add feature
                            </h6>
                            <p className="text-[12px] leading-[140%]">
                                Build new functionality and expand your app's
                                capabilities
                            </p>
                        </div>
                    </div>
                    <div
                        className="p-[12px] flex gap-[12px] rounded-[12px] hover:bg-[#F2F0FE] cursor-pointer"
                        onClick={() => handleBuild('REFACTOR_CODE')}>
                        <div className="min-w-[24px] h-[24px] bg-[#F2F0FE] flex justify-center items-center rounded-[6px]">
                            <Revision width={16} height={16} color="#5B39F3" />
                        </div>
                        <div className="space-y-[4px]">
                            <h6 className="text-[16px] font-semibold leading-[150%]">
                                Refactor codebase
                            </h6>
                            <p className="text-[12px] leading-[140%]">
                                Upgrade version, migrate languages, or
                                restructure code base architecture
                            </p>
                        </div>
                    </div>
                </div>
                <div className="space-y-[12px]">
                    <h5 className="sm:mb-[24px] text-[12px] text-[#999] leading-[140%]">
                        Quality & maintainance
                    </h5>
                    <div
                        className="p-[12px] flex gap-[12px] rounded-[12px] hover:bg-[#F2F0FE] cursor-pointer"
                        onClick={() => handleBuild('FIX_BUGS')}>
                        <div className="min-w-[24px] h-[24px] bg-[#F2F0FE] flex justify-center items-center rounded-[6px]">
                            <Bug width={16} height={16} color="#5B39F3" />
                        </div>
                        <div className="space-y-[4px]">
                            <h6 className="text-[16px] font-semibold leading-[150%]">
                                Fix bugs
                            </h6>
                            <p className="text-[12px] leading-[140%]">
                                Identify and resolve errors, crashes, or
                                unexpected behavior
                            </p>
                        </div>
                    </div>
                    <div
                        className="p-[12px] flex gap-[12px] rounded-[12px] hover:bg-[#F2F0FE] cursor-pointer"
                        onClick={() => handleBuild('FIX_CVES')}>
                        <div className="min-w-[24px] h-[24px] bg-[#F2F0FE] flex justify-center items-center rounded-[6px]">
                            <CheckShield
                                width={16}
                                height={16}
                                color="#5B39F3"
                            />
                        </div>
                        <div className="space-y-[4px]">
                            <h6 className="text-[16px] font-semibold leading-[150%]">
                                Fix security vulnerabilities
                            </h6>
                            <p className="text-[12px] leading-[140%]">
                                Remediate CVEs and strengthen any security gaps
                            </p>
                        </div>
                    </div>
                </div>
                <div className="space-y-[12px]">
                    <h5 className="sm:mb-[24px] text-[12px] text-[#999] leading-[140%]">
                        Testing & documentation
                    </h5>
                    <div
                        className="p-[12px] flex gap-[12px] rounded-[12px] hover:bg-[#F2F0FE] cursor-pointer"
                        onClick={() => handleBuild('ADD_TESTING')}>
                        <div className="min-w-[24px] h-[24px] bg-[#F2F0FE] flex justify-center items-center rounded-[6px]">
                            <Task width={16} height={16} color="#5B39F3" />
                        </div>
                        <div className="space-y-[4px]">
                            <h6 className="text-[16px] font-semibold leading-[150%]">
                                Add testing
                            </h6>
                            <p className="text-[12px] leading-[140%]">
                                Create unit level tests and expand code coverage
                            </p>
                        </div>
                    </div>
                    <div
                        className="p-[12px] flex gap-[12px] rounded-[12px] hover:bg-[#F2F0FE] cursor-pointer"
                        onClick={() => handleBuild('DOCUMENT_CODE')}>
                        <div className="min-w-[24px] h-[24px] bg-[#F2F0FE] flex justify-center items-center rounded-[6px]">
                            <CommentAdd
                                width={16}
                                height={16}
                                color="#5B39F3"
                            />
                        </div>
                        <div className="space-y-[4px]">
                            <h6 className="text-[16px] font-semibold leading-[150%]">
                                Document code
                            </h6>
                            <p className="text-[12px] leading-[140%]">
                                Auto-comment code and create module level usage
                                readme guides
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div className="w-full h-[1px] bg-[#D9D9D9]"></div>
            <div
                className="p-[12px] flex gap-[12px] rounded-[12px] hover:bg-[#F2F0FE] cursor-pointer"
                onClick={() => handleBuild('CUSTOM')}>
                <div className="min-w-[24px] h-[24px] bg-[#F2F0FE] flex justify-center items-center rounded-[6px]">
                    <Wrench width={16} height={16} color="#5B39F3" />
                </div>
                <div className="space-y-[4px]">
                    <h6 className="text-[16px] font-semibold leading-[150%]">
                        Custom
                    </h6>
                    <p className="text-[12px] leading-[140%]">
                        Describe your specific development needs tailored to
                        your project
                    </p>
                </div>
            </div>
        </div>
    );
}
