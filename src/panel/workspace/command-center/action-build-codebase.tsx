import {useCallback, useMemo, useState} from 'react';
import {useLocation, useNavigate} from 'react-router-dom';
import {
    ProjectState,
    ProjectDocumentType,
    ProjectType,
} from '@/lib/entity-types';
import CodeCurlyAdd from '../icons/code-curly-add';
import Task from '../icons/task';
import Bug from '../icons/bug';
import CheckShield from '../icons/check-shield';
import CommentAdd from '../icons/comment-add';
import {useGitInstallationsContext} from '@/context/git-installations-context';
import {UpgradeModal} from '@/modal/upgrade-modal';
import {useSubscriptionContext} from '@/context/subscription-context';
import {LoadingSubmitButton} from '@/components/custom/loading-submit-button';
import {logGAEvent} from '@/lib/utils';
import {useAuthContext} from '@/context/auth-context';
import {useDocumentSubmission} from '@/context/document-submission-context';

interface IProps {
    readonly projectId: string;
    readonly projectState: ProjectState | undefined;
    readonly projectType: ProjectType;
    readonly isLegacyProject: boolean;
    readonly isEditing?: boolean;
    readonly docType?: ProjectDocumentType;
    readonly onApprove?: () => void;
    readonly isDocumentSubmitting?: boolean;
    readonly setIsDocumentSubmitting?: React.Dispatch<
        React.SetStateAction<boolean>
    >;
}

function shouldHide(
    projectType: ProjectType,
    projectState: ProjectState | undefined,
    isLegacyProject: boolean,
    isEditing: boolean = false,
) {
    if (!projectType) return true;
    if (isEditing) return true;
    if (isLegacyProject) return false;
    if (!projectState?.isTechSpecInSync) return true;

    const {technicalSpec, codeGeneration} = projectState ?? {};
    const isTechSpecDone = technicalSpec?.status === 'DONE';
    const isCodeGenerationTodo =
        codeGeneration?.status === undefined ||
        codeGeneration?.status === null ||
        codeGeneration?.status === 'TODO';

    if (technicalSpec?.jobType === 'NEW_PRODUCT') {
        if (isTechSpecDone && isCodeGenerationTodo) {
            return false;
        } else {
            return true;
        }
    } else if (technicalSpec?.jobType === 'EXISTING_PRODUCT') {
        return true;
    } else if (technicalSpec?.jobType === 'SYNC_TECH_SPEC') {
        return true;
    } else if (
        technicalSpec?.jobType === 'ADD_FEATURE' ||
        technicalSpec?.jobType === 'REFACTOR_CODE' ||
        technicalSpec?.jobType === 'CUSTOM' ||
        technicalSpec?.jobType === 'FIX_BUGS' ||
        technicalSpec?.jobType === 'FIX_CVES' ||
        technicalSpec?.jobType === 'ADD_TESTING' ||
        technicalSpec?.jobType === 'DOCUMENT_CODE'
    ) {
        if (
            technicalSpec?.jobType === 'REFACTOR_CODE' ||
            technicalSpec?.jobType === 'CUSTOM'
        ) {
            if (isTechSpecDone && isCodeGenerationTodo) {
                return false;
            } else {
                return true;
            }
        } else {
            if (isTechSpecDone && isCodeGenerationTodo) {
                return false;
            } else {
                return true;
            }
        }
    } else {
        return true;
    }
}

// for NEW_PRODUCT only
export function ActionBuildCodebase(props: IProps) {
    const {
        projectId,
        projectType,
        projectState,
        isEditing,
        isLegacyProject,
        docType,
        setIsDocumentSubmitting,
        onApprove,
        isDocumentSubmitting,
    } = props;

    const navigateTo = useNavigate();
    const location = useLocation();
    const {githubStatus, adoStatus, checkGitInstallations} =
        useGitInstallationsContext();
    const [showUpgradeModal, setShowUpgradeModal] = useState(false);
    const {plan, refreshSubscription} = useSubscriptionContext();
    const {userProfile} = useAuthContext();

    // Make context usage optional
    let setDocumentSubmissionData: any = null;
    try {
        const context = useDocumentSubmission();
        setDocumentSubmissionData = context.setDocumentSubmissionData;
    } catch {
        // Context not available, continue without it
    }

    const icon = useMemo(() => {
        if (projectState?.technicalSpec?.jobType === 'ADD_FEATURE')
            return <CodeCurlyAdd color="white" width={20} height={19.1} />;
        else if (projectState?.technicalSpec?.jobType === 'FIX_BUGS')
            return <Bug color="white" width={20} height={19.1} />;
        else if (projectState?.technicalSpec?.jobType === 'FIX_CVES')
            return <CheckShield color="white" width={20} height={19.1} />;
        else if (projectState?.technicalSpec?.jobType === 'ADD_TESTING')
            return <Task color="white" width={20} height={19.1} />;
        else if (projectState?.technicalSpec?.jobType === 'DOCUMENT_CODE')
            return <CommentAdd color="white" width={20} height={19.1} />;
        else return null;
    }, [projectState?.technicalSpec?.jobType]);

    const label = useMemo(() => {
        if (
            projectState?.technicalSpec?.jobType === 'NEW_PRODUCT' ||
            projectState?.technicalSpec?.jobType === 'REFACTOR_CODE' ||
            projectState?.technicalSpec?.jobType === 'CUSTOM'
        ) {
            return 'Select destination';
        } else if (projectState?.technicalSpec?.jobType === 'ADD_FEATURE') {
            return 'Add feature';
        } else if (projectState?.technicalSpec?.jobType === 'FIX_BUGS') {
            return 'Fix bugs';
        } else if (projectState?.technicalSpec?.jobType === 'FIX_CVES') {
            return 'Fix Security';
        } else if (projectState?.technicalSpec?.jobType === 'ADD_TESTING') {
            return 'Add testing';
        } else if (projectState?.technicalSpec?.jobType === 'DOCUMENT_CODE') {
            return 'Document code';
        } else {
            return 'Build codebase';
        }
    }, [projectState?.technicalSpec?.jobType]);

    const handleApprove = useCallback(async () => {
        // Check if tech spec is out of sync - if so, don't proceed
        if (!projectState?.isTechSpecInSync) {
            return;
        }

        logGAEvent('build_code_button_clicked', {
            event_category: 'Project',
            event_label: projectType,
            project_id: projectId,
            action: label,
            subscription_plan: userProfile?.subscription?.planName,
        });

        // For NEW_PRODUCT, REFACTOR_CODE, and CUSTOM flows, set context data and navigate to git location page
        if (projectState?.technicalSpec?.jobType === 'NEW_PRODUCT') {
            if (!onApprove) {
                console.error('onApprove function is not available');
                return;
            }

            if (setDocumentSubmissionData) {
                setDocumentSubmissionData({
                    onApprove,
                    docType,
                    projectType,
                    userProfile,
                    setIsDocumentSubmitting,
                });
            } else {
                console.error('setDocumentSubmissionData is not available');
                return;
            }

            navigateTo(
                `/workspace/project/${projectId}/back-prop/new-product`,
                {replace: true},
            );
            return;
        } else if (
            projectState?.technicalSpec?.jobType === 'REFACTOR_CODE' ||
            projectState?.technicalSpec?.jobType === 'CUSTOM'
        ) {
            if (!onApprove) {
                console.error('onApprove function is not available');
                return;
            }

            if (setDocumentSubmissionData) {
                setDocumentSubmissionData({
                    onApprove,
                    docType,
                    projectType,
                    userProfile,
                    setIsDocumentSubmitting,
                });
            } else {
                console.error('setDocumentSubmissionData is not available');
                return;
            }

            if (projectState?.technicalSpec?.jobType === 'REFACTOR_CODE') {
                navigateTo(
                    `/workspace/project/${projectId}/back-prop/refactor-code`,
                    {replace: true},
                );
            } else if (projectState?.technicalSpec?.jobType === 'CUSTOM') {
                navigateTo(`/workspace/project/${projectId}/back-prop/custom`, {
                    replace: true,
                });
            }
            return;
        }

        // For other flows (ADD_FEATURE, FIX_BUGS, etc.), execute the onApprove function directly
        if (!onApprove || !setIsDocumentSubmitting) {
            console.error(
                'onApprove or setIsDocumentSubmitting not available for direct execution',
            );
            return;
        }

        setIsDocumentSubmitting(true);

        await refreshSubscription?.();

        if (plan === 'FREE' || userProfile?.subscription?.planName === 'FREE') {
            setShowUpgradeModal(true);
            logGAEvent('upgrade_modal_triggered', {
                event_category: 'Subscription',
                event_label: 'build_code_attempt',
                project_id: projectId,
                current_plan: plan,
                action_attempted: label,
            });
            setIsDocumentSubmitting(false);
            return;
        }

        await checkGitInstallations();
        if (
            docType === 'tech_spec' &&
            githubStatus !== 'ACTIVE' &&
            adoStatus !== 'ACTIVE'
        ) {
            logGAEvent('github_installation_needed', {
                event_category: 'Project',
                event_label: projectType,
                project_id: projectId,
                github_status: githubStatus,
                provider: 'github',
            });
            navigateTo({
                pathname: `/workspace/project/${projectId}/tech-spec`,
                search: '?popup=github-enable',
            });
            setIsDocumentSubmitting(false);
            return;
        }

        try {
            logGAEvent('code_generation_started', {
                event_category: 'Project',
                event_label: projectType,
                project_id: projectId,
                action: label,
                subscription_plan: userProfile?.subscription?.planName,
            });
            await onApprove();
        } catch (error) {
            console.error('Error in direct onApprove execution:', error);
        }
        setIsDocumentSubmitting(false);
    }, [
        onApprove,
        docType,
        githubStatus,
        navigateTo,
        checkGitInstallations,
        setIsDocumentSubmitting,
        projectId,
        plan,
        label,
        projectType,
        userProfile,
        refreshSubscription,
        projectState,
        setDocumentSubmissionData,
        adoStatus,
    ]);
    if (
        !location.pathname.includes('tech-spec') ||
        shouldHide(projectType, projectState, isLegacyProject, isEditing)
    )
        return null;

    return (
        <div className="flex-none">
            <LoadingSubmitButton
                loading={isDocumentSubmitting}
                loadingText={label}
                role="button"
                onClick={handleApprove}
                className="primary-button flex justify-center items-center gap-[8px]">
                <span className="flex justify-center items-center gap-[8px]">
                    {icon}
                    <div>{label}</div>
                </span>
            </LoadingSubmitButton>
            <UpgradeModal
                isOpen={showUpgradeModal}
                onClose={() => setShowUpgradeModal(false)}
            />
        </div>
    );
}
