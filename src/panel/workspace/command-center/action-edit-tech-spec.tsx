import {useCallback} from 'react';
import {ProjectState} from '@/lib/entity-types';
import Edit from '../icons/edit';
import {useLocation, Location} from 'react-router-dom';
import {logGAEvent} from '@/lib/utils';
import {
    Tooltip,
    TooltipTrigger,
    TooltipContent,
    TooltipProvider,
} from '@/components/ui/tooltip';

interface IProps {
    readonly projectState: ProjectState | undefined;
    readonly isEditing?: boolean;
    readonly onStartEditing?: () => void;
    readonly isMobile?: boolean;
    readonly projectId: string;
}

function shouldHide(
    projectState?: ProjectState,
    isEditing?: boolean,
    codeStatus?: string,
    location?: Location,
) {
    if (!location?.pathname.includes('/tech-spec')) return true;
    if (!projectState?.technicalSpec?.status) return true;

    const {technicalSpec} = projectState;
    const showEditOption =
        technicalSpec.status === 'DONE' &&
        (!codeStatus || codeStatus === 'TODO') &&
        !isEditing;

    return !showEditOption;
}

export function ActionEditTechSpec({
    projectState,
    onStartEditing,
    isEditing,
    isMobile,
    projectId,
}: IProps) {
    const {codeGeneration} = projectState ?? {};
    const {status: codeStatus} = codeGeneration ?? {};
    const techStatus = projectState?.technicalSpec?.status;
    const location = useLocation();

    const handleStartEditing = useCallback(() => {
        // logic for showing a popup
        logGAEvent('edit_tech_spec_clicked', {
            event_category: 'Project',
            event_label: projectState?.technicalSpec?.jobType || 'unknown',
            tech_spec_status: projectState?.technicalSpec?.status,
            project_id: projectId,
        });
        onStartEditing?.();
    }, [onStartEditing, projectState, projectId]);

    if (shouldHide(projectState, isEditing, codeStatus, location)) return null;

    return techStatus === 'DONE' ? (
        <TooltipProvider delayDuration={100}>
            <Tooltip>
                <TooltipTrigger asChild>
                    <div className="flex-none">
                        <div
                            role="button"
                            className={`flex items-center justify-center gap-2 border hover:border-[#5B39F3] rounded-full transition-all ${
                                isMobile
                                    ? 'secondary-button'
                                    : 'h-[40px] w-[40px] tertiary-button p-2 border border-transparent'
                            }`}
                            onClick={handleStartEditing}>
                            <Edit height={24} width={24} color="#5B39F3" />
                            {isMobile && 'Edit tech spec'}
                        </div>
                    </div>
                </TooltipTrigger>
                <TooltipContent>
                    <p className="text-[12px]">Edit tech spec</p>
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>
    ) : null;
}
