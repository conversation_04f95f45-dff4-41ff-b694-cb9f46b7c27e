import {use<PERSON>allback, useEffect, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {useF<PERSON>, SubmitHandler, Controller} from 'react-hook-form';
import {z} from 'zod';
import {zodResolver} from '@hookform/resolvers/zod';
import {Input} from '@/components/ui/input';
import {createProject} from '@/lib/backend';
import {useAlertContext} from '@/context/alert-context';
import {LoadingSubmitButton} from '@/components/custom/loading-submit-button';
import {ProjectType} from '@/lib/entity-types';
import {CheckBox} from '../../modal/icons/checkbox';
import {UnCheckBox} from '../../modal/icons/un-checkbox';
import {LayerPlus} from '../../modal/icons/layer-plus';
import {CodeAlt} from '../../modal/icons/code-alt';
import {useSubscriptionContext} from '@/context/subscription-context';
import {UpgradeModal} from '../../modal/upgrade-modal';

const Schema = z.object({
    name: z
        .string()
        .trim()
        .min(1, {message: 'Project name is required'})
        .max(50, {
            message: 'Project name must not be longer than 50 characters.',
        }),
});

type SchemaType = z.infer<typeof Schema>;

export function ProjectNamePanel() {
    const navigateTo = useNavigate();
    const {showAlert} = useAlertContext();
    const {plan, refreshSubscription} = useSubscriptionContext();
    const [showUpgradeModal, setShowUpgradeModal] = useState(false);
    const [projectType, setProjectType] = useState<null | ProjectType>(null);
    const [isProjectTypeSelected, setIsProjectTypeSelected] = useState(false);

    const {
        control,
        handleSubmit,
        formState: {errors, isSubmitting, isSubmitted},
    } = useForm<SchemaType>({
        resolver: zodResolver(Schema),
        mode: 'onTouched',
        defaultValues: {
            name: '',
        },
    });

    const onSubmit: SubmitHandler<SchemaType> = useCallback(
        async data => {
            if (projectType !== null) {
                const {name} = data;
                if (plan === 'FREE' && projectType === 'EXISTING_PRODUCT') {
                    setShowUpgradeModal(true);
                    return;
                }
                try {
                    const project = await createProject(name, projectType);
                    if (project) {
                        // console.log(`Project created: ${JSON.stringify(project)}`);
                        navigateTo(
                            `/workspace/project/${project?.id}/status?from=project-name`,
                            {
                                replace: true,
                            },
                        );
                    } else {
                        showAlert?.({
                            type: 'HttpError',
                            message:
                                'Failed to create project. Please try again.',
                        });
                    }
                } catch (error) {
                    console.error(error);
                    if (error instanceof Error) {
                        showAlert?.({
                            type: 'HttpError',
                            message: `Error creating project: ${error.message}`,
                        });
                    }
                }
            }
        },
        [navigateTo, projectType, showAlert, plan],
    );

    const handleProjectTypeSelect = async (type: ProjectType) => {
        await refreshSubscription?.();
        if (type === 'EXISTING_PRODUCT' && plan === 'FREE') {
            setShowUpgradeModal(true);
            return;
        }
        setProjectType(type);
    };

    useEffect(() => {
        setIsProjectTypeSelected(projectType !== null);
    }, [projectType]);

    return (
        <div className="max-w-[1440px] mx-auto p-[24px] sm:p-[32px] h-full overflow-y-auto">
            <div className="w-full flex flex-col min-h-full">
                <div className="flex flex-col items-start w-full">
                    <div className="text-[#000] font-inter text-[24px] sm:text-[32px] font-semibold leading-[34px] sm:leading-[42px] my-[0px]">
                        Create a project
                    </div>
                    <div className="py-[32px] sm:py-[48px] w-full">
                        <div className="flex flex-col gap-[8px] mb-[16px]">
                            <div className="text-[#333] font-inter text-[16px] font-semibold leading-[22px] sm:leading-[150%]">
                                What kind of project are you working on?
                            </div>
                        </div>
                        <div className="flex flex-col sm:flex-row gap-[16px] w-full">
                            <div
                                className={`p-[16px] sm:p-[24px] border w-full ${
                                    projectType === 'NEW_PRODUCT'
                                        ? 'border-brand-purple bg-[#F2F0FE]'
                                        : 'border-[#999]'
                                } rounded-[24px] flex justify-normal items-start gap-4 cursor-pointer`}
                                onClick={() =>
                                    handleProjectTypeSelect('NEW_PRODUCT')
                                }>
                                <div
                                    className={`p-[12px] bg-[#F2F0FE] ${
                                        projectType === 'NEW_PRODUCT' &&
                                        'bg-white'
                                    } rounded-[16px]`}>
                                    <LayerPlus />
                                </div>
                                <div className="flex-grow">
                                    {/* <span className="mb-2 px-2 py-1 font-semibold bg-secondary text-secondary-foreground rounded-full inline-block">
                                        Free
                                    </span> */}
                                    <span className="mb-0 sm:mb-[8px] text-[16px] sm:text-[18px] font-semibold block leading-[150%]">
                                        New product
                                    </span>
                                    <span className="block text-[14px] sm:text-[16px] leading-[140%] sm:leading-[150%] tracking-[-0.3px]">
                                        Create a brand new software solution
                                        tailored to your business needs
                                    </span>
                                </div>
                                <div className="w-[18px] h-[18px] flex-shrink-0">
                                    {projectType === 'NEW_PRODUCT' ? (
                                        <CheckBox />
                                    ) : (
                                        <UnCheckBox />
                                    )}
                                </div>
                            </div>
                            <div
                                className={`p-[16px] sm:p-[24px] border w-full ${
                                    projectType === 'EXISTING_PRODUCT'
                                        ? 'border-brand-purple bg-[#F2F0FE]'
                                        : 'border-[#999]'
                                } rounded-[24px] flex justify-normal items-start gap-4 cursor-pointer`}
                                onClick={() =>
                                    handleProjectTypeSelect('EXISTING_PRODUCT')
                                }>
                                <div
                                    className={`p-[12px] bg-[#F2F0FE] ${
                                        projectType === 'EXISTING_PRODUCT' &&
                                        'bg-white'
                                    } rounded-[16px]`}>
                                    <CodeAlt />
                                </div>
                                <div className="flex-grow">
                                    {/* <span className="mb-2 px-2 py-1  font-semibold bg-brand-purple text-primary-foreground rounded-full inline-block">
                                        Enterprise
                                    </span> */}
                                    <span className=" mb-0 sm:mb-[8px] text-[16px] sm:text-[18px] font-semibold block">
                                        Existing product
                                    </span>
                                    <span className="block text-[14px] sm:text-[16px] leading-[140%] sm:leading-[150%]">
                                        Add features to your existing products
                                        or refactor your codebase
                                    </span>
                                </div>
                                <div className="w-[18px] h-[18px] flex-shrink-0">
                                    {projectType === 'EXISTING_PRODUCT' ? (
                                        <CheckBox />
                                    ) : (
                                        <UnCheckBox />
                                    )}
                                </div>
                            </div>
                        </div>
                        {isSubmitted && !isProjectTypeSelected && (
                            <div className="form-error-note pt-2 text-form-error-red">
                                Please select a project type
                            </div>
                        )}
                    </div>
                </div>
                <div>
                    <div className="text-[#333] mb-[16px] font-inter text-[16px] font-semibold leading-[22px] sm:leading-[150%]">
                        Name your project
                    </div>

                    <form
                        onSubmit={handleSubmit(onSubmit)}
                        className="w-full flex flex-col flex-grow">
                        <div className="flex-grow">
                            <Controller
                                name="name"
                                control={control}
                                render={({field}) => (
                                    <Input
                                        className={`w-full ${errors.name ? 'input-field-error' : 'input-field'}`}
                                        placeholder="Project title"
                                        type="text"
                                        {...field}
                                    />
                                )}
                            />
                            {errors.name && (
                                <div className="form-error-note pt-2 text-form-error-red">
                                    {errors.name.message}
                                </div>
                            )}
                        </div>
                        <div className="self-end align-bottom mt-[32px] sm:mt-[48px] bottom-0">
                            <LoadingSubmitButton
                                className="primary-button"
                                loadingText="Creating project"
                                loading={isSubmitting}>
                                Create project
                            </LoadingSubmitButton>
                        </div>
                    </form>
                </div>
                <UpgradeModal
                    isOpen={showUpgradeModal}
                    onClose={() => {
                        setShowUpgradeModal(false);
                    }}
                />
            </div>
        </div>
    );
}
