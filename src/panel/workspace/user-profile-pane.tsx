import {useCallback} from 'react';
import {useForm, SubmitHandler} from 'react-hook-form';
import {z} from 'zod';
import {zodResolver} from '@hookform/resolvers/zod';
import {useToast} from '@/hooks/use-toast';
import {Input} from '@/components/ui/input';
import {LoadingSubmitButton} from '@/components/custom/loading-submit-button';
import {useAuthContext} from '@/context/auth-context';

const UserProfileSchema = z.object({
    firstName: z
        .string()
        .min(1, {message: 'First name is required'})
        .max(100, {message: 'First name should have at most 100 characters'}),
    lastName: z
        .string()
        .min(1, {message: 'Last name is required'})
        .max(100, {message: 'Last name should have at most 100 characters'}),
    company: z
        .string()
        .max(100, {message: 'Company name should have at most 100 characters'})
        .optional(),
});

type UserProfileSchemaType = z.infer<typeof UserProfileSchema>;

export function UserProfilePane() {
    const {toast} = useToast();
    const {userProfile, changeUserProfile} = useAuthContext();
    const {
        reset,
        handleSubmit,
        formState: {isDirty, isSubmitting},
    } = useForm<UserProfileSchemaType>({
        resolver: zodResolver(UserProfileSchema),
        mode: 'onTouched',
        defaultValues: {
            // email: signUpEmail,
            firstName: userProfile?.firstName,
            lastName: userProfile?.lastName,
            company: userProfile?.company,
        },
    });

    const onSubmit: SubmitHandler<UserProfileSchemaType> = useCallback(
        async data => {
            try {
                // console.log(data);
                if (changeUserProfile) {
                    changeUserProfile({...userProfile, ...data});
                    reset({...data});
                    toast({
                        variant: 'success',
                        duration: 10000,
                        description: 'Profile updated successfully!',
                    });
                }
            } catch (error) {
                toast({
                    variant: 'destructive',
                    duration: 10000,
                    description: `Failed to update profile: ${error instanceof Error ? error.message : 'Unknown error'}`,
                });
            }
        },
        [changeUserProfile, reset, toast, userProfile],
    );

    return (
        <div className="max-w-[560px]">
            <form onSubmit={handleSubmit(onSubmit)}>
                <h2 className="my-[32px] text-[24px] font-semibold">
                    Profile settings
                </h2>
                <div className="w-full flex flex-col gap-[16px]">
                    <div className="flex flex-col gap-[16px]">
                        <div className="flex flex-col gap-[12px]">
                            <div className="form-label text-[16px] font-semibold">
                                First name
                            </div>
                            <div className="w-full flex flex-col gap-1">
                                <Input
                                    className="w-full input-field  bg-white text-[#999]"
                                    type="text"
                                    value={userProfile?.firstName}
                                    disabled
                                    readOnly
                                    aria-label="First name"
                                />
                            </div>
                        </div>
                        <div className="w-full flex flex-col gap-[12px]">
                            <div className="form-label text-[16px] font-semibold">
                                Last name
                            </div>
                            <div className="w-full flex flex-col gap-1">
                                <Input
                                    className="w-full input-field  bg-white text-[#999]"
                                    type="text"
                                    value={userProfile?.lastName}
                                    disabled
                                    readOnly
                                    aria-label="Last name"
                                />
                            </div>
                        </div>
                    </div>
                    <div className="w-full flex flex-col gap-[12px]">
                        <div className="form-label text-[16px] font-semibold">
                            Company
                        </div>
                        <div className="w-full flex flex-col gap-1">
                            <Input
                                className="w-full input-field  bg-white text-[#999]"
                                type="text"
                                value={userProfile?.company}
                                disabled
                                readOnly
                                aria-label="Company"
                            />
                        </div>
                    </div>
                    <div className="w-full flex flex-col gap-[12px]">
                        <div className="form-label text-[16px] font-semibold">
                            Email
                        </div>
                        <div className="w-full flex flex-col gap-1">
                            <Input
                                className="w-full input-field  bg-white text-[#999]"
                                type="email"
                                value={userProfile?.email}
                                disabled
                                readOnly
                                aria-label="Email address"
                            />
                        </div>
                    </div>
                    {isDirty && (
                        <LoadingSubmitButton
                            loading={isSubmitting}
                            loadingText="Saving changes"
                            className="primary-button sm:w-max">
                            Save changes
                        </LoadingSubmitButton>
                    )}
                </div>
            </form>
        </div>
    );
}
