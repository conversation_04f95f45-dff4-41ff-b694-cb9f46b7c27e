import {useCallback, useEffect, useState} from 'react';
import {escape} from 'html-escaper';
import DOMPurify from 'dompurify';
import {marked} from 'marked';
import mermaid from 'mermaid';
import hljs from 'highlight.js';
import {isIPhone} from '@/lib/utils';
import './github-markdown.css';
import 'highlight.js/styles/default.css';

marked.setOptions({
    breaks: true, // Convert line breaks to <br> tags
    gfm: true, // Use GitHub Flavored Markdown
});

mermaid.initialize({
    startOnLoad: false,
    theme: 'default', // Use a theme that works well with your design
    // themeVariables: {
    //     fontSize: '16px',
    //     primaryColor: '#333333',
    //     primaryTextColor: '#ff0000',
    // },
});

interface Props {
    markdown: string | undefined;
    viewerMessage: string;
}

const MAX_NUM_MERMAID_FIGURES_MOBILE = parseInt(
    import.meta.env.VITE_MAX_NUM_MERMAID_FIGURES_MOBILE ?? '5',
);

export function MarkdownViewer({markdown}: Props) {
    const [html, setHtml] = useState<string>('');

    const parseMarkdown = useCallback(async () => {
        if (markdown) {
            const renderer = new marked.Renderer();
            let numMermaidFigures = 0;
            renderer.code = ({text, lang}) => {
                if (lang === 'mermaid') {
                    numMermaidFigures++;
                    if (
                        !(
                            isIPhone &&
                            numMermaidFigures > MAX_NUM_MERMAID_FIGURES_MOBILE
                        )
                    ) {
                        const escapedText = escape(
                            // looking specifically for this pattern (some text)] and escaping both ( and)
                            // This is the ONLY way to fix both ARUI-1122 and ARUI-1143
                            text
                                .replace(
                                    /\[(.*)\((.*)\)(.*)]/g,
                                    '[$1#40;$2#41;$3]',
                                ) // replace (...)
                                .replace(
                                    /\[(.*)\{(.*)\}(.*)]/g,
                                    '[$1#123;$2#125;$3]',
                                ), // replace {...}
                        );
                        return `<div><pre class="mermaid">${escapedText}</pre></div>`;
                    }
                } else {
                    if (lang && hljs.getLanguage(lang)) {
                        try {
                            const highlighted = hljs.highlight(text, {
                                language: lang,
                            }).value;
                            return `<pre><code class="hljs language-${lang}">${highlighted}</code></pre>`;
                        } catch (err) {
                            console.error('Highlighting error:', err);
                        }
                    }

                    // Fallback to auto-detection
                    const highlighted = hljs.highlightAuto(text).value;
                    return `<pre><code class="hljs">${highlighted}</code></pre>`;
                }
                return `<pre><code>${text}</code></pre>`;
            };
            // console.time("parse MD");
            const rawHtml = await marked(markdown, {renderer});
            // console.timeLog("parse MD");
            const cleanHtml = DOMPurify.sanitize(rawHtml);
            // console.timeEnd("parse MD");
            setHtml(cleanHtml);
        } else {
            setHtml('');
        }
    }, [markdown]);

    // const drawDiagram = useCallback( async () => {
    //     const graphDefinition = 'graph TB\na-->b';
    //     const svg = await mermaid.render('graphDiv', graphDefinition);
    //     console.log(svg);
    //     setSvgText(svg);
    // }, []);

    // useEffect(() => {
    //     drawDiagram();
    // }, [drawDiagram]);

    useEffect(() => {
        try {
            mermaid.init();
        } catch (error) {
            console.error('Error initializing mermaid:', error);
        }
    }, [html]);

    useEffect(() => {
        parseMarkdown();
    }, [parseMarkdown]);

    return (
        <div className="self-stretch overflow-auto">
            <div
                className="markdown-body pb-[64px]"
                dangerouslySetInnerHTML={{__html: html}}
            />
        </div>
    );
}
