import {useCallback, useState, useEffect} from 'react';
import {useLocation, useNavigate} from 'react-router-dom';
import {useToast} from '@/hooks/use-toast';
import {sendVerificationEmail} from '@/lib/backend';
import {useAuthContext} from '@/context/auth-context';
import logo from '/images/logo-blitzy-purple.svg';
import {Link} from 'react-router-dom';

const TIMER_KEY = 'verify-email-timer-end';
const TIMER_DURATION = 180; // 3 minutes in seconds
export function VerifyEmailPanel() {
    const {pathname, search, hash} = useLocation();
    const navigateTo = useNavigate();
    const {toast} = useToast();
    const {
        checkEmailVerificationStatus,
        logout,
        userProfile: {email} = {},
    } = useAuthContext();
    const [isResendDisabled, setIsResendDisabled] = useState(false);
    const [countdown, setCountdown] = useState(TIMER_DURATION);

    // Set initial timer when user lands on the page
    useEffect(() => {
        const storedEndTime = localStorage.getItem(TIMER_KEY);
        if (!storedEndTime) {
            startTimer();
        }
    }, []);

    // Initialize timer state from localStorage on component mount
    useEffect(() => {
        const storedEndTime = localStorage.getItem(TIMER_KEY);
        if (storedEndTime) {
            const endTime = parseInt(storedEndTime, 10);
            const now = Date.now();
            const remainingTime = Math.max(
                0,
                Math.ceil((endTime - now) / 1000),
            );

            if (remainingTime > 0) {
                setIsResendDisabled(true);
                setCountdown(remainingTime);
            } else {
                // Timer has expired, clean up
                localStorage.removeItem(TIMER_KEY);
                setIsResendDisabled(false);
                setCountdown(0);
            }
        }
    }, []);

    // Timer countdown effect
    useEffect(() => {
        let timer: ReturnType<typeof setTimeout>;

        if (isResendDisabled && countdown > 0) {
            timer = setInterval(() => {
                setCountdown(prev => {
                    const newCount = prev - 1;
                    if (newCount <= 0) {
                        setIsResendDisabled(false);
                        localStorage.removeItem(TIMER_KEY);
                        return 0;
                    }
                    return newCount;
                });
            }, 1000);
        }

        return () => {
            if (timer) {
                clearInterval(timer);
            }
        };
    }, [isResendDisabled, countdown]);

    const formatTime = (seconds: number) => {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    };

    const startTimer = () => {
        const endTime = Date.now() + TIMER_DURATION * 1000;
        localStorage.setItem(TIMER_KEY, endTime.toString());
        setIsResendDisabled(true);
        setCountdown(TIMER_DURATION);
    };

    const handleContinue = useCallback(async () => {
        if (
            checkEmailVerificationStatus &&
            (await checkEmailVerificationStatus())
        ) {
            // Clear timer on successful verification
            localStorage.removeItem(TIMER_KEY);
            const url = `${pathname}${search}${hash}`;
            navigateTo(url, {replace: true});
        } else {
            toast({
                variant: 'default',
                duration: 10000,
                description: 'Email not verified',
            });
        }
    }, [
        checkEmailVerificationStatus,
        hash,
        navigateTo,
        pathname,
        search,
        toast,
    ]);

    const handleSignout = useCallback(
        async (e: React.MouseEvent<HTMLAnchorElement>) => {
            e.preventDefault();
            if (logout) {
                // Clear timer on signout
                localStorage.removeItem(TIMER_KEY);
                await logout();
                navigateTo('/');
            }
        },
        [logout, navigateTo],
    );

    const handleResend = useCallback(
        async (e: React.MouseEvent<HTMLAnchorElement>) => {
            e.preventDefault();

            if (isResendDisabled) {
                return;
            }

            startTimer();

            try {
                await sendVerificationEmail();
                toast({
                    variant: 'success',
                    duration: 10000,
                    description: 'Verification email sent',
                });
            } catch (error) {
                console.error(error);
                const {message} = error as Error;
                if (message?.includes('Too many requests')) {
                    toast({
                        variant: 'destructive',
                        duration: 5000,
                        description:
                            message ||
                            'Too many requests. Please wait before trying again.',
                    });
                } else {
                    localStorage.removeItem(TIMER_KEY);
                    setIsResendDisabled(false);
                    setCountdown(0);

                    toast({
                        variant: 'destructive',
                        duration: 10000,
                        description:
                            message || 'Failed to send verification email',
                    });
                }
            }
        },
        [toast, isResendDisabled],
    );

    return (
        <div className="px-[12px] flex flex-col items-center justify-center gap-[40px]">
            <div className="flex flex-col justify-center items-center gap-[24px]">
                <Link to="/">
                    <img src={logo} alt="logo" className="block sm:hidden" />
                </Link>
                <h2 className="text-[24px] sm:text-[32px] text-black">
                    Verify your email
                </h2>
            </div>
            <div className="text-[16px] text-[#333] text-center tracking-[-0.3px]">
                <p>
                    We've sent a email to{' '}
                    <span className="font-semibold">{email}</span>.
                </p>
                <p>Check your inbox to activate your account.</p>
            </div>
            <div
                className="primary-button w-full px-[20px] sm:px-[24px] py-[8px] sm:py-[12px] border border-brand-purple"
                role="button"
                onClick={handleContinue}>
                I have verified my email
            </div>
            <div className="text-[12px] sm:text-[14px] text-[#999] tracking-[-0.3px]">
                Didn't receive an email?{' '}
                <a
                    href="#"
                    onClick={handleResend}
                    className={`text-[#5B39F3] font-inter text-[14px] font-normal leading-[150%] cursor-pointer ${
                        isResendDisabled ? 'opacity-50 pointer-events-none' : ''
                    }`}>
                    {isResendDisabled
                        ? `Resend verification link (${formatTime(countdown)})`
                        : 'Resend verification link'}
                </a>
                <div className="text-center">
                    Or{' '}
                    <a role="button" onClick={handleSignout}>
                        Sign out
                    </a>
                </div>
            </div>
        </div>
    );
}
