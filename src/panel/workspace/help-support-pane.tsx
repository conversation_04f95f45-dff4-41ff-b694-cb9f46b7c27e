import {EXTERNAL_LINKS} from '@/assets/constants';
import iconEnvelope from './icons/envelope.svg';
import iconPulse from './icons/pulse.svg';
import {HelpCircleIcon} from './icons/help-circle';
import iconRight from '/images/chevron-right.svg';

interface Props {
    onClose: (e: React.MouseEvent) => void;
    showBack?: boolean;
}

export function HelpAndSupportPane({onClose, showBack}: Props) {
    return (
        <div
            className="w-[264px] flex flex-col bg-[#D9D9D9] rounded-[16px] overflow-hidden"
            onClick={e => {
                e.stopPropagation();
            }}>
            {showBack && (
                <div
                    className="px-[12px] py-[11.5px] bg-[#FFF] hover:bg-[#F2F0FE] no-underline items-start cursor-pointer border-b border-[#D9D9D9]"
                    onClick={e => {
                        e.stopPropagation();
                        onClose(e);
                    }}>
                    <img
                        src={iconRight}
                        className="w-[24px] h-[24px] rotate-180"
                        alt="Open"
                    />
                </div>
            )}
            <a
                href={`mailto:${EXTERNAL_LINKS.SUPPORT}`}
                className="p-[12px] flex gap-[8px] bg-[#FFF] hover:bg-[#F2F0FE] hover:no-underline items-center">
                <img
                    src={iconEnvelope}
                    className="w-[24px] h-[23px]"
                    alt="Email"
                />
                <span className="text-[16px] font-Inter text-black font-semibold ">
                    {EXTERNAL_LINKS.SUPPORT}
                </span>
            </a>
            <a
                href={EXTERNAL_LINKS.STATUS}
                target="_blank"
                rel="noopener noreferrer"
                className="p-[12px] flex gap-[8px] bg-[#FFF] hover:bg-[#F2F0FE] hover:no-underline items-center">
                <img
                    src={iconPulse}
                    className="w-[24px] h-[23px]"
                    alt="Status"
                />
                <span className="text-[16px] font-Inter text-black font-semibold">
                    System status
                </span>
            </a>

            <a
                href={EXTERNAL_LINKS.FAQ}
                target="_blank"
                rel="noopener noreferrer"
                className="p-[12px] flex gap-[8px] bg-[#FFF] hover:bg-[#F2F0FE] hover:no-underline items-center">
                <HelpCircleIcon className="w-[24px] h-[24px]" variant="black" />
                <span className="text-[16px] font-Inter text-black font-semibold">
                    FAQ
                </span>
            </a>
        </div>
    );
}
