import {useCallback} from 'react';
import {useNavigate} from 'react-router-dom';
import {ProjectHeader} from './project-header';
import iconNew from '/images/union.svg';
import {logGAEvent} from '@/lib/utils';

export function ProjectListHeader() {
    const navigateTo = useNavigate();

    const handleCreateProject = useCallback(async () => {
        logGAEvent('create_project_clicked', {
            source: 'project_list_header',
            location: 'projects_page',
        });
        navigateTo('/workspace/project-name');
    }, [navigateTo]);

    return (
        <ProjectHeader title="Projects" titleLink="/workspace/projects">
            <div className="flex space-x-4">
                <div
                    role="button"
                    onClick={handleCreateProject}
                    className="primary-button px-[8px] py-[8px] sm:px-[20px] sm:py-[8px] flex flex-row gap-[6px] items-center">
                    <img
                        src={iconNew}
                        className="w-[14px] h-[14px]"
                        alt="newProject"
                    />
                    <div className="hidden sm:block">Create new project</div>
                </div>
            </div>
        </ProjectHeader>
    );
}
