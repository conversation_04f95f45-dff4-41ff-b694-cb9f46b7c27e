import {ReactNode} from 'react';
import {HeaderBreadcrumbs} from './header-breadcrumbs.tsx';

interface Props {
    title?: string;
    titleLink?: string;
    children?: ReactNode;
}

export function ProjectHeader(props: Props) {
    const {children, title, titleLink} = props;

    return (
        <div className="flex flex-row h-2[em] items-center justify-between self-stretch">
            <div className="sm:block flex items-center">
                <HeaderBreadcrumbs title={title} titleLink={titleLink} />
            </div>
            {children}
        </div>
    );
}
