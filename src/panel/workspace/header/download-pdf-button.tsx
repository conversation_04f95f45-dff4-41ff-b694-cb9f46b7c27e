import {useCallback, useState} from 'react';
import <PERSON><PERSON>oader from '../icons/small-loader';
import Download from '../icons/download';
import {useProjectContext} from '@/context/project-context';
import {logGAEvent} from '@/lib/utils';
import {
    Tooltip,
    TooltipTrigger,
    TooltipContent,
    TooltipProvider,
} from '@/components/ui/tooltip';

interface Props {
    onDownloadPDF: () => void;
    isMobile?: boolean;
    projectId: string;
}

export function DownloadPdfButton({onDownloadPDF, isMobile, projectId}: Props) {
    const [isExportingPDF, setIsExportingPDF] = useState(false);
    const {pdfStatus} = useProjectContext();
    const isDownloading = isExportingPDF || pdfStatus === 'IN_PROGRESS';

    const handleExportToPDF = useCallback(async () => {
        logGAEvent('pdf_download_initiated', {
            event_category: 'Project',
            project_id: projectId,
        });
        setIsExportingPDF(true);
        await onDownloadPDF();
        setIsExportingPDF(false);
    }, [onDownloadPDF, projectId]);

    return (
        <TooltipProvider delayDuration={100}>
            <Tooltip>
                <TooltipTrigger asChild>
                    <div className="flex-none">
                        <button
                            className={`flex items-center justify-center gap-2 ${isMobile ? 'secondary-button' : 'tertiary-button w-min p-2'} ${isDownloading ? 'cursor-auto' : ''}`}
                            onClick={handleExportToPDF}
                            disabled={isDownloading}>
                            {isDownloading ? (
                                <span className="animate-spin">
                                    <SmallLoader />
                                </span>
                            ) : (
                                <Download />
                            )}
                            {isMobile &&
                                (isDownloading
                                    ? 'Downloading PDF...'
                                    : 'Download PDF')}
                        </button>
                    </div>
                </TooltipTrigger>
                <TooltipContent>
                    <p className="text-[12px]">
                        {isDownloading ? 'Downloading PDF...' : 'Download PDF'}
                    </p>
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>
    );
}
