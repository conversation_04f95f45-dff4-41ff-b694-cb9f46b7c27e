import {useProjectContext} from '@/context/project-context';
import iconRight from '/images/chevron-right.svg';
import {useNavigate} from 'react-router-dom';

interface Props {
    title?: string;
    titleLink?: string;
}

export function HeaderBreadcrumbs({title, titleLink}: Props) {
    const {project} = useProjectContext();
    const navigateTo = useNavigate();

    return (
        <div className="flex flex-row h-2[em] items-center justify-between self-stretch sm:px-0">
            <div className="flex items-center">
                {title && (
                    <div
                        className={`text-text-text-primary text-[18px] font-semibold leading-[28px] ${titleLink && 'cursor-pointer'}`}
                        onClick={() => titleLink && navigateTo(titleLink)}>
                        {title}
                    </div>
                )}
                {project?.name && (
                    <>
                        <img
                            src={iconRight}
                            alt="Right Icon"
                            className="w-[24px] h-[24px] flex-shrink-0 mx-2"
                        />
                        <div className="text-text-text-primary text-[18px] font-semibold leading-[28px]">
                            {project.name}
                        </div>
                    </>
                )}
            </div>
        </div>
    );
}
