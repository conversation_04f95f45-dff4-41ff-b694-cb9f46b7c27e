import {FC, useCallback, useEffect} from 'react';
import {useGitInstallationsContext} from '@/context/git-installations-context';
import {IntegrationCard} from './integartion-card';
import iconGithub from '../icons/github.svg';
import iconAzureDevOps from '../icons/azure-devops.svg';
import {fetchAzureDevOpsInstallUrl} from '@/lib/backend';

const IntegrationsPane: FC = () => {
    const {githubStatus, adoStatus, installations, checkGitInstallations} =
        useGitInstallationsContext();

    // Get GitHub specific details
    const githubInstallation = installations.find(
        installation => installation.svc_type === 'GITHUB',
    );
    const githubInstallationId = githubInstallation?.installation_id;
    const githubTargetName = githubInstallation?.target_name;
    const githubInstallationType = githubInstallation?.installation_type;

    useEffect(() => {
        checkGitInstallations();
    }, [checkGitInstallations]);

    const handleRefreshStatus = useCallback(() => {
        checkGitInstallations();
    }, [checkGitInstallations]);

    const handleConnectGithub = useCallback(() => {
        const url = import.meta.env.VITE_GITHUB_INSTALL_URL;
        if (url) {
            localStorage.setItem(
                'github-connect-redirect-to-integration-tab',
                'true',
            );
            window.location.assign(url);
        }
    }, []);

    const handleConnectAzureDevOps = useCallback(async () => {
        try {
            const url = await fetchAzureDevOpsInstallUrl();
            window.location.assign(url);
        } catch (error) {
            console.error('Error connecting to Azure DevOps:', error);
        }
    }, []);

    const handleCancelRequest = useCallback(() => {
        console.log('Cancel Request');
    }, []);

    const handleViewStatus = useCallback(() => {
        let url: string;
        if (githubInstallationType === 'ORGANIZATION') {
            url = `https://github.com/organizations/${githubTargetName}/settings/installations/${githubInstallationId}`;
        } else {
            url = `https://github.com/settings/installations/${githubInstallationId}`;
        }
        if (url) {
            window.open(url, '_blank');
        }
    }, [githubInstallationId, githubTargetName, githubInstallationType]);

    const handleManage = useCallback(() => {
        let url: string;
        if (githubInstallationType === 'ORGANIZATION') {
            url = `https://github.com/organizations/${githubTargetName}/settings/installations/${githubInstallationId}`;
        } else {
            url = `https://github.com/settings/installations/${githubInstallationId}`;
        }
        if (url) {
            window.open(url, '_blank');
        }
    }, [githubInstallationId, githubTargetName, githubInstallationType]);

    const handleReconnect = useCallback(() => {
        console.log('Reconnect');
    }, []);

    const handleDisconnect = useCallback(() => {
        let url: string;
        if (githubInstallationType === 'ORGANIZATION') {
            url = `https://github.com/organizations/${githubTargetName}/settings/installations/${githubInstallationId}`;
        } else {
            url = `https://github.com/settings/installations/${githubInstallationId}`;
        }
        if (url) {
            window.open(url, '_blank');
        }
    }, [githubInstallationId, githubTargetName, githubInstallationType]);

    const handleConfigure = useCallback(() => {
        let url: string;
        if (githubInstallationType === 'ORGANIZATION') {
            url = `https://github.com/organizations/${githubTargetName}/settings/installations/${githubInstallationId}`;
        } else {
            url = `https://github.com/settings/installations/${githubInstallationId}`;
        }
        if (url) {
            window.open(url, '_blank');
        }
    }, [githubInstallationId, githubTargetName, githubInstallationType]);

    return (
        <div className="pt-[18px]">
            <div className="w-full flex flex-col sm:flex-row gap-[12px]">
                <IntegrationCard
                    integrationIcon={iconGithub}
                    title="Github"
                    description="Access your repositories to write, update, and manage code directly from Blitzy."
                    details={[
                        'Read access to code repositories',
                        'Write access to create pull requests',
                        'Access to issues and project boards',
                    ]}
                    status={githubStatus}
                    installation_type={githubInstallationType}
                    installationId={githubInstallationId}
                    target_name={githubTargetName}
                    onConnect={handleConnectGithub}
                    onRefresh={handleRefreshStatus}
                    onReconnect={handleReconnect}
                    onConfigure={handleConfigure}
                    onCancelRequest={handleCancelRequest}
                    onViewStatus={handleViewStatus}
                    onManage={handleManage}
                    onDisconnect={handleDisconnect}
                />
                <IntegrationCard
                    integrationIcon={iconAzureDevOps}
                    title="Azure DevOps"
                    description="Access your repositories to write, update, and manage code directly from Blitzy."
                    details={[
                        'Read access to code repositories',
                        'Write access to create pull requests',
                        'Access to issues and project boards',
                    ]}
                    status={adoStatus}
                    isAzureDevOps={true}
                    onConnect={handleConnectAzureDevOps}
                />
            </div>
        </div>
    );
};

export default IntegrationsPane;
