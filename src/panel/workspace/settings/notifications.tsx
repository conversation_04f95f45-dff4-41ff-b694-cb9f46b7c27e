import React, {useCallback} from 'react';
import {useAuthContext} from '@/context/auth-context';
import {updateUserConfiguration} from '@/lib/backend';
import {useToast} from '@/hooks/use-toast';

interface TextProps {
    children: React.ReactNode;
    className?: string;
}

const Text = ({children, className = ''}: TextProps) => (
    <span className={`font-inter ${className}`}>{children}</span>
);

interface SwitchProps {
    isOn: boolean;
    onToggle: () => void;
}

const Switch = ({isOn, onToggle}: SwitchProps) => (
    <div
        className={`flex w-[40px] p-[4px] flex-col justify-center items-end rounded-[500px] cursor-pointer transition-colors ${
            isOn ? 'bg-[var(--color-primary-300,#5B39F3)]' : 'bg-[#D9D9D9]'
        }`}
        onClick={onToggle}>
        <div
            className={`w-[16px] h-[16px] rounded-full bg-white transition-transform ${
                isOn ? 'translate-x-0' : 'translate-x-[-16px]'
            }`}></div>
    </div>
);

export function Notifications() {
    const {userProfile, refreshUserProfile} = useAuthContext();
    const {toast} = useToast();
    const {userConfig} = userProfile ?? {};
    const [techSpecEnabled, setTechSpecEnabled] = React.useState(
        userConfig?.techSpecNotificationEnabled ?? false,
    );
    const [codeGenEnabled, setCodeGenEnabled] = React.useState(
        userConfig?.codeGenNotificationEnabled ?? false,
    );
    const [isLoading, setIsLoading] = React.useState(false);

    const handleTechSpecToggle = useCallback(async () => {
        setIsLoading(true);
        const isEnabled = techSpecEnabled;
        setTechSpecEnabled(!isEnabled);
        try {
            await updateUserConfiguration({
                techSpecNotificationEnabled: !isEnabled,
                codeGenNotificationEnabled: codeGenEnabled,
            });
            await refreshUserProfile?.({refreshInBackground: true});
        } catch (error) {
            console.error(error);
            const message =
                error instanceof Error
                    ? error.message
                    : 'Failed to update user configuration.';
            toast({variant: 'destructive', title: message});
            setTechSpecEnabled(isEnabled);
        } finally {
            setIsLoading(false);
        }
    }, [codeGenEnabled, techSpecEnabled, refreshUserProfile, toast]);

    const handleCodeGenToggle = useCallback(async () => {
        setIsLoading(true);
        const isEnabled = codeGenEnabled;
        setCodeGenEnabled(!isEnabled);
        try {
            await updateUserConfiguration({
                techSpecNotificationEnabled: techSpecEnabled,
                codeGenNotificationEnabled: !isEnabled,
            });
            await refreshUserProfile?.({refreshInBackground: true});
        } catch (error) {
            console.error(error);
            const message =
                error instanceof Error
                    ? error.message
                    : 'Failed to update user configuration.';
            toast({variant: 'destructive', title: message});
            setCodeGenEnabled(isEnabled);
        } finally {
            setIsLoading(false);
        }
    }, [codeGenEnabled, techSpecEnabled, refreshUserProfile, toast]);

    return (
        <div className="flex flex-col items-start gap-[24px] self-stretch pt-[44px]">
            <Text className="self-stretch text-[24px] leading-[130%] font-semibold text-[var(--text-text-secondary,#333)]">
                Project updates
            </Text>

            <div className="w-full flex flex-col gap-[12px]">
                <div className="flex w-full sm:w-[776px] p-[24px] flex-col items-start gap-[4px] rounded-[24px] border border-[#D9D9D9]">
                    <div className="flex justify-between items-center w-full">
                        <Text className="text-[16px] leading-[150%] font-semibold text-[var(--text-text-primary,#000)] tracking-[-0.04px]">
                            Tech spec updates
                        </Text>
                        <Switch
                            isOn={techSpecEnabled}
                            onToggle={
                                isLoading ? () => null : handleTechSpecToggle
                            }
                        />
                    </div>
                    <Text className="text-[16px] leading-[150%] font-normal text-[var(--text-text-tertiary,#999)] tracking-[-0.3px]">
                        We'll email you when the tech spec is created or updated
                    </Text>
                </div>

                <div className="flex w-full sm:w-[776px] p-[24px] flex-col items-start gap-[4px] rounded-[24px] border border-[#D9D9D9]">
                    <div className="flex justify-between items-center w-full">
                        <Text className="text-[16px] leading-[150%] font-semibold text-[var(--text-text-primary,#000)] tracking-[-0.04px]">
                            Code generation complete
                        </Text>
                        <Switch
                            isOn={codeGenEnabled}
                            onToggle={
                                isLoading ? () => null : handleCodeGenToggle
                            }
                        />
                    </div>
                    <Text className="text-[16px] leading-[150%] font-normal text-[var(--text-text-tertiary,#999)] tracking-[-0.3px]">
                        Get an email when your PR is ready for review
                    </Text>
                </div>
            </div>
        </div>
    );
}
