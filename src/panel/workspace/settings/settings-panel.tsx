import {useCallback} from 'react';
import {useNavigate} from 'react-router-dom';
import {Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger} from '@/components/ui/tabs';
import {UserProfilePane} from '../user-profile-pane.tsx';
import {ResetPasswordPane} from '../reset-password-pane.tsx';
import {useAuthContext} from '@/context/auth-context';
import {ProjectPanelContent} from '../project-panel-content.tsx';
// import {ProjectHeader} from './header/project-header';
import {PlanPane} from './plan-pane.tsx';
import IntegrationsPane from './integrations.tsx';
import {Notifications} from './notifications.tsx';

interface Props {
    initialTab: string;
}

export function SettingsPanel({initialTab}: Props) {
    const {userProfile} = useAuthContext();
    const navigateTo = useNavigate();
    const handleTabChange = useCallback(
        (tab: string) => {
            navigateTo(`/workspace/settings/${tab}`, {replace: true});
        },
        [navigateTo],
    );

    const activeTab = initialTab;

    return (
        // <ProjectPanelContent header={<ProjectHeader title="Settings" />}>
        <ProjectPanelContent>
            <Tabs
                onValueChange={handleTabChange}
                value={activeTab}
                className="w-full">
                <div className="overflow-x-auto flex w-full hide-scrollbar">
                    <TabsList className="mx-[0px] sm:mx-0">
                        <TabsTrigger
                            className="focus:outline-none focus-visible:ring-0 focus:ring-0 active:outline-none"
                            value="profile">
                            Profile
                        </TabsTrigger>
                        {!['google.com', 'microsoft.com'].includes(
                            userProfile?.providerId || '',
                        ) && (
                            <TabsTrigger
                                className="focus:outline-none focus-visible:ring-0 focus:ring-0 active:outline-none"
                                value="password">
                                Password
                            </TabsTrigger>
                        )}
                        <TabsTrigger
                            className="focus:outline-none focus-visible:ring-0 focus:ring-0 active:outline-none"
                            value="notifications">
                            Notifications
                        </TabsTrigger>
                        <TabsTrigger
                            className="focus:outline-none focus-visible:ring-0 focus:ring-0 active:outline-none"
                            value="integrations">
                            Integrations
                        </TabsTrigger>
                        <TabsTrigger
                            className="focus:outline-none focus-visible:ring-0 focus:ring-0 active:outline-none"
                            value="plan">
                            Plan
                        </TabsTrigger>
                    </TabsList>
                </div>
                <TabsContent value="profile">
                    <UserProfilePane />
                </TabsContent>
                {!['google.com', 'microsoft.com'].includes(
                    userProfile?.providerId || '',
                ) && (
                    <TabsContent value="password">
                        <ResetPasswordPane />
                    </TabsContent>
                )}
                <TabsContent value="integrations">
                    <IntegrationsPane />
                </TabsContent>
                <TabsContent value="plan">
                    <PlanPane />
                </TabsContent>
                <TabsContent value="notifications">
                    <Notifications />
                </TabsContent>
            </Tabs>
        </ProjectPanelContent>
    );
}
