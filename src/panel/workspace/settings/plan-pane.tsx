import {useSubscriptionContext} from '@/context/subscription-context';
// import UpgradeAlertBanner from '@/components/custom/upgrade-alert-banner';
import CheckIcon from '/images/check.svg';
import {useCallback, useMemo, useEffect} from 'react';
import {useAuthContext} from '@/context/auth-context';

// import {useNavigate} from 'react-router-dom';
import {logGAEvent} from '@/lib/utils';
import {useParams} from 'react-router-dom';

type PlanDetails = {
    title: string;
    price: string;
    buttonText: string;
    features: string[];
    trialFlag?: string | null;
    cancelled?: string | null;
};

export function PlanPane() {
    const {
        isTrialing,
        subscriptionStartDate,
        subscriptionEndDate,
        subscriptionStatus,
        goToManageSubscriptionLink,
        gotoPaymentLink,
        refreshSubscription,
    } = useSubscriptionContext();
    const {userProfile} = useAuthContext();
    const {projectId} = useParams();
    // Normalize plan key to match planDetails structure
    const normalizePlanKey = (planKey: string | undefined): string => {
        if (!planKey) return 'Free';
        return planKey.charAt(0).toUpperCase() + planKey.slice(1).toLowerCase();
    };

    // Plan details configuration
    const planDetails: Record<string, PlanDetails> = useMemo(
        () => ({
            Free: {
                title: 'Blitzy Free',
                price: '$0, forever',
                buttonText: 'Upgrade plan',
                features: [
                    'Generate technical specifications for both new and existing products',
                    'Build new products autonomously',
                    'Add new features to existing codebases',
                    'Refactor and modernize legacy code',
                    'Infinite code context — 100M+ lines of code',
                    'Output up to 3M+ lines of code',
                    'Unlimited users across your organization',
                    'Enterprise-grade security',
                ],
            },
            Pro: {
                title: 'Blitzy Pro',
                price: '$1000 per month',
                buttonText:
                    subscriptionStatus === 'CANCELLING'
                        ? 'Resubscribe'
                        : 'Manage plan',
                features: [
                    'Infinite code context — 100M+ lines of code',
                    'Output up to 3M+ lines of code',
                    'Unlimited users across your organization',
                    'Enterprise-grade security',
                ],
                trialFlag: isTrialing ? 'Trialing' : null,
                cancelled:
                    subscriptionStatus === 'CANCELLING' ? 'Cancelled' : null,
            },
            Teams: {
                title: 'Blitzy Team',
                price: '$1000 per month',
                buttonText: 'Contact us',
                features: [
                    'Infinite code context — 100M+ lines of code',
                    'Output up to 3M+ lines of code',
                    'Enterprise-grade security',
                ],
            },
            Enterprise: {
                title: 'Blitzy Enterprise',
                price: 'Tailored pricing for your organization',
                buttonText: 'Contact us',
                features: [
                    'Add features to existing code base',
                    'Refactor existing code bases',
                    'Dedicated instance of the platform',
                ],
            },
        }),
        [isTrialing, subscriptionStatus],
    );

    const currentPlanDetails =
        planDetails[normalizePlanKey(userProfile?.subscription?.planName)];

    const handleManageClick = useCallback(() => {
        logGAEvent('manage_subscription_clicked', {
            user_id: userProfile?.id,
            email: userProfile?.email,
            current_plan: userProfile?.subscription?.planName,
            subscription_status: subscriptionStatus,
        });
        goToManageSubscriptionLink?.();
    }, [
        goToManageSubscriptionLink,
        userProfile?.subscription?.planName,
        subscriptionStatus,
        userProfile?.id,
        userProfile?.email,
    ]);

    const handleUpgradeClick = useCallback(() => {
        logGAEvent('plan_upgrade_clicked', {
            user_id: userProfile?.id,
            email: userProfile?.email,
            current_plan: userProfile?.subscription?.planName,
            subscription_status: subscriptionStatus,
        });
        if (userProfile?.subscription?.planName === 'FREE') {
            gotoPaymentLink?.(projectId);
        } else {
            window.open('https://blitzy.com/#pricing', '_blank');
        }
    }, [
        userProfile?.subscription?.planName,
        gotoPaymentLink,
        subscriptionStatus,
        userProfile?.id,
        userProfile?.email,
        projectId,
    ]);

    useEffect(() => {
        if (refreshSubscription) {
            refreshSubscription();
        }
    }, [refreshSubscription]);

    if (!currentPlanDetails) return null;

    return (
        <div>
            <div className="my-[32px] text-[24px] font-semibold flex flex-col gap-[24px]">
                {/* <UpgradeAlertBanner />
                <div className="text-[#333333] align-self-stretch font-inter text-2xl font-semibold leading-[130%] flex gap-2">
                    Plan details
                </div> */}
                {/* Plan Card */}
                <div className="flex flex-col ">
                    <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-6 w-full">
                        {/* Left Column */}
                        <div className="flex flex-col md:w-1/2 gap-[2px]">
                            <div className="flex items-center gap-[16px]">
                                <h3 className="text-[var(--text-text-secondary,#333)] font-inter text-[32px] font-semibold leading-[130%]">
                                    {currentPlanDetails.title}
                                </h3>
                                {currentPlanDetails.trialFlag && (
                                    <div className="flex items-center gap-1 px-3 py-1 text-[#5B39F3] text-sm rounded-full bg-[var(--background-bg-brand-50,#F2F0FE)]">
                                        {currentPlanDetails.trialFlag}
                                    </div>
                                )}
                                {currentPlanDetails.cancelled && (
                                    <div className="flex items-center gap-1 px-3 py-1 text-[#991010] text-sm rounded-full bg-[#FFDFDF]">
                                        {currentPlanDetails.cancelled}
                                    </div>
                                )}
                            </div>
                            {/* <p className="text-[var(--text-text-secondary,#333)] font-inter text-[16px] font-normal leading-[150%] tracking-[-0.3px]">
                                {currentPlanDetails.price}
                            </p> */}
                            {currentPlanDetails.trialFlag && (
                                <p className="text-[#999999] font-inter text-[12px] font-normal leading-[140%] tracking-[-0.036px]">
                                    Monthly Pro subscription begins{' '}
                                    {subscriptionStartDate}
                                </p>
                            )}
                        </div>
                    </div>

                    {currentPlanDetails.title !== 'Blitzy Free' && (
                        <div className="flex flex-col pt-[32px]">
                            <div className="text-black font-inter text-[16px] font-semibold leading-[150%] tracking-[-0.04px]">
                                {currentPlanDetails.cancelled
                                    ? 'Valid until'
                                    : 'Next billing date'}
                            </div>
                            <div className="text-[#333] font-inter text-[16px] font-normal leading-[150%] tracking-[-0.3px]">
                                {subscriptionEndDate}
                            </div>
                        </div>
                    )}

                    {/* Button */}
                    <div className="flex flex-row gap-[12px] pt-[32px] pb-[32px] ">
                        {userProfile?.subscription?.planName !==
                            'ENTERPRISE' && (
                            <button
                                role="button"
                                className="primary-button sm:w-min"
                                onClick={handleUpgradeClick}>
                                Upgrade
                            </button>
                        )}
                        {userProfile?.subscription?.planName === 'PRO' && (
                            <button
                                role="button"
                                className="secondary-button sm:w-min"
                                onClick={handleManageClick}>
                                Manage
                            </button>
                        )}
                    </div>

                    {userProfile?.subscription?.planName !== 'ENTERPRISE' && (
                        <div className="w-[1376px] h-[1px] bg-[#D9D9D9]"></div>
                    )}

                    {userProfile?.subscription?.planName !== 'ENTERPRISE' && (
                        <div className="flex flex-col pt-[32px]">
                            <h4 className="text-[var(--text-text-secondary,#333)] font-inter text-[16px] font-semibold leading-[150%] tracking-[-0.04px]">
                                Upgrade your plan to get
                            </h4>
                            <ul className="flex flex-col gap-2 mt-2">
                                {currentPlanDetails.features.map(
                                    (feature: string, index: number) => (
                                        <li
                                            key={index}
                                            className="flex items-center gap-2 text-[var(--text-text-secondary,#333)] font-inter text-[16px] font-normal leading-[150%] tracking-[-0.3px]">
                                            <div className="flex items-center justify-center w-6 h-6">
                                                <img
                                                    src={CheckIcon}
                                                    alt="Check Icon"
                                                />
                                            </div>
                                            {feature}
                                        </li>
                                    ),
                                )}
                            </ul>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}
