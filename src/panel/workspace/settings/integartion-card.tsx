import React, {useState} from 'react';
import {ChevronDownIcon, ChevronUpIcon} from '@radix-ui/react-icons';
import DotsHorizontalIcon from '../icons/dots-horizontal-rounded';
import PlugIcon from '../icons/plug';
import Refresh from '../icons/refresh';
import SettingsIcon from '../icons/wrench';
import CheckCircle from '../icons/check-circle';
import Hourglass from '../icons/hourglass';
import {XClose} from '@/modal/icons/x-close';
import Unlink from '../icons/unlink';
import ErrorCircle from '../icons/error-circle';

// =============================================================================
// TYPES & INTERFACES
// =============================================================================

interface IntegrationCardProps {
    integrationIcon: string;
    title: string;
    description: string;
    details: string[];
    status?: string;
    isAzureDevOps?: boolean;
    installation_type?: string;
    installationId?: string;
    target_name?: string;
    onConnect?: () => void;
    onViewStatus?: () => void;
    onManage?: () => void;
    onRefresh?: () => void;
    onReconnect?: () => void;
    onConfigure?: () => void;
    onCancelRequest?: () => void;
    onDisconnect?: () => void;
}

interface ActionMenuItemProps {
    icon: React.ReactNode;
    label: string;
    onClick?: () => void;
    className?: string;
    labelClassName?: string;
}

interface ActionMenuProps {
    isOpen: boolean;
    onClose: () => void;
    onRefresh: () => void;
    onReconnect: () => void;
    onConfigure: () => void;
    onCancelRequest: () => void;
    onDisconnect: () => void;
    showRefresh: boolean;
    showReconnect: boolean;
    showConfigure: boolean;
    currentStatus: string;
    showCancelRequest: boolean;
    showDisconnect: boolean;
}

type StatusKey =
    | 'PENDING'
    | 'ACTIVE'
    | 'UNINSTALLED'
    | 'SUSPENDED'
    | 'REJECTED'
    | 'CONNECTION_EXPIRED'
    | '404';

// =============================================================================
// CONSTANTS
// =============================================================================

const BASE_STATUS_CLASSES =
    'inline-flex px-[12px] py-[4px] justify-center items-center gap-1 rounded-full text-[12px] text-center font-semibold';

const MENU_ACTIONS = [
    {
        key: 'showRefresh',
        icon: <Refresh />,
        label: 'Refresh connection',
        action: 'onRefresh',
        className: 'border-b border-gray-200',
    },
    {
        key: 'showReconnect',
        icon: <PlugIcon />,
        label: (status: string) => (status === '404' ? 'Connect' : 'Reconnect'),
        action: 'onReconnect',
        className: 'border-b border-gray-200',
    },
    {
        key: 'showConfigure',
        icon: <SettingsIcon />,
        label: 'Configure',
        action: 'onConfigure',
        className: 'border-b border-gray-200',
    },
    {
        key: 'showCancelRequest',
        icon: <XClose width={24} height={24} fill="#991010" />,
        label: 'Cancel request',
        action: 'onCancelRequest',
        className: 'text-[#991010] border-b border-gray-200',
        labelClassName: 'text-[#991010]',
    },
    {
        key: 'showDisconnect',
        icon: <Unlink width={24} height={24} color="#991010" />,
        label: 'Disconnect',
        action: 'onDisconnect',
        className: 'text-[#991010] border-b border-gray-200',
        labelClassName: 'text-[#991010]',
    },
] as const;

const STATUS_CONFIGS: Record<StatusKey, any> = {
    PENDING: {
        text: 'Awaiting Approval',
        classes: `${BASE_STATUS_CLASSES} bg-[#FFFBEB] text-[#5F1616]`,
        statusIcon: <Hourglass width={14} height={14} color="#5F1616" />,
        menuConfig: {
            showMenu: true,
            options: {
                showRefresh: false,
                showReconnect: false,
                showConfigure: false,
                showCancelRequest: true,
                showDisconnect: false,
            },
        },
        action: {
            text: 'View Status',
            actionKey: 'onViewStatus',
            className: 'secondary-button',
        },
    },
    ACTIVE: {
        text: 'Connected',
        classes: `${BASE_STATUS_CLASSES} bg-[#C9FCEA] text-[#005335]`,
        statusIcon: <CheckCircle width={14} height={14} color="#005335" />,
        menuConfig: {
            showMenu: true,
            options: {
                showRefresh: true,
                showReconnect: false,
                showConfigure: false,
                showCancelRequest: false,
                showDisconnect: true,
            },
        },
        action: {
            text: 'Manage',
            actionKey: 'onManage',
            className: 'secondary-button',
        },
    },
    UNINSTALLED: {
        text: 'Disconnected',
        classes: `${BASE_STATUS_CLASSES} bg-red-100 text-red-800`,
        statusIcon: <PlugIcon />,
        menuConfig: {
            showMenu: true,
            options: {
                showRefresh: false,
                showReconnect: true,
                showConfigure: false,
                showCancelRequest: false,
                showDisconnect: false,
            },
        },
        action: {
            text: 'Reconnect',
            actionKey: 'onReconnect',
            className: 'primary-button',
        },
    },
    SUSPENDED: {
        text: 'Suspended',
        classes: `${BASE_STATUS_CLASSES} bg-orange-100 text-orange-800`,
        statusIcon: <PlugIcon />,
        menuConfig: {
            showMenu: true,
            options: {
                showRefresh: false,
                showReconnect: true,
                showConfigure: false,
                showCancelRequest: false,
                showDisconnect: false,
            },
        },
        action: {
            text: 'Reconnect',
            actionKey: 'onReconnect',
            className: 'primary-button',
        },
    },
    REJECTED: {
        text: 'Rejected',
        classes: `${BASE_STATUS_CLASSES} bg-red-100 text-red-800`,
        statusIcon: <PlugIcon />,
        menuConfig: {
            showMenu: true,
            options: {
                showRefresh: false,
                showReconnect: true,
                showConfigure: false,
                showCancelRequest: false,
                showDisconnect: false,
            },
        },
        action: {
            text: 'Reconnect',
            actionKey: 'onReconnect',
            className: 'primary-button',
        },
    },
    CONNECTION_EXPIRED: {
        text: 'Connection Expired',
        classes: `${BASE_STATUS_CLASSES} bg-[#FFDFDF] text-[#991010]`,
        statusIcon: <ErrorCircle width={14} height={14} color="#991010" />,
        menuConfig: {
            showMenu: false,
        },
        action: {
            text: 'Reconnect',
            actionKey: 'onReconnect',
            className: 'primary-button',
        },
    },
    '404': {
        text: '',
        menuConfig: {
            showMenu: false,
        },
        action: {
            text: 'Connect',
            actionKey: 'onConnect',
            className: 'primary-button',
        },
    },
};

// =============================================================================
// HELPER COMPONENTS
// =============================================================================

const ActionMenuItem: React.FC<ActionMenuItemProps> = ({
    icon,
    label,
    onClick,
    className = '',
    labelClassName = '',
}) => (
    <div
        role="button"
        onClick={onClick}
        className={`flex w-full p-[12px] items-center gap-2 cursor-pointer hover:bg-gray-50 ${className}`}>
        <div className="flex w-[20px] h-[20px] justify-center items-center flex-shrink-0">
            {icon}
        </div>
        <span
            className={`font-inter text-[16px] p-[4px] font-semibold leading-6 ${labelClassName}`}>
            {label}
        </span>
    </div>
);

const ActionMenu: React.FC<ActionMenuProps> = ({
    isOpen,
    onClose,
    currentStatus,
    ...actionHandlers
}) => {
    if (!isOpen) return null;

    const menuActions = MENU_ACTIONS.filter(menuAction => {
        const showKey = menuAction.key as keyof ActionMenuProps;
        return (actionHandlers as any)[showKey];
    });

    return (
        <>
            <div className="fixed inset-0 z-40" onClick={onClose} />
            <div className="absolute right-0 w-[264px] flex flex-col items-start rounded-[16px] border-[0.5px] border-[#D9D9D9] bg-white z-50 shadow-[0px_-9px_50px_0px_rgba(0,0,0,0.06),0px_374px_105px_0px_rgba(130,130,130,0.00),0px_239px_96px_0px_rgba(130,130,130,0.01),0px_135px_81px_0px_rgba(130,130,130,0.05),0px_60px_60px_0px_rgba(130,130,130,0.09),0px_15px_33px_0px_rgba(130,130,130,0.10)]">
                {menuActions.map(menuAction => {
                    const handler = (actionHandlers as any)[menuAction.action];
                    const label =
                        typeof menuAction.label === 'function'
                            ? menuAction.label(currentStatus)
                            : menuAction.label;

                    return (
                        <ActionMenuItem
                            key={menuAction.key}
                            icon={menuAction.icon}
                            label={label}
                            onClick={handler}
                            className={menuAction.className}
                            labelClassName={
                                (menuAction as any).labelClassName || ''
                            }
                        />
                    );
                })}
            </div>
        </>
    );
};

// =============================================================================
// MAIN COMPONENT
// =============================================================================

export const IntegrationCard: React.FC<IntegrationCardProps> = ({
    integrationIcon,
    title,
    description,
    details,
    status = '404',
    isAzureDevOps = false,
    installation_type,
    installationId,
    target_name,
    onConnect,
    onViewStatus,
    onManage,
    onRefresh,
    onReconnect,
    onConfigure,
    onCancelRequest,
    onDisconnect,
}) => {
    const [isExpanded, setIsExpanded] = useState(false);
    const [isMenuOpen, setIsMenuOpen] = useState(false);

    // Get current status configuration
    const currentStatus = status ?? 'ACTIVE';
    const statusConfig =
        STATUS_CONFIGS[currentStatus as StatusKey] ?? STATUS_CONFIGS.ACTIVE;
    const {text, classes, menuConfig, action, statusIcon} = statusConfig;

    // Azure DevOps special handling
    const showMenu = isAzureDevOps ? false : menuConfig.showMenu;
    const showActionButton = isAzureDevOps
        ? currentStatus !== 'ACTIVE' && onConnect
        : action;

    // Action handlers with menu close
    const createMenuHandler = (handler?: () => void) => () => {
        handler?.();
        setIsMenuOpen(false);
    };

    const menuHandlers = {
        onRefresh: createMenuHandler(onRefresh),
        onReconnect: createMenuHandler(onReconnect),
        onConfigure: createMenuHandler(onConfigure),
        onCancelRequest: createMenuHandler(onCancelRequest),
        onDisconnect: createMenuHandler(onDisconnect),
    };

    // Get action button handler
    const getActionHandler = () => {
        if (isAzureDevOps && currentStatus !== 'ACTIVE') {
            return onConnect;
        }
        const actionMap = {
            onConnect,
            onViewStatus,
            onManage,
            onReconnect,
            onCancelRequest,
            onDisconnect,
        };
        return actionMap[action.actionKey as keyof typeof actionMap];
    };

    const handleStatusClick = () => {
        if (currentStatus === '404' && onConnect) {
            onConnect();
        }
    };

    return (
        <div className="flex w-full sm:w-[450.67px] p-[16px] sm:p-[24px] flex-col items-start gap-[16px] sm:gap-[24px] rounded-[16px] sm:rounded-[24px] border border-[#D9D9D9] bg-white">
            {/* Header Section */}
            <div className="flex justify-between items-center self-stretch">
                <div className="flex items-center gap-[12px]">
                    <img
                        src={integrationIcon}
                        alt={title}
                        className="w-[32px] h-[32px] aspect-square"
                    />
                    <h3 className="text-black text-[24px] font-semibold leading-[130%] truncate">
                        {title}
                    </h3>
                    <span className={classes} onClick={handleStatusClick}>
                        {statusIcon}
                        {text}
                    </span>
                </div>

                {/* Menu button - hide for Azure DevOps and when installation details are missing */}
                {!isAzureDevOps &&
                    showMenu &&
                    installation_type &&
                    installationId &&
                    target_name && (
                        <div className="relative">
                            <div
                                className="w-[24px] h-[24px] object-contain cursor-pointer"
                                onClick={() => setIsMenuOpen(!isMenuOpen)}>
                                <DotsHorizontalIcon
                                    width={24}
                                    height={24}
                                    fill={isMenuOpen ? '#000000' : '#999999'}
                                />
                            </div>
                            <ActionMenu
                                isOpen={isMenuOpen}
                                onClose={() => setIsMenuOpen(false)}
                                currentStatus={currentStatus}
                                {...menuHandlers}
                                {...menuConfig.options}
                            />
                        </div>
                    )}
            </div>

            {/* Description and Details Section */}
            <div className="flex flex-col items-start gap-[12px] self-stretch">
                <p className="text-[#999] text-[16px] font-normal leading-[150%] tracking-[-0.3px]">
                    {description}
                </p>

                <div className="flex justify-between items-center self-stretch">
                    <span className="text-[#999] text-[16px] font-normal leading-[150%] tracking-[-0.3px]">
                        What will it access?
                    </span>
                    <div
                        onClick={() => setIsExpanded(!isExpanded)}
                        className="flex w-[24px] h-[24px] justify-center items-center cursor-pointer">
                        {isExpanded ? (
                            <ChevronUpIcon className="w-[24px] h-[24px] text-[#999]" />
                        ) : (
                            <ChevronDownIcon className="w-[24px] h-[24px] text-[#999]" />
                        )}
                    </div>
                </div>

                {isExpanded && (
                    <div className="flex flex-col items-start self-stretch">
                        {details.map((detail, index) => (
                            <div key={index} className="flex items-start gap-2">
                                <span className="text-[#999] text-[16px] font-normal leading-[150%] tracking-[-0.3px]">
                                    •
                                </span>
                                <span className="text-[#999] text-[16px] font-normal leading-[150%] tracking-[-0.3px]">
                                    {detail}
                                </span>
                            </div>
                        ))}
                    </div>
                )}
            </div>

            {/* Action Button Section */}
            <div className="flex items-center gap-[12px] justify-end self-stretch">
                {showActionButton && (
                    <button
                        onClick={getActionHandler()}
                        className={
                            isAzureDevOps
                                ? 'primary-button'
                                : showActionButton.className
                        }>
                        {isAzureDevOps ? 'Connect' : showActionButton.text}
                    </button>
                )}
            </div>
        </div>
    );
};
