type PropsType = {
    width?: number;
    height?: number;
    fill?: string;
};

export default function CheckIcon({
    width = 24,
    height = 24,
    fill = '#5B39F3',
}: PropsType) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g id="Icon / check">
                <path
                    id="icon"
                    d="M9.99997 15.5001L6.70697 12.3545L5.29297 13.7052L9.99997 18.2015L19.707 8.92907L18.293 7.57837L9.99997 15.5001Z"
                    fill={fill}
                />
            </g>
        </svg>
    );
}
