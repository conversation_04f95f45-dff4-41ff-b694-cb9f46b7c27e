interface CommentAddProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function CommentAdd({
    width = 17,
    height = 16,
    color = 'black',
}: CommentAddProps) {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width={width}
            height={height}
            viewBox="0 0 17 16"
            fill="none">
            <path
                d="M7.66699 9.33301H9.00033V7.33301H11.0003V5.99967H9.00033V3.99967H7.66699V5.99967H5.66699V7.33301H7.66699V9.33301Z"
                fill={color}
            />
            <path
                d="M13.667 1.33301H3.00033C2.26499 1.33301 1.66699 1.93101 1.66699 2.66634V14.6663L5.22233 11.9997H13.667C14.4023 11.9997 15.0003 11.4017 15.0003 10.6663V2.66634C15.0003 1.93101 14.4023 1.33301 13.667 1.33301ZM13.667 10.6663H4.77833L3.00033 11.9997V2.66634H13.667V10.6663Z"
                fill={color}
            />
        </svg>
    );
}
