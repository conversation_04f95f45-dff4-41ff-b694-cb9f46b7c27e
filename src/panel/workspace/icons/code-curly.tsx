interface CodeCurlyProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function CodeCurly({
    width = 20,
    height = 20,
    color = 'white',
}: CodeCurlyProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 20 20"
            fill={color}
            xmlns="http://www.w3.org/2000/svg">
            <g id="Icon / code-curly">
                <path
                    id="icon"
                    d="M7.49978 17.9606H8.33312V16.3685H7.50895C7.25228 16.3637 4.99978 16.2308 4.99978 13.1844C4.99978 11.5979 4.44562 10.6005 3.74812 10.0003C4.44562 9.40009 4.99978 8.40267 4.99978 6.81619C4.99978 3.7698 7.25228 3.63687 7.49978 3.63209H8.33312V2.04004H7.49812C6.05728 2.04322 3.33312 3.04622 3.33312 6.81619C3.33312 9.04507 1.93479 9.19631 1.65479 9.20427L1.66645 10.7963C1.73479 10.7963 3.33312 10.8234 3.33312 13.1844C3.33312 16.9544 6.05728 17.9574 7.49978 17.9606ZM18.3331 9.20427C18.2648 9.20427 16.6665 9.17721 16.6665 6.81619C16.6665 3.04622 13.9423 2.04322 12.4998 2.04004H11.6665V3.63209H12.4906C12.7473 3.63687 14.9998 3.7698 14.9998 6.81619C14.9998 8.40267 15.554 9.40009 16.2515 10.0003C15.554 10.6005 14.9998 11.5979 14.9998 13.1844C14.9998 16.2308 12.7473 16.3637 12.4998 16.3685H11.6665V17.9606H12.5015C13.9423 17.9574 16.6665 16.9544 16.6665 13.1844C16.6665 10.9555 18.0648 10.8043 18.3448 10.7963L18.3331 9.20427Z"
                    fill={color}
                />
            </g>
        </svg>
    );
}
