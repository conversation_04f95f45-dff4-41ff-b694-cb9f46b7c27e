interface ErrorCircleProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function ErrorCircle({
    width = 25,
    height = 25,
    color = 'black',
}: ErrorCircleProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 15 14"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
                d="M7.64576 1.16699C4.44443 1.16699 1.83984 3.78383 1.83984 7.00033C1.83984 10.2168 4.45668 12.8337 7.67318 12.8337C10.8897 12.8337 13.5065 10.2168 13.5065 7.00033C13.5065 3.78383 10.8774 1.16699 7.64576 1.16699ZM7.67318 11.667C5.10009 11.667 3.00651 9.57341 3.00651 7.00033C3.00651 4.42724 5.08726 2.33366 7.64576 2.33366C10.2346 2.33366 12.3398 4.42724 12.3398 7.00033C12.3398 9.57341 10.2463 11.667 7.67318 11.667Z"
                fill="#991010"
            />
            <path
                d="M7.08984 4.08366H8.25651V8.16699H7.08984V4.08366ZM7.08984 8.75033H8.25651V9.91699H7.08984V8.75033Z"
                fill={color}
            />
        </svg>
    );
}
