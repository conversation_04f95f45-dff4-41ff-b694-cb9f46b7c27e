interface CheckCircleProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function CheckCircle({
    width = 25,
    height = 24,
    color = 'white',
}: CheckCircleProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 25 24"
            fill={color}
            xmlns="http://www.w3.org/2000/svg">
            <g id="Icon / check-circle">
                <g id="icon">
                    <path
                        d="M12.25 2.44775C6.736 2.44775 2.25 6.73292 2.25 12.0001C2.25 17.2672 6.736 21.5524 12.25 21.5524C17.764 21.5524 22.25 17.2672 22.25 12.0001C22.25 6.73292 17.764 2.44775 12.25 2.44775ZM12.25 19.6419C7.839 19.6419 4.25 16.2136 4.25 12.0001C4.25 7.78654 7.839 4.35822 12.25 4.35822C16.661 4.35822 20.25 7.78654 20.25 12.0001C20.25 16.2136 16.661 19.6419 12.25 19.6419Z"
                        fill={color}
                    />
                    <path
                        d="M10.249 13.516L7.95002 11.3238L6.53802 12.6764L10.251 16.2155L16.957 9.80975L15.543 8.45905L10.249 13.516Z"
                        fill={color}
                    />
                </g>
            </g>
        </svg>
    );
}
