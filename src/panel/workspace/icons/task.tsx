interface TaskProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function Task({
    width = 17,
    height = 16,
    color = 'black',
}: TaskProps) {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width={width}
            height={height}
            viewBox="0 0 17 16"
            fill="none">
            <path
                d="M3.66683 14.6663H13.0002C13.7355 14.6663 14.3335 14.0683 14.3335 13.333V3.33301C14.3335 2.59767 13.7355 1.99967 13.0002 1.99967H11.6668C11.6668 1.82286 11.5966 1.65329 11.4716 1.52827C11.3465 1.40325 11.177 1.33301 11.0002 1.33301H5.66683C5.49002 1.33301 5.32045 1.40325 5.19543 1.52827C5.0704 1.65329 5.00016 1.82286 5.00016 1.99967H3.66683C2.9315 1.99967 2.3335 2.59767 2.3335 3.33301V13.333C2.3335 14.0683 2.9315 14.6663 3.66683 14.6663ZM3.66683 3.33301H5.00016V4.66634H11.6668V3.33301H13.0002V13.333H3.66683V3.33301Z"
                fill={color}
            />
            <path
                d="M7.66685 9.05699L6.47152 7.86165L5.52885 8.80432L7.66685 10.9423L11.1382 7.47099L10.1955 6.52832L7.66685 9.05699Z"
                fill={color}
            />
        </svg>
    );
}
