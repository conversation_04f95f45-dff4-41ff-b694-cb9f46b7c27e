interface GitMergeProps {
    readonly width?: number;
    readonly height?: number;
    readonly color?: string;
}

export default function GitMerge({
    width = 20,
    height = 20,
    color = 'white',
}: GitMergeProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g id="Icon / git-merge">
                <path
                    id="icon"
                    d="M2.08398 15.1745C2.08398 16.7108 3.39232 17.9606 5.00065 17.9606C6.60898 17.9606 7.91732 16.7108 7.91732 15.1745C7.91732 13.9167 7.03398 12.8636 5.83398 12.5181V9.74159C6.03732 9.9589 6.25815 10.1611 6.51148 10.3338C7.49482 11.0057 8.69232 11.2079 9.77648 11.2079C10.5815 11.2079 11.3207 11.0964 11.8648 10.9842C12.0734 11.5126 12.4453 11.9681 12.9311 12.2901C13.4168 12.6121 13.9936 12.7854 14.5848 12.7872C16.1932 12.7872 17.5015 11.5374 17.5015 10.0011C17.5015 8.46476 16.1932 7.215 14.5848 7.215C13.9256 7.21636 13.2863 7.43098 12.7712 7.82389C12.256 8.2168 11.8954 8.76482 11.7482 9.3786C10.6673 9.63412 8.69482 9.86895 7.48232 9.03949C6.92732 8.65979 6.57398 8.05322 6.39398 7.25958C7.29648 6.78435 7.91732 5.87768 7.91732 4.82613C7.91732 3.2898 6.60898 2.04004 5.00065 2.04004C3.39232 2.04004 2.08398 3.2898 2.08398 4.82613C2.08398 6.08385 2.96732 7.13699 4.16732 7.48247V12.5181C2.96732 12.8636 2.08398 13.9167 2.08398 15.1745ZM14.584 8.80626C15.2732 8.80626 15.834 9.34198 15.834 10.0003C15.834 10.6586 15.2732 11.1943 14.584 11.1943C13.8948 11.1943 13.334 10.6586 13.334 10.0003C13.334 9.34198 13.8948 8.80626 14.584 8.80626ZM6.25065 15.1745C6.25065 15.8328 5.68982 16.3685 5.00065 16.3685C4.31148 16.3685 3.75065 15.8328 3.75065 15.1745C3.75065 14.5161 4.31148 13.9804 5.00065 13.9804C5.68982 13.9804 6.25065 14.5161 6.25065 15.1745ZM3.75065 4.82613C3.75065 4.16782 4.31148 3.63209 5.00065 3.63209C5.68982 3.63209 6.25065 4.16782 6.25065 4.82613C6.25065 5.48444 5.68982 6.02017 5.00065 6.02017C4.31148 6.02017 3.75065 5.48444 3.75065 4.82613Z"
                    fill={color}
                />
            </g>
        </svg>
    );
}
