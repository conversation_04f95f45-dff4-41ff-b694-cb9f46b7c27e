interface CodeCurlyProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function Code({
    width = 20,
    height = 20,
    color = '#5B39F3',
}: CodeCurlyProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
                d="M6.91013 5.45703L2.1543 9.99995L6.91013 14.5429L8.08846 13.4173L4.51096 9.99995L8.08846 6.58261L6.91013 5.45703ZM13.0885 14.5429L17.8443 9.99995L13.0885 5.45703L11.9101 6.58261L15.4876 9.99995L11.9101 13.4173L13.0885 14.5429Z"
                fill={color}
            />
        </svg>
    );
}
