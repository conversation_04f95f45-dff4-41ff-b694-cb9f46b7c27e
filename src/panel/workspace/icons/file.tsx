interface FileProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function File({
    width = 25,
    height = 25,
    color = 'black',
}: FileProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 25 25"
            fill={color}
            xmlns="http://www.w3.org/2000/svg">
            <path
                d="M19.9284 8.91901C19.881 8.81048 19.8146 8.71126 19.7324 8.62601L13.7324 2.62601C13.6471 2.54379 13.5479 2.47742 13.4394 2.43001C13.4094 2.41601 13.3774 2.40801 13.3454 2.39701C13.2617 2.36853 13.1746 2.35138 13.0864 2.34601C13.0654 2.34401 13.0464 2.33301 13.0254 2.33301H6.02539C4.92239 2.33301 4.02539 3.23001 4.02539 4.33301V20.333C4.02539 21.436 4.92239 22.333 6.02539 22.333H18.0254C19.1284 22.333 20.0254 21.436 20.0254 20.333V9.33301C20.0254 9.31201 20.0144 9.29301 20.0124 9.27101C20.0075 9.18272 19.9903 9.09555 19.9614 9.01201C19.9514 8.98001 19.9424 8.94901 19.9284 8.91901ZM16.6114 8.33301H14.0254V5.74701L16.6114 8.33301ZM6.02539 20.333V4.33301H12.0254V9.33301C12.0254 9.59822 12.1307 9.85258 12.3183 10.0401C12.5058 10.2277 12.7602 10.333 13.0254 10.333H18.0254L18.0274 20.333H6.02539Z"
                fill={color}
            />
            <path
                d="M8.02539 12.333H16.0254V14.333H8.02539V12.333ZM8.02539 16.333H16.0254V18.333H8.02539V16.333ZM8.02539 8.33301H10.0254V10.333H8.02539V8.33301Z"
                fill={color}
            />
        </svg>
    );
}
