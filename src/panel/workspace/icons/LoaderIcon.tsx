interface IconLoaderProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function IconLoader({
    width = 14,
    height = 14,
    color = '#B23AF2',
}: IconLoaderProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 14 14"
            fill={color}
            xmlns="http://www.w3.org/2000/svg">
            <g id="Icon / loader">
                <path
                    id="icon"
                    d="M1.1665 6.4165H4.08317V7.58317H1.1665V6.4165ZM9.9165 6.4165H12.8332V7.58317H9.9165V6.4165ZM6.4165 9.9165H7.58317V12.8332H6.4165V9.9165ZM6.4165 1.1665H7.58317V4.08317H6.4165V1.1665ZM2.46267 3.2875L3.2875 2.46267L5.35017 4.52534L4.52534 5.35017L2.46267 3.2875ZM11.537 10.7122L10.7122 11.537L8.6495 9.47434L9.47434 8.6495L11.537 10.7122ZM4.52534 8.6495L5.35017 9.47434L3.2875 11.537L2.46267 10.7122L4.52534 8.6495ZM8.6495 4.52475L10.7122 2.46267L11.537 3.28809L9.47434 5.35017L8.6495 4.52475Z"
                    fill="#B23AF2"
                />
            </g>
        </svg>
    );
}
