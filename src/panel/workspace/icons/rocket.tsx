import React from 'react';

interface RocketIconProps {
    size?: number;
    color?: string;
}

export const Rocket: React.FC<RocketIconProps> = ({
    size = 24,
    color = '#5B39F3',
}) => {
    return (
        <svg
            width={size}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
                d="M20.9201 2.81049C19.7974 2.57031 18.6506 2.44858 17.5001 2.4475C16.4136 2.3816 15.3241 2.52153 14.2944 2.85923C13.2647 3.19693 12.3151 3.72575 11.5001 4.41527C10.4267 5.44055 9.36341 6.46265 8.31008 7.48156C7.10008 7.35738 4.23008 7.29052 2.31008 9.14367C2.12383 9.32264 2.01929 9.56474 2.01929 9.8171C2.01929 10.0695 2.12383 10.3116 2.31008 10.4905L13.6101 21.3038C13.7974 21.4817 14.0509 21.5815 14.3151 21.5815C14.5793 21.5815 14.8327 21.4817 15.0201 21.3038C16.9701 19.3933 16.9101 16.6995 16.7901 15.5724L20.0001 12.5156C23.1901 9.46844 21.7401 3.74661 21.6801 3.5078C21.6339 3.33551 21.5383 3.17896 21.404 3.05576C21.2698 2.93256 21.1022 2.84761 20.9201 2.81049ZM18.5601 11.1688L15.0001 14.5503C14.8848 14.6615 14.7999 14.798 14.7528 14.9479C14.7057 15.0979 14.6979 15.2565 14.7301 15.41C14.9461 16.6879 14.7581 17.9983 14.1901 19.1736L4.52008 9.9174C5.76993 9.36792 7.16709 9.2011 8.52008 9.43979C8.68016 9.4597 8.8429 9.44229 8.9944 9.38906C9.1459 9.33583 9.28166 9.24836 9.39008 9.13411C9.39008 9.13411 10.7901 7.74903 12.9001 5.73349C14.1975 4.73825 15.8402 4.24705 17.5001 4.35796C18.2809 4.36165 19.0601 4.42554 19.8301 4.549C20.0701 5.91498 20.4501 9.36337 18.5601 11.1688Z"
                fill={color}
            />
            <path
                d="M15.73 10.376C16.8346 10.376 17.73 9.52062 17.73 8.4655C17.73 7.41038 16.8346 6.55504 15.73 6.55504C14.6255 6.55504 13.73 7.41038 13.73 8.4655C13.73 9.52062 14.6255 10.376 15.73 10.376Z"
                fill={color}
            />
            <path
                d="M5.00006 15.8207C3.00006 16.776 3.00006 20.5969 3.00006 20.5969C4.85113 20.5464 6.62321 19.8693 8.00006 18.6864L5.00006 15.8207Z"
                fill={color}
            />
        </svg>
    );
};
