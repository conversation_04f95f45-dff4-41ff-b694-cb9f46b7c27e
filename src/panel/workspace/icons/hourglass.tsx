interface HourglassProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function Hourglass({
    width = 14,
    height = 14,
    color = '#5F1616',
}: HourglassProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 15 14"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
                d="M9.42262 6.42924C10.0321 6.06738 10.5371 5.55345 10.8882 4.93777C11.2394 4.32209 11.4246 3.62577 11.4258 2.91699V2.33366H12.0091V1.16699H2.67578V2.33366H3.25911V2.91699C3.2603 3.62577 3.44553 4.32209 3.79668 4.93777C4.14782 5.55345 4.65284 6.06738 5.26228 6.42924C5.46878 6.55058 5.59245 6.74716 5.59245 6.95424V7.04641C5.59245 7.25291 5.46878 7.44891 5.26228 7.57141C4.65284 7.93327 4.14782 8.4472 3.79668 9.06288C3.44553 9.67856 3.2603 10.3749 3.25911 11.0837V11.667H2.67578V12.8337H12.0091V11.667H11.4258V11.0837C11.4247 10.375 11.2395 9.67881 10.8885 9.06323C10.5374 8.44764 10.0325 7.93379 9.4232 7.57199C9.21612 7.44949 9.09245 7.25291 9.09245 7.04641V6.95424C9.09245 6.74716 9.21611 6.55058 9.42262 6.42924ZM8.83053 8.57649C9.26533 8.83496 9.62557 9.20189 9.87599 9.64138C10.1264 10.0809 10.2584 10.5778 10.2591 11.0837V11.667H4.42578V11.0837C4.42655 10.5777 4.55865 10.0806 4.80917 9.64103C5.05968 9.20145 5.42003 8.83444 5.85495 8.57591C6.42136 8.24166 6.75911 7.66941 6.75911 7.04641V6.95424C6.75911 6.33066 6.42078 5.75783 5.85437 5.42416C5.41956 5.16569 5.05932 4.79876 4.80891 4.35927C4.55849 3.91979 4.42647 3.42281 4.42578 2.91699V2.33366H10.2591V2.91699C10.2591 3.94366 9.71195 4.90383 8.83053 5.42416C8.26411 5.75783 7.92578 6.33066 7.92578 6.95424V7.04641C7.92578 7.66941 8.26353 8.24166 8.83053 8.57649Z"
                fill={color}
            />
        </svg>
    );
}
