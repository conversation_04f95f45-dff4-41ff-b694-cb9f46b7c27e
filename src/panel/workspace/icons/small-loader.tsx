interface SmallLoaderProps {
    width?: number;
    height?: number;
}

export default function SmallLoader({
    width = 24,
    height = 24,
}: SmallLoaderProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
                d="M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12ZM5 12C5 15.866 8.13401 19 12 19C15.866 19 19 15.866 19 12C19 8.13401 15.866 5 12 5C8.13401 5 5 8.13401 5 12Z"
                fill="#D4CBFC"
            />
            <path
                d="M5.98959 5.98959C5.40381 5.4038 5.39828 4.44331 6.06503 3.95164C6.71546 3.472 7.42313 3.07188 8.17317 2.7612C9.38642 2.25866 10.6868 2 12 2C13.3132 2 14.6136 2.25866 15.8268 2.76121C16.5769 3.07188 17.2845 3.47201 17.935 3.95164C18.6017 4.44331 18.5962 5.40381 18.0104 5.98959V5.98959C17.4246 6.57538 16.4809 6.55872 15.784 6.1109C15.4343 5.88625 15.0646 5.69264 14.6788 5.53284C13.8295 5.18106 12.9193 5 12 5C11.0807 5 10.1705 5.18106 9.32122 5.53284C8.93543 5.69264 8.56566 5.88624 8.21603 6.11089C7.51907 6.55871 6.57538 6.57538 5.98959 5.98959V5.98959Z"
                fill="#5B39F3"
            />
        </svg>
    );
}
