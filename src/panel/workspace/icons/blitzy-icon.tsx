interface BlitzyIconProps {
    width?: number | string;
    height?: number | string;
    color?: string;
}

export default function BlitzyIcon({
    width = 12,
    height = 18,
    color = 'white',
}: BlitzyIconProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 12 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M0.208984 4.08167C0.208984 2.19583 1.73782 0.666992 3.62366 0.666992H7.73982C11.3417 0.666992 13.1463 5.02283 10.5988 7.57039L10.1203 8.04883C9.75724 8.41193 9.16855 8.41192 8.80545 8.04883C8.44236 7.68573 8.44236 7.09704 8.80545 6.73394L9.28389 6.2555C10.6601 4.87934 9.68501 2.52652 7.73982 2.52652H3.62366C2.76481 2.52652 2.06851 3.22282 2.06851 4.08167V13.919C2.06851 14.7778 2.76481 15.4741 3.62366 15.4741H7.73982C9.6856 15.4741 10.6603 13.1215 9.28389 11.7451L8.80545 11.2667C8.44236 10.9036 8.44236 10.3149 8.80545 9.95182C9.16855 9.58873 9.75724 9.58873 10.1203 9.95182L10.5988 10.4303C13.1461 12.9776 11.3429 17.3337 7.73982 17.3337H3.62366C1.73782 17.3337 0.208984 15.8048 0.208984 13.919V4.08167ZM3.53651 4.53767C3.89961 4.17457 4.4883 4.17457 4.8514 4.53767L8.65705 8.34332C8.83141 8.51768 8.92937 8.75417 8.92937 9.00076C8.92937 9.24735 8.83141 9.48384 8.65705 9.65821L4.8514 13.4639C4.4883 13.827 3.89961 13.827 3.53651 13.4639C3.17342 13.1008 3.17342 12.5121 3.53651 12.149L6.68472 9.00076L3.53651 5.85256C3.17342 5.48946 3.17342 4.90077 3.53651 4.53767Z"
                fill={color}
            />
        </svg>
    );
}
