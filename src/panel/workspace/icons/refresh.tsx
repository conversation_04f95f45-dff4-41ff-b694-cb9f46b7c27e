interface RefreshProps {
    readonly color?: string;
    readonly width?: number;
    readonly height?: number;
}

export default function Refresh({
    color = 'black',
    width = 24,
    height = 24,
}: RefreshProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g id="Icon / refresh">
                <path
                    id="icon"
                    d="M10 11.0447H7.101L7.102 11.0361C7.23257 10.4254 7.48813 9.8453 7.854 9.32914C8.39845 8.56287 9.16215 7.96236 10.054 7.59922C10.356 7.47695 10.671 7.38334 10.992 7.32125C11.6579 7.19229 12.3441 7.19229 13.01 7.32125C13.967 7.50856 14.8451 7.96069 15.535 8.62132L16.951 7.27253C16.3128 6.66275 15.5578 6.17597 14.727 5.83873C14.3033 5.66739 13.8628 5.53683 13.412 5.44899C12.4818 5.26847 11.5232 5.26847 10.593 5.44899C10.1419 5.5372 9.70101 5.66807 9.277 5.83968C8.02753 6.34605 6.95793 7.18665 6.197 8.26024C5.68489 8.98416 5.32676 9.7972 5.143 10.6531C5.115 10.782 5.1 10.9139 5.08 11.0447H2L6 14.8657L10 11.0447ZM14 12.9552H16.899L16.898 12.9628C16.6367 14.1872 15.8812 15.2641 14.795 15.9604C14.2548 16.3101 13.6475 16.5543 13.008 16.6787C12.3424 16.8077 11.6566 16.8077 10.991 16.6787C10.3516 16.554 9.74435 16.3098 9.204 15.9604C8.93862 15.7889 8.69085 15.5938 8.464 15.3777L7.05 16.7284C7.68851 17.338 8.44392 17.8244 9.275 18.1612C9.699 18.3331 10.142 18.464 10.59 18.5509C11.5198 18.7316 12.4782 18.7316 13.408 18.5509C15.2005 18.1956 16.7773 17.1863 17.803 15.7378C18.3146 15.0144 18.6724 14.202 18.856 13.3468C18.883 13.2179 18.899 13.0861 18.919 12.9552H22L18 9.13428L14 12.9552Z"
                    fill={color}
                />
            </g>
        </svg>
    );
}
