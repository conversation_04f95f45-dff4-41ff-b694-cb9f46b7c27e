interface WarningIconProps {
    width?: number;
    height?: number;
    color?: string;
    className?: string;
}

export default function WarningIcon({
    width = 20,
    height = 20,
    color = 'currentColor',
    className = '',
}: WarningIconProps) {
    return (
        <svg
            width={width}
            height={height}
            fill="none"
            stroke={color}
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
            className={className}>
            <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
        </svg>
    );
}
