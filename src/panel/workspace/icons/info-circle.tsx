interface InfoCircleProps {
    readonly width?: number;
    readonly height?: number;
    readonly color?: string;
}

export default function InfoCircle({
    width = 20,
    height = 20,
    color = '#5B39F3',
}: InfoCircleProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g id="Icon / info-circle">
                <g id="icon">
                    <path
                        d="M10.0003 1.6665C5.40533 1.6665 1.66699 5.40484 1.66699 9.99984C1.66699 14.5948 5.40533 18.3332 10.0003 18.3332C14.5953 18.3332 18.3337 14.5948 18.3337 9.99984C18.3337 5.40484 14.5953 1.6665 10.0003 1.6665ZM10.0003 16.6665C6.32449 16.6665 3.33366 13.6757 3.33366 9.99984C3.33366 6.324 6.32449 3.33317 10.0003 3.33317C13.6762 3.33317 16.667 6.324 16.667 9.99984C16.667 13.6757 13.6762 16.6665 10.0003 16.6665Z"
                        fill={color}
                    />
                    <path
                        d="M9.16699 9.1665H10.8337V14.1665H9.16699V9.1665ZM9.16699 5.83317H10.8337V7.49984H9.16699V5.83317Z"
                        fill={color}
                    />
                </g>
            </g>
        </svg>
    );
}
