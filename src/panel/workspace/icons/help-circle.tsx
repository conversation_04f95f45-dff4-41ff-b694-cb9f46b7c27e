interface HelpCircleIconProps {
    className?: string;
    isActive?: boolean;
    variant?: 'default' | 'black';
}

export const HelpCircleIcon: React.FC<HelpCircleIconProps> = ({
    className,
    isActive,
    variant = 'default',
}) => {
    const fillColor =
        variant === 'black'
            ? 'fill-black'
            : isActive
              ? 'fill-black'
              : 'fill-[#666666] group-hover:fill-black';

    return (
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={`group ${className ?? ''}`}>
            <g id="Button/Icon/help-circle">
                <g id="icon">
                    <path
                        d="M12 6C10.957 6.00132 9.95716 6.41622 9.21968 7.1537C8.4822 7.89118 8.0673 8.89104 8.06598 9.934H10.066C10.066 8.867 10.934 8 12 8C13.066 8 13.934 8.867 13.934 9.934C13.934 10.532 13.453 10.966 12.718 11.56C12.4779 11.7481 12.2472 11.948 12.027 12.159C11.029 13.156 11 14.215 11 14.333V15H13L12.999 14.367C13 14.351 13.032 13.981 13.44 13.574C13.59 13.424 13.779 13.274 13.975 13.116C14.754 12.485 15.933 11.532 15.933 9.934C15.9322 8.89106 15.5176 7.89104 14.7802 7.15347C14.0428 6.4159 13.0429 6.00106 12 6ZM11 16H13V18H11V16Z"
                        className={`${fillColor} transition-colors`}
                    />
                    <path
                        d="M12 2C6.486 2 2 6.486 2 12C2 17.514 6.486 22 12 22C17.514 22 22 17.514 22 12C22 6.486 17.514 2 12 2ZM12 20C7.589 20 4 16.411 4 12C4 7.589 7.589 4 12 4C16.411 4 20 7.589 20 12C20 16.411 16.411 20 12 20Z"
                        className={`${fillColor} transition-colors`}
                    />
                </g>
            </g>
        </svg>
    );
};
