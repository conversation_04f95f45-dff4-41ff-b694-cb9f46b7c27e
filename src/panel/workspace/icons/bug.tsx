interface BugProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function Bug({
    width = 17,
    height = 16,
    color = 'black',
}: BugProps) {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width={width}
            height={height}
            viewBox="0 0 17 16"
            fill="none">
            <path
                d="M11.93 4.34565L13.8053 2.47099L12.8627 1.52832L10.9653 3.42565C10.8114 3.31443 10.6504 3.21333 10.4833 3.12299C9.92446 2.82408 9.30046 2.6677 8.66667 2.6677C8.03288 2.6677 7.40888 2.82408 6.85 3.12299C6.68333 3.21099 6.52467 3.31432 6.36867 3.42499L4.47133 1.52832L3.52867 2.47099L5.404 4.34565C5.01357 4.84275 4.70935 5.40185 4.504 5.99965H2V7.33299H3.538C3.49467 7.66299 3.46667 7.99765 3.46667 8.33299C3.46667 8.67099 3.49467 9.00832 3.538 9.34032H2V10.6737H3.83533C3.844 10.6997 3.84933 10.727 3.858 10.7523C3.98333 11.1223 4.13867 11.481 4.32133 11.819C4.35067 11.873 4.38467 11.9223 4.41533 11.975L2.862 13.5283L3.80467 14.471L5.21133 13.0637C5.60361 13.502 6.07036 13.8674 6.59 14.143C6.90933 14.3117 7.24467 14.4423 7.58733 14.5297C8.29516 14.7114 9.0374 14.7116 9.74533 14.5303C10.0918 14.4412 10.4266 14.3114 10.7427 14.1437C11.0527 13.9797 11.352 13.777 11.6313 13.541C11.8033 13.3957 11.9647 13.233 12.1207 13.0637L13.528 14.471L14.4707 13.5283L12.9173 11.975C12.9487 11.9217 12.9827 11.8717 13.012 11.8177C13.194 11.481 13.35 11.1223 13.4747 10.7517C13.4833 10.7257 13.4887 10.699 13.4973 10.673H15.3333V9.33965H13.7947C13.838 9.00699 13.866 8.67032 13.866 8.33232C13.866 7.99699 13.838 7.66232 13.7953 7.33232H15.3333V5.99965H12.8293C12.624 5.402 12.32 4.84294 11.93 4.34565ZM6.454 5.16499C6.75067 4.79299 7.094 4.50299 7.47267 4.30232C7.83946 4.10457 8.24963 4.00105 8.66633 4.00105C9.08304 4.00105 9.49321 4.10457 9.86 4.30232C10.2559 4.51664 10.6032 4.81058 10.88 5.16565C11.0793 5.41432 11.24 5.69899 11.382 5.99965H5.95133C6.094 5.69899 6.254 5.41365 6.454 5.16499ZM12.5333 8.33299C12.5333 8.68099 12.5053 9.02899 12.4493 9.36832C12.3967 9.69499 12.3167 10.017 12.2127 10.3257C12.113 10.6219 11.9883 10.9092 11.84 11.1843C11.6991 11.4448 11.5356 11.6924 11.3513 11.9243C11.1733 12.1463 10.978 12.3483 10.772 12.523C10.5653 12.697 10.346 12.8457 10.1193 12.9657C9.89267 13.0857 9.656 13.177 9.41467 13.239C9.388 13.2457 9.36 13.2483 9.33333 13.2543V9.33299H8V13.2537C7.97333 13.2477 7.94533 13.245 7.91867 13.2383C7.678 13.1763 7.44067 13.085 7.214 12.965C6.98733 12.845 6.768 12.6963 6.562 12.5223C6.34951 12.3418 6.15504 12.1411 5.98133 11.923C5.79709 11.6919 5.63399 11.4446 5.494 11.1843C5.34559 10.9089 5.2207 10.6214 5.12067 10.325C5.01556 10.0124 4.93619 9.69181 4.88333 9.36632C4.77377 8.69308 4.77242 8.00666 4.87933 7.33299H12.4533C12.506 7.66032 12.5333 7.99632 12.5333 8.33299Z"
                fill={color}
            />
        </svg>
    );
}
