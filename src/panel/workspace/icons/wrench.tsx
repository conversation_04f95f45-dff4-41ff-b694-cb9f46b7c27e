interface SkipProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function Wrench({
    width = 24,
    height = 22,
    color = 'black',
}: SkipProps) {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width={width}
            height={height}
            viewBox="0 0 16 16"
            fill="none">
            <path
                d="M3.41512 13.9997C3.66712 14.2517 4.00179 14.3903 4.35779 14.3903C4.71379 14.3903 5.04846 14.2517 5.30046 13.9997L8.19112 11.109C8.66987 11.2567 9.16811 11.3315 9.66912 11.331C10.3258 11.333 10.9763 11.2047 11.5831 10.9535C12.1898 10.7023 12.7407 10.3333 13.2038 9.86768C13.9029 9.17052 14.3795 8.2816 14.5732 7.31346C14.7668 6.34532 14.6689 5.34148 14.2918 4.42901L13.9118 3.50368L11.0825 6.33235L9.66846 4.91768L12.4971 2.08901L11.5711 1.70835C10.967 1.45964 10.3198 1.33211 9.66646 1.33301C8.33112 1.33301 7.07579 1.85301 6.13246 2.79701C5.48778 3.43955 5.03129 4.24631 4.81247 5.12981C4.59365 6.01332 4.62085 6.93987 4.89112 7.80901L2.00046 10.6997C1.75066 10.9498 1.61035 11.2888 1.61035 11.6423C1.61035 11.9958 1.75066 12.3349 2.00046 12.585L3.41512 13.9997ZM6.44712 8.13901L6.27779 7.72835C6.0009 7.05925 5.92886 6.32302 6.07081 5.61294C6.21275 4.90287 6.56229 4.25091 7.07512 3.73968C7.45623 3.35609 7.91802 3.06223 8.42692 2.87945C8.93582 2.69667 9.47904 2.62956 10.0171 2.68301L7.78179 4.91835L11.0811 8.21835L13.3178 5.98168C13.37 6.52009 13.3021 7.06336 13.1192 7.57241C12.9362 8.08147 12.6427 8.54364 12.2598 8.92568C11.2265 9.95901 9.61712 10.277 8.27046 9.72301L7.86046 9.55301L4.35779 13.057H4.35846L4.35779 13.7237V13.057L2.94312 11.6423L6.44712 8.13901Z"
                fill={color}
            />
        </svg>
    );
}
