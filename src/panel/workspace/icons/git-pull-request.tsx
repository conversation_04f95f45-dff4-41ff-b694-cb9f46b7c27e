interface GitPullRequestProps {
    readonly width?: number;
    readonly height?: number;
    readonly color?: string;
}

export default function GitPullRequest({
    width = 14,
    height = 14,
    color = '#5B39F3',
}: GitPullRequestProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 14 14"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g id="Icon / git-pull-request">
                <path
                    id="icon"
                    d="M11.0898 8.84492V4.66475C11.0869 3.72792 10.4616 2.33317 8.75065 2.33317V1.1665L6.41732 2.9165L8.75065 4.6665V3.49984C9.81757 3.49984 9.9179 4.39759 9.92315 4.6665V8.84492C9.08315 9.09809 8.46482 9.86984 8.46482 10.7915C8.46482 11.9173 9.38065 12.8332 10.5065 12.8332C11.6323 12.8332 12.5482 11.9173 12.5482 10.7915C12.5482 9.86984 11.9298 9.09809 11.0898 8.84492ZM10.5065 11.6665C10.0241 11.6665 9.63148 11.2739 9.63148 10.7915C9.63148 10.3091 10.0241 9.9165 10.5065 9.9165C10.9889 9.9165 11.3815 10.3091 11.3815 10.7915C11.3815 11.2739 10.9889 11.6665 10.5065 11.6665ZM5.54232 3.20817C5.54232 2.08234 4.62648 1.1665 3.50065 1.1665C2.37482 1.1665 1.45898 2.08234 1.45898 3.20817C1.45898 4.12984 2.07732 4.90159 2.91732 5.15475V8.84492C2.07732 9.09809 1.45898 9.86984 1.45898 10.7915C1.45898 11.9173 2.37482 12.8332 3.50065 12.8332C4.62648 12.8332 5.54232 11.9173 5.54232 10.7915C5.54232 9.86984 4.92398 9.09809 4.08398 8.84492V5.15475C4.92398 4.90159 5.54232 4.12984 5.54232 3.20817ZM2.62565 3.20817C2.62565 2.72575 3.01823 2.33317 3.50065 2.33317C3.98307 2.33317 4.37565 2.72575 4.37565 3.20817C4.37565 3.69059 3.98307 4.08317 3.50065 4.08317C3.01823 4.08317 2.62565 3.69059 2.62565 3.20817ZM4.37565 10.7915C4.37565 11.2739 3.98307 11.6665 3.50065 11.6665C3.01823 11.6665 2.62565 11.2739 2.62565 10.7915C2.62565 10.3091 3.01823 9.9165 3.50065 9.9165C3.98307 9.9165 4.37565 10.3091 4.37565 10.7915Z"
                    fill={color}
                />
            </g>
        </svg>
    );
}
