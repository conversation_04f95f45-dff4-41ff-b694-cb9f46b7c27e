import {
    Tooltip,
    TooltipTrigger,
    Toolt<PERSON>Content,
    TooltipProvider,
} from '@/components/ui/tooltip';

interface DotsHorizontalRoundedProps {
    className?: string;
    width?: number | string;
    height?: number | string;
    fill?: string;
    isActive?: boolean;
    addTooltip?: boolean;
    tooltipText?: string;
}

export default function DotsHorizontalIcon({
    className,
    width = 25,
    height = 24,
    fill = '#666666',
    isActive = false,
    addTooltip = false,
    tooltipText = '',
}: DotsHorizontalRoundedProps) {
    const iconSVG = (
        <svg
            width={width}
            height={height}
            viewBox="0 0 25 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={className}>
            <g id="Icon / dots-horizontal-rounded">
                <path
                    id="icon"
                    d="M12.3999 10.0895C11.2999 10.0895 10.3999 10.9492 10.3999 12C10.3999 13.0508 11.2999 13.9105 12.3999 13.9105C13.4999 13.9105 14.3999 13.0508 14.3999 12C14.3999 10.9492 13.4999 10.0895 12.3999 10.0895ZM18.3999 10.0895C17.2999 10.0895 16.3999 10.9492 16.3999 12C16.3999 13.0508 17.2999 13.9105 18.3999 13.9105C19.4999 13.9105 20.3999 13.0508 20.3999 12C20.3999 10.9492 19.4999 10.0895 18.3999 10.0895ZM6.3999 10.0895C5.2999 10.0895 4.3999 10.9492 4.3999 12C4.3999 13.0508 5.2999 13.9105 6.3999 13.9105C7.4999 13.9105 8.3999 13.0508 8.3999 12C8.3999 10.9492 7.4999 10.0895 6.3999 10.0895Z"
                    fill={isActive ? 'black' : fill}
                />
            </g>
        </svg>
    );

    if (!addTooltip) return iconSVG;

    return (
        <TooltipProvider delayDuration={100}>
            <Tooltip>
                <TooltipTrigger asChild>{iconSVG}</TooltipTrigger>
                <TooltipContent>
                    <p className="text-[12px]">{tooltipText}</p>
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>
    );
}
