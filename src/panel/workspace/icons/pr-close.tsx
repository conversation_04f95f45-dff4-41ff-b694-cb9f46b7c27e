interface PRCloseProps {
    readonly color?: string;
    readonly width?: number;
    readonly height?: number;
}

export default function PRClose({
    color = '#EC3636',
    width = 20,
    height = 20,
}: PRCloseProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g id="Group 3">
                <path
                    id="Union"
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M18.0638 0L19.4995 1.43566L17.4355 3.4997L19.5005 5.56472L18.0648 7.00038L15.9998 4.93535L13.9352 6.99997L12.4995 5.56431L14.5641 3.4997L12.5005 1.43606L13.9361 0.000404619L15.9998 2.06404L18.0638 0ZM17.0013 8.00005V13.163C18.4406 13.597 19.5 14.92 19.5 16.5C19.5 18.43 17.9308 20 16.0018 20C14.0729 20 12.5037 18.43 12.5037 16.5C12.5037 14.92 13.5631 13.597 15.0024 13.163V8.00005H17.0013ZM14.5026 16.5C14.5026 17.327 15.1753 18 16.0018 18C16.8284 18 17.5011 17.327 17.5011 16.5C17.5011 15.673 16.8284 15 16.0018 15C15.1753 15 14.5026 15.673 14.5026 16.5ZM3.99816 9.36389e-05C5.92714 9.36389e-05 7.49632 1.57009 7.49632 3.50008C7.49632 5.08007 6.43687 6.40306 4.99763 6.83706V13.163C6.43687 13.597 7.49632 14.92 7.49632 16.5C7.49632 18.43 5.92714 20 3.99816 20C2.06917 20 0.5 18.43 0.5 16.5C0.5 14.92 1.55944 13.597 2.99868 13.163V6.83706C1.55944 6.40306 0.5 5.08007 0.5 3.50008C0.5 1.57009 2.06917 9.36389e-05 3.99816 9.36389e-05ZM3.99816 2.00008C3.17159 2.00008 2.49895 2.67308 2.49895 3.50008C2.49895 4.32707 3.17159 5.00007 3.99816 5.00007C4.82472 5.00007 5.49737 4.32707 5.49737 3.50008C5.49737 2.67308 4.82472 2.00008 3.99816 2.00008ZM3.99816 18C4.82472 18 5.49737 17.327 5.49737 16.5C5.49737 15.673 4.82472 15 3.99816 15C3.17159 15 2.49895 15.673 2.49895 16.5C2.49895 17.327 3.17159 18 3.99816 18Z"
                    fill={color}
                />
            </g>
        </svg>
    );
}
