interface CheckShieldProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function CheckShield({
    width = 17,
    height = 16,
    color = 'black',
}: CheckShieldProps) {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width={width}
            height={height}
            viewBox="0 0 17 16"
            fill="none">
            <path
                d="M14.6633 4.60015C14.6522 4.48812 14.6129 4.38072 14.549 4.28803C14.4851 4.19533 14.3987 4.12037 14.298 4.07015L8.96463 1.40349C8.87201 1.35714 8.76986 1.33301 8.6663 1.33301C8.56273 1.33301 8.46058 1.35714 8.36796 1.40349L3.03463 4.07015C2.93414 4.12066 2.84804 4.19571 2.78429 4.28836C2.72054 4.38101 2.68122 4.48825 2.66996 4.60015C2.66263 4.67149 2.0293 11.7782 8.39596 14.6095C8.48103 14.6479 8.5733 14.6677 8.66663 14.6677C8.75996 14.6677 8.85222 14.6479 8.93729 14.6095C15.304 11.7782 14.6706 4.67215 14.6633 4.60015ZM8.66663 13.2648C4.15396 11.0835 3.94063 6.42815 3.9773 5.09015L8.66663 2.74549L13.3526 5.08882C13.3773 6.41482 13.134 11.1008 8.66663 13.2648Z"
                fill={color}
            />
            <path
                d="M7.99998 8.39085L6.47131 6.86218L5.52865 7.80485L7.99998 10.2762L11.8046 6.47152L10.862 5.52885L7.99998 8.39085Z"
                fill={color}
            />
        </svg>
    );
}
