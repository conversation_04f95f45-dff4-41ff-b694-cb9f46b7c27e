interface SkipProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function Skip({
    width = 25,
    height = 25,
    color = 'black',
}: SkipProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 10 12"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
                id="icon"
                d="M0 0.5V11.5L7.77778 6L0 0.5ZM10 11.5V0.5H7.77778V11.5H10Z"
                fill={color}
            />
        </svg>
    );
}
