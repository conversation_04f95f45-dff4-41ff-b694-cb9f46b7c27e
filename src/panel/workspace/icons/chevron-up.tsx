interface BlitzyIconProps {
    width?: number | string;
    height?: number | string;
    color?: string;
    style?: React.CSSProperties;
}

export default function ChevronUp({
    width = 21,
    height = 20,
    color = 'white',
    style,
}: BlitzyIconProps) {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width={width}
            height={height}
            viewBox="0 0 21 20"
            fill="none"
            style={style}>
            <path
                d="M5.50586 8.08889L6.74311 6.91056L10.4995 10.4881L14.2559 6.91056L15.4931 8.08889L10.4995 12.8447L5.50586 8.08889Z"
                fill={color}
            />
        </svg>
    );
}
