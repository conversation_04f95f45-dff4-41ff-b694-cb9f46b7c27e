interface UnlinkProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function Unlink({
    width = 24,
    height = 24,
    color = '#991010',
}: UnlinkProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
                d="M16.949 14.026L19.071 12C20.0067 11.1033 20.5321 9.88883 20.5321 8.62275C20.5321 7.35667 20.0067 6.14222 19.071 5.24553C18.1325 4.35125 16.861 3.84903 15.5355 3.84903C14.2099 3.84903 12.9384 4.35125 12 5.24553L11.293 5.92088L12.707 7.27157L13.414 6.59622C13.9775 6.06033 14.7403 5.75947 15.5355 5.75947C16.3306 5.75947 17.0934 6.06033 17.657 6.59622C18.2185 7.13428 18.5338 7.86302 18.5338 8.62275C18.5338 9.38247 18.2185 10.1112 17.657 10.6493L15.535 12.6753C15.292 12.9059 15.0056 13.0906 14.691 13.2198L13.414 12L14.828 10.6493L14.121 9.97392C13.6579 9.52889 13.1071 9.17605 12.5003 8.93584C11.8935 8.69562 11.2428 8.57281 10.586 8.57451C10.351 8.57451 10.122 8.60507 9.89497 8.63755L3.70697 2.72754L2.29297 4.07824L20.293 21.2724L21.707 19.9217L16.171 14.6335C16.448 14.4578 16.709 14.2553 16.949 14.026ZM10.586 17.4037C10.0224 17.9396 9.25964 18.2405 8.46447 18.2405C7.6693 18.2405 6.9065 17.9396 6.34297 17.4037C5.78147 16.8657 5.46618 16.1369 5.46618 15.3772C5.46618 14.6175 5.78147 13.8887 6.34297 13.3507L7.81897 11.9417L6.40497 10.591L4.92897 12C3.99326 12.8967 3.46784 14.1111 3.46784 15.3772C3.46784 16.6433 3.99326 17.8577 4.92897 18.7544C5.39284 19.1981 5.94394 19.55 6.55059 19.7896C7.15724 20.0293 7.80748 20.1521 8.46397 20.151C9.12063 20.1523 9.77108 20.0295 10.3779 19.7899C10.9848 19.5502 11.536 19.1983 12 18.7544L12.707 18.0791L11.293 16.7284L10.586 17.4037Z"
                fill={color}
            />
        </svg>
    );
}
