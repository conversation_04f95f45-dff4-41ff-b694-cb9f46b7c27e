interface CodeCurlyProps {
    width?: number;
    height?: number;
    color?: string;
}

export default function CodeCurlyAdd({
    width = 20,
    height = 20,
    color = 'white',
}: CodeCurlyProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
                d="M7.4993 17.9603H8.33263V16.3683H7.50846C7.2518 16.3635 4.9993 16.2305 4.9993 13.1842C4.9993 11.5977 4.44513 10.6003 3.74763 10.0001C4.44513 9.39985 4.9993 8.40243 4.9993 6.81595C4.9993 3.76956 7.2518 3.63662 7.4993 3.63185H8.33263V2.03979H7.49763C6.0568 2.04298 3.33263 3.04597 3.33263 6.81595C3.33263 9.04482 1.9343 9.19607 1.6543 9.20403L1.66596 10.7961C1.7343 10.7961 3.33263 10.8231 3.33263 13.1842C3.33263 16.9541 6.0568 17.9571 7.4993 17.9603ZM18.3326 9.20403C18.2643 9.20403 16.666 9.17696 16.666 6.81595C16.666 3.04597 13.9418 2.04298 12.4993 2.03979H11.666V3.63185H12.4901C12.7468 3.63662 14.9993 3.76956 14.9993 6.81595C14.9993 8.40243 15.5535 9.39985 16.251 10.0001C15.5535 10.6003 14.9993 11.5977 14.9993 13.1842C14.9993 16.2305 12.7468 16.3635 12.4993 16.3683H11.666V17.9603H12.501C13.9418 17.9571 16.666 16.9541 16.666 13.1842C16.666 10.9553 18.0643 10.804 18.3443 10.7961L18.3326 9.20403Z"
                fill={color}
            />
            <path
                d="M10.8327 6.81592H9.16602V9.20399H6.66602V10.796H9.16602V13.1841H10.8327V10.796H13.3327V9.20399H10.8327V6.81592Z"
                fill={color}
            />
        </svg>
    );
}
