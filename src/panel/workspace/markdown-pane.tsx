import {ForwardedRef, forwardRef} from 'react';
import {IEditor, MarkdownEditor} from './markdown-editor';
import {MarkdownViewer} from './markdown-viewer';
import {ProjectDocumentType} from '@/lib/entity-types';

interface Props {
    initialMarkdown?: string;
    isEditing: boolean;
    docType: ProjectDocumentType;
    viewerMessage: string;
    onContentChange?: (isChanged: boolean) => void;
}

export const MarkdownPane = forwardRef(
    (props: Props, ref: ForwardedRef<IEditor>) => {
        const {
            initialMarkdown: markdown,
            isEditing,
            docType,
            viewerMessage,
            onContentChange,
        } = props;

        return (
            <>
                {isEditing && (
                    <MarkdownEditor
                        ref={ref}
                        docType={docType}
                        markdown={markdown ?? ''}
                        onContentChange={onContentChange}
                    />
                )}
                {!isEditing && (
                    <MarkdownViewer
                        markdown={markdown}
                        viewerMessage={viewerMessage}
                    />
                )}
            </>
        );
    },
);
