import {useCallback, useEffect} from 'react';
import {use<PERSON><PERSON>, SubmitHandler, Controller} from 'react-hook-form';
import {z} from 'zod';
import {zodResolver} from '@hookform/resolvers/zod';
import {useToast} from '@/hooks/use-toast';
import {Input} from '@/components/ui/input';
import {LoadingSubmitButton} from '@/components/custom/loading-submit-button';
import {useAuthContext} from '@/context/auth-context';
import {TermClause} from '@/components/custom/term-clause';
import {useNavigate} from 'react-router-dom';

const UserProfileSchema = z.object({
    firstName: z
        .string()
        .min(1, {message: 'First name is required'})
        .max(100, {message: 'First name should have at most 100 characters'}),
    lastName: z
        .string()
        .min(1, {message: 'Last name is required'})
        .max(100, {message: 'Last name should have at most 100 characters'}),
    company: z
        .string()
        .min(1, {message: 'Company name is required'})
        .max(100, {message: 'Company name should have at most 100 characters'}),
});

type UserProfileSchemaType = z.infer<typeof UserProfileSchema>;

export function CompleteRegistrationPanel() {
    const {toast} = useToast();
    const navigateTo = useNavigate();
    const {userProfile, changeUserProfile, refreshUserProfile} =
        useAuthContext();
    const {
        reset,
        control,
        handleSubmit,
        trigger,
        formState: {errors, isSubmitting, isValid},
    } = useForm<UserProfileSchemaType>({
        resolver: zodResolver(UserProfileSchema),
        mode: 'onBlur',
        defaultValues: {
            firstName: userProfile?.firstName || '',
            lastName: userProfile?.lastName || '',
            company: userProfile?.company || '',
        },
    });

    // Reset form when userProfile changes (e.g., after refresh)
    useEffect(() => {
        if (userProfile) {
            reset({
                firstName: userProfile.firstName || '',
                lastName: userProfile.lastName || '',
                company: userProfile.company || '',
            });
            // Trigger validation after reset to ensure isValid is updated
            setTimeout(() => {
                trigger();
            }, 0);
        }
    }, [userProfile, reset, trigger]);

    // Function to prevent leading spaces but allow spaces in between
    const handleInputChange = (field: any, value: string) => {
        // Prevent leading spaces
        if (value.startsWith(' ')) {
            return;
        }
        field.onChange(value);
    };

    // Function to trim trailing spaces when user leaves the field
    const handleInputBlur = (field: any) => {
        const trimmedValue = field.value.trimEnd();
        field.onChange(trimmedValue);
        field.onBlur();
    };

    const onSubmit: SubmitHandler<UserProfileSchemaType> = useCallback(
        async data => {
            const isValid = await trigger();
            if (!isValid) {
                toast({
                    variant: 'destructive',
                    duration: 5000,
                    description:
                        'Please fill in all required fields correctly.',
                });
                return;
            }

            try {
                if (changeUserProfile && refreshUserProfile) {
                    await changeUserProfile({...userProfile, ...data});
                    reset({...data});
                    toast({
                        variant: 'success',
                        duration: 10000,
                        description: 'Registration completed!',
                    });

                    await refreshUserProfile();
                    navigateTo(
                        {
                            pathname: '/workspace',
                        },
                        {replace: true},
                    );
                }
            } catch (error) {
                let errorMessage = 'Unknown error';

                if (error && typeof error === 'object' && 'code' in error) {
                    const firebaseError = error as {
                        code: string;
                        message: string;
                    };

                    switch (firebaseError.code) {
                        case 'auth/account-exists-with-different-credential':
                            errorMessage =
                                'An account with this email already exists with a different authentication provider. Please sign in with your existing account or use a different email address.';
                            break;
                        case 'auth/email-already-in-use':
                            errorMessage =
                                'This email address is already registered. Please use a different email or sign in to your existing account.';
                            break;
                        default:
                            errorMessage =
                                firebaseError.message ||
                                'An authentication error occurred.';
                    }
                } else if (error instanceof Error) {
                    errorMessage = error.message;
                }

                toast({
                    variant: 'destructive',
                    duration: 10000,
                    description: `Failed to complete registration: ${errorMessage}`,
                });
            }
        },
        [
            changeUserProfile,
            navigateTo,
            reset,
            toast,
            userProfile,
            refreshUserProfile,
            trigger,
        ],
    );

    return (
        <div className="flex flex-col justify-center items-start gap-[32px] flex-1 self-stretch">
            <div className="home-panel w-full">
                <form className="w-full" onSubmit={handleSubmit(onSubmit)}>
                    <h2 className="mb-[24px] text-[#000] font-inter text-[24px] sm:text-[32px] font-normal leading-[120%] sm:leading-[100%] tracking-[-0.48px] sm:tracking-[-0.64px]">
                        Almost there! Complete your profile
                    </h2>
                    <div className="w-full flex flex-col gap-[16px]">
                        <div className="flex flex-col items-start gap-[16px] self-stretch">
                            {/* First name and Last name - responsive layout */}
                            <div className="flex flex-col sm:flex-row items-start gap-[16px] sm:gap-[24px] self-stretch w-full">
                                {/* First Name */}
                                <div className="flex flex-col gap-[12px] w-full">
                                    <div className="form-label text-[16px] font-semibold">
                                        First name
                                    </div>
                                    <div className="w-full flex flex-col gap-[4px]">
                                        <Controller
                                            name="firstName"
                                            control={control}
                                            render={({field}) => (
                                                <Input
                                                    className={`w-full ${errors.firstName ? 'input-field-error' : 'input-field'}`}
                                                    type="text"
                                                    value={field.value}
                                                    onChange={e =>
                                                        handleInputChange(
                                                            field,
                                                            e.target.value,
                                                        )
                                                    }
                                                    onBlur={() =>
                                                        handleInputBlur(field)
                                                    }
                                                    name={field.name}
                                                />
                                            )}
                                        />
                                        {errors.firstName && (
                                            <div className="form-error-note">
                                                {errors.firstName.message}
                                            </div>
                                        )}
                                    </div>
                                </div>
                                {/* Last Name */}
                                <div className="flex flex-col gap-[12px] w-full">
                                    <div className="form-label text-[16px] font-semibold">
                                        Last name
                                    </div>
                                    <div className="w-full flex flex-col gap-[4px]">
                                        <Controller
                                            name="lastName"
                                            control={control}
                                            render={({field}) => (
                                                <Input
                                                    className={`w-full ${errors.lastName ? 'input-field-error' : 'input-field'}`}
                                                    type="text"
                                                    value={field.value}
                                                    onChange={e =>
                                                        handleInputChange(
                                                            field,
                                                            e.target.value,
                                                        )
                                                    }
                                                    onBlur={() =>
                                                        handleInputBlur(field)
                                                    }
                                                    name={field.name}
                                                />
                                            )}
                                        />
                                        {errors.lastName && (
                                            <div className="form-error-note">
                                                {errors.lastName.message}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                            {/* Company field */}
                            <div className="flex flex-col gap-[12px] w-full">
                                <div className="form-label text-[16px] font-semibold">
                                    Company
                                </div>
                                <div className="w-full flex flex-col gap-[4px]">
                                    <Controller
                                        name="company"
                                        control={control}
                                        render={({field}) => (
                                            <Input
                                                className={`w-full ${errors.company ? 'input-field-error' : 'input-field'}`}
                                                type="text"
                                                value={field.value}
                                                onChange={e =>
                                                    handleInputChange(
                                                        field,
                                                        e.target.value,
                                                    )
                                                }
                                                onBlur={() =>
                                                    handleInputBlur(field)
                                                }
                                                name={field.name}
                                            />
                                        )}
                                    />
                                    {errors.company && (
                                        <div className="form-error-note">
                                            {errors.company.message}
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                        <div className="flex flex-col pt-[12px]">
                            <LoadingSubmitButton
                                loading={isSubmitting}
                                loadingText="Completing registration..."
                                className="primary-button w-auto"
                                disabled={!isValid || isSubmitting}>
                                Complete registration
                            </LoadingSubmitButton>
                        </div>
                    </div>
                </form>
                <div className="w-full flex justify-center">
                    <TermClause srcPage="complete_registration" />
                </div>
            </div>
        </div>
    );
}
