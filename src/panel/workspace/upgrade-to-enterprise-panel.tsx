import BookMeetingPane from '@/modal/book-meeting-pane';
import iconBlitzy from '/images/blitzy-icon-text.svg';
import ArrowBack from './icons/arrow-back';
import {useNavigate} from 'react-router-dom';

export function UpgradeToEnterprisePanel() {
    const navigate = useNavigate();

    return (
        <div className="w-full mx-auto max-w-[1440px] h-dvh px-[24px] py-[16px] flex flex-col overflow-y-scroll">
            <div className="relative sm:px-[12px] py-[12px] sm:py-[12px] flex justify-between items-center">
                <div
                    className="w-[32px] sm:w-[40px] h-[32px] sm:h-[40px] flex justify-center items-center cursor-pointer"
                    onClick={() => navigate(-1)}>
                    <ArrowBack />
                </div>
                <img
                    src={iconBlitzy}
                    alt="Blitzy"
                    className="mx-auto h-[32px] sm:h-[40px]"
                />
                <div className="w-[32px] sm:w-[40px] h-[32px] sm:h-[40px]"></div>
            </div>
            <div className="flex-grow py-[6px] sm:py-[6px]">
                <h1 className="sm:pb-[8px] text-[32px] sm:text-[40px] text-center">
                    Learn more about our Enterprise Plan
                </h1>
                <BookMeetingPane />
            </div>
        </div>
    );
}
