import {IEditor, TiptapEditor} from '@/components/editor/TiptapEditor';
import {ProjectDocumentType} from '@/lib/entity-types';
import {Loader2} from 'lucide-react';
import {ForwardedRef, forwardRef, useEffect, useState} from 'react';

export type {IEditor};

interface Props {
    docType: ProjectDocumentType;
    markdown: string;
    onContentChange?: (isChanged: boolean) => void;
}

export const MarkdownEditor = forwardRef(
    (props: Props, ref: ForwardedRef<IEditor>) => {
        const {markdown = '', docType, onContentChange} = props;
        const [isContentLoading, setIsContentLoading] = useState(false);
        const [loadedMarkdown, setLoadedMarkdown] = useState('');

        // Load content asynchronously for large documents
        useEffect(() => {
            if (markdown && markdown.length > 5000) {
                // Consider it large if > 5000 characters
                setIsContentLoading(true);
                setLoadedMarkdown(''); // Start with empty content

                // Use setTimeout to defer content loading to prevent UI blocking
                const timeoutId = setTimeout(() => {
                    setLoadedMarkdown(markdown);
                    setIsContentLoading(false);
                }, 150); // Small delay to allow UI to render and show loading state

                return () => clearTimeout(timeoutId);
            } else {
                // For smaller documents, load immediately
                setLoadedMarkdown(markdown);
                setIsContentLoading(false);
            }
        }, [markdown]);

        // Handle content changes - this triggers validation
        const handleContentChange = () => {
            // We don't need to do anything with the content here since
            // the parent uses the ref to get content, but we need this
            // to trigger the onUpdate method in TiptapEditor
        };

        return (
            <div className="self-stretch overflow-hidden h-full relative">
                {/* Loading overlay */}
                {isContentLoading && (
                    <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-20">
                        <div className="flex flex-col items-center gap-3">
                            <Loader2 className="w-8 h-8 animate-spin text-[#5B39F3]" />
                            <p className="text-sm text-gray-600">
                                Loading document...
                            </p>
                        </div>
                    </div>
                )}

                <div className="h-full absolute w-full">
                    <TiptapEditor
                        ref={ref}
                        markdown={loadedMarkdown}
                        onChange={handleContentChange}
                        onContentChange={onContentChange}
                        editable={true}
                        docType={docType}
                        isContentLoading={isContentLoading}
                        setIsContentLoading={setIsContentLoading}
                    />
                </div>
            </div>
        );
    },
);

MarkdownEditor.displayName = 'MarkdownEditor';
