import {useCallback, useState, useEffect} from 'react';
import {useNavigate, useParams} from 'react-router-dom';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import {useAuthContext} from '@/context/auth-context';
import LogOut from './icons/log-out';
import {useSubscriptionContext} from '@/context/subscription-context';
import {Rocket} from './icons/rocket';
import Avatar from '@/components/ui/avatar';
import {logGAEvent} from '@/lib/utils';

interface Props {
    onClose: () => void;
    showUserInfo?: boolean;
}

// const PlanLabel: Record<SubscriptionPlan, string> = {
//     FREE: 'Free Plan',
//     PRO: 'Pro Plan',
//     ENTERPRISE: 'Enterprise Plan',
// };

const milliSecondsInOneDay = 86400000;
const trialPeriodInDays = 7;

// Helper function to convert time (milliseconds) to days left
const convertTimeToDaysLeft = (milliseconds: number): number => {
    return Math.ceil(milliseconds / milliSecondsInOneDay); // Use Math.ceil to round up the days remaining
};

// Helper function to calculate trial progress width percentage
const calculateTrialProgress = (trialRemainingInterval: number): string => {
    const daysLeft = convertTimeToDaysLeft(trialRemainingInterval);
    return ((daysLeft * 100) / trialPeriodInDays).toFixed(0) + '%';
};

// Helper function to determine the appropriate plan label for upgrade text
// const getUpgradeText = (plan: string, isTrial: boolean): string => {
//     if (plan === 'FREE' || (plan === 'PRO' && isTrial)) {
//         return 'Pro';
//     } else if (plan === 'PRO' && !isTrial) {
//         return 'Enterprise';
//     }
//     return '';
// };

export function UserPopOverMenuPane({onClose, showUserInfo}: Props) {
    const [signOutConfirm, setSignOutConfirm] = useState(false);
    const {
        trialRemainingInterval,
        gotoPaymentLink,
        subscriptionStatus,
        isTrialing,
        refreshSubscription,
    } = useSubscriptionContext();
    const navigateTo = useNavigate();
    const {logout, userProfile} = useAuthContext();
    const {projectId} = useParams();
    const daysLeft = convertTimeToDaysLeft(trialRemainingInterval ?? 0);
    const widthPercentage = calculateTrialProgress(trialRemainingInterval ?? 0);

    useEffect(() => {
        if (refreshSubscription) {
            refreshSubscription();
        }
    }, [refreshSubscription]);

    const handleSignOut = useCallback(async () => {
        logGAEvent('user_logout', {
            user_id: userProfile?.id,
            email: userProfile?.email,
            logout_source: 'user_popover_menu',
        });

        if (logout) {
            await logout();
            navigateTo('/sign-in', {replace: true});
            if (onClose) {
                onClose();
            }
        }
    }, [logout, navigateTo, onClose, userProfile]);

    // const handleSettings = useCallback(() => {
    //     logGAEvent('Settings Accessed', {
    //         userId: userProfile?.id,
    //     });

    //     navigateTo('/workspace/settings/profile', {replace: true});
    //     if (onClose) {
    //         onClose();
    //     }
    // }, [navigateTo, onClose, userProfile]);

    const handleUpgrade = useCallback(async () => {
        logGAEvent('menu_upgrade_clicked', {
            user_id: userProfile?.id,
            current_plan: userProfile?.subscription?.planName,
        });
        if (userProfile?.subscription?.planName === 'FREE') {
            gotoPaymentLink?.(projectId);
        } else {
            window.open('https://blitzy.com/#pricing', '_blank');
        }

        // if (plan === 'FREE') {
        //     gotoPaymentLink();
        // } else if (plan === 'PRO') {
        //     navigateTo('/workspace/enterprise', {replace: false});
        // }

        if (onClose) {
            onClose();
        }
    }, [onClose, gotoPaymentLink, userProfile, projectId]);

    return (
        <div className="w-[264px] flex flex-col gap-[0.5px] bg-[#D9D9D9] rounded-[16px] overflow-hidden">
            {userProfile?.email && (
                <div className="px-[12px] py-[8px] bg-[#FFF] text-[12px] text-[#999] line-clamp-1">
                    {userProfile.email}
                </div>
            )}
            {showUserInfo && (
                <div className="p-[12px] bg-[#FFF]">
                    <div className="flex items-center gap-[8px]">
                        <Avatar
                            src={
                                userProfile?.avatar?.startsWith('http')
                                    ? userProfile.avatar
                                    : userProfile?.avatar
                                      ? `/images/${userProfile.avatar}`
                                      : undefined
                            }
                            name={userProfile?.firstName}
                            email={userProfile?.email}
                            size="large"
                        />
                        <div className="py-[4px] flex-grow">
                            <span className="text-[16px] text-black font-bold">
                                {userProfile?.firstName && userProfile?.lastName
                                    ? userProfile?.firstName +
                                      ' ' +
                                      userProfile?.lastName
                                    : userProfile?.email?.split('@')[0]}
                            </span>
                            {userProfile?.subscription?.planName === 'FREE' && (
                                <div className="text-[14px] text-[#999] flex justify-between items-center gap-[8px]">
                                    <span className="line-clamp-1">
                                        Free Plan
                                    </span>
                                </div>
                            )}
                            {userProfile?.subscription?.planName === 'PRO' &&
                                ((subscriptionStatus === 'TRIALING' ||
                                    (subscriptionStatus === 'CANCELLING' &&
                                        isTrialing)) &&
                                trialRemainingInterval &&
                                trialRemainingInterval > 0 ? (
                                    <div className="text-[14px] text-[#999] flex justify-between items-center gap-[8px]">
                                        <span className="line-clamp-1">
                                            Pro Trial
                                        </span>
                                        <span>
                                            {daysLeft === 1
                                                ? '1 day left'
                                                : `${daysLeft.toFixed(0)} days left`}
                                        </span>
                                    </div>
                                ) : (
                                    <div className="text-[14px] text-[#999] flex justify-between items-center gap-[8px]">
                                        <span className="line-clamp-1">
                                            Pro Plan
                                        </span>
                                    </div>
                                ))}
                            {userProfile?.subscription?.planName ===
                                'ENTERPRISE' && (
                                <div className="text-[14px] text-[#999] flex justify-between items-center gap-[8px]">
                                    <span className="line-clamp-1">
                                        Enterprise Plan
                                    </span>
                                </div>
                            )}
                        </div>
                    </div>
                    {userProfile?.subscription?.planName === 'PRO' &&
                        (subscriptionStatus === 'TRIALING' ||
                            (subscriptionStatus === 'CANCELLING' &&
                                isTrialing)) &&
                        trialRemainingInterval &&
                        trialRemainingInterval > 0 && (
                            <div className="w-full h-[6px] mt-[8px] bg-[#F2F0FE] rounded-full overflow-hidden">
                                <div
                                    className="h-full bg-gradient-to-r from-[#B23AF2] via-[#5B39F3] to-[#94FAD5]"
                                    style={{width: widthPercentage}}></div>
                            </div>
                        )}
                </div>
            )}
            {userProfile?.subscription?.planName !== 'ENTERPRISE' && (
                <div
                    role="button"
                    className="p-[12px] flex gap-[8px] bg-[#FFF] hover:bg-[#F2F0FE]"
                    onClick={handleUpgrade}>
                    <Rocket color="#5B39F3" size={24} />
                    <span className="px-[8px] text-[16px] text-brand-purple font-semibold">
                        Upgrade plan
                    </span>
                </div>
            )}
            <div
                role="button"
                className="p-[12px] flex gap-[8px] bg-[#FFF] hover:bg-[#F2F0FE]"
                onClick={() => setSignOutConfirm(true)}>
                <LogOut />
                <span className="px-[8px] text-[16px] font-semibold">
                    Sign out
                </span>
            </div>
            <Dialog
                open={signOutConfirm}
                onOpenChange={open => {
                    if (!open) setSignOutConfirm(false);
                }}>
                <DialogContent className="min-h-dvh sm:min-h-0 w-full sm:rounded-lg sm:max-w-md bg-white [&>button]:hidden">
                    <DialogHeader>
                        <DialogTitle>Sign out</DialogTitle>
                        <DialogDescription>
                            Are you sure you want to sign out?
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter className="gap-[16px] sm:gap-0">
                        <div
                            role="button"
                            className="secondary-button sm:ml-auto min-w-max"
                            onClick={() => setSignOutConfirm(false)}>
                            Cancel
                        </div>
                        <div
                            role="button"
                            className="primary-button sm:ml-auto min-w-max"
                            onClick={handleSignOut}>
                            Sign out
                        </div>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
}
