import {useAuthContext} from '@/context/auth-context';

const steps = [
    {key: 'One', label: 'Describe your product'},
    {key: 'Two', label: 'Edit & approve documents'},
    {key: 'Three', label: 'Build your product'},
];

export function GettingStartedPanel() {
    const {userProfile} = useAuthContext();
    return (
        <div className="w-full flex flex-col gap-[60px]">
            <div className="flex flex-col gap-[8px] items-start">
                <div className="flex flex-row items-start gap-[8px] text-[64px] font-bold tracking-[0.5px]">
                    <div>Hi</div>
                    <div className="gradient-name">
                        {userProfile?.firstName ??
                            userProfile?.email?.split('@')[0]}
                    </div>
                </div>
                <div className="text-[#33333380] text-[24px] font-bold tracking-[-0.5px]">
                    Ready to create? Let's walk through it together.
                </div>
            </div>
            <div className="flex flex-col gap-[20px] items-start">
                {steps.map(({key, label}) => (
                    <div
                        key={key}
                        className="py-[12px] px-[16px] flex flex-row gap-[8px] rounded-[32px] bg-[#F6F6F6] text-[16px] font-bold">
                        <div className="text-brand-purple">{key}</div>
                        <div className="text-[#333]">{label}</div>
                    </div>
                ))}
            </div>
        </div>
    );
}
