import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ontent,
    <PERSON><PERSON><PERSON>eader,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    DialogFooter,
    DialogDescription,
} from '@/components/ui/dialog';
import {Controller, SubmitHandler, useForm} from 'react-hook-form';
import {PasswordInput} from '@/components/ui/passwordInput';
import {z} from 'zod';
import {zodResolver} from '@hookform/resolvers/zod';
import {useAuthContext} from '@/context/auth-context';
import {useToast} from '@/hooks/use-toast';
import {useNavigate} from 'react-router-dom';
import {LoadingSubmitButton} from '@/components/custom/loading-submit-button';
import {Link} from 'react-router-dom';
import {
    getAuth,
    linkWithPopup,
    GoogleAuthProvider,
    OAuthProvider,
    linkWithCredential,
} from 'firebase/auth';

const PasswordSchema = z.object({
    password: z
        .string()
        .min(8, {message: 'Password must be at least 8 characters long'}),
});

type PasswordSchemaType = z.infer<typeof PasswordSchema>;

interface LinkAccountsDialogProps {
    isOpen: boolean;
    onClose: () => void;
    email: string;
    provider: 'google' | 'microsoft';
    // Add these props to handle the provider credential from the OAuth flow
    pendingCredential?: any; // The credential from the failed OAuth sign-in
}

export function LinkAccountsDialog({
    isOpen,
    onClose,
    email,
    provider,
    pendingCredential,
}: LinkAccountsDialogProps) {
    const {login} = useAuthContext();
    const {toast} = useToast();
    const navigateTo = useNavigate();
    const auth = getAuth();

    const {
        control,
        handleSubmit,
        formState: {errors, isSubmitting},
    } = useForm<PasswordSchemaType>({
        resolver: zodResolver(PasswordSchema),
        mode: 'onTouched',
    });

    const linkAccounts = async (user: any) => {
        try {
            if (pendingCredential) {
                // Link the pending OAuth credential to the current user
                await linkWithCredential(user, pendingCredential);

                toast({
                    variant: 'success',
                    description: `Successfully linked your ${provider} account!`,
                });
            } else {
                // Alternative: Prompt user to link via popup
                await linkAccountViaPopup(provider);
            }
        } catch (error: any) {
            console.error('Error linking accounts:', error);

            // Handle specific Firebase errors
            if (error.code === 'auth/credential-already-in-use') {
                toast({
                    variant: 'destructive',
                    description:
                        'This account is already linked to another user.',
                });
            } else if (error.code === 'auth/provider-already-linked') {
                toast({
                    variant: 'destructive',
                    description:
                        'This provider is already linked to your account.',
                });
            } else {
                toast({
                    variant: 'destructive',
                    description: 'Failed to link accounts. Please try again.',
                });
            }
            throw error;
        }
    };

    const linkAccountViaPopup = async (providerType: string) => {
        let authProvider;

        switch (providerType) {
            case 'google':
                authProvider = new GoogleAuthProvider();
                break;
            case 'microsoft':
                authProvider = new OAuthProvider('microsoft.com');
                // Optional: Add tenant domain for specific Microsoft configurations
                // authProvider.setCustomParameters({
                //     tenant: 'your-tenant-id' // Only needed for specific Azure AD configurations
                // });
                break;
            default:
                throw new Error('Unsupported provider');
        }

        try {
            await linkWithPopup(auth.currentUser!, authProvider);

            toast({
                variant: 'success',
                description: `Successfully linked your ${providerType} account!`,
            });
        } catch (error: any) {
            console.error('Error linking via popup:', error);
            throw error;
        }
    };

    const onSubmit: SubmitHandler<PasswordSchemaType> = async data => {
        try {
            if (login) {
                // First, sign in with email/password
                const result = await login(email, data.password);

                if ('user_id' in result) {
                    // Login successful, now link the OAuth account
                    try {
                        await linkAccounts(result);

                        toast({
                            variant: 'success',
                            description: 'Accounts linked successfully!',
                        });

                        navigateTo('/workspace', {replace: true});
                        onClose();
                    } catch (linkError) {
                        // Account linking failed, but user is still logged in
                        console.error('Account linking failed:', linkError);
                        // You might want to redirect anyway or handle this differently
                        navigateTo('/workspace', {replace: true});
                    }
                } else {
                    toast({
                        variant: 'destructive',
                        description: result.errorMsg,
                    });
                }
            }
        } catch (error) {
            console.error('Error in login/linking process:', error);
            toast({
                variant: 'destructive',
                description: 'Failed to link accounts. Please try again.',
            });
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="min-h-dvh sm:min-h-0 w-full p-[24px] gap-[24px] sm:max-w-[512px] sm:rounded-[24px] bg-white [&>button]:hidden">
                <DialogHeader className="flex-grow gap-[12px]">
                    <DialogTitle className="text-[24px] font-semibold">
                        Link your accounts
                    </DialogTitle>
                    <DialogDescription className="text-[16px] font-inter font-normal leading-[150%] tracking-[-0.3px] text-[#333]">
                        <span className="font-semibold">{email}</span> already
                        exists in Blitzy. Connect your {provider} account to
                        sign in with either method.
                    </DialogDescription>
                </DialogHeader>

                <form
                    onSubmit={handleSubmit(onSubmit)}
                    className="flex flex-col gap-[6px]">
                    <div className="text-[16px] font-inter font-semibold leading-[150%] tracking-[-0.04px] text-[#333]">
                        Confirm with your Blitzy password
                    </div>
                    <div className="w-full flex flex-col gap-[4px]">
                        <Controller
                            name="password"
                            control={control}
                            render={({field}) => (
                                <PasswordInput
                                    className={`${errors.password ? 'input-field-error' : 'input-field'} pr-10`}
                                    type="password"
                                    showPasswordToggle={true}
                                    {...field}
                                />
                            )}
                        />
                        {errors.password && (
                            <div className="form-error-note">
                                {errors.password.message}
                            </div>
                        )}
                    </div>

                    <div className="w-full text-center text-[#333333] text-[14px] font-normal leading-[21px] tracking-[-0.3px] flex flex-col items-start gap-[8px] sm:gap-[12px]">
                        <Link to="/forgot-password">Forgot your password?</Link>
                    </div>

                    <DialogFooter>
                        <div className="flex flex-col gap-[12px] pt-[24px] w-full sm:justify-end sm:flex-row">
                            <LoadingSubmitButton
                                type="submit"
                                disabled={isSubmitting}
                                className="primary-button sm:order-last">
                                {isSubmitting
                                    ? 'Linking accounts...'
                                    : 'Link accounts'}
                            </LoadingSubmitButton>
                            <button
                                type="button"
                                onClick={onClose}
                                className="secondary-button">
                                <span className="text-semibold">Cancel</span>
                            </button>
                        </div>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    );
}
