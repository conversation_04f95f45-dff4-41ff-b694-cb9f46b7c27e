import {useCallback, useState} from 'react';
import {Link, useNavigate} from 'react-router-dom';
import {useAuthContext} from '@/context/auth-context';
import {Popover, PopoverContent, PopoverTrigger} from '@/components/ui/popover';
import Avatar from '@/components/ui/avatar';
import {logGAEvent} from '@/lib/utils';
import logo from '/images/blitzy-icon-text.svg';
import {HelpCircleIcon} from './icons/help-circle';
import Cog from './icons/cog';
import {UserPopOverMenuPane} from './user-popover-menu-pane';
import {HelpAndSupportPane} from './help-support-pane';

export function MobileTopNavPane() {
    const [isPopoverUserMenuOpen, setPopverUserMenuOpen] = useState(false);
    const [isHelpAndSupportOpen, setHelpAndSupportOpen] = useState(false);
    const {userProfile} = useAuthContext();
    const navigateTo = useNavigate();

    const handleSettings = useCallback(() => {
        logGAEvent('Settings Accessed', {
            userId: userProfile?.id,
        });
        navigateTo('/workspace/settings/profile', {replace: true});
    }, [navigateTo, userProfile]);

    return (
        <header className="h-[64px] px-[24px] py-[16px] bg-white flex justify-between items-center gap-[12px] border-b border-[#D9D9D9]">
            <Link to="/workspace/projects" className="h-[32px]">
                <img className="h-full" src={logo} alt="Blitzy" />
            </Link>
            <div className="flex items-center gap-[24px]">
                <Popover
                    open={isHelpAndSupportOpen}
                    onOpenChange={setHelpAndSupportOpen}>
                    <PopoverTrigger asChild>
                        <span
                            className={` text-[#333] h-[32px] w-[32px] flex items-center justify-center cursor-pointer`}>
                            <HelpCircleIcon
                                className="w-[24px] h-[24px]"
                                isActive={isHelpAndSupportOpen}
                                variant={
                                    isHelpAndSupportOpen ? 'black' : 'default'
                                }
                            />
                        </span>
                    </PopoverTrigger>
                    <PopoverContent className="mr-[0px]" align="end">
                        <HelpAndSupportPane
                            onClose={() => setHelpAndSupportOpen(false)}
                            showBack={false}
                        />
                    </PopoverContent>
                </Popover>

                <div
                    role="button"
                    title="Settings"
                    className="h-[32px] w-[32px] flex items-center justify-center"
                    onClick={handleSettings}>
                    <Cog
                        fill={
                            location.pathname.includes('/settings')
                                ? '#0F0F11'
                                : '#666666'
                        }
                        hoverFill="#0F0F11"
                    />
                </div>

                <Popover
                    open={isPopoverUserMenuOpen}
                    onOpenChange={setPopverUserMenuOpen}>
                    <PopoverTrigger asChild>
                        <div className="flex items-center justify-center cursor-pointer">
                            <Avatar
                                src={
                                    userProfile?.avatar?.startsWith('http')
                                        ? userProfile.avatar
                                        : userProfile?.avatar
                                          ? `/images/${userProfile.avatar}`
                                          : undefined
                                }
                                name={userProfile?.firstName}
                                email={userProfile?.email}
                                size="large"
                            />
                        </div>
                    </PopoverTrigger>
                    <PopoverContent className="mt-[8px] mr-[12px]">
                        <UserPopOverMenuPane
                            onClose={() => setPopverUserMenuOpen(false)}
                            showUserInfo={true}
                        />
                    </PopoverContent>
                </Popover>
            </div>
        </header>
    );
}
