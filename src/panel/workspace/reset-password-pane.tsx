import {useCallback, useState} from 'react';
import {
    EmailAuthProvider,
    reauthenticateWithCredential,
    updatePassword,
} from 'firebase/auth';
import {auth} from '@/firebase';
import {use<PERSON><PERSON>, SubmitHand<PERSON>, Controller} from 'react-hook-form';
import {z} from 'zod';
import {zodResolver} from '@hookform/resolvers/zod';
import {useToast} from '@/hooks/use-toast';
import {LoadingSubmitButton} from '@/components/custom/loading-submit-button';
import {PasswordInput} from '@/components/ui/passwordInput';
import PasswordValidationMessage from '@/components/custom/password-validation-message';
import {
    lowerCaseRegex,
    upperCaseRegex,
    numberRegex,
    specialCharRegex,
} from '@/lib/utils';

const ResetPasswordSchema = z.object({
    oldPassword: z.string().min(1, {message: 'Old password is required'}),
    newPassword: z
        .string()
        .min(12, {message: 'Password must be at least 12 characters long'})
        .refine(password => lowerCaseRegex.test(password), {
            message: 'Password must contain at least one lowercase letter',
        })
        .refine(password => upperCaseRegex.test(password), {
            message: 'Password must contain at least one uppercase letter',
        })
        .refine(password => numberRegex.test(password), {
            message: 'Password must contain at least one number',
        })
        .refine(password => specialCharRegex.test(password), {
            message: 'Password must contain at least one special character',
        }),
});

type ResetPasswordSchemaType = z.infer<typeof ResetPasswordSchema>;

export function ResetPasswordPane() {
    const {toast} = useToast();
    const {
        control,
        reset,
        handleSubmit,
        getValues,
        formState: {isDirty, errors, isSubmitting},
    } = useForm<ResetPasswordSchemaType>({
        resolver: zodResolver(ResetPasswordSchema),
        mode: 'onTouched',
        defaultValues: {
            oldPassword: '',
            newPassword: '',
        },
    });
    const [passwordLengthValid, setPasswordLengthValid] = useState<boolean>();
    const [passwordHasLowerCase, setPasswordHasLowerCase] = useState<boolean>();
    const [passwordHasUpperCase, setPasswordHasUpperCase] = useState<boolean>();
    const [passwordHasNumber, setPasswordHasNumber] = useState<boolean>();
    const [passwordHasSpecialChar, setPasswordHasSpecialChar] =
        useState<boolean>();
    const [showPasswordValidationUI, setShowPasswordValidationUI] =
        useState<boolean>(false);

    const onSubmit: SubmitHandler<ResetPasswordSchemaType> = useCallback(
        async data => {
            const {oldPassword, newPassword} = data;
            if (oldPassword && newPassword) {
                if (auth?.currentUser?.email) {
                    try {
                        const credential = EmailAuthProvider.credential(
                            auth.currentUser.email,
                            oldPassword,
                        );
                        await reauthenticateWithCredential(
                            auth.currentUser,
                            credential,
                        );
                    } catch (error) {
                        let errMsg: string =
                            'An error occurred while reauthenticating';
                        if (error instanceof Error) {
                            if (
                                error.message ===
                                'Firebase: Error (auth/invalid-credential).'
                            ) {
                                errMsg = 'Incorrect current password';
                            } else {
                                errMsg = `${errMsg}: ${error.message}`;
                            }
                        }
                        toast({
                            variant: 'destructive',
                            duration: 10000,
                            description: errMsg,
                        });
                        return;
                    }

                    try {
                        await updatePassword(auth.currentUser, newPassword);
                        reset();
                        toast({
                            duration: 10000,
                            description: 'Password changed',
                        });
                    } catch (error) {
                        let errMsg: string =
                            'An error occurred while updating password';
                        if (error instanceof Error) {
                            errMsg = `${errMsg}: ${error.message}`;
                        }
                        toast({
                            variant: 'destructive',
                            duration: 10000,
                            description: errMsg,
                        });
                    } finally {
                        setPasswordLengthValid(false);
                        setPasswordHasLowerCase(false);
                        setPasswordHasUpperCase(false);
                        setPasswordHasNumber(false);
                        setPasswordHasSpecialChar(false);
                    }
                }
            }
        },
        [toast, reset],
    );

    const handleChange = () => {
        const newPassword = getValues('newPassword');
        setPasswordLengthValid(newPassword.length >= 12);
        setPasswordHasLowerCase(lowerCaseRegex.test(newPassword));
        setPasswordHasUpperCase(upperCaseRegex.test(newPassword));
        setPasswordHasNumber(numberRegex.test(newPassword));
        setPasswordHasSpecialChar(specialCharRegex.test(newPassword));
    };

    return (
        <div className="max-w-[560px]">
            <form
                onSubmit={handleSubmit(onSubmit)}
                onChange={() => handleChange()}>
                <h2 className="my-[32px] text-[24px] font-semibold">
                    Reset password
                </h2>
                <div className="w-full flex flex-col gap-[32px]">
                    <div className="w-full flex flex-col gap-[12px]">
                        <div className="form-label text-[16px] font-semibold">
                            Current password
                        </div>
                        <div className="mb-[4px] w-full flex flex-col gap-[4px]">
                            <Controller
                                name="oldPassword"
                                control={control}
                                render={({field}) => (
                                    <PasswordInput
                                        className={`w-full ${errors.oldPassword ? 'input-field-error' : 'input-field'}`}
                                        type="password"
                                        showPasswordToggle={true}
                                        {...field}
                                    />
                                )}
                            />
                            {errors.oldPassword && (
                                <div className="form-error-note text-[#EC3636]">
                                    {errors.oldPassword.message}
                                </div>
                            )}
                        </div>
                        <div className="form-label text-[16px] font-semibold">
                            New password
                        </div>
                        <div className="w-full flex flex-col gap-[16px]">
                            <Controller
                                name="newPassword"
                                control={control}
                                render={({field}) => (
                                    <PasswordInput
                                        className={`w-full ${errors.newPassword ? 'input-field-error' : 'input-field'}`}
                                        type="password"
                                        showPasswordToggle={true}
                                        onFocus={() =>
                                            setShowPasswordValidationUI(true)
                                        }
                                        {...field}
                                    />
                                )}
                            />
                            {/* {errors.newPassword && (
                                <div className="form-error-note">
                                    {errors.newPassword.message}
                                </div>
                            )}
                            <div className="form-note">
                                Your password must contain at least 12
                                characters, one lower case letter, and one
                                number.
                            </div> */}
                            {showPasswordValidationUI && (
                                <div className="px-[24px] py-[12px] text-[12px] leading-[15.6px] flex flex-col gap-[4px] border border-[#D9D9D9] rounded-[24px]">
                                    <p className="text-[#999]">
                                        Your password must contain:
                                    </p>
                                    <PasswordValidationMessage
                                        isValid={passwordLengthValid}
                                        message="At least 12 characters"
                                    />
                                    <PasswordValidationMessage
                                        isValid={passwordHasLowerCase}
                                        message="At least 1 lower case letter"
                                    />
                                    <PasswordValidationMessage
                                        isValid={passwordHasUpperCase}
                                        message="At least 1 upper case letter"
                                    />
                                    <PasswordValidationMessage
                                        isValid={passwordHasNumber}
                                        message="At least 1 number"
                                    />
                                    <PasswordValidationMessage
                                        isValid={passwordHasSpecialChar}
                                        message="At least 1 special character"
                                    />
                                </div>
                            )}
                        </div>
                    </div>
                    {isDirty && (
                        <LoadingSubmitButton
                            loadingText="resetting..."
                            loading={isSubmitting}
                            className="primary-button sm:w-max mb-[16px] sm:mb-0">
                            Reset password
                        </LoadingSubmitButton>
                    )}
                </div>
            </form>
        </div>
    );
}
