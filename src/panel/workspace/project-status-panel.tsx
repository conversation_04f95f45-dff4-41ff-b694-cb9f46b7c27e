import {useEffect} from 'react';
import {Navigate, useSearchParams} from 'react-router-dom';
import {useProjectContext} from '@/context/project-context';
import {ProjectPanelContent} from './project-panel-content';
import {useGitInstallationsContext} from '@/context/git-installations-context';
import FullScreenLoading from '@/components/custom/full-screen-loading';
import {isLegacyProject} from '@/lib/utils';

export function ProjectStatusPanel() {
    const {project, projectState, isFetchingProject, isFetchingProjectState} =
        useProjectContext();
    const {
        overallStatus,
        isLoading: isGitInstallationsLoading,
        checkGitInstallations,
    } = useGitInstallationsContext();
    const [params] = useSearchParams();

    useEffect(() => {
        checkGitInstallations();
    }, [checkGitInstallations]);

    if (
        isFetchingProject === true ||
        isFetchingProjectState === true ||
        isGitInstallationsLoading ||
        overallStatus === undefined
    ) {
        const isNewProject = params.get('from') === 'project-name';
        return (
            <FullScreenLoading
                message={`${isNewProject ? 'Creating' : 'Retrieving'} project...`}
            />
        );
    }

    if (!project?.id) return null;

    if (overallStatus !== 'ACTIVE') {
        if (project.type === 'NEW_PRODUCT') {
            if (!projectState?.technicalSpec?.id) {
                return (
                    <Navigate
                        replace
                        to={`/workspace/project/${project.id}/prompt?action=NEW_PRODUCT`}
                    />
                );
            } else {
                return (
                    <Navigate
                        replace
                        to={`/workspace/project/${project.id}/tech-spec`}
                    />
                );
            }
        }
        return (
            <Navigate
                replace
                to={`/workspace/project/${project.id}/back-prop/connect-git`}
            />
        );
    }

    if (project.type === 'EXISTING_PRODUCT') {
        if (project.status === 'GITHUB_PENDING') {
            return (
                <Navigate
                    replace
                    to={`/workspace/project/${project.id}/back-prop/source-code`}
                />
            );
        } else if (
            !projectState?.technicalSpec ||
            projectState.technicalSpec.status === 'TODO'
        ) {
            return (
                <Navigate
                    replace
                    to={`/workspace/project/${project.id}/prompt?action=ONBOARD_CODE`}
                />
            );
        } else {
            return (
                <Navigate
                    replace
                    to={`/workspace/project/${project.id}/tech-spec`}
                />
            );
        }
    } else if (project.type === 'NEW_PRODUCT' && !project.isDisabled) {
        if (
            !projectState?.technicalSpec ||
            projectState.technicalSpec.status === 'TODO'
        ) {
            return (
                <Navigate
                    replace
                    to={`/workspace/project/${project.id}/prompt?action=NEW_PRODUCT`}
                />
            );
        } else {
            return (
                <Navigate
                    replace
                    to={`/workspace/project/${project.id}/tech-spec`}
                />
            );
        }
    } else if (isLegacyProject(project)) {
        // always redirect to tech-spec page which will redirect to prompt page if necessary
        return (
            <Navigate
                replace
                to={`/workspace/project/${project.id}/tech-spec`}
            />
        );
    }

    return (
        <ProjectPanelContent>
            <FullScreenLoading message="Unknown Project State" />
        </ProjectPanelContent>
    );
}
