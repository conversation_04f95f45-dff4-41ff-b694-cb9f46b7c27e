import {useCallback, useEffect, useState} from 'react';
import {ProjectPanelContent} from '../project-panel-content.tsx';
import iconCheck from '../icons/check.svg';
import iconGithub from '../icons/github.svg';
import iconAzureDevOps from '../icons/azure-devops.svg';
import {useGitInstallationsContext} from '@/context/git-installations-context';
import {useNavigate} from 'react-router-dom';
// import {logGAEvent} from '@/lib/utils';
import {UnCheckBox} from '@/modal/icons/un-checkbox.tsx';
import {CheckBox} from '@/modal/icons/checkbox.tsx';
import {fetchAzureDevOpsInstallUrl} from '@/lib/backend.ts';
import {useToast} from '@/hooks/use-toast.ts';

type GitPlatform = 'github' | 'azure-devops';

const gitPlatforms: {label: string; value: GitPlatform; icon: string}[] = [
    {label: 'GitHub', value: 'github', icon: iconGithub},
    {label: 'Azure DevOps', value: 'azure-devops', icon: iconAzureDevOps},
];

export function GitConnectPanel() {
    const [selectedGitPlatform, setSelectedGitPlatform] =
        useState<GitPlatform | null>('github');
    const {githubStatus, overallStatus, installations, checkGitInstallations} =
        useGitInstallationsContext();
    const navigateTo = useNavigate();
    const {toast} = useToast();

    useEffect(() => {
        checkGitInstallations();
    }, [checkGitInstallations]);

    const handleConnectGitPlatform = useCallback(async () => {
        if (!selectedGitPlatform) {
            toast({
                title: 'Please select a Git platform.',
                variant: 'destructive',
            });
            return;
        }
        const projectID = window.location.pathname.split('/')[3];
        // logGAEvent('github_connect_button_clicked', {
        //     event_category: 'Integration',
        //     event_label: overallStatus || 'unknown',
        //     project_id: projectID,
        //     action: overallStatus === 'PENDING' ? 'view_status' : 'connect',
        // });
        if (selectedGitPlatform === 'github') {
            if (githubStatus === 'PENDING') {
                navigateTo('/workspace/settings/integrations');
                return;
            }
            const url = import.meta.env.VITE_GITHUB_INSTALL_URL;
            if (url) {
                // logGAEvent('github_oauth_initiated', {
                //     event_category: 'Integration',
                //     project_id: projectID,
                // });
                localStorage.setItem(
                    'github-connect-redirect-to-project-id',
                    projectID,
                );
                window.location.assign(url);
            }
        } else if (selectedGitPlatform === 'azure-devops') {
            try {
                const url = await fetchAzureDevOpsInstallUrl();
                window.location.assign(url);
            } catch (error) {
                console.error('Error connecting to Azure DevOps:', error);
                toast({
                    title: 'Failed to initiate connection to Azure DevOps',
                    description: 'Please try again later',
                    variant: 'destructive',
                });
            }
        }
    }, [githubStatus, navigateTo, selectedGitPlatform, toast]);

    // Check if any installation is pending
    const hasPendingInstallation = installations.some(
        installation => installation.status === 'PENDING',
    );

    // Check if any installation is expired
    const hasExpiredInstallation = installations.some(
        installation => installation.status === 'EXPIRED',
    );

    const currentStatus = hasPendingInstallation
        ? 'PENDING'
        : hasExpiredInstallation
          ? 'EXPIRED'
          : (overallStatus ?? 'INACTIVE');

    return (
        <ProjectPanelContent>
            {currentStatus === 'PENDING' ? (
                <div className="min-h-full flex flex-col items-center justify-center">
                    <div className="max-w-[574px] flex flex-col items-center justify-center gap-[32px] sm:gap-[48px]">
                        <img
                            src={iconGithub}
                            alt="Git platform"
                            className="w-[64px] h-[64px]"
                        />
                        <p className="text-[20px] sm:text-[24px] text-center font-semibold leading-[130%] text-black">
                            Git platform connection waiting for organization
                            approval...
                        </p>
                        <button
                            className="secondary-button w-fit"
                            onClick={() =>
                                navigateTo('/workspace/settings/integrations')
                            }>
                            Check status
                        </button>
                    </div>
                </div>
            ) : (
                <div className="min-h-full flex flex-col sm:items-center sm:justify-center">
                    <div className="bg-white h-auto py-[8px] pb-[24px] flex flex-col w-full sm:m-0 sm:w-[574px]">
                        <div className="w-full flex flex-col items-center justify-between flex-grow gap-[24px]">
                            <div className="w-full flex flex-col items-center gap-0 flex-grow justify-center">
                                <h2 className="mb-[24px] sm:mb-[48px] text-[20px] sm:text-[24px] text-center font-semibold leading-[130%] text-black">
                                    {currentStatus === 'EXPIRED'
                                        ? 'Git platform connection expired'
                                        : 'Connect your Git platform to get started'}
                                </h2>
                                <div className="space-y-[24px] w-full">
                                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-[12px]">
                                        {gitPlatforms.map(platform => {
                                            const isSelected =
                                                selectedGitPlatform ===
                                                platform.value;
                                            return (
                                                <div
                                                    key={platform.value}
                                                    className={`flex-1 p-[16px] sm:p-[24px] flex justify-between items-center gap-[16px] border ${isSelected ? 'bg-[#F2F0FE] border-[#5B39F3]' : 'bg-[#FFF] border-[#999]'} rounded-[24px] cursor-pointer`}
                                                    onClick={() =>
                                                        setSelectedGitPlatform(
                                                            platform.value,
                                                        )
                                                    }>
                                                    <div className="flex items-center gap-[16px]">
                                                        <img
                                                            src={platform.icon}
                                                            alt={platform.label}
                                                            className="w-[32px] h-[32px]"
                                                        />
                                                        <span className="text-[18px] font-semibold leading-[28px]">
                                                            {platform.label}
                                                        </span>
                                                    </div>
                                                    {isSelected ? (
                                                        <CheckBox />
                                                    ) : (
                                                        <UnCheckBox />
                                                    )}
                                                </div>
                                            );
                                        })}
                                    </div>
                                    <div className="flex flex-col w-full text-[16px] leading-[150%] p-[24px] sm:p-[32px] gap-[16px] sm:gap-[24px] rounded-[24px] border border-[#D9D9D9]">
                                        {[
                                            'Create technical specs from your codebase',
                                            'Add new features with natural language',
                                            'Refactor outdated technologies easily',
                                            'Sync your repo with your existing product',
                                        ].map((text, index) => (
                                            <div
                                                key={index}
                                                className="flex items-start gap-[8px]">
                                                <img
                                                    src={iconCheck}
                                                    alt="Check"
                                                    className="w-[24px] h-[24px] flex-shrink-0"
                                                />
                                                <span className="text-[16px] leading-[150%] tracking-[-0.03px] text-[#333]">
                                                    {text}
                                                </span>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                            <button
                                onClick={handleConnectGitPlatform}
                                className="primary-button text-[18px] w-fit">
                                {currentStatus === 'EXPIRED'
                                    ? 'Reconnect'
                                    : 'Connect'}
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </ProjectPanelContent>
    );
}
