import {useEffect} from 'react';
import {useNavigate} from 'react-router-dom';
import {GitLocationPanel} from './git-location-panel';
import {useGitInstallationsContext} from '@/context/git-installations-context';
import {useProjectContext} from '@/context/project-context';

interface GitLocationWrapperProps {
    type: 'new-product' | 'onboard-code' | 'refactor-code' | 'custom';
}

export function GitLocationWrapper({type}: GitLocationWrapperProps) {
    const {project} = useProjectContext();
    const {overallStatus, isLoading} = useGitInstallationsContext();
    const navigateTo = useNavigate();

    useEffect(() => {
        if (!isLoading && overallStatus !== 'ACTIVE') {
            navigateTo(
                `/workspace/project/${project?.id}/back-prop/connect-git`,
            );
        }
    }, [navigateTo, project?.id, overallStatus, isLoading]);

    return <GitLocationPanel type={type} />;
}
