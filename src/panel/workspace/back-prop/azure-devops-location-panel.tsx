import {useCallback, useEffect, useState} from 'react';
import {useNavigate, useSearchParams} from 'react-router-dom';
import {useToast} from '@/hooks/use-toast';
import {useProjectContext} from '@/context/project-context';
import {useSubscriptionContext} from '@/context/subscription-context';
import {useAuthContext} from '@/context/auth-context';
import {useDocumentSubmission} from '@/context/document-submission-context';
import {LoadingSubmitButton} from '@/components/custom/loading-submit-button';
import {ComboSelect} from '@/components/custom/combo-select';
import {SelectItem} from '@/components/custom/select-with-filter';
import {Input} from '@/components/ui/input';
import {UpgradeModal} from '@/modal/upgrade-modal';
import {useGitInstallationsContext} from '@/context/git-installations-context';
import {
    GitLocation,
    GitLocationType,
    ProjectDocumentType,
} from '@/lib/entity-types';
import {
    onboardGitLocations,
    onboardGitLocationsForNewProduct,
    getAzureDevOpsAccounts,
    getAzureDevOpsProjects,
    getAzureDevOpsRepositories,
    getAzureDevOpsBranches,
    getBranchDetailsForRepository,
} from '@/lib/backend';
import {logGAEvent, isValidRepoName} from '@/lib/utils';
import {PlatformError} from '@/lib/platform-error';
import InfoCircle from '../icons/info-circle';
import CodeCurly from '../icons/code-curly';
import Revision from '../icons/revision';
import Wrench from '../icons/wrench';

type PropsType = {
    type: 'new-product' | 'onboard-code' | 'refactor-code' | 'custom';
};

function validateNewRepoName(repoName: string) {
    let error = '';
    let isValid = true;

    if (!repoName) {
        error = 'Repository name is required';
        isValid = false;
    } else if (repoName.length > 100) {
        error = 'Repository name must be maximum 100 characters';
        isValid = false;
    } else if (!isValidRepoName(repoName)) {
        error =
            'Repository name can only contain ASCII letters, digits, and the characters ., -, and _';
        isValid = false;
    } else {
        error = '';
        isValid = true;
    }

    return {isValid, error};
}

export default function AzureDevOpsGitPanel({type}: PropsType) {
    const [searchParams] = useSearchParams();

    // Get values from URL parameters for fallback
    const urlDocType = searchParams.get('docType') as ProjectDocumentType;

    // Use context data first, then URL params as fallback
    // This allows the git location panel to receive data from the ActionBuildCodebase component
    // when navigating from tech-spec page for NEW_PRODUCT, REFACTOR_CODE, and CUSTOM flows
    const {
        onApprove,
        isDocumentSubmitting,
        setIsDocumentSubmitting,
        userProfile: contextUserProfile,
    } = useDocumentSubmission();

    const [orgId, setOrgId] = useState<string>();
    const [orgList, setOrgList] = useState<SelectItem[]>([]);
    const [orgListLoading, setOrgListLoading] = useState(false);
    const [projectId, setProjectId] = useState('');
    const [projectList, setProjectList] = useState<SelectItem[]>([]);
    const [projectListLoading, setProjectListLoading] = useState(false);
    const [repoName, setRepoName] = useState('');
    const [repoList, setRepoList] = useState<SelectItem[]>([]);
    const [repoListLoading, setRepoListLoading] = useState(false);
    const [newRepoName, setNewRepoName] = useState('');
    const [newRepoNameError, setNewRepoNameError] = useState('');
    const [branchName, setBranchName] = useState('');
    const [branchList, setBranchList] = useState<SelectItem[]>([]);
    const [branchListLoading, setBranchListLoading] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [showUpgradeModal, setShowUpgradeModal] = useState(false);
    const {toast} = useToast();

    const navigateTo = useNavigate();
    const {project, projectState, refreshProjectState, refreshProject} =
        useProjectContext();

    // Add hooks for document submission logic
    const {overallStatus, checkGitInstallations} = useGitInstallationsContext();
    const {plan, refreshSubscription} = useSubscriptionContext();
    const {userProfile} = useAuthContext();

    useEffect(() => {
        (async () => {
            try {
                setOrgListLoading(true);
                const accounts = await getAzureDevOpsAccounts();
                const orgList = accounts.map(account => ({
                    label: account.name,
                    value: String(account.id),
                    disabled: false,
                }));
                setOrgList(orgList);
            } catch (error) {
                console.error(error);
                toast({
                    variant: 'default',
                    duration: 10000,
                    description: 'Failed to load Azure DevOps accounts',
                });
            } finally {
                setOrgListLoading(false);
            }
        })();
    }, [toast]);

    useEffect(() => {
        const {error} = validateNewRepoName(newRepoName);
        setNewRepoNameError(error);
    }, [newRepoName]);

    const handleContinue = useCallback(async () => {
        if (projectState?.codeGeneration?.status === 'IN_PROGRESS') {
            toast({
                variant: 'default',
                duration: 10000,
                description: 'Code generation is in progress',
            });
            navigateTo(`/workspace/project/${project?.id}/tech-spec`, {
                replace: true,
            });
            return;
        }

        try {
            // Validate repository name if creating a new repository
            if (repoName === 'Create new repository') {
                const newRepoNameTrimmed = newRepoName.trim();
                const {isValid, error} =
                    validateNewRepoName(newRepoNameTrimmed);
                setNewRepoNameError(error);
                if (!isValid) return;
            }

            if (!project?.id) return;

            setIsSubmitting(true);

            // Set document submitting state if provided from context
            if (setIsDocumentSubmitting) {
                setIsDocumentSubmitting(true);
            }

            // Check branch details for onboard-code type
            if (
                type === 'onboard-code' &&
                repoName &&
                branchName &&
                orgId &&
                projectId
            ) {
                try {
                    // Find the repository ID from the repoList based on the selected repoName
                    const selectedRepo = repoList.find(
                        repo => repo.value === repoName,
                    );
                    if (selectedRepo) {
                        const branchDetails =
                            await getBranchDetailsForRepository(
                                selectedRepo.value,
                                branchName,
                            );
                        if (branchDetails.isLocked) {
                            toast({
                                variant: 'default',
                                duration: 10000,
                                description:
                                    'This branch is currently locked and cannot be used. Please select a different branch.',
                            });
                            return;
                        }
                    }
                } catch (error) {
                    console.error('Error checking branch details:', error);
                    if (
                        error instanceof PlatformError &&
                        error.code === 'HttpStatusOther'
                    ) {
                        toast({
                            variant: 'default',
                            duration: 10000,
                            description: error.message,
                        });
                        return;
                    }
                }
            }

            const gitLocations: GitLocation[] = [];

            const currentLocation = {
                orgName: orgList?.find(org => org.value === orgId)?.label || '',
                projectName:
                    projectList?.find(p => p.value === projectId)?.label || '',
                repoId: repoName === 'Create new repository' ? '' : repoName,
                repoName:
                    repoName === 'Create new repository'
                        ? newRepoName
                        : repoName,
                branchName,
                createRepo: repoName === 'Create new repository',
                type: (type === 'onboard-code'
                    ? 'SOURCE'
                    : 'TARGET') as GitLocationType,
                // Azure DevOps specific fields
                azureOrgId: orgId,
                azureProjectId: projectId,
            };

            logGAEvent('git_location_continue_clicked', {
                event_category: 'Project',
                event_label: type,
                project_id: project.id,
                organization: orgId,
                project: projectId,
                repository:
                    repoName === 'Create new repository'
                        ? newRepoName
                        : repoName,
                branch: branchName || 'new_branch',
                provider: 'azure_devops',
            });
            gitLocations.push(currentLocation);

            // Add source location for refactor-code and custom projects
            if (
                (type === 'refactor-code' || type === 'custom') &&
                project.gitSource
            ) {
                gitLocations.push({
                    ...project.gitSource,
                });
            }

            // Onboard git locations based on project type
            if (type === 'new-product') {
                await onboardGitLocationsForNewProduct(
                    project.id,
                    currentLocation,
                );
            } else {
                await onboardGitLocations(project.id, gitLocations);
            }

            const proj = await refreshProject?.();
            await refreshProjectState?.({project: proj});

            // Document submission flow for NEW_PRODUCT, REFACTOR_CODE, and CUSTOM
            // This handles the code generation process after git location setup
            if (
                type === 'new-product' ||
                type === 'refactor-code' ||
                type === 'custom'
            ) {
                if (!onApprove) {
                    console.error(
                        'onApprove function not found in context for Azure DevOps',
                    );
                    toast({
                        variant: 'default',
                        duration: 10000,
                        description:
                            'Unable to start code generation. Please try again.',
                    });
                    setIsDocumentSubmitting?.(false);
                    setIsSubmitting(false);
                    return;
                }

                if (setIsDocumentSubmitting) {
                    await refreshSubscription?.();

                    // Check if user has FREE plan and show upgrade modal if needed
                    if (
                        plan === 'FREE' ||
                        contextUserProfile?.subscription?.planName === 'FREE'
                    ) {
                        setShowUpgradeModal(true);
                        logGAEvent('upgrade_modal_triggered', {
                            event_category: 'Subscription',
                            event_label: 'build_code_attempt',
                            project_id: project.id,
                            current_plan: plan,
                            action_attempted: 'build_code',
                            provider: 'azure_devops',
                        });
                        setIsDocumentSubmitting(false);
                        setIsSubmitting(false);
                        return;
                    }

                    // Check GitHub installation status
                    await checkGitInstallations();
                    if (
                        urlDocType === 'tech_spec' &&
                        overallStatus !== 'ACTIVE'
                    ) {
                        logGAEvent('github_installation_needed', {
                            event_category: 'Project',
                            event_label: type,
                            project_id: project.id,
                            github_status: overallStatus,
                            provider: 'azure_devops',
                        });
                        navigateTo({
                            pathname: `/workspace/project/${project.id}/tech-spec`,
                            search: '?popup=github-enable',
                        });
                        setIsDocumentSubmitting(false);
                        setIsSubmitting(false);
                        return;
                    }

                    try {
                        logGAEvent('code_generation_started', {
                            event_category: 'Project',
                            event_label: type,
                            project_id: project.id,
                            action: 'build_code',
                            subscription_plan:
                                userProfile?.subscription?.planName,
                            provider: 'azure_devops',
                        });

                        await onApprove();

                        // Navigate back to tech-spec page after successful submission
                        navigateTo(
                            `/workspace/project/${project.id}/tech-spec`,
                            {
                                replace: true,
                            },
                        );
                    } catch (error) {
                        console.error(
                            'Error in Azure DevOps onApprove:',
                            error,
                        );

                        toast({
                            variant: 'default',
                            duration: 10000,
                            description:
                                'Failed to start code generation. Please try again.',
                        });
                    }

                    setIsDocumentSubmitting(false);
                }
            } else if (type === 'onboard-code') {
                // Navigate to prompt page for onboard-code flow
                navigateTo(
                    `/workspace/project/${project.id}/prompt?action=ONBOARD_CODE`,
                    {replace: true},
                );
            }
        } catch (error: unknown) {
            console.error('Error in Azure DevOps handleContinue:', error);
            let message = 'Failed to onboard codebase';
            if (
                error instanceof PlatformError &&
                error.code === 'HttpStatus409'
            ) {
                message = error.message;
            } else if (error instanceof Error) {
                message = error.message;
            }
            toast({
                variant: 'default',
                duration: 10000,
                description: message,
            });
        } finally {
            setIsSubmitting(false);
            if (setIsDocumentSubmitting) {
                setIsDocumentSubmitting(false);
            }
        }
    }, [
        projectState?.codeGeneration?.status,
        toast,
        navigateTo,
        project?.id,
        project?.gitSource,
        repoName,
        setIsDocumentSubmitting,
        orgList,
        projectList,
        newRepoName,
        branchName,
        type,
        orgId,
        projectId,
        refreshProject,
        refreshProjectState,
        onApprove,
        refreshSubscription,
        plan,
        contextUserProfile?.subscription?.planName,
        checkGitInstallations,
        overallStatus,
        urlDocType,
        userProfile?.subscription?.planName,
        repoList,
    ]);

    const handleOrgChange = useCallback(
        (orgId: string) => {
            setOrgId(orgId);
            setProjectId('');
            setRepoName('');
            setNewRepoName('');
            setNewRepoNameError('');
            setBranchName('');
            (async () => {
                try {
                    setProjectListLoading(true);
                    const projects = await getAzureDevOpsProjects(orgId);
                    const projectList = projects.map(project => ({
                        label: project.name,
                        value: String(project.id),
                        disabled: false,
                    }));
                    setProjectList(projectList);
                } catch (error) {
                    console.error(error);
                    toast({
                        variant: 'default',
                        duration: 10000,
                        description: 'Failed to load Azure DevOps projects',
                    });
                } finally {
                    setProjectListLoading(false);
                }
            })();
        },
        [toast],
    );

    const handleProjectChange = useCallback(
        (projectId: string) => {
            setProjectId(projectId);
            setRepoName('');
            setNewRepoName('');
            setNewRepoNameError('');
            setBranchName('');
            (async () => {
                try {
                    if (!orgId) return;
                    setRepoListLoading(true);
                    const repos = await getAzureDevOpsRepositories(
                        orgId,
                        projectId,
                    );
                    const repoList = repos.map(repo => ({
                        label: repo.name,
                        value: repo.id as string,
                        disabled: false,
                    }));
                    setRepoList(repoList);
                } catch (error) {
                    console.error(error);
                    toast({
                        variant: 'default',
                        duration: 10000,
                        description: 'Failed to load Azure DevOps repositories',
                    });
                } finally {
                    setRepoListLoading(false);
                }
            })();
        },
        [orgId, toast],
    );

    const handleRepoChange = useCallback(
        (repo: string) => {
            if (!orgId) return;
            setRepoName(repo);
            setBranchName('');
            if (type !== 'onboard-code') return;
            (async () => {
                try {
                    setBranchListLoading(true);
                    const branches = await getAzureDevOpsBranches(
                        orgId,
                        projectId,
                        repo,
                    );
                    const branchList = branches.map(branch => ({
                        label: branch.name,
                        value: branch.name,
                        disabled: false,
                    }));
                    setBranchList(branchList);
                } catch (error) {
                    console.error(error);
                    toast({
                        variant: 'default',
                        duration: 10000,
                        description: 'Failed to load Azure DevOps branches',
                    });
                } finally {
                    setBranchListLoading(false);
                }
            })();
        },
        [orgId, type, projectId, toast],
    );

    const handleBranchChange = useCallback((branch: string) => {
        setBranchName(branch);
    }, []);

    return (
        <>
            <div className="flex-grow w-full space-y-[32px] sm:space-y-[42px]">
                <div className="sm:min-h-[300px] flex flex-col gap-[24px] sm:pl-[64px] sm:border-l border-[#D9D9D9]">
                    <div>
                        <label>
                            <span className="inline-block mb-[12px] text-[16px] text-[#333] font-semibold leading-[24px]">
                                Organization
                            </span>
                            <ComboSelect
                                value={orgId ?? ''}
                                options={orgList}
                                onChange={handleOrgChange}
                                placeholder="Select organization"
                                itemsLoading={orgListLoading}
                                loadingMessage="Loading..."
                                emptyMessage="No results found"
                                disabled={isSubmitting || isDocumentSubmitting}
                            />
                        </label>
                    </div>
                    {orgId && (
                        <div>
                            <label>
                                <span className="inline-block mb-[12px] text-[16px] text-[#333] font-semibold leading-[24px]">
                                    Project
                                </span>
                                <ComboSelect
                                    value={projectId}
                                    options={projectList}
                                    onChange={handleProjectChange}
                                    placeholder="Select project"
                                    itemsLoading={projectListLoading}
                                    loadingMessage="Loading..."
                                    emptyMessage="No results found"
                                    disabled={
                                        isSubmitting || isDocumentSubmitting
                                    }
                                />
                            </label>
                        </div>
                    )}
                    {orgId && projectId && (
                        <div>
                            <label>
                                <span className="inline-block mb-[12px] text-[16px] text-[#333] font-semibold leading-[24px]">
                                    Repository
                                </span>
                                <ComboSelect
                                    value={repoName}
                                    options={[
                                        ...(type !== 'onboard-code'
                                            ? [
                                                  {
                                                      label: 'Create new repository',
                                                      value: 'Create new repository',
                                                      disabled: false,
                                                  },
                                              ]
                                            : []),
                                        ...repoList,
                                    ]}
                                    onChange={handleRepoChange}
                                    placeholder="Select repository"
                                    itemsLoading={repoListLoading}
                                    loadingMessage="Loading..."
                                    emptyMessage="No results found"
                                    disabled={
                                        isSubmitting || isDocumentSubmitting
                                    }
                                />
                            </label>
                        </div>
                    )}
                    {orgId &&
                        projectId &&
                        repoName === 'Create new repository' && (
                            <div>
                                <label>
                                    <span className="inline-block mb-[12px] text-[16px] text-[#333] font-semibold leading-[24px]">
                                        Name your repository
                                    </span>
                                    <Input
                                        value={newRepoName}
                                        onChange={e =>
                                            setNewRepoName(e.target.value)
                                        }
                                        placeholder="Repository name"
                                        disabled={
                                            isSubmitting || isDocumentSubmitting
                                        }
                                        className={`w-full h-[48px] p-[12px] text-[16px] gap-[16px] leading-[19px] border border-[#999] rounded-[32px] hover:border-[#D4CBFC] focus:outline-none focus:border-[#D4CBFC] ${newRepoNameError && 'border-[#EC3636]'} placeholder:text-[#999]`}
                                    />
                                    {newRepoNameError && (
                                        <div className="mt-[4px] text-[14px] text-[#EC3636]">
                                            {newRepoNameError}
                                        </div>
                                    )}
                                </label>
                            </div>
                        )}
                    {orgId && projectId && repoName && (
                        <div>
                            <label>
                                <span className="inline-block mb-[12px] text-[16px] text-[#333] font-semibold leading-[24px]">
                                    Branch
                                </span>
                                {type !== 'onboard-code' ? (
                                    <div className="flex items-center gap-[4px]">
                                        <InfoCircle
                                            width={16}
                                            height={16}
                                            color="#333"
                                        />
                                        <p className="text-[16px] text-[#333]">
                                            Blitzy will create a new branch
                                        </p>
                                    </div>
                                ) : (
                                    <ComboSelect
                                        value={branchName}
                                        options={branchList}
                                        onChange={handleBranchChange}
                                        placeholder="Select branch"
                                        itemsLoading={branchListLoading}
                                        loadingMessage="Loading..."
                                        emptyMessage="No results found"
                                        disabled={
                                            isSubmitting || isDocumentSubmitting
                                        }
                                    />
                                )}
                            </label>
                        </div>
                    )}
                </div>
                {orgId &&
                    projectId &&
                    repoName &&
                    (type !== 'onboard-code' || branchName) && (
                        <div className="flex justify-end items-center gap-[24px]">
                            <LoadingSubmitButton
                                loading={isSubmitting || isDocumentSubmitting}
                                loadingText={
                                    type === 'new-product'
                                        ? 'Build codebase'
                                        : type === 'refactor-code'
                                          ? 'Refactor codebase'
                                          : type === 'custom'
                                            ? 'Generate Code'
                                            : 'Continue'
                                }
                                onClick={handleContinue}
                                className="primary-button w-auto">
                                <span className="flex justify-center items-center gap-[8px]">
                                    {type === 'new-product' ? (
                                        <CodeCurly />
                                    ) : type === 'refactor-code' ? (
                                        <Revision
                                            color="white"
                                            width={20}
                                            height={19.1}
                                        />
                                    ) : type === 'custom' ? (
                                        <Wrench
                                            color="white"
                                            width={20}
                                            height={19.1}
                                        />
                                    ) : null}
                                    {type === 'new-product'
                                        ? 'Build codebase'
                                        : type === 'refactor-code'
                                          ? 'Refactor codebase'
                                          : type === 'custom'
                                            ? 'Generate code'
                                            : 'Continue'}
                                </span>
                            </LoadingSubmitButton>
                        </div>
                    )}
            </div>
            <UpgradeModal
                isOpen={showUpgradeModal}
                onClose={() => setShowUpgradeModal(false)}
            />
        </>
    );
}
