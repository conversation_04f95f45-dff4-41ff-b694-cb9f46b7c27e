import {useEffect, useMemo, useState} from 'react';
import {Link} from 'react-router-dom';
import {ProjectPanelContent} from '../project-panel-content';
import iconGithub from '../icons/github.svg';
import iconAzureDevOps from '../icons/azure-devops.svg';
import {isValidRepoName} from '@/lib/utils';
import {
    Select,
    SelectContent,
    SelectItem as SelectItemOption,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import GitHubGitPanel from './github-location-panel';
import AzureDevOpsGitPanel from './azure-devops-location-panel';
import {UpgradeModal} from '@/modal/upgrade-modal';
import {useGitInstallationsContext} from '@/context/git-installations-context';
import {useDocumentSubmission} from '@/context/document-submission-context';

type PropsType = {
    type: 'new-product' | 'onboard-code' | 'refactor-code' | 'custom';
};

export function validateNewRepoName(repoName: string) {
    let error = '';
    let isValid = true;

    if (!repoName) {
        error = 'Repository name is required';
        isValid = false;
    } else if (repoName.length > 100) {
        error = 'Repository name must be maximum 100 characters';
        isValid = false;
    } else if (!isValidRepoName(repoName)) {
        error =
            'Repository name can only contain ASCII letters, digits, and the characters ., -, and _';
        isValid = false;
    } else {
        error = '';
        isValid = true;
    }

    return {isValid, error};
}

const gitPlatformOptionsArray = [
    {
        label: 'GitHub',
        value: 'github',
        isConnected: false,
        svc_type: 'GITHUB',
        icon: iconGithub,
    },
    {
        label: 'Azure DevOps',
        value: 'azure-devops',
        svc_type: 'AZURE_DEVOPS',
        isConnected: false,
        icon: iconAzureDevOps,
    },
    // {
    //     label: 'GitLab',
    //     value: 'gitlab',
    //     isConnected: true,
    //     icon: iconGithub,
    // },
    // {
    //     label: 'Bitbucket',
    //     value: 'bitbucket',
    //     isConnected: true,
    //     icon: iconAzureDevOps,
    // },
];

export function GitLocationPanel({type}: PropsType) {
    const [gitPlatformOptions, setGitPlatformOptions] = useState(
        gitPlatformOptionsArray,
    );
    const [gitPlatform, setGitPlatform] = useState<string>('');
    const [showUpgradeModal, setShowUpgradeModal] = useState(false);
    const {hasActiveGithubInstallation, hasActiveAzureDevOpsInstallation} =
        useGitInstallationsContext();

    // Get document submission state to disable platform selection when submitting
    const {isDocumentSubmitting} = useDocumentSubmission();

    useEffect(() => {
        (async () => {
            const newGitPlatformOptions = gitPlatformOptionsArray.map(
                gitPlatformOption => {
                    if (gitPlatformOption.svc_type === 'GITHUB') {
                        gitPlatformOption.isConnected =
                            hasActiveGithubInstallation;
                    } else if (gitPlatformOption.svc_type === 'AZURE_DEVOPS') {
                        gitPlatformOption.isConnected =
                            hasActiveAzureDevOpsInstallation;
                    }
                    return gitPlatformOption;
                },
            );

            setGitPlatformOptions(newGitPlatformOptions);
            setGitPlatform(
                newGitPlatformOptions.find(
                    gitPlatformOption => gitPlatformOption.isConnected,
                )?.value || '',
            );
        })();
    }, [hasActiveGithubInstallation, hasActiveAzureDevOpsInstallation]);

    const handleGitPlatformChange = (gitPlatform: string) => {
        setGitPlatform(gitPlatform);
    };

    const selectedGitPlatform = useMemo(
        () =>
            gitPlatformOptions.find(
                gitPlatformOption => gitPlatformOption.value === gitPlatform,
            ),
        [gitPlatform, gitPlatformOptions],
    );

    const gitPlatformsConnected = useMemo(() => {
        return gitPlatformOptions.filter(
            gitPlatform => gitPlatform.isConnected,
        );
    }, [gitPlatformOptions]);

    const gitPlatformsNotConnected = useMemo(() => {
        return gitPlatformOptions.filter(
            gitPlatform => !gitPlatform.isConnected,
        );
    }, [gitPlatformOptions]);

    return (
        <ProjectPanelContent>
            <div className="flex flex-col gap-[32px] sm:gap-[48px] pb-[32px] w-full">
                <div>
                    <h2 className="mb-[12px] text-[20px] sm:text-[24px] text-black font-semibold">
                        {type === 'new-product'
                            ? "Where should we put the new product's code?"
                            : type === 'onboard-code'
                              ? 'Import source code'
                              : type === 'refactor-code'
                                ? 'Where should we put the refactored code?'
                                : type === 'custom'
                                  ? 'Where should we put the code?'
                                  : 'Where should we put the code?'}
                    </h2>
                    <p className="text-[16px] text-[#333]">
                        {type === 'new-product'
                            ? 'Select the destination for your codebase'
                            : type === 'onboard-code'
                              ? 'Connect your codebase to get a detailed technical spec, so you can add features or refactor as needed!'
                              : type === 'refactor-code'
                                ? 'Select the destination for your modernized codebase'
                                : type === 'custom'
                                  ? 'Select the destination'
                                  : 'Select the destination'}
                    </p>
                </div>
                <div className="min-h-[300px] flex flex-col sm:flex-row sm:justify-start sm:items-start gap-[32px] sm:gap-[0px]">
                    <div className="sm:px-[48px] py-0 w-full sm:w-[300px] flex flex-col justify-start sm:justify-center items-center gap-[20px] sm:aspect-square">
                        {gitPlatformsConnected.length > 0 && (
                            <img
                                src={selectedGitPlatform?.icon}
                                alt={
                                    selectedGitPlatform?.label || 'Git platform'
                                }
                                className="w-[64px] h-[64px]"
                            />
                        )}
                        <div className="sm:space-y-[4px] w-full">
                            {gitPlatformsConnected.length <= 0 ? (
                                <p className="text-[16px] text-[#000] text-center font-semibold leading-[140%] sm:leading-[150%]">
                                    Connect Your Git Platform
                                </p>
                            ) : gitPlatformsConnected.length === 1 ? (
                                <p className="text-[16px] text-[#000] text-center font-semibold leading-[140%] sm:leading-[150%]">
                                    {gitPlatformsConnected[0].label}
                                </p>
                            ) : (
                                <Select
                                    value={gitPlatform}
                                    onValueChange={handleGitPlatformChange}
                                    disabled={isDocumentSubmitting}>
                                    <SelectTrigger className="w-full sm:w-[204px]">
                                        <SelectValue placeholder="Select Git platform" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {gitPlatformsConnected.map(
                                            gitPlatform => (
                                                <SelectItemOption
                                                    value={gitPlatform.value}
                                                    key={gitPlatform.value}>
                                                    {gitPlatform.label}
                                                </SelectItemOption>
                                            ),
                                        )}
                                    </SelectContent>
                                </Select>
                            )}
                            {gitPlatformsNotConnected.length > 0 && (
                                <div className="flex flex-col items-center gap-[0px]">
                                    {gitPlatformsNotConnected.map(
                                        gitPlatform => (
                                            <Link
                                                key={gitPlatform.value}
                                                to="/workspace/settings/integrations"
                                                className="text-[16px] text-brand-purple text-center leading-[140%] sm:leading-[150%]">
                                                + {gitPlatform.label}
                                            </Link>
                                        ),
                                    )}
                                </div>
                            )}
                        </div>
                    </div>
                    {gitPlatform === 'github' && <GitHubGitPanel type={type} />}
                    {gitPlatform === 'azure-devops' && (
                        <AzureDevOpsGitPanel type={type} />
                    )}
                </div>
            </div>
            <UpgradeModal
                isOpen={showUpgradeModal}
                onClose={() => setShowUpgradeModal(false)}
            />
        </ProjectPanelContent>
    );
}
