import {useCallback, useState} from 'react';
import {ProjectDialogType} from '@/modal/project-dialog';
import {
    PendingStates,
    ProjectDocumentType,
    ProjectState,
} from '@/lib/entity-types';

export function useProjectDialog() {
    const [dialogType, setDialogType] = useState<
        ProjectDialogType | undefined
    >();

    const closeDialog = useCallback(() => {
        setDialogType(undefined);
    }, []);

    const checkProjectDocumentDialog = useCallback(
        (
            projectState: ProjectState | undefined,
            docType: ProjectDocumentType,
            isTechSpecSubmitting?: boolean,
            currentIndex?: number,
        ) => {
            if (projectState) {
                if (docType === 'tech_spec') {
                    if (projectState?.technicalSpec?.status === 'IN_PROGRESS') {
                        if (
                            currentIndex === null ||
                            currentIndex === undefined
                        ) {
                            setDialogType('pending-tech-spec');
                        } else {
                            setDialogType(undefined);
                        }
                        return;
                    }
                    if (
                        PendingStates.includes(
                            projectState?.technicalSpec?.status || '',
                        )
                    ) {
                        setDialogType('pending-tech-spec');
                    } else if (
                        ['DONE', 'SUBMITTED'].includes(
                            projectState?.technicalSpec?.status || '',
                        )
                    ) {
                        setDialogType(undefined);
                    } else if (
                        isTechSpecSubmitting &&
                        projectState?.codeGeneration?.status === 'TODO'
                    ) {
                        setDialogType('start-building');
                    }
                }
            }
        },
        [],
    );
    return {dialogType, checkProjectDocumentDialog, closeDialog};
}
