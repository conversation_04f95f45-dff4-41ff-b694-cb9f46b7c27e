import {useCallback, useEffect, useState} from 'react';
import {getCodeGenCommitStatus} from '@/lib/backend';
import {CodeGenCommitStatus} from '@/lib/entity-types';
import {useProjectContext} from '@/context/project-context';

export function useCommitStatus(projectId: string, codeGenId: string) {
    const [status, setStatus] = useState<CodeGenCommitStatus>();
    const {projectState} = useProjectContext();
    const codeGenStatus = projectState?.codeGeneration;

    const checkCommitStatus = useCallback(async () => {
        if (projectId && codeGenId && codeGenStatus?.status === 'DONE') {
            try {
                const response = await getCodeGenCommitStatus(
                    projectId,
                    codeGenId,
                );
                setStatus(response);
                return response;
            } catch (err) {
                console.error('Failed to fetch commit status:', err);
            }
        }
    }, [projectId, codeGenId, codeGenStatus]);

    const resetStatus = useCallback(() => {
        setStatus(undefined);
    }, []);

    useEffect(() => {
        if (projectState?.codeGeneration) {
            checkCommitStatus();
        }
    }, [checkCommitStatus, projectState?.codeGeneration]);

    return {
        status,
        checkCommitStatus,
        resetStatus,
    };
}
