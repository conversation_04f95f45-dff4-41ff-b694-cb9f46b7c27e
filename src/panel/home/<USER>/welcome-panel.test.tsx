import {it, expect} from 'vitest';
import '@testing-library/jest-dom/vitest';
import {MemoryRouter} from 'react-router-dom';
import {render, screen} from '@testing-library/react';
import {WelcomePanel} from '../welcome-panel';

it('WelcomePanel is rendered correctly', () => {
    render(<WelcomePanel />, {
        wrapper: ({children}) => (
            <MemoryRouter initialEntries={['/']}>{children}</MemoryRouter>
        ),
    });

    screen.debug();

    expect(screen.getByText('Create your Blitzy account')).toBeInTheDocument();
});
