import {useCallback, useState} from 'react';
import {Link, useNavigate, useSearchParams} from 'react-router-dom';
import {z} from 'zod';
import {Controller, SubmitHandler, useForm} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import {Input} from '@/components/ui/input';
import {useToast} from '@/hooks/use-toast';
import {HomePanelShell} from './home-panel-shell';
import {useAuthContext} from '@/context/auth-context';
import logoGoogle from '/images/logo-google.svg';
import {Config} from '@/config';
import {LoadingSubmitButton} from '@/components/custom/loading-submit-button';
import {logGAEvent} from '@/lib/utils';
import {PasswordInput} from '@/components/ui/passwordInput';
import {TermClause} from '@/components/custom/term-clause';
import {MicrosoftLoginButton} from '@/components/custom/microsoft-login-button';
import {FirebaseError} from 'firebase/app';
import {LinkAccountsDialog} from '@/panel/workspace/link-accounts-dialog';
import {SSOSignInButton} from '@/components/custom/sso-login-button';

const EmailSchema = z.object({
    email: z.string().min(1, {message: 'Email is required'}).email(),
    password: z.string().min(1, {message: 'Password is required'}),
});

type EmailSchemaType = z.infer<typeof EmailSchema>;

export function SignInPanel() {
    const {googleLogin, login} = useAuthContext();
    const {toast, dismiss} = useToast();
    const navigateTo = useNavigate();
    const [searchParams] = useSearchParams();

    const defaulEmail = searchParams.get('email') as string;
    const [isLinkAccountsDialogOpen, setIsLinkAccountsDialogOpen] =
        useState(false);
    const [existingAccountEmail, setExistingAccountEmail] =
        useState<string>('');
    const [existingAccountProvider, setExistingAccountProvider] = useState<
        'google' | 'microsoft'
    >('google');

    const {
        control,
        handleSubmit,
        formState: {errors, isSubmitting},
    } = useForm<EmailSchemaType>({
        resolver: zodResolver(EmailSchema),
        mode: 'onTouched',
        defaultValues: {
            email: defaulEmail ?? '',
        },
    });

    const handleGoogleLogin = useCallback(async () => {
        if (googleLogin) {
            dismiss();
            try {
                await googleLogin();
                logGAEvent('sign_in', {
                    method: 'google',
                });
                navigateTo(
                    {
                        pathname: '/workspace',
                    },
                    {replace: true},
                );
            } catch (error) {
                if (error instanceof FirebaseError) {
                    if (
                        error.code ===
                        'auth/account-exists-with-different-credential'
                    ) {
                        const email = error.customData?.email as string;
                        if (email) {
                            setIsLinkAccountsDialogOpen(true);
                            setExistingAccountEmail(email);
                            setExistingAccountProvider('google');
                        }
                    } else {
                        console.error('Google login error:', error);
                        toast({
                            variant: 'destructive',
                            description:
                                'An error occurred during sign in. Please try again.',
                        });
                    }
                }
            }
        }
    }, [googleLogin, navigateTo, toast, dismiss]);

    const onSubmit: SubmitHandler<EmailSchemaType> = useCallback(
        async data => {
            dismiss();
            const {email, password} = data;
            if (email && password && login) {
                const result = await login(email, password);
                if ('user_id' in result) {
                    navigateTo('/workspace', {replace: true});
                } else {
                    const errorMsg = result.errorMsg;
                    if (errorMsg.includes('too-many-requests')) {
                        toast({
                            variant: 'destructive',
                            duration: 10000,
                            description:
                                'Too many requests. Please try again later.',
                        });
                    } else {
                        toast({
                            variant: 'destructive',
                            duration: 10000,
                            description: errorMsg,
                        });
                    }
                }
            }
        },
        [login, navigateTo, toast, dismiss],
    );

    return (
        <>
            <HomePanelShell
                title="Sign in"
                subtitle={
                    <>
                        New to Blitzy?&nbsp;
                        <Link
                            to="/welcome"
                            onClick={() => {
                                logGAEvent('signup_link_clicked', {
                                    source: 'sign_in_page',
                                    user_id: undefined,
                                });
                            }}>
                            Create an account
                        </Link>
                    </>
                }>
                <div className="flex flex-col justify-center items-start gap-[32px] flex-1 align-self-stretch">
                    <form className="w-full" onSubmit={handleSubmit(onSubmit)}>
                        <div className="w-full flex flex-col gap-[8px] sm:gap-[12px]">
                            <div className="form-label">Email</div>
                            <div className="mb-[4px] w-full flex flex-col gap-[4px]">
                                <Controller
                                    name="email"
                                    control={control}
                                    render={({field}) => (
                                        <Input
                                            className={`w-full ${errors.email ? 'input-field-error' : 'input-field'}`}
                                            type="email"
                                            placeholder="<EMAIL>"
                                            {...field}
                                        />
                                    )}
                                />
                                {errors.email && (
                                    <div className="form-error-note">
                                        {errors.email.message}
                                    </div>
                                )}
                            </div>
                            <div className="form-label">Password</div>
                            <div className="w-full flex flex-col gap-[4px]">
                                <Controller
                                    name="password"
                                    control={control}
                                    render={({field}) => (
                                        <PasswordInput
                                            className={`${errors.password ? 'input-field-error' : 'input-field'} pr-10`}
                                            type="password"
                                            showPasswordToggle={true}
                                            placeholder="Enter your password"
                                            {...field}
                                        />
                                    )}
                                />
                                {errors.password && (
                                    <div className="form-error-note">
                                        {errors.password.message}
                                    </div>
                                )}
                            </div>
                            <div className="flex flex-col pt-[8px] sm:pt-[12px]">
                                <LoadingSubmitButton
                                    loading={isSubmitting}
                                    loadingText="Signing in..."
                                    className="primary-button w-auto">
                                    Sign in
                                </LoadingSubmitButton>
                            </div>
                        </div>
                    </form>

                    <div className="text-[var(--text-text-secondary,#333)] font-inter text-[16px] font-normal leading-[150%] tracking-[-0.3px] flex items-center gap-[8px] self-stretch">
                        <div className="flex-1 h-[1px] bg-[var(--border-border-secondary,#D9D9D9)]"></div>
                        or
                        <div className="flex-1 h-[1px] bg-[var(--border-border-secondary,#D9D9D9)]"></div>
                    </div>

                    {Config.SUPPORT_GOOGLE_LOGIN && (
                        <>
                            <div
                                role="button"
                                onClick={handleGoogleLogin}
                                className="flex justify-center items-center gap-[8px] sm:gap-[12px] self-stretch rounded-[32px] bg-[var(--background-bg-tertiary,#F5F5F5)] px-[16px] sm:px-[24px] py-[10px] sm:py-[12px]">
                                <img
                                    className="w-[20px] h-[20px]"
                                    src={logoGoogle}
                                    alt="google"
                                />
                                <div className="text-[var(--text-text-secondary,#333)] text-[18px] font-semibold leading-[28px]">
                                    Continue with Google
                                </div>
                            </div>
                        </>
                    )}
                    <div className="flex flex-col gap-[16px] w-full">
                        <SSOSignInButton />
                        {Config.SUPPORT_MICROSOFT_LOGIN && (
                            <MicrosoftLoginButton />
                        )}
                    </div>
                    <div className="w-full text-center text-[#333333] text-[14px] font-normal leading-[21px] tracking-[-0.3px] flex flex-col items-center gap-[8px] sm:gap-[12px]">
                        <Link
                            to="/forgot-password"
                            onClick={() => {
                                logGAEvent('forgot_password_clicked', {
                                    source: 'sign_in_page',
                                    user_id: undefined,
                                });
                            }}>
                            Forgot your password?
                        </Link>
                        <TermClause srcPage="sign_in_page" />
                    </div>
                </div>
                <LinkAccountsDialog
                    isOpen={isLinkAccountsDialogOpen}
                    onClose={() => setIsLinkAccountsDialogOpen(false)}
                    email={existingAccountEmail}
                    provider={existingAccountProvider}
                />
            </HomePanelShell>
        </>
    );
}
