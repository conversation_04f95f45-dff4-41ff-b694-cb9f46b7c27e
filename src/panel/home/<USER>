import {useCallback, useEffect} from 'react';
import {Link, useNavigate} from 'react-router-dom';
import logoGoogle from '/images/logo-google.svg';
import {z} from 'zod';
import {zodResolver} from '@hookform/resolvers/zod';
import {Input} from '@/components/ui/input';
import {HomePanelShell} from './home-panel-shell';
import {useAuthContext} from '@/context/auth-context';
import {Controller, SubmitHandler, useForm} from 'react-hook-form';
import {useHomeContext} from '@/home';
import {Config} from '@/config';
import {logGAEvent, useUtmTracker} from '@/lib/utils';
import {LoadingSubmitButton} from '@/components/custom/loading-submit-button';
import {TermClause} from '@/components/custom/term-clause';
import {MicrosoftLoginButton} from '@/components/custom/microsoft-login-button';
import {SSOSignInButton} from '@/components/custom/sso-login-button';

const EmailSchema = z.object({
    email: z.string().min(1, {message: 'Email is required'}).email(),
});

type EmailSchemaType = z.infer<typeof EmailSchema>;

export function WelcomePanel() {
    const {signUpEmail, setSignUpEmail} = useHomeContext();
    const {googleLogin} = useAuthContext();
    const navigateTo = useNavigate();
    const {
        control,
        handleSubmit,
        formState: {errors, isSubmitting},
        getValues,
    } = useForm<EmailSchemaType>({
        resolver: zodResolver(EmailSchema),
        mode: 'onTouched',
        defaultValues: {
            email: '',
        },
        values: {
            email: signUpEmail || '',
        },
    });

    const {getStoredUtmParams} = useUtmTracker();
    useEffect(() => {
        getStoredUtmParams();
    }, [getStoredUtmParams]);

    const handleGoogleLogin = useCallback(async () => {
        if (googleLogin) {
            await googleLogin();
            navigateTo(
                {
                    pathname: '/workspace/projects',
                    search: '?popup=feature-display',
                },
                {replace: true},
            );
        }
    }, [googleLogin, navigateTo]);

    const onSubmit: SubmitHandler<EmailSchemaType> = useCallback(
        async data => {
            // console.log(data);
            const {email} = data;
            if (email && setSignUpEmail) {
                setSignUpEmail(email);
                logGAEvent('signup_email_continue', {
                    method: 'email',
                    email: email,
                    step: 'email_submission',
                });
                navigateTo('/sign-up');
            }
        },
        [navigateTo, setSignUpEmail],
    );

    return (
        <>
            <HomePanelShell
                title="Create your Blitzy account"
                subtitle={
                    <>
                        Already have an account?&nbsp;
                        <Link
                            to="sign-in"
                            onClick={() => {
                                logGAEvent('signin_link_clicked', {
                                    source: 'signup_email_page',
                                    user_id: undefined,
                                });
                            }}>
                            Sign in
                        </Link>
                    </>
                }>
                <div className="w-full flex flex-col gap-[32px] items-center">
                    {Config.SUPPORT_GOOGLE_LOGIN && (
                        <>
                            <div
                                role="button"
                                onClick={handleGoogleLogin}
                                className=" flex w-full flex-row gap-[8px] justify-center items-center px-[12px] py-[16px] rounded-[32px] bg-[#F6F6F6]">
                                <img
                                    className="w-[20px] h-[20px]"
                                    src={logoGoogle}
                                    alt="google"
                                />
                                <div className="text-[#333] text-[16px] font-bold">
                                    Continue with Google
                                </div>
                            </div>
                        </>
                    )}
                    <form className="w-full" onSubmit={handleSubmit(onSubmit)}>
                        <div className="w-full flex flex-col gap-[12px]">
                            <div className="form-label">Work email</div>
                            <div className="mb-[12px] w-full flex flex-col gap-[4px]">
                                <Controller
                                    name="email"
                                    control={control}
                                    render={({field}) => (
                                        <Input
                                            className={`w-full ${errors.email ? 'input-field-error' : 'input-field'}`}
                                            type="email"
                                            placeholder="<EMAIL>"
                                            {...field}
                                        />
                                    )}
                                />
                                {errors.email && (
                                    <div className="form-error-note">
                                        {errors.email.message}
                                    </div>
                                )}
                            </div>
                            <LoadingSubmitButton
                                loadingText="One moment..."
                                loading={isSubmitting}
                                className="primary-button">
                                {getValues().email && !errors.email
                                    ? 'Create Account'
                                    : 'Continue'}
                            </LoadingSubmitButton>
                        </div>
                    </form>
                    <div className="text-[var(--text-text-secondary,#333)] font-inter text-[16px] font-normal leading-[150%] tracking-[-0.3px] flex items-center gap-[8px] self-stretch">
                        <div className="flex-1 h-[1px] bg-[var(--border-border-secondary,#D9D9D9)]"></div>
                        or
                        <div className="flex-1 h-[1px] bg-[var(--border-border-secondary,#D9D9D9)]"></div>
                    </div>
                    <div className="flex flex-col gap-[16px] w-full">
                        <SSOSignInButton />
                        {Config.SUPPORT_MICROSOFT_LOGIN && (
                            <MicrosoftLoginButton
                                successPath="/workspace/projects"
                                successSearch="?popup=feature-display"
                            />
                        )}
                    </div>
                </div>
                <TermClause srcPage="welcome_page" />
            </HomePanelShell>
        </>
    );
}
