import {useAuthContext} from '@/context/auth-context';
import iconBlitzy from '/images/blitzy-icon-text.svg';
import gradient from '/images/gradient.png';
import {useLocation, useNavigate} from 'react-router-dom';
import {useEffect} from 'react';

export function PageNotFound() {
    const {userProfile, isUserProfileLoading} = useAuthContext();
    const navigate = useNavigate();
    const {pathname} = useLocation();

    console.error('404 ' + pathname);

    // Handle automatic redirect for unauthenticated users
    useEffect(() => {
        if (userProfile === undefined && isUserProfileLoading === false) {
            navigate('/');
        }
    }, [userProfile, isUserProfileLoading, navigate]);

    const handleGoBack = () => {
        // First try to go back in history
        if (window.history.length > 1) {
            navigate(-1);
        } else {
            // If no history, go to home
            navigate('/');
        }
    };

    return (
        <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-white  relative">
            {/* Background gradient */}
            <img
                src={gradient}
                alt=""
                className="absolute top-0 left-0 right-0 w-full h-full object-cover"
                aria-hidden="true"
            />

            {/* Logo header */}
            <header className="absolute top-10 left-0 right-0 flex justify-center w-full">
                <img
                    src={iconBlitzy}
                    alt="Blitzy"
                    className="w-full max-w-7xl min-w-[830px] h-10 sm:h-[40px] mt-4"
                />
            </header>

            {/* Main content */}
            <main className="max-w-2xl mx-auto text-center relative z-10">
                <h1 className="bg-gradient-to-r from-[#B23AF2] via-[#5B39F3] to-[#94FAD5] text-transparent bg-clip-text text-5xl  font-normal leading-tight tracking-tight mb-1">
                    Page not found
                </h1>

                <p className="text-lg font-normal text-gray-700 leading-tight tracking-tight mb-10">
                    Sorry! We couldn't find the page you were looking for.
                </p>

                <button
                    onClick={handleGoBack}
                    className="primary-button min-w-max px-5 py-2 mb-6"
                    aria-label="Go back to previous page">
                    Go back
                </button>

                <div className="text-sm">
                    <span className="text-gray-400">Need help? </span>
                    <a href="/support" aria-label="Contact support">
                        Send us a message
                    </a>
                </div>
            </main>
        </div>
    );
}
