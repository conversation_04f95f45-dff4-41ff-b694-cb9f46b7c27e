import logo from '/images/logo-blitzy-purple.svg';
import {Link} from 'react-router-dom';

interface HomePanelShellProps {
    children: React.ReactNode;
    title: string;
    subtitle: React.ReactNode;
    icons?: React.ReactNode;
}

export function HomePanelShell({
    children,
    title,
    subtitle,
    icons,
}: HomePanelShellProps) {
    return (
        <div className="home-panel">
            <div className="w-full flex flex-col gap-[8px] sm:gap-[12px] items-center">
                <Link to="/">
                    <img
                        src={logo}
                        alt="logo"
                        className="block sm:hidden mb-[12px]"
                    />
                </Link>
                {icons}
                <div className="text-[24px] text-center sm:text-[32px] leading-[1em] tracking-[-0.48px] sm:tracking-[-0.64px]">
                    {title}
                </div>
                <div className="text-[#33333380] text-center text-[16px] leading-[1.2em] tracking-[-0.3px]">
                    {subtitle}
                </div>
            </div>
            {children}
        </div>
    );
}
