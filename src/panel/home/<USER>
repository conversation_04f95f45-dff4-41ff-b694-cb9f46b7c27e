import {useCallback, useState} from 'react';
import {sendPasswordResetEmail} from 'firebase/auth';
import {z} from 'zod';
import {Controller, SubmitHandler, useForm} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import {auth} from '@/firebase';
import {Input} from '@/components/ui/input';
import {LoadingSubmitButton} from '@/components/custom/loading-submit-button';
import {HomePanelShell} from './home-panel-shell';
import {Link, useNavigate} from 'react-router-dom';
import {useToast} from '@/hooks/use-toast';
const EmailSchema = z.object({
    email: z.string().min(1, {message: 'Email is required'}).email(),
});

type EmailSchemaType = z.infer<typeof EmailSchema>;

export function ForgotPasswordPanel() {
    const [isOnForgotPasswordPage, setIsOnForgotPasswordPage] = useState(true);
    const navigate = useNavigate();
    const {toast} = useToast();

    const {
        control,
        handleSubmit,
        formState: {errors, isSubmitting},
    } = useForm<EmailSchemaType>({
        resolver: zodResolver(EmailSchema),
        mode: 'onTouched',
        defaultValues: {
            email: '',
        },
    });

    const onSubmit: SubmitHandler<EmailSchemaType> = useCallback(
        async data => {
            const {email} = data;
            try {
                await sendPasswordResetEmail(auth, email);
                setIsOnForgotPasswordPage(false);
            } catch (error: unknown) {
                const firebaseError = error as {code?: string};
                const errorCode = firebaseError?.code;

                switch (errorCode) {
                    case 'auth/user-not-found':
                        toast({
                            variant: 'destructive',
                            duration: 10000,
                            description:
                                'If an account exists, a password reset email will be sent',
                        });
                        return;
                    default: {
                        const errorMessage = (() => {
                            switch (errorCode) {
                                case 'auth/invalid-email':
                                    return 'Please enter a valid email address';
                                case 'auth/too-many-requests':
                                    return 'Too many attempts. Please try again later';
                                case 'auth/network-request-failed':
                                    return 'Network error. Please check your connection';
                                default:
                                    console.error(
                                        'Password reset error:',
                                        error,
                                    );
                                    return 'An error occurred. Please try again';
                            }
                        })();

                        toast({
                            variant: 'destructive',
                            duration: 10000,
                            description: errorMessage,
                        });

                        throw error;
                    }
                }
            }
        },
        [toast],
    );

    const handleBack = (e: React.MouseEvent<HTMLAnchorElement>) => {
        e.preventDefault();
        if (isOnForgotPasswordPage) {
            navigate(-1);
        } else {
            setIsOnForgotPasswordPage(true);
        }
    };

    return (
        <HomePanelShell
            title={
                isOnForgotPasswordPage
                    ? 'Forgot your password?'
                    : 'Check your inbox'
            }
            subtitle={`We ${isOnForgotPasswordPage ? 'will send' : 'have sent'} a password reset link to your email`}>
            {isOnForgotPasswordPage ? (
                <div className="flex flex-col justify-center items-start gap-10  flex-1 align-self-stretch">
                    <form className="w-full" onSubmit={handleSubmit(onSubmit)}>
                        <div className="w-full flex flex-col gap-[12px]">
                            <div className="form-label">Email</div>
                            <div className="mb-[4px] w-full flex flex-col gap-[4px]">
                                <Controller
                                    name="email"
                                    control={control}
                                    render={({field}) => (
                                        <Input
                                            className={`w-full ${errors.email ? 'input-field-error' : 'input-field'}`}
                                            type="email"
                                            placeholder="<EMAIL>"
                                            {...field}
                                        />
                                    )}
                                />
                                {errors.email && (
                                    <div className="form-error-note">
                                        {errors.email.message}
                                    </div>
                                )}
                            </div>
                            <LoadingSubmitButton
                                loadingText="Sending..."
                                loading={isSubmitting}
                                className="primary-button w-auto">
                                Send reset link
                            </LoadingSubmitButton>
                        </div>
                    </form>
                </div>
            ) : (
                <button
                    className="primary-button"
                    onClick={() => navigate('/sign-in')}>
                    Sign in
                </button>
            )}
            <div className="w-full text-center text-[#333333] text-[14px] font-normal leading-[21px] tracking-[-0.3px]">
                <Link
                    to={
                        isOnForgotPasswordPage ? '/sign-in' : '/forgot-password'
                    }
                    onClick={handleBack}>
                    Back to{' '}
                    {isOnForgotPasswordPage ? 'sign in' : 'forgot password'}
                </Link>
            </div>
        </HomePanelShell>
    );
}
