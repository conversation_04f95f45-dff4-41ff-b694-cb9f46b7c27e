import {useCallback, useEffect, useState} from 'react';
import {Link, Navigate, useNavigate} from 'react-router-dom';
import {useF<PERSON>, SubmitHandler, Controller} from 'react-hook-form';
import {z} from 'zod';
import {zodResolver} from '@hookform/resolvers/zod';
import {Input} from '@/components/ui/input';
import {HomePanelShell} from './home-panel-shell';
import {register} from '@/lib/backend';
import {useHomeContext} from '@/home';
import {useAlertContext} from '@/context/alert-context';
import {extractCompanyFromEmail, logGAEvent, useUtmTracker} from '@/lib/utils';
import {
    lowerCaseRegex,
    upperCaseRegex,
    numberRegex,
    specialCharRegex,
} from '@/lib/utils';
import {LoadingSubmitButton} from '@/components/custom/loading-submit-button';
import {PlatformError} from '@/lib/platform-error';
import {useAuthContext} from '@/context/auth-context';
import {PasswordInput} from '@/components/ui/passwordInput';
import PasswordValidationMessage from '@/components/custom/password-validation-message';
import {TermClause} from '@/components/custom/term-clause';
import {MicrosoftLoginButton} from '@/components/custom/microsoft-login-button';
import {SSOSignInButton} from '@/components/custom/sso-login-button';
import {Config} from '@/config';

// const EMAIL_MUST_BE_VERIFIED =
//     import.meta.env.VITE_EMAIL_MUST_BE_VERIFIED === 'true';
// const POLLING_INTERVAL =
//     Number(import.meta.env.VITE_SIGNUP_UPDATE_PROFILE_POLLING_INTERVAL) || 2000;
// const MAX_RETRIES =
//     Number(import.meta.env.VITE_SIGNUP_UPDATE_PROFILE_MAX_RETRIES) || 5;

const SignUpSchema = z.object({
    // email: z.string().email().min(1, {message: 'Email is required'}),
    firstName: z.string().min(1, {message: 'First name is required'}),
    lastName: z.string().min(1, {message: 'Last name is required'}),
    company: z.string().min(1, {message: 'Company name is required'}),
    password: z
        .string()
        .min(12, {message: 'Password must be at least 12 characters long'})
        .refine(password => lowerCaseRegex.test(password), {
            message: 'Password must contain at least one lowercase letter',
        })
        .refine(password => upperCaseRegex.test(password), {
            message: 'Password must contain at least one uppercase letter',
        })
        .refine(password => numberRegex.test(password), {
            message: 'Password must contain at least one number',
        })
        .refine(password => specialCharRegex.test(password), {
            message: 'Password must contain at least one special character',
        }),
});

type SignUpSchemaType = z.infer<typeof SignUpSchema>;

export function SignUpPanel() {
    const navigateTo = useNavigate();
    const {signUpEmail} = useHomeContext();
    const {showAlert} = useAlertContext();
    const {login} = useAuthContext();
    const {getStoredUtmParams} = useUtmTracker();
    const utmParams = getStoredUtmParams();
    // const [isLoading, setIsLoading] = useState(false); // Added loading state
    // const [message, setMessage] = useState<string | null>(null); // Added message state
    const [passwordLengthValid, setPasswordLengthValid] = useState<boolean>();
    const [passwordHasLowerCase, setPasswordHasLowerCase] = useState<boolean>();
    const [passwordHasUpperCase, setPasswordHasUpperCase] = useState<boolean>();
    const [passwordHasNumber, setPasswordHasNumber] = useState<boolean>();
    const [passwordHasSpecialChar, setPasswordHasSpecialChar] =
        useState<boolean>();
    const [showPasswordValidationUI, setShowPasswordValidationUI] =
        useState<boolean>(false);

    const {
        control,
        handleSubmit,
        setValue,
        getValues,
        formState: {errors, isSubmitting},
    } = useForm<SignUpSchemaType>({
        resolver: zodResolver(SignUpSchema),
        mode: 'onTouched',
        defaultValues: {
            // email: signUpEmail,
            firstName: '',
            lastName: '',
            company: '',
            password: '',
        },
    });
    // Function to prevent leading spaces but allow spaces in between
    const handleInputChange = (field: any, value: string) => {
        // Prevent leading spaces
        if (value.startsWith(' ')) {
            return;
        }
        field.onChange(value);
    };

    // Function to trim trailing spaces when user leaves the field
    const handleInputBlur = (field: any) => {
        const trimmedValue = field.value.trimEnd();
        field.onChange(trimmedValue);
        field.onBlur();
    };
    useEffect(() => {
        if (signUpEmail) {
            const companyName = extractCompanyFromEmail(signUpEmail);
            if (companyName) {
                setValue('company', companyName);
            }
        }
    }, [signUpEmail, setValue]);
    /*
    const pollProfileUpdate = useCallback(
        async (
            firstName: string,
            lastName: string,
            company: string | undefined,
        ) => {
            let retries = 0;

            while (retries < MAX_RETRIES) {
                try {
                    await updateUserProfile({firstName, lastName, company});
                    setMessage('Profile updated successfully!');
                    if (refreshUserProfile) {
                        await refreshUserProfile();
                    }
                    setIsLoading(false);
                    logGAEvent('profile_update_success', {email: signUpEmail});
                    if (EMAIL_MUST_BE_VERIFIED) {
                        navigateTo('/workspace/verify-email', {replace: true});
                    } else {
                        navigateTo(
                            {
                                pathname: `/workspace/projects`,
                                search: '?popup=feature-display',
                            },
                            {replace: true},
                        );
                    }
                    return;
                } catch (error) {
                    retries++;
                    if (retries >= MAX_RETRIES) {
                        console.error('Max retries reached:', error);
                        setMessage(
                            'Profile update failed. Registration cancelled.',
                        );
                        setIsLoading(false);
                        showAlert?.({
                            type: 'HttpError',
                            message: 'Profile update timed out.',
                        });
                        break;
                    }
                    await sleep(POLLING_INTERVAL);
                }
            }
        },
        [refreshUserProfile, signUpEmail, navigateTo, showAlert],
    );

    const postSignUp = useCallback(
        async (
            firstName: string,
            lastName: string,
            company: string | undefined,
        ) => {
            setMessage('Updating profile...');
            setIsLoading(true);
            await pollProfileUpdate(firstName, lastName, company);
        },
        [pollProfileUpdate],
    );
*/
    const onSubmit: SubmitHandler<SignUpSchemaType> = useCallback(
        async data => {
            const {password} = data;
            if (signUpEmail && password && login) {
                try {
                    await register({
                        email: signUpEmail,
                        avatarBlob: '',
                        ...data,
                        company: data.company || '',
                        utm_source: utmParams.utm_source,
                        utm_medium: utmParams.utm_medium,
                        utm_campaign: utmParams.utm_campaign,
                        utm_id: utmParams.utm_id,
                    });
                    const result = await login(signUpEmail, password);
                    if ('user_id' in result) {
                        // Login function already handles dataLayer push
                        logGAEvent('signup_completed', {
                            method: 'email',
                            email: signUpEmail,
                            company: data.company,
                            user_id: result.user_id,
                        });
                        navigateTo('/workspace', {replace: true});
                        return;
                    } else {
                        showAlert?.({
                            type: 'PlatformError',
                            message: 'New account sign in error',
                        });
                        return;
                    }
                } catch (error) {
                    if (error instanceof PlatformError) {
                        const e = error as PlatformError;
                        if (e.code === 'HttpStatus409') {
                            showAlert?.({
                                type: 'PlatformError',
                                message: 'Email already in use',
                            });
                            return;
                        } else if (e.code === 'HttpStatus400') {
                            showAlert?.({
                                type: 'PlatformError',
                                message:
                                    e.message ||
                                    'There was an issue with your sign-up request.',
                            });
                            return;
                        } else {
                            showAlert?.({
                                type: 'PlatformError',
                                message: e.message,
                            });
                            return;
                        }
                    }
                    // if (error instanceof FirebaseError) {
                    //     const e = error as FirebaseError;
                    //     if (e.code === 'auth/email-already-in-use') {
                    //         showAlert?.({
                    //             type: 'Firebase',
                    //             message: 'Email already in use',
                    //         });
                    //     } else if (e.code === 'auth/weak-password') {
                    //         showAlert?.({
                    //             type: 'Firebase',
                    //             message: 'Weak password',
                    //         });
                    //     } else {
                    //         console.error(e.message);
                    //     }
                    // }
                }
            }
        },
        [login, navigateTo, showAlert, signUpEmail, utmParams],
    );

    if (!signUpEmail) {
        return <Navigate replace to="/" />;
    }

    const handleChange = () => {
        const password = getValues('password');
        setPasswordLengthValid(password.length >= 12);
        setPasswordHasLowerCase(lowerCaseRegex.test(password));
        setPasswordHasUpperCase(upperCaseRegex.test(password));
        setPasswordHasNumber(numberRegex.test(password));
        setPasswordHasSpecialChar(specialCharRegex.test(password));
    };

    return (
        <>
            <HomePanelShell
                title="Create your Blitzy account"
                subtitle={
                    <>
                        Already have an account?&nbsp;
                        <Link
                            to="/sign-in"
                            onClick={() => {
                                logGAEvent('signin_link_clicked', {
                                    source: 'signup_email_page',
                                    user_id: undefined,
                                });
                            }}>
                            Sign in
                        </Link>
                    </>
                }>
                {/* {isLoading && (
                    <div className="loader">{message || 'Processing...'}</div>
                )}{' '} */}
                {/* Added loader */}
                <form
                    onSubmit={handleSubmit(onSubmit)}
                    onChange={() => handleChange()}>
                    <div className="w-full flex flex-col gap-[16px] items-center">
                        <div className="w-full flex flex-col gap-[16px] sm:flex-row sm:gap-[24px]">
                            <div className="w-full flex flex-col gap-[12px]">
                                <div className="form-label">First name</div>
                                <div className="w-full flex flex-col gap-[4px]">
                                    <Controller
                                        name="firstName"
                                        control={control}
                                        render={({field}) => (
                                            <Input
                                                className={`w-full ${errors.firstName ? 'input-field-error' : 'input-field'}`}
                                                type="text"
                                                value={field.value}
                                                onChange={e =>
                                                    handleInputChange(
                                                        field,
                                                        e.target.value,
                                                    )
                                                }
                                                onBlur={() =>
                                                    handleInputBlur(field)
                                                }
                                                name={field.name}
                                            />
                                        )}
                                    />
                                    {errors.firstName && (
                                        <div className="form-error-note">
                                            {errors.firstName.message}
                                        </div>
                                    )}
                                </div>
                            </div>
                            <div className="w-full flex flex-col gap-[12px]">
                                <div className="form-label">Last name</div>
                                <div className="w-full flex flex-col gap-[4px]">
                                    <Controller
                                        name="lastName"
                                        control={control}
                                        render={({field}) => (
                                            <Input
                                                className={`w-full ${errors.lastName ? 'input-field-error' : 'input-field'}`}
                                                type="text"
                                                value={field.value}
                                                onChange={e =>
                                                    handleInputChange(
                                                        field,
                                                        e.target.value,
                                                    )
                                                }
                                                onBlur={() =>
                                                    handleInputBlur(field)
                                                }
                                                name={field.name}
                                            />
                                        )}
                                    />
                                    {errors.lastName && (
                                        <div className="form-error-note">
                                            {errors.lastName.message}
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                        <div className="w-full flex flex-col gap-[12px]">
                            <div className="form-label">Company</div>
                            <div className="w-full flex flex-col gap-[4px]">
                                <Controller
                                    name="company"
                                    control={control}
                                    render={({field}) => (
                                        <Input
                                            className={`w-full ${errors.company ? 'input-field-error' : 'input-field'}`}
                                            type="text"
                                            value={field.value}
                                            onChange={e =>
                                                handleInputChange(
                                                    field,
                                                    e.target.value,
                                                )
                                            }
                                            onBlur={() =>
                                                handleInputBlur(field)
                                            }
                                            name={field.name}
                                        />
                                    )}
                                />
                                {errors.company && (
                                    <div className="form-error-note">
                                        {errors.company.message}
                                    </div>
                                )}
                            </div>
                        </div>
                        <div className="w-full flex flex-col gap-[12px]">
                            <div className="form-label">Password</div>
                            <div className="w-full flex flex-col gap-[16px]">
                                <Controller
                                    name="password"
                                    control={control}
                                    render={({field}) => (
                                        <PasswordInput
                                            className={`w-full ${errors.password ? 'input-field-error' : 'input-field'}`}
                                            type="password"
                                            showPasswordToggle={true}
                                            onFocus={() =>
                                                setShowPasswordValidationUI(
                                                    true,
                                                )
                                            }
                                            value={field.value}
                                            onChange={e =>
                                                handleInputChange(
                                                    field,
                                                    e.target.value,
                                                )
                                            }
                                            onBlur={() =>
                                                handleInputBlur(field)
                                            }
                                            name={field.name}
                                        />
                                    )}
                                />
                                {/* {errors.password && (
                                    <div className="form-error-note">
                                        {errors.password.message}
                                    </div>
                                )} */}
                                {/* <div className="form-note">
                                    Your password must contain at least 12
                                    characters, one lower case letter, and one
                                    number.
                                </div> */}
                                {showPasswordValidationUI && (
                                    <div className="px-[24px] py-[12px] text-[12px] leading-[15.6px] flex flex-col gap-[4px] border border-[#D9D9D9] rounded-[24px]">
                                        <p className="text-[#999]">
                                            Your password must contain:
                                        </p>
                                        <PasswordValidationMessage
                                            isValid={passwordLengthValid}
                                            message="At least 12 characters"
                                        />
                                        <PasswordValidationMessage
                                            isValid={passwordHasLowerCase}
                                            message="At least 1 lower case letter"
                                        />
                                        <PasswordValidationMessage
                                            isValid={passwordHasUpperCase}
                                            message="At least 1 upper case letter"
                                        />
                                        <PasswordValidationMessage
                                            isValid={passwordHasNumber}
                                            message="At least 1 number"
                                        />
                                        <PasswordValidationMessage
                                            isValid={passwordHasSpecialChar}
                                            message="At least 1 special character"
                                        />
                                    </div>
                                )}
                            </div>
                        </div>
                        <LoadingSubmitButton
                            className="primary-button w-full mt-[8px]"
                            loading={isSubmitting}
                            loadingText="Signing up...">
                            Create Account
                        </LoadingSubmitButton>
                    </div>
                </form>

                <div className="text-[var(--text-text-secondary,#333)] font-inter text-[16px] font-normal leading-[150%] tracking-[-0.3px] flex items-center gap-[8px] self-stretch">
                    <div className="flex-1 h-[1px] bg-[var(--border-border-secondary,#D9D9D9)]"></div>
                    or
                    <div className="flex-1 h-[1px] bg-[var(--border-border-secondary,#D9D9D9)]"></div>
                </div>
                <div className="flex flex-col gap-[16px]">
                    <SSOSignInButton />
                    {Config.SUPPORT_MICROSOFT_LOGIN && <MicrosoftLoginButton />}
                </div>

                <div className=" flex flex-col gap-[12px] ">
                    <TermClause srcPage="sign_up_page" />
                </div>
            </HomePanelShell>
        </>
    );
}
