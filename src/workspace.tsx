import {useCallback, useEffect, useState} from 'react';
import {
    Link,
    Navigate,
    Outlet,
    useLocation,
    useSearchParams,
} from 'react-router-dom';
import {useAuthContext} from './context/auth-context';
import {
    WORKSPACE_DIALOG_TYPES,
    WorkspaceDialog,
    WorkspaceDialogType,
} from './modal/workspace-dialog';
import {TopNavPane} from './panel/workspace/top-nav-pane';
import {MobileTopNavPane} from './panel/workspace/mobile-top-nav-pane';
import {Home} from './home';
import {SubscriptionPlan} from './lib/entity-types';
import {
    SubscriptionProvider,
    useSubscriptionContext,
} from './context/subscription-context';
import {UsageProvider} from './context/usage-context';
import {GitInstallationsProvider} from './context/git-installations-context';

function RawWorkspace({userId}: {userId?: string}) {
    const {plan, refreshSubscription} = useSubscriptionContext();
    const {pathname} = useLocation();
    const {userProfile} = useAuthContext();
    const [showSignInMsg, setShowSignInMsg] = useState(false);
    const [dialogType, setDialogType] = useState<
        WorkspaceDialogType | undefined
    >();
    const [searchParams, setSearchParams] = useSearchParams();

    const popup = searchParams.get('popup') as WorkspaceDialogType;

    useEffect(() => {
        if (popup && WORKSPACE_DIALOG_TYPES.includes(popup)) {
            setDialogType(popup);
        }
    }, [popup]);

    const handleCloseDialog = useCallback(() => {
        setSearchParams('popup', undefined);
        setDialogType(undefined);
    }, [setSearchParams]);

    useEffect(() => {
        const handle = setTimeout(() => {
            if (!userProfile) {
                setShowSignInMsg(true);
            }
        }, 20000); // delay. fix ARUI-981: increase the heuristic delay to 20s
        return () => clearTimeout(handle);
    }, [userProfile]);

    useEffect(() => {
        const userID = userProfile?.id || userId;
        if (userID) {
            const cacheKey = `${userID}:onboarding_modal_displayed`;
            const displayedPlan = localStorage.getItem(
                cacheKey,
            ) as SubscriptionPlan | null;
            if (refreshSubscription) {
                refreshSubscription();
            }
            if (plan && displayedPlan !== plan) {
                localStorage.setItem(cacheKey, plan);
                setDialogType('feature-display');
            }
        }
    }, [userId, userProfile, plan, refreshSubscription]);

    // for a few pages, we want to use the 'Home template' because of Figma design. Add new cases here.
    if (pathname.endsWith('-github')) return <Home />;

    return (
        <div className="w-screen h-dvh overflow-hidden flex flex-col">
            <div className="hidden sm:block sticky top-0">
                <TopNavPane onClose={() => {}} />
            </div>
            <div className="sticky top-0 sm:hidden">
                <MobileTopNavPane />
            </div>

            <div className="p-0  mx-auto w-full items-center justify-center h-full overflow-hidden sm:min-w-[600px]">
                {showSignInMsg && !userProfile && (
                    <div className="text-[32px] font-bold">
                        Please <Link to="/sign-in">sign in to continue</Link>
                    </div>
                )}
                {userProfile && (
                    <div className="outlet-wrapper h-full">
                        <Outlet />
                        {dialogType && (
                            <WorkspaceDialog
                                type={dialogType as WorkspaceDialogType}
                                onClose={handleCloseDialog}
                            />
                        )}
                    </div>
                )}
            </div>
        </div>
    );
}

export function Workspace({userId}: {userId?: string}) {
    const {isEmailVerified, userProfile} = useAuthContext();
    if (!isEmailVerified) {
        return <Navigate to="/verify-email" replace />;
    } else if (userProfile && !userProfile.registrationCompleted) {
        return <Navigate to="/complete-registration" replace />;
    }

    return (
        <SubscriptionProvider userId={userId}>
            <UsageProvider>
                <GitInstallationsProvider>
                    <RawWorkspace userId={userId} />
                </GitInstallationsProvider>
            </UsageProvider>
        </SubscriptionProvider>
    );
}
