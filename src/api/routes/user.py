import random
import time
import traceback
from datetime import datetime
from typing import Any, Dict, List, Optional

from blitzy_utils.logger import logger
from blitzy_utils.sercert_manager import (
    get_github_access_token, get_github_user,
    save_github_credentials_to_github_handler,
    save_github_credentials_to_secret_manager)
from common_models.db_client import get_db_session
from common_models.models import (GeoLocation, GithubInstallationStatus,
                                  Subscription, SubscriptionStatus,
                                  SubscriptionType, User, UserConfig)
from firebase_admin import auth
from firebase_admin import auth as firebase_auth
from firebase_admin.auth import UserRecord
from firebase_admin.exceptions import InvalidArgumentError
from flask import Blueprint
from flask_utils.decorators import flask_pydantic_response, validate_request
from flask_utils.models_config.model_utils import map_to_model
from sqlalchemy import text
from sqlalchemy.exc import OperationalError
from sqlalchemy.orm import Session

from src.api.models import CreateUserInput
from src.api.models import GeoLocation as GeoLocationModel
from src.api.models import (RegisterUserInput, SaveGithubInstallationInfo,
                            Status200, Status201, Status401, Status404,
                            Status409)
from src.api.models import Subscription as SubscriptionModel
from src.api.models import UpdateProfileInput, UpdateUserConfigInput
from src.api.models import User as UserModel
from src.api.routes.hubspot import create_hubspot_contact
from src.api.utils.auth_utils import firebase_sign_in_with_email_password
from src.api.utils.firebase_utils import (send_verification_email,
                                          sign_in_with_custom_token)
from src.consts import GITHUB_CLIENT_ID, GITHUB_CLIENT_SECRET
from src.error.base_error import BlitzyError
from src.error.errors import (FirebaseUserNotFoundError,
                              PasswordDoesNotMeetRequirementsError,
                              TooManyAttemptsError, UserAlreadyExistsError)
from src.middleware.decorators import fetch_user_info, get_user_info
from src.service.email_validation_service import validate_registration_email
from src.service.geolocation_service import get_user_geolocation_by_id
from src.service.github_installation_service import create_github_installation
from src.service.ip_service import (add_geolocation, get_client_ip,
                                    get_geolocation_from_ip)
from src.service.subscription_service import (get_subscription_info_by_user_id,
                                              save_subscription)
from src.service.user_config_service import (get_user_config_by_user_id,
                                             save_config)
from src.service.user_service import (add_user,
                                      create_user_using_firebase_user_id,
                                      get_user_by_id, get_user_by_user_email,
                                      update_user_by_id)

user_bp = Blueprint("user", __name__, url_prefix="/v1/user")

SETUP_ACTION_REQUEST = "request"


@user_bp.route("/profile", methods=["GET"])
@get_user_info(skip_user_verification=True)
@flask_pydantic_response
def get_user(user_info):
    user = get_user_by_id(user_info["id"])
    if not user:
        return Status404(message=f"User with ID {user_info['id']} does not exist"), 404
    user_pydantic = map_to_model(user, UserModel)
    return user_pydantic, 200


@user_bp.route("/config", methods=["PUT"])
@validate_request(UpdateUserConfigInput)
@get_user_info
@flask_pydantic_response
def update_user_config(user_info, payload: UpdateUserConfigInput):
    user = get_user_by_id(user_info["id"])
    if not user:
        return Status404(message=f"User with ID {user_info['id']} does not exist"), 404

    process_update_user_config(user_info, payload)
    return Status200(message="Success"), 200


@user_bp.route("/geolocation", methods=["GET"])
@get_user_info()
@flask_pydantic_response
def get_user_geolocation(user_info):
    geolocation = get_user_geolocation_by_id(user_info["id"])
    if not geolocation:
        return {}, 200
    geolocation_model = map_to_model(geolocation, GeoLocationModel)
    return geolocation_model, 200


@user_bp.route("", methods=["POST"])
@validate_request(CreateUserInput)
@flask_pydantic_response
def create_user(payload: CreateUserInput):
    with get_db_session() as session:
        session.execute(text("SET TRANSACTION ISOLATION LEVEL SERIALIZABLE"))
        user_info = get_user_by_id(payload.userId)
        if user_info:
            logger.error(f"User with ID {payload.userId} already exists")
            return Status409(message=f"User with ID {payload.userId} already exists"), 409
        inserted_record = create_user_using_firebase_user_id(payload, session)
        session.commit()
        return Status201(message="Success", id=inserted_record["id"]), 201


@user_bp.route("/profile", methods=["PUT"])
@validate_request(UpdateProfileInput)
@get_user_info
@flask_pydantic_response
def update_user(user_info, payload: UpdateProfileInput):
    with get_db_session() as session:
        session.execute(text("SET TRANSACTION ISOLATION LEVEL SERIALIZABLE"))
        update_payload = {
            User.first_name: payload.firstName,
            User.last_name: payload.lastName,
            User.company: payload.company,
            User.avatar_blob: payload.avatarBlob,
            User.registration_completed: True,
        }
        update_user_by_id(user_info["id"], update_payload, session)
        session.commit()
    return Status200(message="Success"), 200


@user_bp.route("/subscription/status", methods=["GET"])
@get_user_info
@flask_pydantic_response
def get_user_subscription_details(user_info):
    subscription = get_subscription_info_by_user_id(user_info["id"])
    subscription_pydantic = map_to_model(subscription, SubscriptionModel)
    return subscription_pydantic, 200


@user_bp.route("/reconcile", methods=["POST"])
@get_user_info
@flask_pydantic_response
def users_reconcile(user_info):
    logger.info("Starting user reconciliation. It might take some time to add all missing users.")
    missing_users = find_missing_users()
    if missing_users:
        logger.info("Missing users found. Starting syncing missing users.")
        sync_missing_users(missing_users)
        logger.info("Users synced to the database successfully.")
    return Status200(message="Success"), 200


def find_missing_users() -> List[CreateUserInput]:
    """
    Find missing user.
    :return: List of missing users.
    """
    missing_users: List[CreateUserInput] = []
    batch_size: int = 1000
    with get_db_session() as session:
        page_token = None
        logger.info(f"Fetching list of users with batch size {batch_size} from firebase to check if user exists.")
        while True:
            result = auth.list_users(max_results=batch_size, page_token=page_token)

            for user in result.users:
                user_info = get_user_by_firebase_id(user.uid, session)
                if not user_info:
                    # If user not found in database append it to the missing users.
                    user_input = CreateUserInput(email=user.email, userId=user.uid)
                    missing_users.append(user_input)

            page_token = result.next_page_token
            if not page_token:
                break
    logger.info(f"Found {len(missing_users)} missing users.")
    return missing_users


def get_user_by_firebase_id(firebase_user_id: str, session: Optional[Session]) -> Optional[User]:
    """
    Get user by firebase id.
    :param firebase_user_id: Firebase user id.
    :param session: Session if any.
    :return: User info if found.
    """
    with get_db_session(session) as session:
        user = session.query(User).filter(User.user_id == firebase_user_id).first()
        if user:
            session.expunge(user)
            return user


def sync_missing_users(missing_users: List[CreateUserInput]) -> None:
    """
    Sync missing users.
    :param missing_users: List of missing users.
    """
    with get_db_session() as session:
        for user in missing_users:
            logger.info(f"Creating user with ID {user.userId}")
            create_user_using_firebase_user_id(user, session)
        session.commit()


@user_bp.route("/register", methods=["POST"])
@validate_request(RegisterUserInput)
@flask_pydantic_response
def register_user(payload: RegisterUserInput):
    # Validate email first
    validation_error, status_code = validate_registration_email(payload.email)
    if validation_error:
        return validation_error, status_code
    user = get_user_by_user_email(payload.email)
    if user:
        return Status409(message=f"User with email ID {payload.email} already exists"), 409
    user_data = create_firebase_user(payload)
    user_model = insert_user_into_database_with_retry(payload, user_data.uid)
    if not payload.emailVerified:
        logger.info(f"Sending verification email to user with ID {user_model.id}")
        auth_data = firebase_sign_in_with_email_password(payload.email, payload.password)
        send_verification_email(auth_data["idToken"])
    return Status201(id=user_model.id, message="User registered and verification email sent.", ), 201


@user_bp.route("/register/all", methods=["POST"])
@validate_request(RegisterUserInput)
@flask_pydantic_response
def register_user_without_verification(payload: RegisterUserInput):
    user = get_user_by_user_email(payload.email)
    if user:
        return Status409(message=f"User with email ID {payload.email} already exists"), 409
    user_data = create_firebase_user(payload)
    user_model = insert_user_into_database_with_retry(payload, user_data.uid)
    if not payload.emailVerified:
        logger.info(f"Sending verification email to user with ID {user_model.id}")
        auth_data = firebase_sign_in_with_email_password(payload.email, payload.password)
        send_verification_email(auth_data["idToken"])
    return Status201(id=user_model.id, message="User registered and verification email sent.", ), 201


@user_bp.route("/verification/status", methods=["GET"])
@flask_pydantic_response
@get_user_info(skip_user_verification=True)
def verify_user(user_info):
    try:
        with get_db_session() as session:
            db_id = user_info["id"]
            user_model = get_user_by_id(db_id)

            # Check if user is already verified.
            if user_model.is_verified:
                return Status200(message="User already verified"), 200

            # Get current information from firebase user.
            firebase_user = firebase_auth.get_user(user_model.user_id)
            if not firebase_user.email_verified:
                logger.info(f"User with ID {db_id} is not verified yet. Checked with firebase."
                            f" Information received from firebase: {firebase_user.email_verified}")
                return Status401(message="User is not verified yet."), 401

            # Update database with new information and invalidate current cache so in next run, fresh
            # information will be fetched from the database.
            logger.info(f"User with ID {db_id} is verified, updating database")
            update_payload = {
                User.is_verified: True,
            }
            update_user_by_id(db_id, update_payload, session)
            fetch_user_info.cache_clear()
            session.commit()
            logger.info(f"User {db_id} verified successfully")
            return Status200(message="User verified successfully."), 200
    except auth.UserNotFoundError:
        logger.error(f"No user found for UID: {user_model.user_id}")
        # You are not supposed to be here but still!!!
        raise FirebaseUserNotFoundError(message="Current user not found in firebase.")


@user_bp.route("/verification/send", methods=["POST"])
@get_user_info(skip_user_verification=True)
@flask_pydantic_response
def send_user_verification_email(user_info):
    try:
        user_db_model = get_user_by_id(user_info["id"])
        if user_db_model.is_verified:
            return Status200(message="User already verified"), 200

        custom_token = firebase_auth.create_custom_token(user_db_model.user_id).decode("utf-8")
        auth_response = sign_in_with_custom_token(custom_token)
        send_verification_email(auth_response["idToken"])
        return Status200(message="Verification email sent"), 200
    except auth.TooManyAttemptsTryLaterError:
        traceback.print_exc()
        logger.error(f"Too many email verification attempts. Please try later for user {user_info['id']}")
        raise BlitzyError(message="Too many verification attempts", status_code=500)
    except TooManyAttemptsError as e:
        logger.info(f"Too many attempts to send verification for user {user_info['id']}")
        raise TooManyAttemptsError(e.message)
    except auth.UserNotFoundError:
        logger.error(f"No user found for UID: {user_info['user_id']}")
        # You are not supposed to be here but still!!!
        raise FirebaseUserNotFoundError(message="Current user not found in firebase.")
    except Exception as e:
        logger.info(f"Failed to send verification email {e}")
        raise e


@user_bp.route("/github/install", methods=["POST"])
@validate_request(SaveGithubInstallationInfo)
@get_user_info()
@flask_pydantic_response
def set_github_token(user_info, payload: SaveGithubInstallationInfo):
    user_id = user_info["id"]
    logger.info(f"Attempting to verify github installation for user {user_id}")

    if payload.setupAction != SETUP_ACTION_REQUEST and not payload.installationID:
        logger.warning(f"Installation id is missing for user {user_id} and setup action is {payload.setupAction}.")
        raise BlitzyError(message="Missing installation id", status_code=400)

    logger.info(f"Attempting to generate github access token for user {user_id}")
    access_token = get_github_access_token(GITHUB_CLIENT_ID, GITHUB_CLIENT_SECRET, payload.code)
    if not access_token:
        logger.error(f"Failed to generate github access token for the user {user_id}")
        raise BlitzyError(message="Failed to generate github access token", status_code=400)
    logger.info(f"Successfully generated github access token for the user {user_id}")

    user_info = get_github_user(access_token)
    if not user_info:
        logger.error(f"Failed to get github user for the user {user_id}")
        raise BlitzyError(message="Failed to get github user info", status_code=400)

    if payload.setupAction == SETUP_ACTION_REQUEST:
        logger.info(f"Github installation for {user_id} is requested. Persisting the payload to database.")
        save_github_credentials_to_secret_manager(
            user_id, access_token, payload.code, payload.installationID,
            payload.setupAction)
        create_github_installation(user_id, GithubInstallationStatus.PENDING, payload, user_info)
        logger.info("Github installation requested successfully")
        return Status200(message="Token saved successfully"), 200

    logger.info("Saving github credentials to secret manager.")
    save_github_credentials_to_github_handler(access_token, payload.code, payload.installationID,
                                              payload.setupAction)

    logger.info("Saving github credentials to github manager as well")
    save_github_credentials_to_secret_manager(
        user_id, access_token, payload.code, payload.installationID,
        payload.setupAction)
    logger.info(f"Github installation for {user_id} is verified. Creating github installation record in database.")
    create_github_installation(user_id, GithubInstallationStatus.ACTIVE, payload, user_info)
    logger.info(f"Github installation record created successfully for user {user_id}")
    logger.info("Github installation successful")
    return Status200(message="Token saved successfully"), 200


def create_firebase_user(payload: RegisterUserInput) -> UserRecord:
    """
    Create firebase user using given payload.
    :param payload: Payload sent by user.
    :return: UserRecord returned from firebase.
    """
    try:
        user_data = firebase_auth.create_user(
            email=payload.email,
            password=payload.password,
            display_name=f"{payload.firstName} {payload.lastName}",
            email_verified=payload.emailVerified,
        )
    except InvalidArgumentError as e:
        if str(e).__contains__("PASSWORD_DOES_NOT_MEET_REQUIREMENTS"):
            raise PasswordDoesNotMeetRequirementsError(message="Password does not meet requirements", status_code=400)
        raise e
    except Exception as e:
        # Handle Firebase EmailAlreadyExistsError
        if "EMAIL_EXISTS" in str(e) or "The user with the provided email already exists" in str(e):
            raise UserAlreadyExistsError(payload.email)
        raise e
    return user_data


def insert_user_into_database_with_retry(payload: RegisterUserInput, user_id: str) -> User:
    """
    Insert the user into the database along with geolocation information, retry 3 times in
    case of concurrency issues
    :param payload: Payload sent by user.
    :param user_id: Firebase user id.
    :return: User model.
    """
    max_retries = 5
    last_exception = None
    for attempt in range(max_retries):
        try:
            return _insert_user_into_database(payload, user_id)
        except OperationalError as e:
            last_exception = e
            error_msg = str(e)
            # Check if it's a retryable serialization failure
            if "SerializationFailure" in error_msg or "concurrent modification" in error_msg:
                if attempt < max_retries - 1:
                    # Simple exponential backoff: 100ms, 200ms, 400ms
                    sleep_time = 0.1 * (2 ** attempt) + random.uniform(0, 0.5)
                    logger.warning(f"Serialization failure on attempt {attempt + 1}, retrying in {sleep_time:.3f}s")
                    time.sleep(sleep_time)
                    continue
            # Non-retryable error or max retries reached
            raise

    # Shouldn't happen in reality (we will not iterate over the loop without an exception),
    # but makes mypy happy
    raise last_exception


def _insert_user_into_database(payload: RegisterUserInput, user_id: str) -> User:
    """
    PLEASE use insert_user_into_database_with_retry to insert user into the database, it handles concurrency errors
    Insert user into the database along with geolocation information.

    :param payload: Payload sent by user.
    :param user_id: Firebase user id.
    :return: User model.
    """
    # This function is only called by /register API. This API basically registers user with given email and password.
    # As user has filled all the required data e.g. First Name, Last Name and Company Name we mark
    # registration_completed true.

    with get_db_session() as session:
        user = User(
            user_id=user_id,
            email=payload.email,
            first_name=payload.firstName,
            last_name=payload.lastName,
            company=payload.company,
            avatar_blob=payload.avatarBlob,
            is_verified=payload.emailVerified,
            registration_completed=True,
        )
        user = add_user(user, session)
        logger.info(f"Created user with ID {user.id}")

        ip_address = get_client_ip()
        if ip_address:
            geo_data = get_geolocation_from_ip(ip_address)
            if geo_data:
                geolocation = GeoLocation(
                    user_id=user.id,
                    ip_address=geo_data['ip'],
                    country_code=geo_data['country_code'],
                    city=geo_data['city'],
                    region=geo_data['region'],
                    latitude=geo_data['latitude'],
                    longitude=geo_data['longitude'],
                    timezone=geo_data['timezone']
                )
                add_geolocation(geolocation, session)
                logger.info(f"Successfully created geolocation information for user_id: {user.id}")
            else:
                logger.error(
                    message=f"Failed to get geolocation information for user_id: {user_id}"
                )

        subscription = Subscription(
            user_id=user.id,
            plan_name=SubscriptionType.FREE,
            start_date=datetime.utcnow(),
            status=SubscriptionStatus.NONE
        )
        save_subscription(subscription, session)
        session.commit()
        logger.info(f"Successfully created subscription for user {user.id}")

        config = UserConfig(
            user_id=user.id,
            tech_spec_notification_enabled=True,
            code_gen_notification_enabled=True,
            platform_config={}
        )
        save_config(config, session)
        session.commit()
        logger.info(f"Successfully created user config for user {user.id}")

        # Create HubSpot contact
        try:
            create_hubspot_contact(
                email=payload.email,
                first_name=payload.firstName,
                last_name=payload.lastName,
                company=payload.company,
                plan=SubscriptionType.FREE,  # Initial plan is FREE
                utm_source=getattr(payload, 'utm_source', None),
                utm_medium=getattr(payload, 'utm_medium', None),
                utm_campaign=getattr(payload, 'utm_campaign', None),
                utm_id=getattr(payload, 'utm_id', None)
            )
            logger.info(f"Successfully created HubSpot contact for user {user.id}")
        except Exception as e:
            # Log error but don't interrupt user creation
            logger.error(f"Failed to create HubSpot contact for user {user.id}: {str(e)}")

        logger.info(f"Successfully created user with ID {user.id}")
        return user


def process_update_user_config(user_info: Dict[str, Any], payload: UpdateUserConfigInput):
    with get_db_session() as session:
        user_id = user_info["id"]
        user_config = get_user_config_by_user_id(user_id)

        if not user_config:
            logger.info(f"User config not found for user {user_id}, creating new config.")
            create_user_config_using_payload(user_id, payload, session)
            logger.info(f"User config created successfully for user {user_id}")
            session.commit()
            return

        logger.info("User config exists, updating preferences")
        update_user_platform_config(user_config, payload, session)
        session.commit()
        logger.info("User config updated successfully")


def create_user_config_using_payload(user_id: str, payload: UpdateUserConfigInput, session: Session):
    user_config = UserConfig(
        user_id=user_id,
        tech_spec_notification_enabled=payload.techSpecNotificationEnabled,
        code_gen_notification_enabled=payload.codeGenNotificationEnabled,
        platform_config=payload.platformConfig
    )

    session.add(user_config)
    session.flush()


def update_user_platform_config(user_config: UserConfig, payload: UpdateUserConfigInput, session: Session):
    user_config = session.merge(user_config)
    user_config.tech_spec_notification_enabled = payload.techSpecNotificationEnabled
    user_config.code_gen_notification_enabled = payload.codeGenNotificationEnabled
    user_config.platform_config = payload.platformConfig
