import http
from typing import Any, Dict

from blitzy_utils.logger import logger
from flask import Blueprint
from flask_utils.decorators import flask_pydantic_response

from src.api.models import GetBranchDetailsForRepositoryOutput
from src.error.errors import Status400Error, Status404Error
from src.middleware.decorators import get_user_info
from src.service.branch_lock_service import has_active_lock
from src.service.github_project_repo_service import (
    get_github_branch_pattern_by_repository_id_and_branch_name,
    get_source_github_project_repo_by_repo_id)
from src.service.project_service import get_all_projects_by_user_id

repository_bp = Blueprint("repository", __name__, url_prefix="/v1/repository")


@repository_bp.route("/<repository_id>/branches/<branch_name>", methods=["GET"])
@get_user_info
@flask_pydantic_response
def get_branch_info(user_info: Dict[str, Any], repository_id: str, branch_name: str):
    # just to avoid copying a message everywhere inside the function
    not_found_error = Status404Error(
        message=f"Branch '{branch_name}' or repository with ID {repository_id} are not found in user projects."
    )

    # just basic input validation to avoid calling RDS without reason
    # empty branch name
    if not branch_name or not branch_name.strip():
        raise Status400Error(message=f"Invalid branch name '{branch_name}', branch name can't be empty")
    # Check for control characters
    if any(ord(char) < 32 for char in branch_name):
        raise Status400Error(message=f"Invalid branch name '{branch_name}', branch name can't have special characters")

    # Check that the user has access to the project, just fetch all user projects for now, might need update later
    user_id = user_info["id"]
    user_projects_ids = [project.id for project in get_all_projects_by_user_id(user_id)]
    logger.debug(f"User {user_id} projects IDs are {user_projects_ids}")

    github_project_repo = get_source_github_project_repo_by_repo_id(repository_id)
    if not github_project_repo:
        raise not_found_error
    elif github_project_repo.project_id not in user_projects_ids:
        # keep this condition separate to have a clear log message
        logger.warning(f"Repository with ID {repository_id} not found in user {user_id} projects")
        # but for the end user we return 404, we don't want one customer to scan our data trying
        # to find all existing repos IDs
        raise not_found_error

    branch_pattern = get_github_branch_pattern_by_repository_id_and_branch_name(repository_id, branch_name)
    if not branch_pattern:
        raise not_found_error

    return (GetBranchDetailsForRepositoryOutput(
        locked=has_active_lock(branch_pattern.id),
    )), http.HTTPStatus.OK
