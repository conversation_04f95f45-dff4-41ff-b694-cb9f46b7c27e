import random
import string

from blitzy_utils.logger import logger
from flask import Blueprint, jsonify, request

from src.api.models import Status404
from src.blitzy.utils import publish_notification
from src.consts import GENERATE_DOCUMENT_TOPIC, PROJECT_ID, publisher
from src.middleware.decorators import get_user_info
from src.service.job_service import get_job_by_id as get_job_by_id_service

job_bp = Blueprint("job", __name__, url_prefix="/v1/job")


@job_bp.route("", methods=["POST"])
def trigger_job():
    post_data = request.json
    request_data = get_request_data(post_data)

    logger.info(f"request data: {request_data}")

    if "error" in request_data:
        logger.info(f"POST Job, Response 400: {request_data}")
        return jsonify(request_data), 400

    prompt = request_data["prompt"]
    prefix = request_data["prefix"]
    repo_name = get_repo_name(prefix)

    notification_data = {
        "prompt": prompt,
        "repo_name": repo_name,
        "order": 0
    }
    publish_notification(publisher, notification_data, PROJECT_ID, GENERATE_DOCUMENT_TOPIC)

    return jsonify({
        "repo_name": f"{repo_name}",
        "github_url": f"https://github.com/blitzy-public-samples/{repo_name}"
    }), 200


@job_bp.route("/<job_id>", methods=["GET"])
@get_user_info
def get_job_by_id(user_info, job_id: str):
    job_info = get_job_by_id_service(job_id)
    if not job_info:
        return Status404(message=f"Job with ID {job_id} not found")
    return jsonify(job_info), 200


def get_request_data(post_data):
    prefix = ""
    if "prefix" not in post_data or not isinstance(post_data["prefix"], str):
        return {"error": "prefix must be set as a string"}
    prefix = post_data["prefix"]

    prompt = ""
    if "prompt" not in post_data or not isinstance(post_data["prompt"], str) or len(post_data["prompt"]) < 100:
        return {"error": "prompt must be a string longer than 100 characters"}
    prompt = post_data["prompt"]

    request_data = {
        "prefix": prefix,
        "prompt": prompt,
    }

    return request_data


def get_repo_name(prefix: str):
    hash_part = "".join(random.choices(string.ascii_lowercase + string.digits, k=6))
    return f"{prefix}-{hash_part}"
