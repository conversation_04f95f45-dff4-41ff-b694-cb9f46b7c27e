from blitzy_utils.logger import logger
from common_models.models import VersionControlSystem
from flask import Blueprint
from flask_utils.decorators import flask_pydantic_response
from flask_utils.models_config.model_utils import map_to_model

from src.api.models import (GetGitInstallationsOutput,
                            GetGitInstallationsOutputItem)
from src.middleware.decorators import get_user_info
from src.service.git_installation_service import \
    get_latest_git_integration_by_user_id_and_svc_type

git_bp = Blueprint("git", __name__, url_prefix="/v1/git")


@git_bp.route("", methods=["GET"])
@get_user_info()
@flask_pydantic_response
def get_git_installation_info(user_info):
    """
    Return information about all active user git(azure or github) integrations.
    Returns:
    """
    user_id = user_info["id"]
    response: GetGitInstallationsOutput = GetGitInstallationsOutput(items=[])
    logger.debug(f"Will fetch latest github integration info for user {user_id}")
    github_integration = get_latest_git_integration_by_user_id_and_svc_type(
        user_id,
        VersionControlSystem.GITHUB,
    )
    if github_integration:
        logger.debug(
            f"Found github integration info for user {user_id}: {github_integration.to_dict()}"
        )
        response.items.append(
            map_to_model(github_integration, GetGitInstallationsOutputItem)
        )
    else:
        logger.info(f"Didn't find github integration info for user {user_id}")

    logger.debug(f"Will fetch latest azure integration info for user {user_id}")
    azure_integration = get_latest_git_integration_by_user_id_and_svc_type(
            user_id,
            VersionControlSystem.AZURE_DEVOPS,
    )
    if azure_integration:
        logger.debug(
            f"Found azure integration info for user {user_id}: {azure_integration.to_dict()}"
        )
        response.items.append(
            map_to_model(azure_integration, GetGitInstallationsOutputItem)
        )
    else:
        logger.info(f"Didn't find azure integration info for user {user_id}")

    logger.debug(f"Will return response: {response}")
    return response, 200
