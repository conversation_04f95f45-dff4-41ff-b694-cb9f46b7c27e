from blitzy_utils.logger import logger
from common_models.models import VersionControlSystem
from flask import Blueprint
from flask_utils.decorators import flask_pydantic_response, validate_request

from src.api.models import (CreateBranchInput, CreateCommitInput,
                            CreatePullRequestInput, CreateRepositoryInput,
                            Status201)
from src.api.utils.github_utils import create_github_repository
from src.azure.azure_app_service import AzureAppService
from src.error.errors import InstallationNotFoundError, ResourceNotFound
from src.github.github_app_service import GithubAppService
from src.service.github_installation_access_service import (
    get_active_github_installation_by_repo_id,
    get_github_installation_by_user_and_svc)

operations_bp = Blueprint("operations", __name__, url_prefix="/operations")


@operations_bp.route("/repositories/<repo_id>/branches", methods=["POST"])
@flask_pydantic_response
@validate_request(CreateBranchInput)
def create_branch(repo_id: str, payload: CreateBranchInput):
    installation = get_active_github_installation_by_repo_id(repo_id)
    if not installation:
        raise InstallationNotFoundError(message=f"Active Github Installation for repo {repo_id} not found.")

    github_service = GithubAppService()
    github_service.create_branch(installation.installation_id, repo_id, payload.branchName, payload.baseBranch)
    return Status201(message=f"Branch {payload.branchName} created successfully"), 201


@operations_bp.route("/repositories/<repo_id>/branches/<branch_name>/commits", methods=["POST"])
@flask_pydantic_response
@validate_request(CreateCommitInput)
def create_commit(repo_id: str, branch_name: str, payload: CreateCommitInput):
    installation = get_active_github_installation_by_repo_id(repo_id)
    if not installation:
        raise InstallationNotFoundError(message=f"Active Github Installation for repo {repo_id} not found.")

    logger.info("Installation found, proceeding to create commit")
    github_service = GithubAppService()
    output = github_service.create_commit(repo_id, branch_name, installation.installation_id, payload)
    return output, 201


@operations_bp.route("/repositories/<repo_id>/pr", methods=["POST"])
@flask_pydantic_response
@validate_request(CreatePullRequestInput)
def create_pull_request(repo_id: str, payload: CreatePullRequestInput):
    installation = get_active_github_installation_by_repo_id(repo_id)
    if not installation:
        raise InstallationNotFoundError(message=f"Active Github Installation for repo {repo_id} not found.")

    logger.info(f"Github Installation {installation.installation_id}  for repo {repo_id} found,"
                f" proceeding to create pull request")
    github_service = GithubAppService()
    output = github_service.create_pull_request(repo_id, installation.installation_id, payload)
    return output, 201


@operations_bp.route("/organizations/<org_name>/repositories", methods=["POST"])
@flask_pydantic_response
@validate_request(CreateRepositoryInput)
def create_repo_for_org(org_name: str, payload: CreateRepositoryInput):
    # use payload.azureProjectId is none or not. if not None, use svc_type AzureDevOps
    svc_type: VersionControlSystem = VersionControlSystem.GITHUB
    if payload.azureProjectId is not None:
        svc_type = VersionControlSystem.AZURE_DEVOPS

    github_installation = get_github_installation_by_user_and_svc(
        payload.userId, svc_type=svc_type
    )
    if not github_installation:
        raise ResourceNotFound(f"Active installation for user {payload.userId} not found.")

    if github_installation.svc_type == VersionControlSystem.GITHUB:
        logger.info(
            f"Github installation {github_installation.installation_id} found for {org_name}, creating repository")
        repo = create_github_repository(github_installation.installation_id, payload.repoName, payload.private)
    elif github_installation.svc_type == VersionControlSystem.AZURE_DEVOPS:
        instance = AzureAppService()
        logger.info(
            f"Azure installation {github_installation.installation_id} found for {org_name}, creating repository")
        repo = instance.create_azure_repository(github_installation.installation_id,
                                                payload.repoName,
                                                payload.azureOrgId,
                                                payload.azureProjectId,
                                                payload.private)
        return repo, 201
    else:
        raise ResourceNotFound(f"Unknown SVC type for installation {github_installation.installation_id}.")

    return repo.raw_data, 201
