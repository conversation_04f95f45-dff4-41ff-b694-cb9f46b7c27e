from dataclasses import dataclass
from enum import Enum
from typing import Dict, Optional

import requests
from blitzy_utils.logger import logger
from common_models.models import SubscriptionType
from flask import Blueprint, jsonify, request

from src.consts import HUBSPOT_API_KEY, STATUS_500_RESPONSE
from src.error.errors import HubSpotError

hubspot_bp = Blueprint("hubspot", __name__, url_prefix="/v1/hubspot")


class ContactProperty(Enum):
    """Enum for commonly used HubSpot contact properties"""
    EMAIL = "email"
    FIRSTNAME = "firstname"
    LASTNAME = "lastname"
    COMPANY = "company"
    PHONE = "phone"
    PLAN = "plan"
    UTM_SOURCE = "utm_source"
    UTM_MEDIUM = "utm_medium"
    UTM_CAMPAIGN = "utm_campaign"
    UTM_ID = "utm_id"


@dataclass
class HubSpotConfig:
    """Configuration for HubSpot API"""
    api_key: str
    base_url: str = "https://api.hubapi.com"


class HubSpotClient:
    def __init__(self):
        """
        Initialize HubSpot client with configuration from environment
        """
        if not HUBSPOT_API_KEY:
            raise HubSpotError("HUBSPOT_API_KEY environment variable is not set")

        self.config = HubSpotConfig(api_key=HUBSPOT_API_KEY)
        self.headers = {
            "Authorization": f"Bearer {self.config.api_key}",
            "Content-Type": "application/json"
        }

    def create_or_update_contact(self, email: str, properties: Dict[str, str]) -> Dict:
        """
        Create or update a contact in HubSpot using their email address

        Args:
            email (str): Email address of the contact
            properties (Dict[str, str]): Dictionary of contact properties to update

        Returns:
            Dict: Response from HubSpot API

        Raises:
            HubSpotError: If the API request fails
        """
        try:
            # Format the properties for HubSpot API
            formatted_properties = [
                {"property": k, "value": v} for k, v in properties.items()
            ]

            # Prepare the request payload
            payload = {
                "properties": formatted_properties
            }

            # Make the API request
            endpoint = f"{self.config.base_url}/contacts/v1/contact/createOrUpdate/email/{email}"
            response = requests.post(
                endpoint,
                headers=self.headers,
                json=payload
            )

            # Check for successful response
            response.raise_for_status()

            return response.json()

        except requests.exceptions.RequestException as e:
            raise HubSpotError(f"Failed to create or update contact: {str(e)}")


def create_hubspot_contact(
        email: str,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
        company: Optional[str] = None,
        phone: Optional[str] = None,
        plan: Optional[SubscriptionType] = None,
        utm_source: Optional[str] = None,
        utm_medium: Optional[str] = None,
        utm_campaign: Optional[str] = None,
        utm_id: Optional[str] = None
) -> Dict:
    """
    Helper function to create or update a HubSpot contact with common properties

    Args:
        email (str): Contact's email address
        first_name (Optional[str]): Contact's first name
        last_name (Optional[str]): Contact's last name
        company (Optional[str]): Contact's company name
        phone (Optional[str]): Contact's phone number
        plan (Optional[str]): Contact's plan (Trial/Pro/Enterprise)
        utm_source (Optional[str]): UTM source parameter
        utm_medium (Optional[str]): UTM medium parameter
        utm_campaign (Optional[str]): UTM campaign parameter
        utm_id (Optional[str]): UTM ID parameter

    Returns:
        Dict: Response from HubSpot API
    """
    # Initialize HubSpot client
    client = HubSpotClient()

    # Prepare properties dictionary
    properties = {}
    if first_name:
        properties[ContactProperty.FIRSTNAME.value] = first_name
    if last_name:
        properties[ContactProperty.LASTNAME.value] = last_name
    if company:
        properties[ContactProperty.COMPANY.value] = company
    if phone:
        properties[ContactProperty.PHONE.value] = phone
    if plan:
        properties[ContactProperty.PLAN.value] = plan.value.upper()

    # Add UTM parameters
    if utm_source:
        properties[ContactProperty.UTM_SOURCE.value] = utm_source
    if utm_medium:
        properties[ContactProperty.UTM_MEDIUM.value] = utm_medium
    if utm_campaign:
        properties[ContactProperty.UTM_CAMPAIGN.value] = utm_campaign
    if utm_id:
        properties[ContactProperty.UTM_ID.value] = utm_id

    # Create or update contact
    return client.create_or_update_contact(email, properties)


@hubspot_bp.route("/contact", methods=["POST"])
def create_or_update_hubspot_contact():
    """
    Flask route to create or update a HubSpot contact
    """
    try:
        data = request.get_json()

        if not data.get("email"):
            return jsonify({"message": "Email is required"}), 500

        response = create_hubspot_contact(
            email=data.get("email"),
            first_name=data.get("first_name"),
            last_name=data.get("last_name"),
            company=data.get("company"),
            phone=data.get("phone"),
            plan=data.get("plan")
        )

        logger.info(f"Successfully created/updated HubSpot contact for email: {data.get('email')}")
        return jsonify({
            "message": "Contact created/updated successfully",
            "data": response
        }), 200

    except HubSpotError as e:
        logger.error(f"HubSpot API error: {str(e)}")
        return jsonify({"message": str(e)}), 500
    except Exception as e:
        logger.error(f"Failed to create/update HubSpot contact: {str(e)}")
        return jsonify({"message": STATUS_500_RESPONSE}), 500
