from blitzy_utils.logger import logger
from flask import Blueprint, Response, request, stream_with_context
from flask_utils.decorators import flask_pydantic_response, validate_request

from src.api.models import FileUploadOutput, StringUploadInput
from src.consts import GCS_BUCKET_NAME, storage_client
from src.error.errors import InvalidRequest
from src.service.company_service import get_company_by_id
from src.utils.gcs_utils import bucket_exists, create_bucket
from src.utils.url_utils import URLUtils

storage_bp = Blueprint("storage", __name__, url_prefix="/v1/storage")
UPLOAD_TIMEOUT = 600


@storage_bp.route("/upload", methods=["POST"])
@flask_pydantic_response
def upload_file():
    file_path = request.args.get("file_path")
    company_id = request.args.get("company_id")

    logger.info(
        f"Received request to upload file to {GCS_BUCKET_NAME}/{file_path} for company {company_id}"
    )

    if not file_path:
        raise InvalidRequest("file_path query parameter is required")

    file_path = URLUtils.decode_file_path(file_path)
    content_type = request.headers.get("Content-Type", "application/octet-stream")

    file_path = URLUtils.validate_file_path(file_path)
    logger.info(f"File path validated successfully: {file_path}")

    company_bucket_name = get_company_bucket_name(company_id=company_id)
    create_bucket_if_not_exists(company_bucket_name)
    company_file_path = f"{company_bucket_name}/{file_path}"
    logger.info(f"Uploading file to {company_file_path}")
    bucket = storage_client.bucket(company_bucket_name)
    blob = bucket.blob(file_path)
    blob.upload_from_file(
        request.stream,
        content_type=content_type,
        timeout=UPLOAD_TIMEOUT
    )
    logger.info("File uploaded successfully")
    return FileUploadOutput(filePath=company_file_path), 201


@storage_bp.route("/upload-string", methods=["POST"])
@validate_request(StringUploadInput)
@flask_pydantic_response
def upload_string(payload: StringUploadInput):
    company_id = payload.companyId
    file_path = payload.filePath
    data = payload.data
    content_type = payload.contentType if payload.contentType else "text/plain"

    logger.info(f"Received request to upload string data to {file_path} for company {company_id}")

    company_bucket_name = get_company_bucket_name(company_id=company_id)
    create_bucket_if_not_exists(company_bucket_name)

    bucket = storage_client.bucket(company_bucket_name)
    blob = bucket.blob(file_path)
    blob.upload_from_string(data, content_type=content_type)

    gcs_path = f"gs://{company_bucket_name}/{file_path}"
    logger.info(f"Uploaded to: {gcs_path}")

    return FileUploadOutput(filePath=gcs_path), 201


@storage_bp.route("/download", methods=["GET"])
def download_file():
    encoded_file_path = request.args.get("file_path")
    company_id = request.args.get("company_id")
    if not encoded_file_path:
        raise InvalidRequest("file_path query parameter is required")

    file_path = URLUtils.decode_file_path(encoded_file_path)
    file_path = URLUtils.validate_file_path(file_path)
    as_text = request.args.get("as_text", "false").lower() == "true"

    company_bucket_name = get_company_bucket_name(company_id=company_id)

    bucket = storage_client.bucket(company_bucket_name)
    blob = bucket.blob(file_path)
    if not blob.exists():
        raise InvalidRequest(f"File {file_path} not found in bucket {company_bucket_name}")

    logger.info(f"Downloading file: gs://{company_bucket_name}/{file_path}")
    if as_text:
        try:
            text_content = blob.download_as_text()
            logger.info(f"Downloaded as text: {len(text_content)} characters")
            return text_content
        except UnicodeDecodeError as e:
            logger.error(f"Cannot decode binary file as text: {str(e)}")
            raise InvalidRequest("Cannot download binary file as text. "
                                 "Remove as_text=true parameter for binary files like PDF, images, videos, etc.")

    def generate_file_stream():
        """Generate file content in chunks"""
        with blob.open("rb") as f:
            while True:
                chunk = f.read(1024 * 1024)  # 1MB chunks
                if not chunk:
                    break
                yield chunk

    # Get filename for download header
    filename = file_path.split("/")[-1]

    return Response(
        stream_with_context(generate_file_stream()),
        content_type="application/octet-stream",
        headers={
            "Content-Disposition": f"attachment; filename='{filename}'"
        }
    )


def create_bucket_if_not_exists(bucket_name):
    logger.info(f"Checking if bucket {bucket_name} exists")
    is_exists = bucket_exists(bucket_name)
    if not is_exists:
        logger.info(f"Bucket {bucket_name} does not exist. Creating")
        create_bucket(bucket_name)
        return

    logger.info(f"Bucket {bucket_name} exists.")


def get_company_bucket_name(company_id):
    if not company_id:
        return GCS_BUCKET_NAME

    company_info = get_company_by_id(company_id)
    if not company_info:
        raise InvalidRequest(f"Company with id {company_id} not found")
    return f"{GCS_BUCKET_NAME}-{company_id}"
