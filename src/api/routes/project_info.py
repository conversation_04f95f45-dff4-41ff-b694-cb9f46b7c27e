from flask import Blueprint
from flask_utils.decorators import flask_pydantic_response

from src.api.models import ProjectInfoOutput
from src.error.errors import ResourceNotFound
from src.service.company_service import get_company_id_by_project_id
from src.service.github_branch_pattern_projects_service import \
    get_branch_id_by_project_id
from src.service.github_project_repo_service import \
    get_github_project_repo_id_by_project_id
from src.service.project_service import get_project_by_id

project_info_bp = Blueprint("project_info", __name__, url_prefix="/v1/project/info")


@project_info_bp.route("/<project_id>", methods=["GET"])
@flask_pydantic_response
def get_project_info(project_id: str):
    project_info = get_project_by_id(project_id)
    if not project_info:
        raise ResourceNotFound(f"Project with id {project_id} not found")

    repo_ids = get_github_project_repo_id_by_project_id(project_id=project_id)
    branch_ids = get_branch_id_by_project_id(project_id=project_id)
    company_id = get_company_id_by_project_id(project_id)

    output = ProjectInfoOutput()
    output.repoIds = repo_ids
    output.branchIds = branch_ids
    output.companyId = company_id if company_id else ""
    return output
