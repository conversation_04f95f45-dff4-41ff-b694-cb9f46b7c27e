from blitzy_utils.logger import logger
from flask import Blueprint
from flask_utils.decorators import flask_pydantic_response
from flask_utils.models_config.model_utils import map_to_model

from src.api.models import GitHubProjectOutput, Status404
from src.service.github_repository_service import \
    get_github_repository_by_project_id

project_bp = Blueprint("project_bp", __name__, url_prefix="/v1/project")


@project_bp.route("/<project_id>/github/repo", methods=["GET"])
@flask_pydantic_response
def get_github_repo_by_project_id(project_id: str):
    """Get the github repository by project id."""
    github_repo = get_github_repository_by_project_id(project_id)
    if not github_repo:
        logger.warning(f"No github repository found for project id {project_id}")
        return Status404(message=f"No github repository found for project id {project_id}"), 404

    github_repo_pydantic = map_to_model(github_repo, GitHubProjectOutput)
    return github_repo_pydantic, 200
