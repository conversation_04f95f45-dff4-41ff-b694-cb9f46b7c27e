import json
from base64 import b64decode
from time import time
from typing import Dict

from blitzy_utils.logger import logger
from firebase_admin import auth as firebase_auth
from flask import Blueprint, jsonify, request
from flask_utils.decorators import flask_pydantic_response, validate_request
from google.auth import crypt, jwt

from src.api.models import LoginInput, LoginOutput
from src.api.utils.auth_utils import firebase_sign_in_with_email_password
from src.consts import (BASIC_AUTH_HEADER_NAME, ID_TOKEN_HEADER_NAME,
                        JWT_AUDIENCE, JWT_EXPIRY_LENGTH, PROJECT_ID,
                        SECRETS_CLIENT, UI_AUTH_SECRET_ID, UI_SA_EMAIL)

auth_bp = Blueprint("auth", __name__, url_prefix="/v1/auth")


@auth_bp.route("", methods=["POST"])
def exchange_tokens():
    id_token = request.headers.get(ID_TOKEN_HEADER_NAME)
    basic_auth_header = request.headers.get(BASIC_AUTH_HEADER_NAME)

    if id_token:
        return handle_id_token_auth(id_token)
    elif basic_auth_header and basic_auth_header.startswith("Basic "):
        return handle_basic_auth(basic_auth_header)
    else:
        logger.info("POST Auth, Response 401: Unauthorized")
        return jsonify({"error": "Invalid credentials"}), 401


def handle_id_token_auth(id_token):
    try:
        decoded_id_token = firebase_auth.verify_id_token(id_token)
        return create_jwt_token(decoded_id_token["uid"])
    except Exception as e:
        logger.info(f"POST Auth, Response 401: Unauthorized, {e}")
        return jsonify({"error": "Token Exchange failed"}), 401


def handle_basic_auth(auth_header):
    try:
        encoded_credentials = auth_header.split(" ")[1]
        decoded_credentials = b64decode(encoded_credentials).decode("utf-8")
        email, password = decoded_credentials.split(":")

        # Call Firebase Auth REST API
        auth_response = firebase_sign_in_with_email_password(email, password)

        id_token = auth_response["idToken"]
        decoded_id_token = firebase_auth.verify_id_token(id_token)
        return create_jwt_token(decoded_id_token["uid"])
    except Exception as e:
        logger.info(f"POST Auth, Response 401: Unauthorized, {e}")
        return jsonify({"error": "Invalid credentials"}), 401


def create_jwt_token(uid):
    try:
        now = int(time())

        # build payload
        payload = {
            "iat": now,
            "exp": now + JWT_EXPIRY_LENGTH,
            "iss": UI_SA_EMAIL,
            "aud": JWT_AUDIENCE,
            "sub": UI_SA_EMAIL,
            "email": UI_SA_EMAIL,
            "uid": uid
        }

        service_account_info = json.loads(get_secret(PROJECT_ID, UI_AUTH_SECRET_ID))
        signer = crypt.RSASigner.from_service_account_info(service_account_info)
        jwt_token = jwt.encode(signer, payload).decode("utf-8")
        return jsonify({"access_token": jwt_token}), 200
    except Exception as e:
        logger.info(f"POST Auth, Response 401: Unauthorized, {e}")
        return jsonify({"error": "JWT Token Exchange failed"}), 401


def get_secret(project_id, secret_id):
    # Build the resource name of the secret version.
    name = f"projects/{project_id}/secrets/{secret_id}/versions/latest"
    secret_response = SECRETS_CLIENT.access_secret_version(request={"name": name})
    return secret_response.payload.data.decode("UTF-8")


@auth_bp.route("/login", methods=["POST"])
@validate_request(LoginInput)
@flask_pydantic_response
def sign_in_user(payload: LoginInput):
    auth_data = firebase_sign_in_with_email_password(payload.email, payload.password)
    response = convert_dict_to_login_response(auth_data)
    return response, 200


def convert_dict_to_login_response(auth_data: Dict) -> LoginOutput:
    login_response = LoginOutput()
    login_response.kind = auth_data.get("kind")
    login_response.localId = auth_data.get("localId")
    login_response.email = auth_data.get("email")
    login_response.displayName = auth_data.get("displayName")
    login_response.idToken = auth_data.get("idToken")
    login_response.registered = auth_data.get("registered")
    login_response.refreshToken = auth_data.get("refreshToken")
    login_response.expiresIn = auth_data.get("expiresIn")
    return login_response
