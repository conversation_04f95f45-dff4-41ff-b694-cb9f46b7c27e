from datetime import datetime

from flask import Blueprint, jsonify

from src.middleware.decorators import get_user_info

utils_bp = Blueprint("utils", __name__)


@utils_bp.route("/v1/uptime-check", methods=["GET"])
@get_user_info
def get_uptime_check(user_info):
    return jsonify({'accessed_ts': datetime.now().isoformat()}), 200


@utils_bp.route("/v1/health-check", methods=["GET"])
def health_check():
    return jsonify({"OK": True}), 200
