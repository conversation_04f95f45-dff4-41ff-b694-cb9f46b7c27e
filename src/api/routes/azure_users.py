"""
Azure DevOps user-related API endpoints for archie-github-handler.

This module contains Azure DevOps-specific user operations that directly implement
Azure DevOps functionality without service type detection.
"""

from blitzy_utils.logger import logger
from common_models.models import VersionControlSystem
from flask import Blueprint
from flask_utils.decorators import flask_pydantic_response

from src.api.models import (AzureProjectList, BranchList, GithubOrgBasic,
                            GithubOrgBasicList, RepositoryList)
from src.api.utils.azure_utils import (get_azure_branches_by_repo_id,
                                       get_azure_branches_by_repo_with_context,
                                       get_azure_projects_by_user_and_org,
                                       get_azure_repos_by_user_org_and_project)
from src.azure.azure_app_service import AzureAppService
from src.error.errors import ResourceNotFound
from src.service.azure_service import fetch_azure_secret_for_user
from src.service.user_service import get_user_by_id

# Create Azure users blueprint
azure_users_bp = Blueprint("azure_users", __name__, url_prefix="/v1/azure/users")


@azure_users_bp.route("/<user_id>/organizations", methods=["GET"])
@flask_pydantic_response
def get_azure_user_organizations(user_id: str):
    """
    Get Azure DevOps organizations for a specific user.

    This endpoint only handles Azure DevOps organizations.
    """
    try:
        user_info = get_user_by_id(user_id)
        if not user_info:
            raise ResourceNotFound(f"User with id {user_id} not found")

        access_token = fetch_azure_secret_for_user(
            user_id, VersionControlSystem.AZURE_DEVOPS
        ).accessToken
        # Since this is /v1/azure/* endpoint, we know it's Azure DevOps
        # No need to check installation service type
        instance = AzureAppService()
        response = instance.get_azure_devops_org_details(access_token)

        # Ensure we return a proper structure even if response is None or empty
        if response is None:
            return serialize_organizations({}), 200

        return serialize_organizations(response), 200
    except Exception as e:
        logger.error(f"Error getting Azure organizations for user {user_id}: {str(e)}")
        # Return empty organizations structure with 500 status code for exceptions
        return serialize_organizations({}), 500


@azure_users_bp.route("/<user_id>/organizations/<org_id>/projects", methods=["GET"])
@flask_pydantic_response
def get_azure_user_projects(user_id: str, org_id: str):
    """
    Get Azure DevOps projects for a specific user and organization.

    This endpoint only handles Azure DevOps projects.
    """
    try:
        user_info = get_user_by_id(user_id)
        if not user_info:
            raise ResourceNotFound(f"User with id {user_id} not found")

        # Get Azure DevOps projects
        project_list = get_azure_projects_by_user_and_org(user_info, org_id)

        # Ensure we return an empty list if project_list is None or empty
        if not project_list:
            return AzureProjectList(results=[]), 200

        response = AzureProjectList(results=project_list)
        return response, 200
    except Exception as e:
        logger.error(
            f"Error getting Azure projects for user {user_id}, org {org_id}: {str(e)}"
        )
        # Return empty projects list instead of raising exception
        return AzureProjectList(results=[]), 200


@azure_users_bp.route(
    "/<user_id>/organizations/<org_id>/projects/<project_id>/repositories",
    methods=["GET"],
)
@flask_pydantic_response
def get_azure_user_repositories(user_id: str, org_id: str, project_id: str):
    """
    Get Azure DevOps repositories for a specific user, organization, and project.

    This endpoint only handles Azure DevOps repositories.
    """
    try:
        user_info = get_user_by_id(user_id)
        if not user_info:
            raise ResourceNotFound(f"User with id {user_id} not found")

        # Get Azure DevOps repositories
        repo_list = get_azure_repos_by_user_org_and_project(
            user_info, org_id, project_id
        )

        # Ensure we return an empty list if repo_list is None or empty
        if not repo_list:
            return RepositoryList(results=[]), 200

        response = RepositoryList(results=repo_list)
        return response, 200
    except Exception as e:
        logger.error(
            f"Error getting Azure repositories for user {user_id}, org {org_id}, project {project_id}: {str(e)}"
        )
        # Return empty repositories list with 500 status code for exceptions
        return RepositoryList(results=[]), 500


@azure_users_bp.route(
    "/<user_id>/organizations/<org_id>/repositories/<repo_id>/branches", methods=["GET"]
)
@flask_pydantic_response
def get_azure_user_branches_unified(user_id: str, org_id: str, repo_id: str):
    """
    Get Azure DevOps branches for a specific user, organization, and repository.

    This endpoint derives project_id automatically from the database.
    This endpoint only handles Azure DevOps branches.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Use get_azure_branches_by_repo_with_context which handles project derivation
    branches_list = get_azure_branches_by_repo_with_context(
        user_info, org_id, None, repo_id
    )
    response = BranchList(results=branches_list)

    return response, 200


@azure_users_bp.route(
    "/<user_id>/organizations/<org_id>/projects/<project_id>/repositories/<repo_id>/branches",
    methods=["GET"],
)
@flask_pydantic_response
def get_azure_user_branches_with_project(
    user_id: str, org_id: str, project_id: str, repo_id: str
):
    """
    Get Azure DevOps branches for a specific user, organization, project, and repository.

    This endpoint uses explicit project_id in the hierarchical structure.
    This endpoint only handles Azure DevOps branches.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get Azure DevOps branches with explicit project_id
    branches_list = get_azure_branches_by_repo_id(
        user_info, org_id, project_id, repo_id
    )
    response = BranchList(results=branches_list)

    return response, 200


def serialize_organizations(organizations_data):
    """
    Transform organizations data from nested object to flat array format using Pydantic models.

    Args:
        organizations_data: Can be either:
            - List[Dict[str, Any]]: List of dicts where each dict has one key (org name) and value is org data
            - Dict[str, Any]: Flat dict where keys are org names and values are org data

    Returns:
        GithubOrgBasicList: Pydantic model with "results" array containing GithubOrgBasic objects
    """
    mapped_list = []

    # Handle both input formats
    if isinstance(organizations_data, list):
        # Handle list format (same as map_org_list_to_pydantic input)
        for org in organizations_data:
            # Extract the org data (same logic as map_org_list_to_pydantic)
            info = next(iter(org.values()))
            mapped_list.append(GithubOrgBasic(**info))

    elif isinstance(organizations_data, dict):
        # Handle flat dict format
        for _org_name, org_data in organizations_data.items():
            mapped_list.append(GithubOrgBasic(**org_data))

    else:
        raise ValueError(f"Unsupported organizations_data type: {type(organizations_data)}")

    github_org_list = GithubOrgBasicList(results=mapped_list)
    return github_org_list
