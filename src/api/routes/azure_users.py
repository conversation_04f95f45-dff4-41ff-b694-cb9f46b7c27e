"""
Azure DevOps user-related API endpoints.

This module contains Azure DevOps-specific user operations that are separate from GitHub functionality.
"""

from blitzy_utils.logger import logger
from blitzy_utils.service_client import ServiceClient
from flask import Blueprint
from flask_utils.decorators import flask_pydantic_response

from src.middleware.decorators import get_user_info

# Create Azure users blueprint
azure_users_bp = Blueprint("azure_users", __name__, url_prefix="/v1/azure/users")


@azure_users_bp.route("/<user_id>/organizations", methods=["GET"])
@get_user_info()
@flask_pydantic_response
def get_azure_user_organizations(user_info, user_id):
    """
    Get Azure DevOps organizations for a specific user.

    This endpoint only handles Azure DevOps organizations.
    """
    # no need user_id permission check since we have jwt token verification at load balancer level
    response = get_organizations_from_azure_handler(user_id)
    return response, 200


@azure_users_bp.route("/<user_id>/organizations/<org_id>/projects", methods=["GET"])
@get_user_info()
@flask_pydantic_response
def get_azure_user_projects(user_info, user_id, org_id):
    """
    Get Azure DevOps projects for a specific user and organization.

    This endpoint only handles Azure DevOps projects.
    """

    response = get_projects_from_azure_handler(user_id, org_id)
    return response, 200


@azure_users_bp.route(
    "/<user_id>/organizations/<org_id>/projects/<project_id>/repositories",
    methods=["GET"],
)
@get_user_info()
@flask_pydantic_response
def get_azure_user_repositories(user_info, user_id, org_id, project_id):
    """
    Get Azure DevOps repositories for a specific user, organization, and project.

    This endpoint only handles Azure DevOps repositories.
    """

    response = get_repositories_from_azure_handler(user_id, org_id, project_id)
    return response, 200


@azure_users_bp.route(
    "/<user_id>/organizations/<org_id>/repositories/<repo_id>/branches", methods=["GET"]
)
@get_user_info()
@flask_pydantic_response
def get_azure_user_branches_unified(user_info, user_id, org_id, repo_id):
    """
    Get Azure DevOps branches for a specific user, organization, and repository.

    This endpoint derives project_id automatically from the database.
    This endpoint only handles Azure DevOps branches.
    """

    response = get_branches_from_azure_handler_unified(user_id, org_id, repo_id)
    return response, 200


@azure_users_bp.route(
    "/<user_id>/organizations/<org_id>/projects/<project_id>/repositories/<repo_id>/branches",
    methods=["GET"],
)
@get_user_info()
@flask_pydantic_response
def get_azure_user_branches_with_project(
    user_info, user_id, org_id, project_id, repo_id
):
    """
    Get Azure DevOps branches for a specific user, organization, project, and repository.

    This endpoint uses explicit project_id in the hierarchical structure.
    This endpoint only handles Azure DevOps branches.
    """

    response = get_branches_from_azure_handler_with_project(
        user_id, org_id, project_id, repo_id
    )
    return response, 200


# Azure handler functions that call archie-github-handler /v1/azure/* endpoints


def get_organizations_from_azure_handler(user_id: str):
    """
    Call archie-github-handler service to get Azure DevOps organizations for a user.
    """
    with ServiceClient() as client:
        response = client.get("github", f"/v1/azure/users/{user_id}/organizations")
        response.raise_for_status()
        return response.json()


def get_projects_from_azure_handler(user_id: str, org_id: str):
    """
    Call archie-github-handler service to get Azure DevOps projects for an organization.
    """
    with ServiceClient() as client:
        response = client.get(
            "github",
            f"/v1/azure/users/{user_id}/organizations/{org_id}/projects",
        )
        response.raise_for_status()
        return response.json()


def get_repositories_from_azure_handler(user_id: str, org_id: str, project_id: str):
    """
    Call archie-github-handler service to get repositories from Azure DevOps project.
    """
    with ServiceClient() as client:
        response = client.get(
            "github",
            f"/v1/azure/users/{user_id}/organizations/{org_id}/projects/{project_id}/repositories",
        )
        response.raise_for_status()
        return response.json()


def get_branches_from_azure_handler_unified(user_id: str, org_id: str, repo_id: str):
    """
    Call archie-github-handler service to get branches from Azure DevOps repository.
    This uses the endpoint that derives project_id automatically from the database.
    """
    with ServiceClient() as client:
        response = client.get(
            "github",
            f"/v1/azure/users/{user_id}/organizations/{org_id}/repositories/{repo_id}/branches",
        )
        response.raise_for_status()
        return response.json()


def get_branches_from_azure_handler_with_project(
    user_id: str, org_id: str, project_id: str, repo_id: str
):
    """
    Call archie-github-handler service to get branches from Azure DevOps repository.
    This uses the hierarchical endpoint with explicit project_id.
    """
    with ServiceClient() as client:
        response = client.get(
            "github",
            f"/v1/azure/users/{user_id}/organizations/{org_id}/projects/{project_id}/repositories/{repo_id}/branches",
        )
        response.raise_for_status()
        return response.json()
