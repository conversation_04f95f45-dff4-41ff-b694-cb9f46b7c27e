# Operations API Documentation

Base URL: `http://localhost:8081/v1/github/operations`

## Branches

### Create Branch

Creates a new branch in a repository.

**Endpoint:** `POST /repositories/{repositoryId}/branches`

```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "branchName": "main",
    "baseBranch": "main",
    "description": "test"
  }' \
  http://localhost:8081/v1/github/operations/repositories/995556255/branches
```

**Parameters:**
- `branchName` (string): Name of the new branch
- `baseBranch` (string): Branch to create from
- `description` (string): Optional description

## Commits

### Create Commit

Creates a new commit with file changes on a branch.

**Endpoint:** `POST /repositories/{repositoryId}/branches/{branchName}/commits`

```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Hello there",
    "files": [
      {
        "path": "main.py",
        "content": "def hello():\n    return '\''world'\''",
        "operation": "CREATE"
      }
    ]
  }' \
  http://localhost:8081/v1/github/operations/repositories/995556255/branches/main/commits
```

**Parameters:**
- `message` (string): Commit message
- `files` (array): Array of file operations
  - `path` (string): File path
  - `content` (string): File content
  - `operation` (string): Operation type (CREATE, UPDATE, DELETE)

## Pull Requests

### Create Pull Request

Creates a new pull request between branches.

**Endpoint:** `POST /repositories/{repositoryId}/pull-requests`

```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Pull request from main to test1",
    "description": "test",
    "headBranch": "main",
    "baseBranch": "test1",
    "draft": false,
    "maintainerCanModify": true
  }' \
  http://localhost:8081/v1/github/operations/repositories/995556255/pull-requests
```

**Parameters:**
- `title` (string): Pull request title
- `description` (string): Pull request description
- `headBranch` (string): Source branch
- `baseBranch` (string): Target branch
- `draft` (boolean): Whether PR is a draft
- `maintainerCanModify` (boolean): Allow maintainer modifications
