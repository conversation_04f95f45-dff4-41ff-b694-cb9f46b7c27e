import os
from datetime import datetime, timezone
from typing import Optional

import stripe
from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import SubscriptionStatus, SubscriptionType, User
from flask import jsonify
from sqlalchemy.orm import Session
from tenacity import (retry, retry_if_result, stop_after_attempt,
                      wait_exponential)

from src.api.routes.hubspot import create_hubspot_contact
from src.consts import (PRO_USER_ALLOWED_JOB_RUNS,
                        STRIPE_BLITZY_PRO_PRODUCT_ID, STRIPE_STATUS_MAPPING)
from src.service.subscription_service import (
    get_subscription_info_by_customer_id, get_subscription_info_by_user_id,
    get_user_email_by_subscription, is_none, save_subscription)

stripe.api_key = os.getenv("STRIPE_API_KEY")


def handle_checkout_session_completed(event):
    """Handle checkout.session.completed webhook event."""
    stripe_session = event["data"]["object"]
    user_id = stripe_session["client_reference_id"]
    customer_id = stripe_session["customer"]
    subscription_id = stripe_session["subscription"]

    subscription = get_subscription_info_by_user_id(user_id)
    if not subscription:
        logger.warning(f"No subscription found with customer_id: {customer_id}"
                       f" for checkout.session.completed webhook")
        return jsonify(success=True), 200
    stripe_subscription = stripe.Subscription.retrieve(subscription_id)

    # Initial subscription setup
    setup_initial_subscription(subscription, customer_id, subscription_id, stripe_subscription)

    # Add HubSpot update after subscription setup
    update_hubspot_contact_plan(
        email=get_user_email_by_subscription(subscription),
        plan=subscription.plan_name
    )

    with get_db_session() as session:
        save_subscription(subscription=subscription, session=session)
        session.commit()


def setup_initial_subscription(subscription, customer_id, subscription_id, stripe_subscription):
    """
    Set up initial subscription details, considering user's geolocation for trial eligibility.
    Trial is only offered to US users or users with no country code.
    """
    subscription.stripe_customer_id = customer_id
    subscription.stripe_subscription_id = subscription_id
    subscription.plan_name = SubscriptionType.PRO

    if not subscription.has_trial_received:
        with get_db_session() as session:
            if is_trial_eligible(subscription.user_id, session):
                setup_trial_subscription(subscription, stripe_subscription)
            else:
                # For non-US users, set up active subscription directly
                setup_active_subscription(subscription)
    else:
        setup_active_subscription(subscription)

    update_subscription_period_dates(subscription, stripe_subscription)


def setup_trial_subscription(subscription, stripe_subscription):
    """Set up trial-specific subscription details."""
    subscription.status = SubscriptionStatus.TRIALING
    subscription.is_trialing = True
    subscription.has_trial_received = True
    subscription.plan_name = SubscriptionType.PRO

    subscription.trial_start_date = datetime.fromtimestamp(
        stripe_subscription["trial_start"], tz=timezone.utc)
    subscription.trial_end_date = datetime.fromtimestamp(
        stripe_subscription["trial_end"], tz=timezone.utc)


def setup_active_subscription(subscription):
    """Set up regular (non-trial) subscription details."""
    subscription.status = SubscriptionStatus.ACTIVE
    subscription.is_trialing = False
    subscription.plan_name = SubscriptionType.PRO


def update_subscription_period_dates(subscription, stripe_subscription):
    """Update subscription period dates."""
    subscription.current_period_start_date = datetime.fromtimestamp(
        stripe_subscription["current_period_start"], tz=timezone.utc)
    subscription.current_period_end_date = datetime.fromtimestamp(
        stripe_subscription["current_period_end"], tz=timezone.utc)


def handle_subscription_updated(event):
    """Handle customer.subscription.updated webhook event."""
    subscription_id = event["data"]["object"]["id"]
    stripe_subscription = stripe.Subscription.retrieve(subscription_id)
    customer_id = stripe_subscription["customer"]
    subscription = get_subscription_info_by_customer_id(customer_id=customer_id)
    if not subscription:
        logger.warning(
            f"No subscription found with customer_id: {customer_id} for customer.subscription.updated webhook")
        return jsonify(success=True), 200

    update_subscription_details(subscription, stripe_subscription)

    # Add HubSpot update after status changes
    update_hubspot_contact_plan(
        email=get_user_email_by_subscription(subscription),
        plan=subscription.plan_name
    )

    with get_db_session() as session:
        save_subscription(subscription=subscription, session=session)
        session.commit()


def update_period_dates(subscription, stripe_subscription):
    """Update the subscription period dates from Stripe data."""
    subscription.current_period_start_date = datetime.fromtimestamp(
        stripe_subscription["current_period_start"], tz=timezone.utc)
    subscription.current_period_end_date = datetime.fromtimestamp(
        stripe_subscription["current_period_end"], tz=timezone.utc)


def update_subscription_details(subscription, stripe_subscription):
    """Update subscription details based on Stripe subscription data."""
    update_period_dates(subscription, stripe_subscription)
    handle_trial_status(subscription, stripe_subscription)
    if stripe_subscription["cancel_at_period_end"]:
        handle_cancellation_status(subscription, stripe_subscription)
    elif not stripe_subscription["cancel_at_period_end"]:
        handle_renewal_status(subscription, stripe_subscription)
    elif not subscription.is_trialing and not stripe_subscription["cancel_at_period_end"]:
        update_general_status(subscription, stripe_subscription)


def handle_trial_status(subscription, stripe_subscription):
    """Handle trial-specific subscription status."""
    is_in_trial_period = (
            stripe_subscription["trial_end"] and
            datetime.fromtimestamp(
                stripe_subscription["trial_end"],
                tz=timezone.utc
            ) > datetime.now(tz=timezone.utc)
    )
    if stripe_subscription["status"] == "trialing" or is_in_trial_period:
        # Check if trial end date has been extended
        new_trial_end = datetime.fromtimestamp(
            stripe_subscription["trial_end"],
            tz=timezone.utc
        )
        if subscription.trial_end_date and new_trial_end > subscription.trial_end_date:
            # Trial has been extended, reset remaining_runs to 2
            subscription.remaining_runs = 2

        subscription.is_trialing = True
        subscription.status = SubscriptionStatus.TRIALING
        subscription.trial_start_date = datetime.fromtimestamp(
            stripe_subscription["trial_start"], tz=timezone.utc)
        subscription.trial_end_date = new_trial_end


def handle_cancellation_status(subscription, stripe_subscription):
    """Handle subscription cancellation status."""
    subscription.current_period_end_date = datetime.fromtimestamp(
        stripe_subscription["current_period_end"], tz=timezone.utc)
    if subscription.is_trialing:
        # Keep as cancelling if cancelled during trial
        subscription.status = SubscriptionStatus.CANCELLING
    elif stripe_subscription["status"] == "active":
        # Set as cancelling when active subscription is cancelled
        subscription.status = SubscriptionStatus.CANCELLING
    elif stripe_subscription["status"] == "canceled":
        # Set as cancelled if cancelled during PRO
        subscription.status = SubscriptionStatus.CANCELLED
        subscription.plan_name = SubscriptionType.FREE
        subscription.is_trialing = False


def handle_renewal_status(subscription, stripe_subscription):
    """Handle subscription renewal status."""
    subscription.current_period_end_date = datetime.fromtimestamp(
        stripe_subscription["current_period_end"], tz=timezone.utc)
    subscription.current_period_start_date = datetime.fromtimestamp(
        stripe_subscription["current_period_start"], tz=timezone.utc)

    is_in_trial_period = (
            stripe_subscription["trial_end"] and
            datetime.fromtimestamp(
                stripe_subscription["trial_end"],
                tz=timezone.utc
            ) > datetime.now(tz=timezone.utc)
    )

    if not is_in_trial_period:
        subscription.status = SubscriptionStatus.ACTIVE
        subscription.is_trialing = False
        subscription.plan_name = SubscriptionType.PRO


def update_general_status(subscription, stripe_subscription):
    """Update general subscription status based on Stripe status."""
    subscription.status = STRIPE_STATUS_MAPPING.get(
        stripe_subscription["status"],
        SubscriptionStatus.NONE
    )


def update_cancelled_subscription(subscription, stripe_subscription):
    """Update subscription details when cancelled."""
    subscription.status = SubscriptionStatus.CANCELLED
    subscription.remaining_runs = 0
    subscription.end_date = datetime.fromtimestamp(
        stripe_subscription["ended_at"], tz=timezone.utc)
    subscription.plan_name = SubscriptionType.FREE
    subscription.is_trialing = False


def process_successful_payment(subscription, stripe_subscription):
    """Process a successful subscription payment."""
    # Check if this is initial payment for trial setup
    is_trial_setup = (
            stripe_subscription["trial_end"] and
            datetime.fromtimestamp(
                stripe_subscription["trial_end"],
                tz=timezone.utc
            ) > datetime.now(tz=timezone.utc)
    )

    if not is_trial_setup:  # Only process for non-trial payments
        # Update period dates for the new billing cycle
        subscription.current_period_start_date = datetime.fromtimestamp(
            stripe_subscription["current_period_start"], tz=timezone.utc)
        subscription.current_period_end_date = datetime.fromtimestamp(
            stripe_subscription["current_period_end"], tz=timezone.utc)

        # Set status to ACTIVE only for actual payments (not trial)
        subscription.status = SubscriptionStatus.ACTIVE
        subscription.is_trialing = False

    subscription.remaining_runs = PRO_USER_ALLOWED_JOB_RUNS

    with get_db_session() as session:
        save_subscription(subscription=subscription, session=session)
        session.commit()


def update_failed_payment_subscription(subscription):
    """Update subscription details when payment fails."""
    subscription.status = SubscriptionStatus.PAST_DUE
    subscription.plan_name = SubscriptionType.FREE
    with get_db_session() as session:
        save_subscription(subscription=subscription, session=session)
        session.commit()


def handle_subscription_deleted(event):
    """Handle customer.subscription.deleted webhook event."""
    subscription_id = event["data"]["object"]["id"]
    stripe_subscription = stripe.Subscription.retrieve(subscription_id)
    customer_id = stripe_subscription["customer"]
    subscription = get_subscription_info_by_customer_id(customer_id=customer_id)
    if not subscription:
        logger.error(f"No subscription found with customer_id: {customer_id} for customer.subscription.deleted webhook")
        return jsonify(success=True), 200
    update_cancelled_subscription(subscription, stripe_subscription)

    # Update HubSpot when subscription is cancelled
    update_hubspot_contact_plan(
        email=get_user_email_by_subscription(subscription),
        plan=SubscriptionType.FREE
    )

    with get_db_session() as session:
        save_subscription(subscription=subscription, session=session)
        session.commit()


def handle_invoice_payment_failed(event):
    """Handle invoice.payment_failed webhook event."""
    subscription_id = event["data"]["object"]["subscription"]
    stripe_subscription = stripe.Subscription.retrieve(subscription_id)
    customer_id = stripe_subscription["customer"]
    invoice = event["data"]["object"]
    billing_reason = invoice.get("billing_reason")

    # If this is a new subscription creation that failed, don't try to look up the subscription
    if billing_reason == "subscription_create":
        logger.info(f"New subscription {subscription_id} payment failed for customer {customer_id}")
        return jsonify(success=True), 200

    subscription = get_subscription_info_by_customer_id(customer_id=customer_id)

    if not subscription:
        logger.warning(f"No subscription found with customer_id: {customer_id} for invoice.payment_failed webhook.")
        return jsonify(success=True), 200
    update_failed_payment_subscription(subscription)


def handle_invoice_payment_succeeded(event):
    """Handle invoice.payment_succeeded webhook event."""
    try:
        invoice = event["data"]["object"]
        subscription_id = invoice.get("subscription")

        if not subscription_id:
            logger.error(f"Received invoice.payment_succeeded webhook for invoice {invoice['id']} without subscription")
            return jsonify(success=True), 200

        stripe_subscription = stripe.Subscription.retrieve(subscription_id)

        # Check if this is from CLI
        metadata = invoice.get("subscription_details", {}).get("metadata", {})
        is_cli_grant = metadata.get("source") == "cli_free_pro_grant"

        if is_cli_grant:
            handle_cli_subscription(stripe_subscription)
        else:
            handle_regular_subscription(stripe_subscription)

        logger.info(f"Successfully processed invoice payment for subscription {subscription_id}")
        return jsonify(success=True), 200
    except Exception as e:
        logger.warning(f"Error in handle_invoice_payment_succeeded: {str(e)}")
        return jsonify(success=True), 200


def handle_cli_subscription(stripe_subscription):
    """Handle subscription created through CLI."""
    customer_id = stripe_subscription["customer"]
    customer = stripe.Customer.retrieve(customer_id)
    email = customer.email
    with get_db_session() as session:
        try:
            # First get the user by email
            user = get_user_by_email(email, session)
            if not user:
                logger.warning(f"No user found with email: {email}")
                return

            # Get subscription (whether it exists or not)
            subscription = get_subscription_info_by_user_id(user.id)

            # Update subscription
            if subscription:
                update_cli_subscription(subscription, stripe_subscription, session)
            else:
                logger.warning(f"Subscription does not found for {user.id}")

        except Exception as e:
            logger.error(f"Error in handle_cli_subscription: {str(e)}")
            return jsonify(success=True), 200


def handle_regular_subscription(stripe_subscription):
    """Handle regular (non-CLI) subscription update."""
    customer_id = stripe_subscription["customer"]
    subscription = get_subscription_info_by_customer_id(customer_id)
    if not subscription:
        logger.warning(
            f"No subscription found with customer_id: {customer_id} for invoice.payment_succeeded webhook")
        return jsonify(success=True), 200
    else:
        process_successful_payment(subscription, stripe_subscription)


def update_cli_subscription(subscription, stripe_subscription, session):
    """Update existing subscription for CLI-created trial."""
    subscription.stripe_subscription_id = stripe_subscription.id
    subscription.stripe_customer_id = stripe_subscription.customer
    subscription.status = SubscriptionStatus.TRIALING
    subscription.is_trialing = True
    subscription.has_trial_received = True
    subscription.plan_name = SubscriptionType.PRO
    subscription.remaining_runs = 2
    subscription.trial_start_date = datetime.fromtimestamp(
        stripe_subscription.trial_start, tz=timezone.utc)
    subscription.trial_end_date = datetime.fromtimestamp(
        stripe_subscription.trial_end, tz=timezone.utc)
    subscription.current_period_start_date = datetime.fromtimestamp(
        stripe_subscription.current_period_start, tz=timezone.utc)
    subscription.current_period_end_date = datetime.fromtimestamp(
        stripe_subscription.current_period_end, tz=timezone.utc)

    session.add(subscription)
    session.commit()
    logger.info(f"Updated existing subscription for customer {subscription.stripe_customer_id}")
    return jsonify(success=True), 200


def update_hubspot_contact_plan(email: str, plan: SubscriptionType) -> None:
    """
    Update HubSpot contact's plan when subscription changes
    """
    try:
        create_hubspot_contact(
            email=email,
            plan=plan
        )
    except Exception as e:
        # Log error but don't interrupt the main flow
        logger.error(f"Failed to update HubSpot contact plan: {str(e)}")


@retry(
    retry=retry_if_result(is_none),
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10)
)
def get_user_by_email(email: str, session: Optional[Session] = None) -> Optional[User]:
    """
    Get user by email with retry logic.

    Args:
        email (str): User's email address
        session (Session): Optional database session

    Returns:
        Optional[User]: User object if found, None otherwise
    """
    with get_db_session(session) as session:
        user = session.query(User).filter(
            User.email == email,
            User.is_deleted.is_(False)
        ).first()

        if user and not session:
            session.expunge(user)
        return user


def is_trial_eligible(user_id: str, session: Session) -> bool:
    """
    Check if a user is eligible for a trial based on their location.
    Trial is only offered to US users.

    Args:
        user_id: The ID of the user to check
        session: The database session to use for queries

    Returns:
        bool: True if user is eligible for trial, False otherwise
    """
    # Sid: Temporarily disabling Trials
    # geolocation = (session.query(GeoLocation)
    #                .filter(GeoLocation.user_id == user_id)
    #                .filter(GeoLocation.is_deleted.is_(False))
    #                .order_by(GeoLocation.created_at.desc())
    #                .first())
    # if not geolocation:
    #     return False
    # # User is eligible if their country code is 'US'
    # return geolocation.country_code == 'US'
    return False


def is_valid_product_id(event: dict) -> bool:
    """
    Check if the event is for the Blitzy Pro product.

    Args:
        event (dict): The Stripe webhook event payload

    Returns:
        bool: True if event is for Blitzy Pro product, False otherwise
    """
    try:
        product_id = None
        # Get the main object from the event payload
        obj = event.get("data", {}).get("object", {})

        # Handle checkout session events by retrieving the subscription from Stripe
        if event.get("type") == "checkout.session.completed":
            subscription_id = obj.get("subscription")
            if subscription_id:
                subscription = stripe.Subscription.retrieve(subscription_id)
                product_id = subscription.plan.product
        # Handle subscription update and delete events
        elif event.get("type") in ["customer.subscription.updated", "customer.subscription.deleted"]:
            items = obj.get("items", {}).get("data", [])
            # Check if items exist and contain plan and product
            if items and "plan" in items[0] and "product" in items[0]["plan"]:
                product_id = items[0]["plan"]["product"]
        # Handle invoice payment events
        elif event.get("type") in ["invoice.payment_succeeded", "invoice.payment_failed"]:
            lines = obj.get("lines", {}).get("data", [])
            if lines:
                plan = lines[0].get("plan")
                # Prefer plan.product if available
                if plan and "product" in plan:
                    product_id = plan["product"]
                else:
                    # Fallback to price.product if plan is missing
                    price = lines[0].get("price")
                    if price and "product" in price:
                        product_id = price["product"]

        # If product_id is missing, log and skip
        if product_id is None:
            logger.info("Product ID not found in webhook event, skipping")
            return False
        # If product_id does not match the expected product, skip
        if product_id != STRIPE_BLITZY_PRO_PRODUCT_ID:
            logger.info(f"Skipping webhook processing for product {product_id}")
            return False

        return True

    except Exception as e:
        logger.error(f"Error checking product ID in webhook: {e}")
        return False
