from typing import Any, Dict

import firebase_admin
import requests

from src.consts import FIREBASE_API_KEY
from src.error.errors import Too<PERSON>any<PERSON>ttemptsError

TOO_MANY_ATTEMPTS_TRY_LATER_MESSAGE = "TOO_MANY_ATTEMPTS_TRY_LATER"


def get_firebase_url():
    project_id = firebase_admin.get_app().project_id
    return f"https://{project_id}.firebaseapp.com"


def send_verification_email(id_token: str):
    """
    Send verification email to the user.
    :param id_token: Token used for sending email
    """
    url = f"https://identitytoolkit.googleapis.com/v1/accounts:sendOobCode?key={FIREBASE_API_KEY}"
    payload = {
        "requestType": "VERIFY_EMAIL",
        "idToken": id_token
    }
    headers = {
        "Content-Type": "application/json"
    }
    response = requests.post(url, json=payload, headers=headers)

    if response.status_code == 400:
        error_message = response.json().get("error", {}).get("message")
        if error_message and error_message == TOO_MANY_ATTEMPTS_TRY_LATER_MESSAGE:
            raise TooManyAttemptsError("Email has been already sent to the user. Please try again later.")
    response.raise_for_status()
    return response.json()


def sign_in_with_custom_token(custom_token) -> Dict[str, Any]:
    """
    Sign in firebase user with custom token
    :param custom_token:
    :return:
    """
    url = f"https://identitytoolkit.googleapis.com/v1/accounts:signInWithCustomToken?key={FIREBASE_API_KEY}"
    payload = {
        "token": custom_token,
        "returnSecureToken": True
    }
    response = requests.post(url, json=payload)
    response.raise_for_status()
    return response.json()
