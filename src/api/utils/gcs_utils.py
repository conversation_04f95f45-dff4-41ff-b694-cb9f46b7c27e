import traceback
from datetime import timed<PERSON><PERSON>
from typing import Optional

from blitzy_utils.consts import DEFAULT_NAME
from blitzy_utils.logger import logger
from google.cloud import storage
from google.cloud.storage import Blob

from src.consts import DOCUMENT_EXPIRY_TIME, GCS_BUCKET_NAME


def generate_signed_url(
        blob: Blob
):
    """
    Generate a signed URL for accessing a blob in Google Cloud Storage. The method
    allows users to generate a temporary public URL for downloading the specified
    blob object in a specific bucket with an expiration time and HTTP method as
    parameters.

    :param blob_name: Blob name with file path.
    :param storage_client: Optional. The storage client used to access Google
        Cloud Storage. Defaults to a new instance of `google.cloud.storage.Client`.
    :param company_id: Optional. We use it to generate bucket name. If not provided, we use DEFAULT_NAME from
        `blitzy_utils.consts`
    :return: Presigned URL.
    """
    # Generate the signed URL
    url = blob.generate_signed_url(
        version="v4",
        expiration=timedelta(minutes=DOCUMENT_EXPIRY_TIME),
        method="GET",
    )

    return url


def upload_to_gcs(file_obj, blob_name: str, content_type="text/markdown") -> dict:
    """Upload file or string obj to Google Cloud Storage"""
    try:
        storage_client = storage.Client()
        bucket = storage_client.bucket(GCS_BUCKET_NAME)
        blob = bucket.blob(blob_name)

        if isinstance(file_obj, (str, bytes)):
            blob.upload_from_string(file_obj, content_type=content_type)
        else:
            blob.upload_from_file(file_obj, content_type=content_type)

        return {
            "success": True,
            "message": "File uploaded successfully",
            "file_path": f"gs://{GCS_BUCKET_NAME}/{blob_name}"
        }

    except Exception as e:
        traceback.print_exc()
        return {
            "success": False,
            "message": f"Error uploading file: {str(e)}"
        }


def check_file_size(file_obj, max_size_mb: float) -> dict:
    """
    Validate file size.
    """
    try:
        # Convert MB to bytes
        max_size_bytes = max_size_mb * 1024 * 1024

        # Get file size
        file_obj.seek(0, 2)  # Seek to end of file
        size_bytes = file_obj.tell()  # Get current position (size)
        file_obj.seek(0)  # Reset to beginning

        # Convert to MB
        size_mb = size_bytes / (1024 * 1024)

        return {
            "success": True,
            "size_bytes": size_bytes,
            "size_mb": round(size_mb, 2),
            "within_limit": size_bytes <= max_size_bytes,
            "max_size_mb": max_size_mb
        }

    except Exception as e:
        traceback.print_exc()
        return {
            "success": False,
            "message": f"Error checking file size: {str(e)}",
            "size_bytes": 0,
            "size_mb": 0,
            "within_limit": False
        }
