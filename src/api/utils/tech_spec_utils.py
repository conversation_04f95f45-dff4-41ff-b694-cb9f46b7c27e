from datetime import datetime, timezone
from typing import Any, Dict, Optional

from blitzy_utils.common import (generate_input_prompt_document_path,
                                 generate_technical_spec_document_path)
from blitzy_utils.consts import (DEFAULT_NAME, PLATFORM_EVENTS_TOPIC,
                                 TECH_SPECIFICATION_NAME)
from blitzy_utils.enums import JobStatus, ProjectPhase, ProjectPhaseAction
from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import (BranchLock, BranchLockReason, CodeGeneration,
                                  PlatformMetrics, Project, ProjectRunType,
                                  ReportStatus, Status, Subscription,
                                  SubscriptionStatus, SubscriptionType,
                                  TechnicalSpec, TechSpecDocumentContext,
                                  TechSpecJobType)
from google.api_core import exceptions
from google.cloud import storage
from sqlalchemy.orm import Session

from src.api.models import PDFInProgressResponse
from src.api.utils.code_gen_utils import (
    create_project_run_for_code_gen,
    send_add_feature_generate_reverse_file_map_notification)
from src.api.utils.gcs_utils import generate_signed_url, upload_to_gcs
from src.api.utils.project_utils import IN_PROGRESS_JOB_STATUS
from src.blitzy.utils import publish_notification
from src.consts import (BLOB_NAME, GCS_BUCKET_NAME,
                        GENERATE_REPO_STRUCTURE_TOPIC,
                        PRO_USER_ALLOWED_JOB_RUNS,
                        PRO_USER_TRIAL_SUBMISSION_LIMIT, PROJECT_ID,
                        is_dev_env, publisher)
from src.error.errors import (InvalidSubscriptionError, JobInProgressError,
                              JobLimitExhaustedError, JobNotCompleteError,
                              RevertTechSpecError, SubscriptionNotFoundError)
from src.service.code_gen_service import get_code_gen_by_tech_spec_id
from src.service.github_project_repo_service import \
    get_target_github_project_repo_by_id
from src.service.platform_metrics_service import (get_platform_metrics,
                                                  update_platform_metrics)
from src.service.project_service import update_project_timeline
from src.service.subscription_service import (get_subscription_info_by_user_id,
                                              save_subscription)
from src.service.tech_spec_document_context_service import \
    get_tech_spec_document_context
from src.service.tech_spec_service import (
    get_all_tech_specs_by_project_id_order_by_created_at,
    update_tech_spec_pdf_report_status_by_project_id)


def generate_technical_spec_document_url(repo_url: str, tech_spec_id: str) -> str:
    """
    Generates technical specification document URL.
    :param repo_url: Project repo URL.
    :param tech_spec_id: Tech Spec ID
    :return: File path.
    """
    file_path = f"{BLOB_NAME}/{repo_url}/blitzy/documentation/{TECH_SPECIFICATION_NAME}_{tech_spec_id}.md"
    return file_path


def generate_deprecated_v2_technical_spec_document_url(project_info: Project, tech_spec_id: str) -> str:
    """
    Generates technical specification document URL.
    :param project_info: Project info object.
    :param tech_spec_id: Tech Spec ID
    :return: File path.
    """
    file_path = f"{BLOB_NAME}/{project_info.repo_url}/blitzy/documentation/{TECH_SPECIFICATION_NAME}.md"
    return file_path


def generate_deprecated_technical_spec_document_url(project_info: Project) -> str:
    """
    Generates depricated technical specification document URL.
    :param project_info: Project info object.
    :return: File path.
    """
    file_path = f"{BLOB_NAME}/{project_info.repo_url}/documentation/{TECH_SPECIFICATION_NAME}.md"
    return file_path


def get_back_prop_tech_spec_presigned_url(project_info, tech_spec: TechnicalSpec, company_id: Optional[str]):
    """
    Generates a pre-signed URL for accessing a back prop tech spec document stored in Google Cloud Storage (GCS).
    The method attempts to locate the file using the specified project information. If the file
    is not found under the primary path, it tries using a fallback deprecated path before raising
    an exception. A signed URL is generated for the file if found.
    :param project_info: Project Info.
    :param tech_spec: Use tech spec to generate presigned url.
    :param company_id: Company ID. (Optional)
    :return: A signed URL granting temporary access to the file in GCS.
    """
    storage_client = storage.Client()
    bucket = storage_client.bucket(GCS_BUCKET_NAME)

    file_path = generate_technical_spec_document_path(tech_spec.job_metadata, GCS_BUCKET_NAME)
    blob = bucket.blob(file_path)
    if not blob.exists():
        bucket = storage_client.bucket(GCS_BUCKET_NAME + f"-{company_id}")
        blob = bucket.blob(file_path)
    return generate_signed_url(blob)


def get_tech_spec_presigned_url(project_info, tech_spec_id: str, company_id: str):
    """
    Generates a pre-signed URL for accessing a forward prop tech spec document stored in Google Cloud Storage (GCS).
    The method attempts to locate the file using the specified project information. If the file
    is not found under the primary path, it tries using a fallback deprecated path before raising
    an exception. A signed URL is generated for the file if found.
    :param project_info: Project Info.
    :return: A signed URL granting temporary access to the file in GCS.
    """
    storage_client = storage.Client()
    bucket = storage_client.bucket(GCS_BUCKET_NAME)

    file_path1 = generate_technical_spec_document_url(project_info.repo_url, tech_spec_id)
    blob1 = bucket.blob(file_path1)
    try:
        if blob1.exists():
            return generate_signed_url(blob1)
    except exceptions.NotFound:
        logger.warning(f"File {file_path1} not found. Attempting to fetch using different path.")

    file_path2 = generate_deprecated_v2_technical_spec_document_url(project_info, tech_spec_id)
    blob2 = bucket.blob(file_path2)
    try:
        if blob2.exists():
            return generate_signed_url(blob2)
    except exceptions.NotFound:
        logger.warning(f"File {file_path2} not found. Attempting to fetch using different path.")

    file_path3 = generate_deprecated_technical_spec_document_url(project_info)
    blob3 = bucket.blob(file_path3)
    try:
        if blob3.exists():
            return generate_signed_url(blob3)
    except exceptions.NotFound:
        logger.warning(f"File {file_path3} not found.")
        raise

    bucket = storage_client.bucket(GCS_BUCKET_NAME + f"-{company_id}")
    blob4 = bucket.blob(file_path1)
    if blob4.exists():
        return generate_signed_url(blob4)
    else:
        logger.warning(f"File {file_path1} not found.")


def submit_code_generation(user_id: str, repo_url: str, repo_id: str, project_id: str, job_id: str,
                           code_gen: CodeGeneration, dest_repo_name: str, tech_spec: TechnicalSpec,
                           git_project_repo_id: str, company_id: str):
    """
    Submit code generation event to the topic.
    :param user_id: User ID.
    :param repo_url: Repo URL used for code generation.
    :param repo_id: Repo ID.
    :param project_id: Project ID.
    :param job_id: Job ID.
    :param code_gen: Code generation object.
    :param dest_repo_name: Dest repo name
    :param tech_spec: Technical Specification.
    :param git_project_repo_id: Github project repo id.
    :param company_id: Company ID.
    :return: Notification data.
    """

    notification_data = {
        "user_id": user_id,
        "repo_name": repo_url,
        "repo_id": repo_id,
        "project_id": project_id,
        "job_id": job_id,
        "code_gen_id": code_gen.id,
        "tech_spec_id": tech_spec.id,
        "dest_repo_name": dest_repo_name,
        "git_project_repo_id": git_project_repo_id,
        "company_id": company_id
    }
    publish_notification(publisher, notification_data, PROJECT_ID, GENERATE_REPO_STRUCTURE_TOPIC)
    return notification_data


def validate_subscription(user_info: Dict[str, Any], session: Optional[Session] = None):
    """
    Validate subscription details.

    :param user_info: A dictionary containing user-related information.
    :param session: Session if any.
    :return: None. The function performs validation checks and raises exceptions if conditions are not met.
    """
    subscription_info = get_subscription_info_by_user_id(user_info["id"], session)
    if not subscription_info:
        raise SubscriptionNotFoundError(f"Failed to find subscription info for user with ID {user_info['id']}")

    if subscription_info.plan_name == SubscriptionType.FREE:
        validate_free_user_subscription(user_info, subscription_info)
    elif subscription_info.plan_name == SubscriptionType.PRO:
        validate_pro_user_subscription(user_info, subscription_info, session)
    elif subscription_info.plan_name == SubscriptionType.ENTERPRISE:
        validate_enterprise_user_subscription(user_info, subscription_info)
    elif subscription_info.plan_name == SubscriptionType.TEAMS:
        validate_teams_user_subscription(user_info, subscription_info)
    else:
        raise InvalidSubscriptionError(f"Subscription type {subscription_info.plan_name.value} not supported.")


def validate_free_user_subscription(user_info: Dict[str, Any], subscription_info: Subscription,
                                    session: Optional[Session] = None):
    """
    Validate free user subscription details.
    :param user_info: A dictionary containing information about the user.
    :param subscription_info: An object of type Subscription that contains subscription details.
    :param session: Session if any.
    :return: Raises an InvalidSubscriptionError if a free user attempts to generate code.
    """
    raise InvalidSubscriptionError("Code generation is not supported for free user.")


def validate_pro_user_subscription(user_info: Dict[str, Any], subscription_info: Subscription,
                                   session: Optional[Session] = None):
    """
    Validate pro user subscription details.
    :param user_info: A dictionary containing information about the user.
    :param subscription_info: An object of type Subscription that contains subscription details.
    :param session: Session if any.
    :return: Raises an InvalidSubscriptionError if a free user attempts to generate code.
    """
    validate_pro_user_non_trial_subscription(user_info, subscription_info, session)


def validate_enterprise_user_subscription(user_info: Dict[str, Any], subscription_info: Subscription,
                                          session: Optional[Session] = None):
    """
    :param user_info: A dictionary containing information about the user.
    :param subscription_info: An object of type Subscription that contains subscription details.
    :param session: Session if any.
    :return: No restriction on enterprise user.
    """
    # Enterprise user doesn't have any restrictions yet.
    return True


def validate_teams_user_subscription(user_info: Dict[str, Any], subscription_info: Subscription,
                                     session: Optional[Session] = None):
    """
    :param user_info: A dictionary containing information about the user.
    :param subscription_info: An object of type Subscription that contains subscription details.
    :param session: Session if any.
    :return: No restriction on teams user.
    """
    # Teams user doesn't have any restrictions yet.
    return True


def validate_pro_user_trial_subscription(user_info: Dict[str, Any], subscription_info: Subscription,
                                         session: Optional[Session] = None):
    """
    Validate trial pro user subscription details.
    :param user_info: A dictionary containing information about the user.
    :param subscription_info: An instance of Subscription containing details about the user's subscription.
    :param session: Session if any.
    :return: Raises InvalidSubscriptionError in case of trial restrictions or when conditions are not met.
    """
    current_time = datetime.now(timezone.utc)

    if subscription_info.status == SubscriptionStatus.CANCELLED:
        # Don't allow if trial is cancelled.
        logger.warning(f"User {user_info['id']} cancelled trial. Code submission not allowed.")
        raise InvalidSubscriptionError("Code generations is not allowed if trial is cancelled.")

    if not subscription_info.trial_end_date > current_time > subscription_info.trial_start_date:
        # Validate date.
        logger.warning(
            f"For user {user_info['id']}. Trial start date is {subscription_info.trial_start_date} and end"
            f" date is {subscription_info.trial_end_date}")
        raise InvalidSubscriptionError("Trial expired. Cannot submit job.")

    # TODO: Temporary change, remove this once stripe integration bug is resolved.
    if subscription_info.remaining_runs is None:
        subscription_info.remaining_runs = PRO_USER_ALLOWED_JOB_RUNS

    if not subscription_info.remaining_runs > 0:
        # Validate if runs are available.
        logger.warning(f"For user {user_info['id']}, remaining runs are {subscription_info.remaining_runs}.")
        raise JobLimitExhaustedError("Cannot generate code. Job submission limit exceeded.")

    if not check_and_update_system_job_limit(session):
        # Validate against system-wide job submission limits.
        raise JobLimitExhaustedError("Cannot generate code. System-wide job submission limit exceeded.")

    subscription_info.remaining_runs -= 1
    save_subscription(subscription_info, session)


def validate_pro_user_non_trial_subscription(user_info: Dict[str, Any], subscription_info: Subscription,
                                             session: Optional[Session] = None):
    """
    Validate non-trial pro user subscription details.
    :param user_info: A dictionary containing information about the user.
    :param subscription_info: An instance of Subscription containing details about the user's subscription.
    :param session: Session if any.
    :return: Raises InvalidSubscriptionError in case of trial restrictions or when conditions are not met.
    """
    # Skip this check if we are in dev mode.
    if is_dev_env():
        return

    current_time = datetime.now(timezone.utc)
    if not subscription_info.current_period_start_date < current_time < subscription_info.current_period_end_date:
        # Validate date.
        logger.warning(
            f"For user {user_info['id']}. Plan start date is {subscription_info.current_period_start_date} and end"
            f" date is {subscription_info.current_period_end_date}")
        raise InvalidSubscriptionError("Subscription expired. Cannot submit job.")

    # TODO: Prevent this issue
    if subscription_info.remaining_runs is None:
        subscription_info.remaining_runs = PRO_USER_ALLOWED_JOB_RUNS

    # if not subscription_info.remaining_runs > 0:
    #     # Validate if runs are available.
    #     raise JobLimitExhaustedError("Cannot generate code. Job submission exceeds allowed runs.")
    #
    # subscription_info.remaining_runs -= 1
    save_subscription(subscription_info, session)


def check_and_update_system_job_limit(session: Optional[Session] = None) -> bool:
    """
    Validates and updates the system-wide limit for trial user code submissions.

    This function checks whether the total number of code submissions by trial users
    has exceeded the allowed global limit (`PRO_USER_TRIAL_SUBMISSION_LIMIT`). If the
    limit has not been reached, it increments the count and updates the platform metrics
    accordingly.

    :param session: Session if any.
    :return: Whether limit has been exceeded.
    """
    platform_metrics = get_platform_metrics(session)
    if platform_metrics.trial_user_code_submissions >= PRO_USER_TRIAL_SUBMISSION_LIMIT:
        return False

    platform_metrics.trial_user_code_submissions += 1

    update_payload = {
        PlatformMetrics.trial_user_code_submissions: platform_metrics.trial_user_code_submissions
    }
    update_platform_metrics(platform_metrics.id, update_payload, session)
    return True


def trigger_tech_spec_pdf_generation(project_info: Project, tech_spec: TechnicalSpec):
    """
    Triggers the generation of a PDF for a technical specification of a project. This process involves sending
    a notification to initiate the PDF generation, updating the PDF generation status for the project in the
    database, and returning a response indicating the progress status.

    :param project_info: Information about the project for which the PDF generation is being triggered.
    :param tech_spec: Technical specification details required for the PDF generation process.
    :return: A response object indicating that the PDF generation process is in progress.
    """
    send_generate_pdf_notification(project_info, tech_spec)

    logger.info(f"Setting PDF generation status to in progress for project {project_info.id} in database")
    set_pdf_generation_in_progress(project_info.id)

    response = PDFInProgressResponse()
    response.status = ReportStatus.IN_PROGRESS.value
    return response


def set_pdf_generation_in_progress(project_id: str):
    """
    Sets the status of PDF generation for a specific project to "IN_PROGRESS".

    This function updates the status of the technical specification PDF report
    generation for the project identified by its unique project ID. The status is
    stored in the database, and the changes are committed during the session.

    :param project_id: The unique identifier for the project whose PDF generation
        status needs to be updated.
    :return: None
    """
    with get_db_session() as session:
        update_tech_spec_pdf_report_status_by_project_id(project_id, ReportStatus.IN_PROGRESS, session)
        update_project_timeline(project_id, session)
        session.commit()


def send_generate_pdf_notification(project_info: Project, tech_spec: TechnicalSpec):
    """
    Sends a notification to indicate the generation of a PDF within a project. This function processes
    the provided project information and job identifier to construct a notification payload related
    to a specific phase and action, marking the status as 'in progress'. The constructed notification
    is then published to the appropriate platform events topic.

    :param project_info: Contains detailed information about the project including its identifier.
    :param tech_spec: Technical specification details required for the PDF generation process.
    :return: None
    """

    github_repo = get_target_github_project_repo_by_id(project_info.id)
    if not github_repo:
        logger.warning(f"Error fetching github repo for project {project_info.id}")

    notification_data = {
        "projectId": project_info.id,
        # "jobId": job_id,
        "repo_id": github_repo.repo_id if github_repo else None,
        "phase": ProjectPhase.TECHNICAL_SPECIFICATION.value,
        "action": ProjectPhaseAction.GENERATE_PDF.value,
        "status": JobStatus.IN_PROGRESS.value,
        "tech_spec_id": tech_spec.id
    }

    publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)


def generate_tech_spec_pdf_presigned_url(project_info: Project, tech_spec: TechnicalSpec, company_id: str) -> str:
    """
    Generates a pre-signed URL for downloading a PDF document containing
    the technical specifications of a given project.

    A storage client is used to create the signed URL, which grants
    temporary access to the document. The URL is generated specifically
    for the PDF file and is accessible through the returned value.

    :param project_info: Information about the project for which the
        technical specification PDF needs a pre-signed URL.
    :param tech_spec: Technical specification object.
    :param company_id: Company identifier used for generating the signed URL.
    :return: A pre-signed URL allowing temporary access to the technical
        specification PDF document.
    """
    storage_client = storage.Client()
    if tech_spec.job_type == TechSpecJobType.NEW_PRODUCT:
        if tech_spec.job_metadata:
            repo_name = tech_spec.job_metadata.get("repo_name")
            file_path = generate_technical_spec_document_url(repo_name, tech_spec.id)
        else:
            file_path = generate_technical_spec_document_url(project_info.repo_url, tech_spec.id)
    else:
        file_path = generate_technical_spec_document_path(tech_spec.job_metadata, GCS_BUCKET_NAME)
    file_path = file_path.replace(".md", ".pdf")
    bucket = storage_client.bucket(GCS_BUCKET_NAME)
    blob = bucket.blob(file_path)
    if not blob.exists():
        bucket = storage_client.bucket(GCS_BUCKET_NAME + f"-{company_id}")
        blob = bucket.blob(file_path)

    return generate_signed_url(blob)


def generate_technical_spec_document_pdf_url(project_info: Project) -> str:
    """
    Generates technical specification document pdf URL.
    :param project_info: Project info object.
    :return: File path.
    """
    file_path = (f"{BLOB_NAME}/{project_info.repo_url}/blitzy/documentation"
                 f"/Blitzy - {project_info.name} - {TECH_SPECIFICATION_NAME}.pdf")
    return file_path


def upload_tech_spec_prompt_to_gcs(project_info: Project, notification_payload: Dict[str, Any], prompt: str):
    file_url = generate_input_prompt_document_path(notification_payload)
    result = upload_to_gcs(prompt, file_url)
    if not result["success"]:
        raise Exception(f"Failed to upload prompt file for project {project_info.id} with error {result['message']}")


def validate_tech_spec_submit(project_info: Project, tech_spec: TechnicalSpec):
    project_id = project_info.id
    # Check if code generation job is submitted. If it's submitted don't accept the request.
    if tech_spec.status == Status.SUBMITTED:
        raise JobInProgressError(f"Technical specifications are already submitted for the project {project_id}")

    # Check if technical specification job is in progress. If it's in progress raise an exception.
    if tech_spec.status in IN_PROGRESS_JOB_STATUS and tech_spec.status != Status.TODO:
        raise JobNotCompleteError(f"Technical Specification job for project {project_id}"
                                  f" has not completed yet. Cannot submit file.")


def handle_new_product_tech_spec_submit(user_info: Dict[str, Any], project_info: Project, tech_spec: TechnicalSpec,
                                        session: Optional[Session] = None):
    """
    Handles the submission of a new product's technical specification. The function manages the
    process of saving the submitted technical specification, creating a corresponding project
    run for code generation, and initiating the code generation process for the specified project.

    :param user_info: A dictionary containing relevant user information such as
                      user ID and other associated details.
    :param project_info: An instance of `Project` representing information related
                         to the project for which the technical specification is being
                         submitted.
    :param tech_spec: An instance of `TechnicalSpec` that encapsulates the technical
                      specifications for the new product being submitted.
    :param session: (Optional) A database session object that can be provided for
                    explicitly managing transactions.
    """
    code_gen = save_code_gen_for_tech_spec(tech_spec, project_info, session)
    code_gen_job = create_project_run_for_code_gen(project_info.id, ProjectRunType.NEW_PRODUCT, code_gen.id, session)
    target_github_repo_info = get_target_github_project_repo_by_id(project_info.id, session)
    # company_id is saved with user_info and from company table. When user belongs to a company, it has this field.
    # when it is empty, it is set to default value. when submitting the tech spec, it is needed
    # for code structure generation.
    company_id = user_info["company_id"] if user_info.get("company_id") else DEFAULT_NAME
    notification_data = submit_code_generation(user_info["id"], project_info.repo_url, target_github_repo_info.repo_id,
                                               project_info.id, code_gen_job.id, code_gen,
                                               target_github_repo_info.repo_name, tech_spec, target_github_repo_info.id,
                                               company_id)
    code_gen.job_metadata = notification_data


def handle_common_tech_spec_submit(user_info: Dict[str, Any], project_info: Project, tech_spec: TechnicalSpec,
                                   session: Session, project_run_type: ProjectRunType, backprop_command: str):
    """
    Common handler for tech spec submissions that creates code generation, project run, and sends notifications.

    :param user_info: A dictionary containing user-related information.
    :param project_info: An instance of the Project class containing details about the target project.
    :param tech_spec: An instance of TechnicalSpec that provides the technical specification.
    :param session: An instance of the database session to persist data across operations.
    :param project_run_type: The ProjectRunType enum value for the specific job type.
    :param backprop_command: The BackpropCommand string value for the specific job type.
    :return: None
    """
    code_gen = save_code_gen_for_tech_spec(tech_spec, project_info, session)
    code_gen_job = create_project_run_for_code_gen(project_info.id, project_run_type, code_gen.id, session)

    send_add_feature_generate_reverse_file_map_notification(project_info, code_gen_job, user_info, code_gen,
                                                            tech_spec, True, backprop_command, False, session)


def save_code_gen_for_tech_spec(tech_spec: TechnicalSpec, project_info: Project, session: Session) -> CodeGeneration:
    """
    Saves a new code generation task associated with a given technical specification
    and project into the database. The code generation task is initialized with a
    queued status and links to the provided technical specification and project
    identifiers. The function persists the task to the database and flushes the
    session for immediate effect.

    :param tech_spec: The technical specification object for which the code
        generation task is being created.
    :param project_info: The project object linked to the code generation task.
    :param session: The database session to use for persisting the code generation
        task.
    :return: The created and persisted code generation task instance.
    """
    code_gen = CodeGeneration(
        status=Status.QUEUED,
        tech_spec_id=tech_spec.id,
        project_id=project_info.id,
    )
    session.add(code_gen)
    session.flush()
    return code_gen


def insert_tech_spec_document_context_record(notification_payload: Dict[str, Any], session: Optional[Session] = None):
    """
    Inserts a tech spec document context record if it does not already exist.

    :param notification_payload: Payload containing details necessary to create
        the tech spec document context record.
    :param session: Optional database session instance. If not provided, a new
        session will be created.
    :return: None
    """
    project_id = notification_payload["project_id"]
    logger.info(f"Attempting to create tech spec document context record for project {project_id}")

    repo_name = notification_payload["repo_name"]
    repo_id = notification_payload["repo_id"]
    branch_id = notification_payload["branch_id"]
    branch_name = notification_payload["branch_name"]
    head_commit_hash = notification_payload["head_commit_hash"]

    with get_db_session(session) as session:
        current_tech_spec_document_context = get_tech_spec_document_context(repo_name, repo_id, branch_name, branch_id,
                                                                            head_commit_hash, session=session)
        if current_tech_spec_document_context:
            logger.info(f"Tech spec document context record already exists for project {project_id}, skipping")
            return
        logger.info(f"Creating tech spec document context record for project {project_id}")
        tech_spec_document_context = TechSpecDocumentContext(
            project_id=project_id,
            repo_name=repo_name,
            repo_id=repo_id,
            branch_name=branch_name,
            branch_id=branch_id,
            head_commit_hash=head_commit_hash,
            context_metadata=notification_payload,
        )
        session.add(tech_spec_document_context)
        session.flush()
        logger.info(f"Tech spec document context record created for project {project_id}")


def create_branch_lock(branch_pattern_id: str, project: Project, branch_lock_reason: BranchLockReason,
                       session: Session):
    """
    Create a branch lock for a given branch pattern in a project.

    :param branch_pattern_id: Identifier of the branch pattern to lock.
    :type branch_pattern_id: str
    :param project: The project associated with the branch lock.
    :type project: Project
    :param branch_lock_reason: The reason for locking the branch.
    :type branch_lock_reason: BranchLockReason
    :param session: Database session to persist the branch lock.
    :type session: Session
    :return: The created branch lock instance.
    """
    branch_lock = BranchLock(
        branch_pattern_id=branch_pattern_id,
        project_id=project.id,
        lock_reason=branch_lock_reason,
        is_active=True,
        locked_at=datetime.utcnow()
    )

    session.add(branch_lock)


def revert_tech_spec_by_project_id(project_info: Project):
    """
    Reverts the latest technical specification for a project if certain conditions are met.

    Following conditions are checked for reverting the latest technical specification:
    - Whether tech spec job is in Progress.
    - Whether tech spec job is of type EXISTING_PRODUCT.
    - Whether tech spec is associated with code generation.

    :param project_info: The project whose latest technical specification needs to be reverted.
    :type project_info: Project
    :raises RevertTechSpecError: If the latest technical specification is not in DONE state.
    :raises RevertTechSpecError: If the job type of the latest technical specification is EXISTING_PRODUCT.
    :raises RevertTechSpecError: If the latest technical specification is associated with code generation.
    :return: None
    """
    with get_db_session() as session:
        tech_spec_list = get_all_tech_specs_by_project_id_order_by_created_at(project_info.id, session)

        if not tech_spec_list:
            raise RevertTechSpecError(f"No technical specifications found for project ID {project_info.id}.")

        latest_tech_spec = tech_spec_list[0]
        if latest_tech_spec.status != Status.DONE:
            raise RevertTechSpecError("Latest Tech Spec is not DONE. Cannot revert Tech Spec.")

        if latest_tech_spec.job_type == TechSpecJobType.SYNC_TECH_SPEC:
            raise RevertTechSpecError("Cannot revert sync tech spec.")

        if latest_tech_spec.job_type == TechSpecJobType.EXISTING_PRODUCT:
            raise RevertTechSpecError("Cannot revert tech spec beyond this.")

        code_gen = get_code_gen_by_tech_spec_id(latest_tech_spec.id, session)
        if code_gen:
            raise RevertTechSpecError("Current tech spec is used for code generation. Cannot revert tech spec.")

        latest_tech_spec.soft_delete()
        update_project_timeline(project_info.id, session)
        session.commit()
