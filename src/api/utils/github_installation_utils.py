from blitzy_utils.service_client import ServiceClient

from src.api.routes.github_installation import (
    get_only_github_repos_from_github_handler,
    get_organizations_from_github_handler,
    get_repos_from_github_handler_with_scv_inference)
from src.error.errors import check_service_error


def get_github_org_from_github_handler(user_id: str):
    response = get_organizations_from_github_handler(user_id)
    org_list = response["results"]
    org_list_dict = {}
    for org in org_list:
        org_list_dict[org["name"]] = org
    return org_list_dict


def get_github_repo_from_github_handler(user_id: str, account_name: str, repo_inference_id: str):
    response = get_only_github_repos_from_github_handler(user_id, account_name, repo_inference_id)
    repo_list = response["results"]
    repo_list_dict = {}
    for repo in repo_list:
        repo_list_dict[repo["id"]] = repo
    return repo_list_dict

def get_github_repo_from_github_handler_with_scv_inference(user_id: str, account_name: str, repo_id_inference: str):
    response = get_repos_from_github_handler_with_scv_inference(user_id, account_name, repo_id_inference)
    repo_list = response["results"]
    repo_list_dict = {}
    for repo in repo_list:
        repo_list_dict[repo["id"]] = repo
    return repo_list_dict


def create_github_repo_using_github_handler(user_id, org_name, repo_name, azure_project_id, azure_org_id, private=True):
    payload = {
        "repoName": repo_name,
        "private": private,
        "userId": user_id,
        "azureProjectId": azure_project_id,
        "azureOrgId": azure_org_id
    }

    with ServiceClient() as client:
        endpoint = f"/v1/github/operations/organizations/{org_name}/repositories"
        response = client.post("github", endpoint, json=payload, timeout=180)
        check_service_error(response)
        return response.json()
