from src.gcp.secrets_handler import SecretManagerOperations


def create_or_update_secret(user_id: str, access_token: str, code: str, installation_id: str, setup_action: str):
    secret_data = {
        "access_token": access_token,
        "code": code,
        "installation_id": installation_id,
        "setup_action": setup_action
    }

    secret_manager = SecretManagerOperations()
    response = secret_manager.create_or_update_user_secret(
        user_id=user_id,
        secret_data=secret_data
    )
    return response


def create_or_update_github_secret(access_token: str, code: str, installation_id: str, setup_action: str):
    secret_data = {
        "access_token": access_token,
        "code": code,
        "installation_id": installation_id,
        "setup_action": setup_action
    }

    secret_manager = SecretManagerOperations()
    response = secret_manager.create_or_update_github_secret(
        installation_id=installation_id,
        secret_data=secret_data
    )
    return response


def create_or_update_azure_secret(
        access_token: str, refresh_token: str,
        tenant_id: str, used_redirect_uri: str,
        used_scope: str
):
    # Validate all required fields are not null or empty
    required_fields = {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "tenant_id": tenant_id,
        "used_redirect_uri": used_redirect_uri,
        "used_scope": used_scope
    }
    
    # Check for null or empty values
    for field_name, field_value in required_fields.items():
        if field_value is None or (isinstance(field_value, str) and field_value.strip() == ""):
            raise ValueError(f"Field '{field_name}' cannot be null or empty")
    
    secret_data = {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "tenant_id": tenant_id,
        "used_redirect_uri": used_redirect_uri,
        "used_scope": used_scope
    }

    secret_manager = SecretManagerOperations()
    response = secret_manager.create_or_update_azure_secret(
        tenant_id=tenant_id,
        secret_data=secret_data
    )
    return response
