from typing import Dict, Optional

from requests import post as post_request

from src.consts import FIREBASE_API_KEY


def firebase_sign_in_with_email_password(email: str, password: str) -> Optional[Dict]:
    """
    Sign in user using email and password.
    :param email: Email ID.
    :param password: Password.
    :return: Payload received from firebase.
    """
    # Call Firebase Auth REST API
    url = f"https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key={FIREBASE_API_KEY}"
    payload = {
        "email": email,
        "password": password,
        "returnSecureToken": True
    }
    response = post_request(url, json=payload)
    response.raise_for_status()
    auth_data = response.json()
    return auth_data
