import traceback
from datetime import datetime
from typing import Optional

from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import (AccessRole, AccessType, GithubInstallation,
                                  GitHubInstallationAccess,
                                  GithubInstallationStatus,
                                  GithubInstallationType)

from src.service.github_installation_access_service import \
    delete_by_integration_id
from src.service.github_integration_service import (
    get_github_integration_by_github_user_id,
    get_github_integration_by_installation_id)
from src.service.pending_installation_requests_service import \
    handle_pending_installation_record_by_github_user_id

installation_type_map = {
    "Organization": GithubInstallationType.ORGANIZATION,
    "User": GithubInstallationType.USER
}


def handle_installation_event(payload: dict):
    """Handle the installation event."""
    logger.info(f"Handling installation with payload {payload}")
    logger.info(f"Handling event for installation {payload["installation"]["id"]}")
    action = payload.get("action")

    if action == "deleted":
        handle_github_app_delete_event(payload)
    elif action == "created":
        handle_github_app_install_event(payload)
    elif action == "suspend":
        handle_github_app_suspend_event(payload)
    elif action == "unsuspend":
        handle_github_app_unsuspend_event(payload)
    else:
        logger.warning(f"No handler for action {action}")

    logger.info(f"Finished handling github installation event with action {action}")


def handle_github_app_delete_event(payload: dict):
    """Handle the app_deleted event."""
    installation_id = str(payload["installation"]["id"])
    logger.info(f"Handling app_deleted event for installation ID {installation_id}")

    with get_db_session() as session:
        try:
            github_installation = get_github_integration_by_installation_id(installation_id, session)
            if not github_installation:
                logger.info(f"No installation information found for installation ID {installation_id}."
                            " Creating delete github app record.")
                github_installation = get_delete_github_app_integration_model(github_installation, payload)
                session.add(github_installation)
                session.commit()
                return

            logger.info(f"Found installation information for installation ID {installation_id}."
                        " Updating existing record.")
            github_installation = get_delete_github_app_integration_model(github_installation, payload)
            integration_id = github_installation.id

            logger.info(f"Deleting github installation access records with id {integration_id}")
            deleted_records = delete_by_integration_id(integration_id, session)
            logger.info(f"Deleted {deleted_records} github installation access records with id {integration_id}")
            session.commit()
        except Exception as e:
            traceback.print_exc()
            logger.error("Failed to handle app_deleted event", error=str(e))
            session.rollback()


def handle_github_app_install_event(payload: dict):
    """Handle the Github app install event."""
    installation_id = str(payload["installation"]["id"])
    requester_github_user_id = ""
    logger.info(f"Handling app install event for installation ID {installation_id}")

    if payload.get("requester") and payload["requester"].get("id"):
        requester_github_user_id = str(payload["requester"]["id"])

    with get_db_session() as session:
        try:
            # Update secret for pending user record and then delete pending installation record if any.
            deleted_count = handle_pending_installation_record_by_github_user_id(requester_github_user_id,
                                                                                 installation_id, session)
            logger.info(
                f"Deleted {deleted_count} pending installation records for github user id {requester_github_user_id}")

            github_installation = get_github_integration_by_github_user_id(requester_github_user_id, session)
            if not github_installation and requester_github_user_id:
                logger.info(f"Github installation record for installation ID {installation_id} not found."
                            " Checking if there's any pending record available for github user id"
                            f" {requester_github_user_id}")
                github_installation = get_github_integration_by_installation_id(installation_id, session)

            if not github_installation:
                logger.info(f"No installation information found for installation ID {installation_id}."
                            f" Creating install github app record.")
                github_installation = get_install_github_app_integration_model(github_installation, payload)
                session.add(github_installation)
                session.commit()
                return

            get_install_github_app_integration_model(github_installation, payload)
            session.commit()
        except Exception as e:
            traceback.print_exc()
            logger.error("Failed to handle app_install event", error=str(e))
            session.rollback()


def generate_github_installation_access_user_record(
        github_installation: GithubInstallation) -> GitHubInstallationAccess:
    """
    Generate github installation access record for a user from the GitHub installation record.
    This is intended to be used for the user who installed the app, so user is always an owner and
    access level set to USER

    :param github_installation:
    :return:
    """
    return GitHubInstallationAccess(
        integration_id=github_installation.installation_id,
        access_type=AccessType.USER,
        entity_id=github_installation.user_id,
        is_owner=True,
        role=AccessRole.OWNER,
    )


def handle_github_app_suspend_event(payload: dict):
    """Handle the app_suspend event."""
    installation_id = str(payload["installation"]["id"])
    logger.info(f"Handling app_suspend event for installation ID {installation_id}")

    with get_db_session() as session:
        try:
            github_installation = get_github_integration_by_installation_id(installation_id, session)
            if not github_installation:
                logger.info(f"No installation information found for installation ID {installation_id}."
                            " Creating suspend github app record.")
                github_installation = get_suspend_github_app_integration_model(github_installation, payload)
                session.add(github_installation)
                session.commit()
                return

            logger.info(f"Found github installation information for installation ID {installation_id}."
                        " Updating existing record.")
            github_installation = get_suspend_github_app_integration_model(github_installation, payload)
            session.commit()
        except Exception as e:
            traceback.print_exc()
            logger.error("Failed to handle app_deleted event", error=str(e))
            session.rollback()


def handle_github_app_unsuspend_event(payload: dict):
    """Handle the app_suspend event."""
    installation_id = str(payload["installation"]["id"])
    logger.info(f"Handling app_suspend event for installation ID {installation_id}")

    with get_db_session() as session:
        try:
            github_installation = get_github_integration_by_installation_id(installation_id, session)
            if not github_installation:
                logger.info(f"No installation information found for installation ID {installation_id}."
                            " Creating unsuspend github app record.")
                github_installation = get_unsuspend_github_app_integration_model(github_installation, payload)
                session.add(github_installation)
                session.commit()
                return

            logger.info(f"Found github installation information for installation ID {installation_id}."
                        " Updating existing record.")
            github_installation = get_unsuspend_github_app_integration_model(github_installation, payload)
            session.commit()
        except Exception as e:
            traceback.print_exc()
            logger.error("Failed to handle app_deleted event", error=str(e))
            session.rollback()


def get_delete_github_app_integration_model(github_integration: GithubInstallation, payload: dict):
    if not github_integration:
        github_integration = generate_github_app_integration_model(payload)

    github_integration.status = GithubInstallationStatus.UNINSTALLED
    github_integration.uninstalled_at = datetime.utcnow()
    if github_integration.installation_metadata:
        github_integration.installation_metadata.update({"sender": payload["sender"]})
    else:
        github_integration.installation_metadata = {"sender": payload["sender"]}
    return github_integration


def get_suspend_github_app_integration_model(github_integration: GithubInstallation, payload: dict):
    if not github_integration:
        github_integration = generate_github_app_integration_model(payload)

    github_integration.status = GithubInstallationStatus.SUSPENDED
    github_integration.installation_metadata.update({"sender": payload["sender"]})
    return github_integration


def get_unsuspend_github_app_integration_model(github_integration: GithubInstallation, payload: dict):
    if not github_integration:
        github_integration = generate_github_app_integration_model(payload)

    github_integration.status = GithubInstallationStatus.ACTIVE
    github_integration.installation_metadata.update({"sender": payload["sender"]})
    return github_integration


def get_install_github_app_integration_model(github_integration: GithubInstallation, payload: dict):
    if not github_integration:
        github_integration = generate_github_app_integration_model(payload)

    github_integration.status = GithubInstallationStatus.ACTIVE
    github_integration.installed_at = datetime.utcnow()
    github_integration.approved_at = datetime.utcnow()
    github_integration.approved_by = payload["sender"]["id"]
    github_integration.requires_approval = False

    if not github_integration.installation_id:
        github_integration.installation_id = str(payload["installation"]["id"])

    account = payload["installation"]["account"]
    if not github_integration.installation_type:
        github_integration.installation_type = installation_type_map[account["type"]]

    if not github_integration.target_name:
        github_integration.target_name = str(account["login"])

    if not github_integration.target_id:
        github_integration.target_id = str(account["id"])

    return github_integration


def generate_github_app_integration_model(payload: dict, github_integration: Optional[GithubInstallation] = None):
    installation_id = payload["installation"]["id"]
    account = payload["installation"]["account"]
    target_id = str(account["id"])
    target_name = str(account["login"])
    sender = payload["sender"]
    installation_type = account["type"]

    github_integration = GithubInstallation()
    github_integration.installation_id = installation_id
    github_integration.target_id = target_id
    github_integration.target_name = target_name
    github_integration.installation_type = installation_type_map[installation_type]
    github_integration.requires_approval = False
    github_integration.installation_metadata = {
        "sender": sender,
    }
    return github_integration
