from datetime import datetime

from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import (GithubInstallation, GithubInstallationStatus,
                                  GitHubRepository, GitHubRepositoryStatus)


def handle_repository_event(payload: dict):
    """Handle the repository events."""
    logger.debug(f"Handling installation with payload {payload}")

    action = payload.get("action")
    logger.info(f"Handling repository event for action: f{payload['action']}")
    if action == "created":
        handle_repository_created_event(payload)
    elif action == "renamed":
        handle_repository_rename_event(payload)
    else:
        logger.warning(f"No handler for action {action}")

    logger.info(f"Finished handling github repository event with action {action}")


def handle_repository_created_event(payload: dict):
    """Handle the repository created event."""
    repo = payload["repository"]
    repo_name = repo["name"]
    owner = repo["owner"]
    owner_id = str(owner["id"])

    with get_db_session() as session:
        installation: GithubInstallation = session.query(GithubInstallation).filter(
            GithubInstallation.target_id == owner_id,
            GithubInstallation.status == GithubInstallationStatus.ACTIVE).first()

        if not installation:
            logger.warning(f"No installation found for owner id {owner_id}, skipping.")
            return

        logger.info(
            f"Found installation for owner id {owner_id}. It's associated with github user id {installation.user_id}")
        logger.info("Attempting to find pending repository create record in github_repositories table",
                    repo_name=repo_name, user_id=installation.user_id)

        github_repository: GitHubRepository = session.query(GitHubRepository).filter(
            GitHubRepository.repo_name == repo_name, GitHubRepository.user_id == installation.user_id).first()

        if not github_repository:
            logger.info("No pending repository create record found. Skipping.")
            return

        logger.info("Pending repository create record found. Updating record with required information.")
        github_repository.repo_id = repo["id"]
        github_repository.repo_full_name = repo["full_name"]
        github_repository.status = GitHubRepositoryStatus.ACTIVE
        github_repository.onboarded_at = datetime.utcnow()
        github_repository.repository_metadata = repo
        session.commit()
        logger.info("Repository create event handled successfully.")


def handle_repository_rename_event(payload: dict):
    """Handle the repository rename event."""
    repo = payload["repository"]
    repo_name = repo["name"]
    repo_id = str(repo["id"])
    changes = payload['changes']['repository']

    with get_db_session() as session:
        github_repository: GitHubRepository = session.query(GitHubRepository).filter(
            GitHubRepository.repo_id == repo_id).first()

        if not github_repository:
            logger.warning(f"No repository found for repo id {repo_id}, skipping.")
            return

        logger.info(f"Found repository for repo id {repo_id}. Attempting to update record with required information.")

        old_names = github_repository.previous_names or {}

        github_repository.repo_name = repo_name
        github_repository.repo_full_name = repo["full_name"]
        github_repository.previous_names = {
            **old_names,
            datetime.utcnow().isoformat(): changes['name']['from']
        }
        session.commit()
        logger.info("Repository rename event handled successfully.")
