from datetime import datetime
from typing import Optional

from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import (BlitzyCommit, BlitzyCommitPRAction,
                                  BlitzyCommitStatus, GitHubProjectRepo)
from sqlalchemy.orm import Session

from github import Github, GithubIntegration
from src.consts import GITHUB_APP_ID, GITHUB_PRIVATE_KEY
from src.service.blitzy_commit_service import \
    get_blitzy_commit_by_repo_id_and_pr_number
from src.service.code_gen_service import get_code_gen_by_id
from src.service.github_integration_service import \
    get_github_installation_by_repo_id
from src.service.github_project_repo_service import (
    get_github_project_repo_by_repo_and_branch,
    update_github_project_repo_by_id)
from src.service.tech_spec_service import get_tech_spec_by_id


def handle_pr_event(payload: dict) -> None:
    """
    Handles a pull request event.

    :param payload: The payload dictionary containing details of the PR event.
    :return: None
    """
    logger.info(f"Handling PR event: {payload}")
    action = payload["action"]

    if action == "closed":
        handle_pr_event_closed(payload)
    else:
        logger.warning(f"No handler for action {action}")


def handle_pr_event_closed(payload: dict):
    """
    Handles a closed Pull Request (PR) event.

    :param payload: The event payload containing PR and repository details.
    :type payload: dict
    """
    logger.info("Handling PR event closed")

    pr_number = payload["pull_request"]["number"]
    repo_id = str(payload["repository"]["id"])
    merged = payload["pull_request"]["merged"]

    base_branch = payload["pull_request"]["base"]["ref"]
    base_repo_full_name = payload["pull_request"]["base"]["repo"]["full_name"]

    with get_db_session() as session:
        blitzy_commit = get_blitzy_commit_by_repo_id_and_pr_number(repo_id, pr_number, session)
        if not blitzy_commit:
            logger.warning(f"No blitzy commit found for repo id {repo_id} and pr number {pr_number}. Skipping.")
            return

        if merged:
            blitzy_commit.status = BlitzyCommitStatus.MERGED
            blitzy_commit.pr_action = BlitzyCommitPRAction.CLOSED
        else:
            blitzy_commit.status = BlitzyCommitStatus.CLOSED
            blitzy_commit.pr_action = BlitzyCommitPRAction.CLOSED
            # Rollback i.e. delete tech-spec if PR is closed.
            rollback_tech_spec_on_close(blitzy_commit, session)

        blitzy_commit.resolved_at = datetime.utcnow()

        blitzy_commit.commit_metadata = {
            **(blitzy_commit.commit_metadata or {}),
            "pr_closed_info": payload
        }

        if merged:
            logger.info("PR is merged to the base branch. Updating latest commit information. ")
            # Update project github repo records. As once PR by blitzy is merged we don't need
            # to generate new tech spec. For this to happen we need to record latest commit ID as part of the database.
            installation = get_github_installation_by_repo_id(repo_id, session)

            if not installation:
                logger.warning(f"No installation found for repo id {repo_id}. Skipping.")
                return

            logger.info(f"Found installation for repo id {repo_id}. Attempting to update latest commit information.")
            latest_commit = get_latest_commit(
                installation_id=installation.installation_id,
                repo_full_name=base_repo_full_name,
                branch_name=base_branch
            )

            if not latest_commit:
                logger.warning(f"No latest commit found for repo id {repo_id}. Skipping.")
                return

            logger.info(
                f"Found latest commit for repo id {repo_id}. Attempting to update record with required information.")
            project_repo = get_github_project_repo_by_repo_and_branch(repo_id, base_branch, session)

            if not project_repo:
                logger.warning(f"No project repo found for repo id {repo_id} and branch {base_branch}. Skipping.")
                return

            latest_commit_hash = latest_commit["sha"]

            logger.info(f"Commit hashes before PR merge: previous={project_repo.previous_commit_hash}, "
                        f"current={project_repo.current_commit_hash}, "
                        f"latest from GitHub={latest_commit_hash}")

            update_payload = {
                GitHubProjectRepo.needs_scan: True,
                GitHubProjectRepo.github_current_commit_hash: latest_commit_hash,
                GitHubProjectRepo.updated_at: datetime.utcnow()
            }

            if latest_commit_hash != project_repo.previous_commit_hash:
                logger.info(
                    f"Updating previous_commit_hash from {project_repo.previous_commit_hash} to"
                    f" {project_repo.current_commit_hash}")
                update_payload[GitHubProjectRepo.previous_commit_hash] = project_repo.current_commit_hash
            else:
                logger.warning(f"Not updating previous_commit_hash because latest_commit_hash"
                               f" ({latest_commit_hash}) equals previous_commit_hash"
                               f" ({project_repo.previous_commit_hash})")

            update_github_project_repo_by_id(project_repo.id, update_payload, session=session)

            logger.info("Updated github project repo record with latest commit hash and needs_scan flag to False.")

        session.commit()
        logger.info("PR event closed handled successfully.")


def get_latest_commit(installation_id: str, repo_full_name: str, branch_name: str):
    """
    Fetches the latest commit details from a specified branch of a repository.

    :param installation_id: Installation ID for authentication with GitHub.
    :type installation_id: str
    :param repo_full_name: Full name of the repository (e.g., "owner/repo").
    :type repo_full_name: str
    :param branch_name: Name of the branch to retrieve the latest commit from.
    :type branch_name: str
    :return: A dictionary containing latest commit details such as SHA, URL, message,
             author, and date. Returns None if an error occurs.
    :rtype: dict or None
    """
    try:
        git_integration = GithubIntegration(GITHUB_APP_ID, GITHUB_PRIVATE_KEY)
        access_token = git_integration.get_access_token(int(installation_id))
        g = Github(access_token.token)

        repo = g.get_repo(repo_full_name)
        branch = repo.get_branch(branch_name)
        latest_commit = branch.commit

        return {
            "sha": latest_commit.sha,
            "url": latest_commit.html_url,
            "message": latest_commit.commit.message,
            "author": latest_commit.commit.author.name,
            "date": latest_commit.commit.author.date.isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting latest commit: {str(e)}")
        return None


def rollback_tech_spec_on_close(blitzy_commit: BlitzyCommit, session: Optional[Session] = None):
    """
    Rollback the technical specification and mark it as deleted when closing.

    The function interacts with the database to retrieve and soft delete the
    technical specification based on the provided blitzy commit. It ensures
    the relevant data is handled appropriately during a rollback process.

    :param blitzy_commit: The BlitzyCommit object containing commit information.
    :param session: Optional database session. If not provided, a new session will be
        created and closed within the function.
    :return: None
    """
    with get_db_session(session) as session:
        code_gen = get_code_gen_by_id(blitzy_commit.code_gen_id, session)
        if not code_gen:
            logger.warning(f"No code gen found for blitzy commit id {blitzy_commit.id}. Skipping.")
            return

        tech_spec = get_tech_spec_by_id(code_gen.tech_spec_id, session)
        if not tech_spec:
            logger.warning(f"No tech spec found for code gen id {code_gen.id}. Skipping.")
            return

        tech_spec.soft_delete()
