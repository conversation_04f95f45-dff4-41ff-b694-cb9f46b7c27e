import {clsx, type ClassValue} from 'clsx';
import {twMerge} from 'tailwind-merge';
import {
    DocStatus,
    GitLocation,
    Project,
    ProjectDocumentType,
    ProjectState,
    Subscription,
    UserProfile,
    UtmParams,
} from './entity-types';
import {getAuth} from 'firebase/auth';
import {useEffect} from 'react';
// Extend Window interface to include dataLayer
declare global {
    interface Window {
        dataLayer: any[];
        _hsq: any[];
        clarity: any;
    }
}

function getCurrentUserEmail(): string | undefined {
    const user = getAuth().currentUser;
    return user?.email || undefined;
}

function getCurrentUserId(): string | undefined {
    const uid = getAuth().currentUser?.uid;
    return uid || undefined;
}

export const isIPhone = /iPhone/.test(navigator.userAgent);

export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs));
}

// Function to push environment to the dataLayer
export function setEnvironmentToDataLayer() {
    const environment = import.meta.env.VITE_BLITZY_APP_ENV;
    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({
        Environment: environment,
    });
}

export function documentStatus(
    docType: ProjectDocumentType,
    projectState?: ProjectState,
): DocStatus | undefined {
    if (docType === 'tech_spec') {
        return projectState?.technicalSpec?.status;
    }
}

export function currentSectionIndex(
    docType: ProjectDocumentType,
    projectState?: ProjectState,
): number | undefined {
    if (docType === 'tech_spec') {
        return projectState?.technicalSpec?.jobMetadata?.currentIndex;
    }
}

export function totalSteps(
    docType: ProjectDocumentType,
    projectState?: ProjectState,
): number | undefined {
    if (docType === 'tech_spec') {
        return projectState?.technicalSpec?.jobMetadata?.totalSteps;
    }
}

// export function isoToUnixTime(isoTime: string | undefined): number {
//     if (!isoTime) return 0;
//     return dayjs(isoTime).unix();
// }

export function projectLastUpdatedAt(project: Project): number {
    return Math.max(project.updatedAt ?? 0, project.promptUpdatedAt ?? 0);
}

function logHubSpotEvent(eventName: string, parameters: {[key: string]: any}) {
    const userEmail = getCurrentUserEmail();

    window._hsq = window._hsq || [];

    if (userEmail) {
        window._hsq.push([
            'identify',
            {
                email: userEmail,
            },
        ]);
    }

    const hubspotEventName = `pe44505231_${eventName.toLowerCase().replace(/[^a-z0-9_]/g, '_')}`;
    window._hsq.push([
        'trackCustomBehavioralEvent',
        {
            name: hubspotEventName,
            properties: parameters,
        },
    ]);
}

export function logGAEvent(
    eventName: string,
    parameters: {[key: string]: any},
) {
    // Ensure the dataLayer exists
    window.dataLayer = window.dataLayer || [];

    // Push the event to the dataLayer
    window.dataLayer.push({
        event: eventName,
        ...parameters,
        user_id: getCurrentUserId(),
        uid: getCurrentUserId(),
    });

    const hubspotEventName = eventName
        .toLowerCase()
        .replace(/[^a-z0-9_]/g, '_');

    logHubSpotEvent(hubspotEventName, parameters);

    if (window.clarity) {
        window.clarity('event', eventName, parameters);
    }
}

// export function logHttpError(
//     url: string,
//     httpMethod: string,
//     httpStatus: number,
//     statusText: string,
// ) {
//     logGAEvent('blitzy_error', {
//         error_type: 'http',
//         http_method: httpMethod,
//         status: httpStatus,
//         status_text: statusText,
//         url,
//     });
// }

export function checkSubscriptionStatus(subscription: Subscription) {
    const plan = subscription.planName;
    const subscriptionStartDate = subscription.currentPeriodStartDate;
    const subscriptionEndDate = subscription.currentPeriodEndDate;
    const trialEndDate = subscription.trialEndDate;
    const isTrialing = !!subscription.is_trialing;
    const status = subscription.status;

    const isStarted = !!(
        subscriptionStartDate && subscriptionStartDate * 1000 <= Date.now()
    );
    const isExpired = !!(
        subscriptionEndDate && subscriptionEndDate * 1000 < Date.now()
    );
    // Calculating the remaining time difference from now till trial ends in milliseconds
    const trialRemainingInterval = trialEndDate
        ? Number(trialEndDate) * 1000 - Date.now()
        : 0;

    return {
        plan,
        isStarted,
        isExpired,
        trialRemainingInterval,
        trialEndDate,
        subscriptionStartDate,
        subscriptionEndDate,
        status,
        isTrialing,
    };
}

export function sleep(milliseconds: number = 0) {
    return new Promise(resolve => setTimeout(resolve, milliseconds));
}

export function isValidUserProfile(profile: UserProfile) {
    return profile?.firstName && profile.firstName.length > 0;
}

export function extractCompanyFromEmail(email: string): string {
    if (!email) return '';

    // Get domain part after @
    const domain = email.split('@')[1];
    if (!domain) return '';

    // Common email providers to ignore
    const commonProviders = [
        'gmail.com',
        'yahoo.com',
        'hotmail.com',
        'outlook.com',
        'aol.com',
        'icloud.com',
        'proton.me',
        'protonmail.com',
        'mail.com',
    ];

    if (commonProviders.includes(domain.toLowerCase())) {
        return '';
    }

    // Remove common TLDs and clean up domain
    const companyName = domain
        .split('.')[0] // Get first part before TLD
        .replace(/-/g, ' ') // Replace hyphens with spaces
        .split(' ') // Split into words
        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()) // Capitalize each word
        .join(' '); // Join back with spaces

    return companyName;
}

export const useUtmTracker = () => {
    useEffect(() => {
        // Check if running in browser environment
        if (typeof window === 'undefined') return;

        // Only capture UTM parameters if they haven't been captured before in this session
        if (!sessionStorage.getItem('initial_utm_captured')) {
            const urlParams = new URLSearchParams(window.location.search);

            const utmParams: UtmParams = {
                utm_source: urlParams.get('utm_source') || '',
                utm_medium: urlParams.get('utm_medium') || '',
                utm_campaign: urlParams.get('utm_campaign') || '',
                utm_id: urlParams.get('utm_id') || '',
                landing_page: window.location.pathname,
                referrer: document.referrer || '',
                landing_timestamp: new Date().toISOString(),
            };
            // Store UTM parameters and mark as captured
            Object.entries(utmParams).forEach(([key, value]) => {
                if (value) {
                    sessionStorage.setItem(key, value);
                }
            });

            sessionStorage.setItem('initial_utm_captured', 'true');
        }
    }, []); // Only run once when component mounts

    const getStoredUtmParams = (): UtmParams => {
        // Check if running in browser environment
        if (typeof window === 'undefined') {
            return {
                utm_source: '',
                utm_medium: '',
                utm_campaign: '',
                utm_id: '',
                landing_page: '',
                referrer: '',
                landing_timestamp: '',
            };
        }

        return {
            utm_source: sessionStorage.getItem('utm_source') || '',
            utm_medium: sessionStorage.getItem('utm_medium') || '',
            utm_campaign: sessionStorage.getItem('utm_campaign') || '',
            utm_id: sessionStorage.getItem('utm_id') || '',
            landing_page: sessionStorage.getItem('landing_page') || '',
            referrer: sessionStorage.getItem('referrer') || '',
            landing_timestamp:
                sessionStorage.getItem('landing_timestamp') || '',
        };
    };

    // Helper function to check if UTM parameters exist in the current URL
    const hasUtmParameters = (): boolean => {
        if (typeof window === 'undefined') return false;

        const urlParams = new URLSearchParams(window.location.search);
        return Boolean(
            urlParams.get('utm_source') ||
                urlParams.get('utm_medium') ||
                urlParams.get('utm_campaign') ||
                urlParams.get('utm_id'),
        );
    };

    return {
        getStoredUtmParams,
        hasUtmParameters,
    };
};

// Helper function to encode UTM parameters for URLs
// export const encodeUtmParams = (params: Partial<UtmParams>): string => {
//     const urlParams = new URLSearchParams();

//     Object.entries(params).forEach(([key, value]) => {
//         if (value) {
//             urlParams.append(key, value);
//         }
//     });

//     return urlParams.toString();
// };

// export function convertGitHubUrlToRepoPath(
//     url: string,
//     branch: string = 'main',
// ): string {
//     const regex = /https:\/\/github\.com\/([^/]+\/[^/]+)/;
//     const match = url.match(regex);
//     return match ? `${match[1]}/${branch}` : '';
// }

export function isLegacyProject(project: Project | undefined): boolean {
    return project
        ? project.type === 'NEW_PRODUCT' && !!project.isDisabled
        : false;
}

export function isValidRepoName(repoName: string) {
    // Regex to match only ASCII letters, digits, and the characters '.', '-', and '_'
    const validRepoNameRegex = /^[a-zA-Z0-9._-]+$/;
    return validRepoNameRegex.test(repoName);
}

export const lowerCaseRegex = /[a-z]/;
export const upperCaseRegex = /[A-Z]/;
export const numberRegex = /\d/;
export const specialCharRegex = /[\^$*.[\]{}()?"!@#%&/\\,><':;|_~`]/;

export function gitUrl(location: GitLocation | undefined): string {
    if (!location?.orgName || !location?.repoName) return '';
    if (location.azureProjectName) {
        return `https://dev.azure.com/${location.orgName}/${location.azureProjectName}/_git/${location.repoName}?version=GB${location.branchName}`;
    } else {
        return `https://github.com/${location?.orgName}/${location?.repoName}/tree/${location?.branchName}`;
    }
}
