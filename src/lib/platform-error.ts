type ErrorCodeType =
    | 'HttpStatus409'
    | 'HttpStatus400'
    | 'HttpStatus429'
    | 'HttpStatus500'
    | 'HttpStatusOther';

export class PlatformError extends Error {
    /** The error code for this error. */
    readonly code: ErrorCodeType;

    /** Custom data for this error. */
    customData?: Record<string, unknown> | undefined;

    constructor(
        code: ErrorCodeType,
        message?: string,
        customData?: Record<string, unknown> | undefined,
    ) {
        super();
        this.code = code;
        if (message) {
            this.message = message; // base class Error
        }
        this.customData = customData;
    }
}
