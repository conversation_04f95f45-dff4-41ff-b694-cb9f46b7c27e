import {getAuth, User} from 'firebase/auth';
import {jwtDecode} from 'jwt-decode';
import dayjs from 'dayjs';
import {
    Project,
    ProjectType,
    ProjectDetail,
    ProjectDocumentType,
    RegisterUserInput,
    Status201Result,
    Subscription,
    UserProfile,
    // GithubInstallationStatus,
    GithubInstallationStatusResponse,
    ProjectStatus,
    TechSpecRunStatus,
    ProjectWithStatus,
    GitAccount,
    GitRepo,
    GitBranch,
    GitLocation,
    TechSpecStatus,
    CodeGenCommitStatus,
    CodeGenStatus,
    TechSpecJobType,
    Usage,
    UserConfiguration,
    GetGitInstallationsOutput,
    AzureProject,
    GithubPRStatus,
} from './entity-types';
import {sleep} from './utils';
import {PlatformError} from './platform-error';
import {logGAEvent} from '@/lib/utils';

const BACKEND_URL = import.meta.env.VITE_BLITZY_OS_SERVER_URL;
const MOCK_BACKEND_URL = import.meta.env.VITE_BLITZY_OS_MOCK_SERVER_URL;

const SERVER_URL = MOCK_BACKEND_URL ?? BACKEND_URL;

const FETCH_DOC_RETRY_THRESHOLD =
    parseInt(import.meta.env.VITE_FETCH_DOC_RETRY_THRESHOLD) || 3;

let apiAccessToken: string | undefined = undefined;

function isApiAccessTokenValid(): boolean {
    if (apiAccessToken) {
        const decoded = jwtDecode(apiAccessToken);
        if (decoded?.exp) {
            const expTs = dayjs.unix(decoded.exp);
            // console.log("access token expires at ", expTs.toISOString());
            const now = dayjs();
            // console.log("now is ", now.toISOString());
            const diff = expTs.diff(now, 'second');
            return diff > 300; // 5 minutes before token expires
        }
    }
    return false;
}

function setAPIAccessToken(token: string | undefined) {
    apiAccessToken = token;
}

export async function refreshAPIAcessToken(user?: User): Promise<void> {
    const u = user ?? getAuth().currentUser;
    const userIdToken = u ? await u.getIdToken(true) : undefined;

    if (userIdToken) {
        // console.debug('Refreshing API access token');

        const url = `${SERVER_URL}/auth`;
        const resp = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Id-Token': userIdToken,
            },
        });

        if (!resp.ok) {
            const correlationId = resp.headers.get('x-correlation-id');
            const errorData = await resp.json();
            logGAEvent('api_response_error', {
                endpoint: url,
                method: 'POST',
                action: 'refresh_api_access_token',
                response: resp,
                status_code: resp.status,
                error_message: resp.statusText,
                error_code: errorData?.errorCode || 'no error code',
                correlation_id: correlationId,
                message: `Failed to refresh API access token: ${resp.statusText}`,
            });

            throw new Error(
                `Failed to refresh API access token: ${resp.statusText}`,
            );
        }

        const jsonObj = await resp.json();
        setAPIAccessToken(jsonObj?.access_token);

        if (jsonObj?.access_token) {
            // console.debug('API access token refreshed');
        } else {
            console.error('Invalid API access token response:', jsonObj);
            throw new Error('API access token not received');
        }
    } else {
        // console.debug('Unsetting API access token');
        setAPIAccessToken(undefined);
    }
}

async function decorateHeaders(
    headers?: Record<string, string>,
    addAuthorization = true,
): Promise<Record<string, string>> {
    if (addAuthorization) {
        if (!isApiAccessTokenValid()) {
            await refreshAPIAcessToken();
        }
    }
    return {
        ...(headers ?? {}),
        'Content-Type': headers?.['Content-Type'] ?? 'application/json',
        ...(addAuthorization && {Authorization: `Bearer ${apiAccessToken}`}),
    };
}

export async function retrieveUserProfile(): Promise<UserProfile | undefined> {
    // throw new Error('401 not authorized');
    const url = `${SERVER_URL}/user/profile`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (!resp.ok) {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'GET',
            action: 'retrieve_user_profile',
            response: resp,
            status_code: resp.status,
            error_message: resp.statusText,
            error_code: errorData?.errorCode || 'no error code',
            correlation_id: correlationId,
            message: `Failed to retrieve user profile: ${resp.statusText}`,
        });
        throw new Error(`Failed to retrieve user profile: ${resp.statusText}`);
    }

    const jsonObj = await resp.json();
    if (jsonObj?.id) {
        const userProfile = jsonObj as UserProfile;
        if (!userProfile.firstName) {
            userProfile.firstName = userProfile.email?.split('@')[0] ?? 'NoFN';
        }
        return userProfile;
    } else {
        console.error('Invalid user profile response:', jsonObj);
        throw new Error('User profile not found');
    }
}

export async function updateUserProfile(profile: UserProfile) {
    const url = `${SERVER_URL}/user/profile`;
    const resp = await fetch(url, {
        method: 'PUT',
        headers: await decorateHeaders(),
        body: JSON.stringify(profile),
    });

    if (!resp.ok) {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'PUT',
            action: 'update_user_profile',
            profile: profile,
            response: resp,
            status_code: resp.status,
            error_code: errorData?.errorCode || 'no error code',
            error_message: resp.statusText,
            correlation_id: correlationId,
            message: `Failed to update user profile: ${resp.statusText}`,
        });
        console.error(
            `Failed to update user profile. Status: ${resp.status}, Message: ${resp.statusText}`,
        );
        throw new Error(`Failed to update user profile: ${resp.statusText}`);
    }
    const correlationId = resp.headers.get('x-correlation-id');
    logGAEvent('api_response_success', {
        endpoint: url,
        method: 'PUT',
        action: 'update_user_profile',
        response: resp,
        status_code: resp.status,
        correlation_id: correlationId,
        message: `User ${profile.email} profile updated successfully`,
    });
}

export async function createProject(
    name: string,
    type: ProjectType | undefined = 'NEW_PRODUCT',
): Promise<Project | undefined> {
    const url = `${SERVER_URL}/project`;
    const resp = await fetch(url, {
        method: 'POST',
        headers: await decorateHeaders(),
        body: JSON.stringify({name, type}),
    });
    const correlationId = resp.headers.get('x-correlation-id');
    if (!resp.ok) {
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: '/project',
            method: 'POST',
            action: 'create_project',
            response: resp,
            status_code: resp.status,
            error_code: errorData?.errorCode || 'no error code',
            error_message: resp.statusText || 'No status text provided',
            project_type: type,
            correlation_id: correlationId,
            message: `Failed to create project. Status: ${resp.status}, ${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });
        console.error(
            `Failed to create project. Status: ${resp.status}, Message: ${resp.statusText}`,
        );
        throw new Error(`Failed to create project: ${resp.statusText}`);
    }

    const {id} = await resp.json();

    if (id) {
        logGAEvent('api_response_success', {
            endpoint: '/project',
            method: 'POST',
            action: 'create_project',
            project_id: id,
            project_type: type,
            project_name: name,
            response_time: performance.now(),
            correlation_id: correlationId,
            message: `Project ${name} of type ${type} created`,
        });
        return {name, id} as Project;
    } else {
        logGAEvent('api_response_validation_error', {
            endpoint: '/project',
            method: 'POST',
            action: 'create_project',
            error_type: 'missing_id',
            project_type: type,
            project_name: name,
            response_time: performance.now(),
            correlation_id: correlationId,
            message: `Project ${name} of type ${type} creation failed: No project ID returned`,
        });
        throw new Error('Project creation failed: No project ID returned');
    }
}

export async function fetchProjects(): Promise<ProjectWithStatus[]> {
    const url = `${SERVER_URL}/project`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (resp.ok) {
        const json = (await await resp.json()) as {
            projects: ProjectWithStatus[];
        };
        const projects = json?.projects ?? [];
        projects.forEach(proj => (proj.type = proj.type ?? proj.initialType));
        return projects;
    } else {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'GET',
            action: 'fetch_projects',
            response: resp,
            status_code: resp.status,
            error_message: resp.statusText,
            error_code: errorData?.errorCode || 'no error code',
            correlation_id: correlationId,
            message: `Failed to fetch projects: ${resp.statusText}`,
        });
        console.error(`${resp.status}: ${resp.statusText}`);
        return [];
    }
}

export async function fetchProject(projectId: string): Promise<{
    project: ProjectDetail | undefined;
    status: ProjectStatus | undefined;
}> {
    const url = `${SERVER_URL}/project/${projectId}`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (!resp.ok) {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'GET',
            action: 'fetch_project',
            response: resp,
            status_code: resp.status,
            error_code: errorData?.errorCode || 'no error code',
            error_message: resp.statusText || 'No status text provided',
            project_id: projectId,
            correlation_id: correlationId,
            message: `Failed to fetch project: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });

        if (resp.status === 404) {
            throw new PlatformError('HttpStatusOther', 'Project not found', {
                error_code: 'PROJECT_NOT_FOUND',
                redirect_to: '/workspace/projects',
                forceRefresh: true,
            });
        }

        throw new Error(`Failed to fetch project: ${resp.statusText}`);
    }

    const project = (await resp.json()) as ProjectDetail;
    if (project?.id) {
        project.type = project.type ?? project.initialType;
        return {project, status: project?.status};
    } else {
        console.error('Project data invalid:', project);
        throw new Error('Project not found');
    }
}

// we only support one sink and one source for the time being
export async function fetchProjectGitRepos(projectId: string): Promise<{
    sink: GitLocation | undefined;
    source: GitLocation | undefined;
}> {
    const url = `${SERVER_URL}/project/${projectId}/github/repos`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (!resp.ok) {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'GET',
            action: 'fetch_project_git_repos',
            response: resp,
            status_code: resp.status,
            error_code: errorData?.errorCode || 'no error code',
            error_message: resp.statusText || 'No status text provided',
            project_id: projectId,
            correlation_id: correlationId,
            message: `Failed to fetch project git repos: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });
        throw new Error(`Failed to fetch project: ${resp.statusText}`);
    }

    const response = await resp.json();

    let repos: GitLocation[] = [];
    if (Array.isArray(response)) {
        repos = response;
    } else if (response.results && Array.isArray(response.results)) {
        repos = response.results;
    }

    let source: GitLocation | undefined;
    let sink: GitLocation | undefined;

    repos.forEach(repo => {
        if (repo.type === 'SOURCE') {
            if (source === undefined) source = repo;
        } else if (repo.type === 'TARGET') {
            if (sink === undefined) sink = repo;
        }
    });

    return {source, sink};
}
/*
export async function fetchProjectJobStatus(
    projectId: string,
): Promise<ProjectState> {
    const url = `${SERVER_URL}/project/${projectId}/job/status`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (!resp.ok) {
        logHttpError(url, 'GET', resp.status, resp.statusText);
        throw new Error(
            `Failed to fetch project job status: ${resp.statusText}`,
        );
    }

    const json = (await resp.json()) as ProjectState;
    return json;
}
*/
/*
export async function fetchProjectStatus(
    projectId: string,
): Promise<ProjectStatus> {
    // const url = `${SERVER_URL}/project/${projectId}/status`;
    // TODO: use the above URL when the backend is ready
    const url = `${SERVER_URL}/project/${projectId}`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (!resp.ok) {
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'GET',
            action: 'fetch_project_status',
            response: resp,
            status_code: resp.status,
            error_message: resp.statusText,
            project_id: projectId,
            message: `Failed to fetch project status: ${resp.statusText}`,
        });
        throw new Error(`Failed to fetch project status: ${resp.statusText}`);
    }

    const {status} = (await resp.json()) as {status: ProjectStatus};
    return status;
}
*/
export async function fetchTechSpecStatus(
    projectId: string,
): Promise<TechSpecStatus | undefined> {
    const url = `${SERVER_URL}/project/${projectId}/tech-spec/latest`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (!resp.ok) {
        if (resp.status === 404) {
            return undefined;
        } else {
            const correlationId = resp.headers.get('x-correlation-id');
            const errorData = await resp.json();
            logGAEvent('api_response_error', {
                endpoint: url,
                method: 'GET',
                action: 'fetch_tech_spec_status',
                response: resp,
                status_code: resp.status,
                error_code: errorData?.errorCode || 'no error code',
                error_message: resp.statusText || 'No status text provided',
                project_id: projectId,
                correlation_id: correlationId,
                message: `Failed to fetch tech-spec status: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
            });
            throw new Error(
                `Failed to fetch tech-spec status: ${resp.statusText}`,
            );
        }
    }

    const json = (await resp.json()) as TechSpecStatus;
    if (json) {
        return {
            ...json,
            jobType: json.jobType,
        };
    }
    return undefined;
}

export async function fetchTechSpecRunStatus(
    projectId: string,
): Promise<TechSpecRunStatus> {
    // const url = `${SERVER_URL}/project/${projectId}/tech-spec/job/status`;
    const url = `${SERVER_URL}/project/${projectId}/tech-spec/latest/run/status`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (!resp.ok) {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'GET',
            action: 'fetch_tech_spec_run_status',
            response: resp,
            status_code: resp.status,
            error_code: errorData?.errorCode || 'no error code',
            error_message: resp.statusText || 'No status text provided',
            project_id: projectId,
            correlation_id: correlationId,
            message: `Failed to fetch tech-spec run status: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });
        throw new Error(
            `Failed to fetch tech-spec run status: ${resp.statusText}`,
        );
    }

    const json = (await resp.json()) as TechSpecRunStatus;
    return json;
}

export async function fetchTechSpecSyncStatus(
    projectId: string,
): Promise<{isTechSpecInSync: boolean}> {
    const url = `${SERVER_URL}/project/${projectId}/tech-spec/sync/status`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (resp.status === 400) {
        return {isTechSpecInSync: true};
    }
    if (!resp.ok) {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'GET',
            action: 'fetch_tech_spec_sync_status',
            response: resp,
            status_code: resp.status,
            error_message: resp.statusText || 'No status text provided',
            error_code: errorData?.errorCode || 'no error code',
            project_id: projectId,
            correlation_id: correlationId,
            message: `Failed to fetch tech-spec sync status: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });
        throw new Error(
            errorData?.error ||
                `Failed to fetch tech-spec sync status: ${resp.statusText}`,
        );
    }

    const {needsScan} = (await resp.json()) as {needsScan: boolean};
    return {isTechSpecInSync: !needsScan};
}

export async function syncTechSpec(projectId: string) {
    const url = `${SERVER_URL}/project/${projectId}/tech-spec/sync/status`;
    const resp = await fetch(url, {
        method: 'POST',
        headers: await decorateHeaders(),
    });

    if (!resp.ok) {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'POST',
            action: 'sync_tech_spec',
            response: resp,
            status_code: resp.status,
            error_message: resp.statusText || 'No status text provided',
            project_id: projectId,
            error_code: errorData?.errorCode || 'no error code',
            correlation_id: correlationId,
            message: `Failed to post tech-spec sync status: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });

        if (errorData.errorCode === 'BRANCH_LOCKED') {
            throw new PlatformError('HttpStatus409', 'This branch is locked.', {
                error_code: 'BRANCH_LOCK',
            });
        } else {
            throw new Error(
                `Failed to post tech-spec sync status: ${resp.statusText}`,
            );
        }
    }
}
/*
export async function fetchCodeGenStatus(
    projectId: string,
): Promise<CodeGenStatus | undefined> {
    const url = `${SERVER_URL}/project/${projectId}/code-gen`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (resp.status === 404) {
        return undefined;
    } else if (!resp.ok) {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'GET',
            action: 'fetch_code_gen_status',
            response: resp,
            status_code: resp.status,
            error_message: resp.statusText || 'No status text provided',
            error_code: errorData?.errorCode || 'no error code',
            project_id: projectId,
            correlation_id: correlationId,
            message: `Failed to fetch code-gen job status: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });
        throw new Error(
            `Failed to fetch code-gen job status: ${resp.statusText}`,
        );
    }

    const json = (await resp.json()) as CodeGenStatus;
    return json;
}
*/
export async function fetchTechSpecCodeGenStatus(
    projectId: string,
    techSpecID: string,
): Promise<CodeGenStatus | undefined> {
    const url = `${SERVER_URL}/project/${projectId}/tech-spec/${techSpecID}/code-gen`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (resp.status === 404) {
        return undefined;
    } else if (!resp.ok) {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'GET',
            action: 'fetch_code_gen_status',
            response: resp,
            status_code: resp.status,
            error_message: resp.statusText || 'No status text provided',
            error_code: errorData?.errorCode || 'no error code',
            project_id: projectId,
            correlation_id: correlationId,
            message: `Failed to fetch code-gen job status: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });
        throw new Error(
            `Failed to fetch code-gen job status: ${resp.statusText}`,
        );
    }

    const json = (await resp.json()) as CodeGenStatus;
    return json;
}
/*
export async function fetchCodeGenRunStatus(
    projectId: string,
): Promise<CodeGenRunStatus> {
    const url = `${SERVER_URL}/project/${projectId}/code-gen/run/status`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (!resp.ok) {
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'GET',
            action: 'fetch_code_gen_run_status',
            response: resp,
            status_code: resp.status,
            error_message: resp.statusText,
            project_id: projectId,
            message: `Failed to fetch code-gen job status: ${resp.statusText}`,
        });
        throw new Error(
            `Failed to fetch code-gen job status: ${resp.statusText}`,
        );
    }

    const json = (await resp.json()) as CodeGenRunStatus;
    return json;
}
*/
/*
export async function finalizeProjectPrompt(
    projectId: string,
    body?: object,
): Promise<Project | undefined> {
    const resp = await fetch(
        `${SERVER_URL}/project/${projectId}/finalize/prompt`,
        {
            method: 'POST',
            headers: await decorateHeaders(),
            body: body ? JSON.stringify(body) : undefined,
        },
    );

    const project = await resp.json();
    if (project?.id) {
        return project as Project;
    } else {
        console.error(project);
    }
}
*/
export async function updateProjectPrompt(
    project: Project,
    prompt: string,
    type: TechSpecJobType,
    isDraft: boolean,
    maxRetry = 3,
) {
    const url = `${SERVER_URL}/project/${project.id}/tech-spec/prompt`;
    let retry = 0;
    let errorMsg: string = '';
    while (retry < maxRetry) {
        const resp = await fetch(url, {
            method: 'POST',
            headers: await decorateHeaders(),
            body: JSON.stringify({
                prompt,
                type,
                isDraft,
            }),
        });

        if (resp.ok) {
            const correlationId = resp.headers.get('x-correlation-id');
            logGAEvent('api_response_success', {
                endpoint: url,
                method: 'POST',
                action: 'update_project_prompt',
                response: resp,
                status_code: resp.status,
                project_id: project.id,
                project_type: project.type,
                correlation_id: correlationId,
                message: 'Project prompt updated successfully',
            });
            return;
        } else {
            const correlationId = resp.headers.get('x-correlation-id');
            const errorData = await resp.json();
            logGAEvent('api_response_error', {
                endpoint: url,
                method: 'POST',
                action: 'update_project_prompt',
                response: resp,
                status_code: resp.status,
                error_message:
                    resp.statusText ||
                    errorData.error ||
                    'No status text provided',
                project_id: project.id,
                project_type: project.type,
                error_code: errorData?.errorCode || 'no error code',
                correlation_id: correlationId,
                message: `Failed to update project prompt: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
            });
            if (resp.status === 409) {
                throw new PlatformError(
                    'HttpStatus409',
                    errorData.error as string,
                );
            }
            if (errorData.errorCode === 'BRANCH_LOCKED') {
                throw new PlatformError(
                    'HttpStatus409',
                    'This branch is currently being used by another project. Please select a different branch to continue and restart.',
                    {error_code: 'BRANCH_LOCK'},
                );
            } else {
                errorMsg = `Failed to update project prompt: ${resp.statusText}`;
            }
            retry++;
            await sleep(1000);
        }
    }

    throw new Error(errorMsg);
}

export async function updateProjectDocumentPrompt(
    project: Project,
    prompt: string,
    maxRetry = 3,
) {
    const url = `${SERVER_URL}/project/${project.id}/document/prompt`;
    let retry = 0;
    let errorMsg: string = '';
    while (retry < maxRetry) {
        const resp = await fetch(url, {
            method: 'POST',
            headers: await decorateHeaders(),
            body: JSON.stringify({
                prompt,
            }),
        });

        if (resp.ok) {
            const correlationId = resp.headers.get('x-correlation-id');
            logGAEvent('api_response_success', {
                endpoint: url,
                method: 'POST',
                action: 'update_project_document_prompt',
                response: resp,
                status_code: resp.status,
                project_id: project.id,
                project_type: project.type,
                correlation_id: correlationId,
                message: 'Project document prompt updated successfully',
            });
            return;
        } else {
            const correlationId = resp.headers.get('x-correlation-id');
            const errorData = await resp.json();
            logGAEvent('api_response_error', {
                endpoint: url,
                method: 'POST',
                action: 'update_project_document_prompt',
                response: resp,
                status_code: resp.status,
                error_message: resp.statusText || 'No status text provided',
                project_id: project.id,
                project_type: project.type,
                error_code: errorData.errorCode,
                correlation_id: correlationId,
                message: `Failed to update project prompt: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
            });
            errorMsg = `Failed to update project document prompt: ${resp.statusText}`;
            retry++;
            await sleep(1000);
        }
    }

    throw new Error(errorMsg);
}

export async function downloadProjectDocument(
    project: Project,
    docType: ProjectDocumentType,
): Promise<string | undefined> {
    for (let i = 0; i < FETCH_DOC_RETRY_THRESHOLD; i++) {
        try {
            const document = await fetchProjectDocument(project, docType);
            return document;
        } catch (error) {
            console.error(error);
            await sleep(1000);
        }
    }
    throw new Error('Failed to download project document');
}

async function fetchProjectDocument(
    project: Project,
    docType: ProjectDocumentType,
): Promise<string | undefined> {
    // This is a two step document fetch process. The first end point returns a preSignedURL. A subsquent
    // call to the preSignedURL will download the actual document.
    //TODO: @vaibhav, when server returns 404, we need to retry a few times (number of retry should be defined in .env file)
    // before throwing the error.
    const url = `${SERVER_URL}/project/${project.id}/${docType === 'software_req' ? 'soft-req' : 'tech-spec'}/latest/document`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (!resp.ok) {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'GET',
            action: 'fetch_project_document',
            response: resp,
            status_code: resp.status,
            error_code: errorData?.errorCode || 'no error code',
            error_message: resp.statusText || 'No status text provided',
            project_id: project.id,
            project_type: project.type,
            correlation_id: correlationId,
            message: `Failed to fetch project document: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });
        throw new Error(`Failed to fetch project document: ${resp.statusText}`);
    } else {
        const {preSignedURL} = (await resp.json()) as {preSignedURL: string};
        if (preSignedURL) {
            const downloadUrl = preSignedURL.startsWith('http')
                ? preSignedURL
                : `${SERVER_URL}/project/${preSignedURL}`;
            const downloadResp = await fetch(downloadUrl, {
                method: 'GET',
                headers: await decorateHeaders(),
            });
            if (!downloadResp.ok) {
                const correlationId =
                    downloadResp.headers.get('x-correlation-id');
                const errorData = await downloadResp.json();
                logGAEvent('api_response_error', {
                    endpoint: downloadUrl,
                    method: 'GET',
                    action: 'fetch_project_document',
                    response: downloadResp,
                    status_code: downloadResp.status,
                    error_code: errorData?.errorCode || 'no error code',
                    error_message:
                        downloadResp.statusText || 'No status text provided',
                    project_id: project.id,
                    project_type: project.type,
                    correlation_id: correlationId,
                    message: `Failed to download project document: ${downloadResp.status}${downloadResp.statusText ? `, Message: ${downloadResp.statusText}` : ''}`,
                });
                throw new Error(
                    `Failed to download project document: ${resp.statusText}`,
                );
            } else {
                return await downloadResp.text();
            }
        }
    }
}

export async function updateProjectDocument(
    projectId: string,
    docId: string,
    doc: string,
    projectType: ProjectType,
    docType: ProjectDocumentType,
    maxRetry = 3,
) {
    const url = `${SERVER_URL}/project/${projectId}/${docType === 'software_req' ? 'soft-req' : 'tech-spec'}/${docId}/document`;
    const blob = new Blob([doc], {type: 'text/markdown'});
    const file = new File([blob], docType + '_document.md', {
        type: 'text/markdown',
    });
    const formData = new FormData();
    formData.append('file', file);

    let retry = 0;
    let errorMsg: string = '';
    while (retry < maxRetry) {
        const resp = await fetch(url, {
            method: 'PUT',
            headers: {
                Authorization: `Bearer ${apiAccessToken}`,
            },
            body: formData,
        });

        if (resp.ok) {
            const correlationId = resp.headers.get('x-correlation-id');
            logGAEvent('api_response_success', {
                endpoint: url.replace(SERVER_URL, ''),
                method: 'PUT',
                action: 'update_project_document',
                project_id: projectId,
                project_type: projectType,
                document_type: docType,
                document_size: doc.length,
                correlation_id: correlationId,
                message: `Project ${projectId} document updated successfully`,
            });
            return;
        } else {
            const correlationId = resp.headers.get('x-correlation-id');
            const errorData = await resp.json();
            logGAEvent('api_response_error', {
                endpoint: url.replace(SERVER_URL, ''),
                method: 'PUT',
                action: 'update_project_document',
                response: resp,
                status_code: resp.status,
                error_message: resp.statusText || 'No status text provided',
                error_code: errorData?.errorCode || 'no error code',
                project_id: projectId,
                project_type: projectType,
                document_type: docType,
                document_size: doc.length,
                retries: maxRetry,
                correlation_id: correlationId,
                message: errorMsg,
            });
            errorMsg = `Failed to update project document: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`;
            retry++;
            await sleep(1000);
        }
    }
    throw new Error(errorMsg);
}

export async function approveProjectDocument(
    projectId: string,
    docType: ProjectDocumentType,
    maxRetry = 3,
) {
    let retry = 0;
    let errorMsg: string = '';

    while (retry < maxRetry) {
        const url = `${SERVER_URL}/project/${projectId}/${docType === 'software_req' ? 'soft-req' : 'tech-spec'}/latest/document/approve`;
        const resp = await fetch(url, {
            method: 'POST',
            headers: await decorateHeaders(),
        });

        if (resp.ok) {
            const correlationId = resp.headers.get('x-correlation-id');
            logGAEvent('api_response_success', {
                endpoint: url.replace(SERVER_URL, ''),
                method: 'POST',
                action: 'approve_project_document',
                project_id: projectId,
                document_type: docType,
                response_time: performance.now(),
                correlation_id: correlationId,
                message: `Project ${projectId} document approved successfully`,
            });
            return;
        } else {
            if (resp.status === 429) {
                const correlationId = resp.headers.get('x-correlation-id');
                const errorData = await resp.json();
                logGAEvent('api_response_error', {
                    endpoint: url.replace(SERVER_URL, ''),
                    method: 'POST',
                    action: 'approve_project_document',
                    response: resp,
                    status_code: resp.status,
                    error_code: errorData?.errorCode || 'no error code',
                    error_message: resp.statusText || 'No status text provided',
                    project_id: projectId,
                    document_type: docType,
                    correlation_id: correlationId,
                    message: "You've reached the account limit",
                });
                throw new PlatformError(
                    'HttpStatus429',
                    "You've reached the account limit",
                );
            } else {
                errorMsg = `Failed to approve project document: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`;
                retry++;
                await sleep(1000);
            }
        }
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: `/project/${projectId}/${docType === 'software_req' ? 'soft-req' : 'tech-spec'}/latest/document/approve`,
            method: 'POST',
            action: 'approve_project_document',
            response: resp,
            status_code: resp.status,
            error_code: errorData?.errorCode || 'no error code',
            error_message: resp.statusText || 'No status text provided',
            project_id: projectId,
            document_type: docType,
            retries: maxRetry,
            correlation_id: correlationId,
            message: errorMsg,
        });
    }
    throw new Error(errorMsg);
}

/*
export async function updateProjectPrompt(
    projectId: string,
    prompt: string,
): Promise<Project | undefined> {
    const resp = await fetch(
        `${SERVER_URL}/project/${projectId}/update/prompt`,
        {
            method: 'POST',
            headers: await decorateHeaders(),
            body: JSON.stringify({prompt}),
        },
    );

    const project = await resp.json();
    if (project?.id) {
        return project as Project;
    } else {
        console.error(project);
    }
}
*/
/*
export async function buildProject(
    projectId: string,
): Promise<Project | undefined> {
    const url = `${SERVER_URL}/project/${projectId}/build`;
    const resp = await fetch(url, {
        method: 'POST',
        headers: await decorateHeaders(),
    });

    if (!resp.ok) {
        logHttpError(url, 'POST', resp.status, resp.statusText);
        throw new Error(`Failed to build project: ${resp.statusText}`);
    }

    const project = await resp.json();
    if (project?.id) {
        return project as Project;
    } else {
        console.error('Build project response invalid:', project);
        throw new Error('Failed to build project');
    }
}
*/

export async function fetchUserSubscriptionStatus() {
    const url = `${SERVER_URL}/user/subscription/status`;
    const response = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (!response.ok) {
        const correlationId = response.headers.get('x-correlation-id');
        const errorData = await response.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'GET',
            action: 'fetch_user_subscription_status',
            status_code: response.status,
            response: response,
            error_message: response.statusText || 'No status text provided',
            error_code: errorData?.errorCode || 'no error code',
            correlation_id: correlationId,
            message: `Failed to fetch user subscription status: ${response.status}${response.statusText ? `, Message: ${response.statusText}` : ''}`,
        });
        throw new Error(
            `Failed to fetch user subscription status: ${response.statusText}`,
        );
    } else {
        const userSubcriptionData = (await response.json()) as Subscription;
        return userSubcriptionData;
    }
}

export async function getStripePaymentLink(
    projectId?: string,
): Promise<{url: string} | undefined> {
    const url = `${SERVER_URL}/stripe/create-checkout-session`;
    const body = projectId
        ? JSON.stringify({project_id: projectId})
        : JSON.stringify({});

    const resp = await fetch(url, {
        method: 'POST',
        headers: await decorateHeaders(),
        body: body,
    });

    if (!resp.ok) {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'POST',
            action: 'create_stripe_payment_link',
            status_code: resp.status,
            response: resp,
            error_message: resp.statusText || 'No status text provided',
            error_code: errorData?.errorCode || 'no error code',
            correlation_id: correlationId,
            message: `Failed to create Stripe payment link: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });
        throw new Error(
            `Failed to create Stripe payment link: ${resp.statusText}`,
        );
    }

    const json = await resp.json();
    if (json?.url) {
        return {url: json.url};
    } else {
        console.error(
            'Invalid response when creating Stripe payment link:',
            json,
        );
        throw new Error('Failed to retrieve payment link');
    }
}

// sign up a new user
export async function register(
    registerInfo: RegisterUserInput,
): Promise<Status201Result> {
    const url = `${SERVER_URL}/user/register`;
    const response = await fetch(url, {
        method: 'POST',
        headers: await decorateHeaders({}, false),
        body: JSON.stringify(registerInfo),
    });

    if (!response.ok) {
        const correlationId = response.headers.get('x-correlation-id');
        const errorData = await response.json();
        logGAEvent('api_response_error', {
            endpoint: '/user/register',
            method: 'POST',
            action: 'register_user',
            status_code: response.status,
            response: response,
            error_message: response.statusText || 'No status text provided',
            error_code: errorData?.errorCode || 'no error code',
            email: registerInfo.email,
            correlation_id: correlationId,
            message: `Failed to register user. Status: ${response.status}${response.statusText ? `, Message: ${response.statusText}` : ''}`,
        });
        switch (response.status) {
            case 409:
            case 400:
            case 500: {
                throw new PlatformError(
                    `HttpStatus${response.status}`,
                    errorData.message || response.statusText,
                );
            }
            default:
                throw new PlatformError('HttpStatusOther', response.statusText);
        }
    } else {
        const registerResult = (await response.json()) as Status201Result;
        const correlationId = response.headers.get('x-correlation-id');
        logGAEvent('api_response_success', {
            endpoint: '/user/register',
            method: 'POST',
            action: 'register_user',
            user_id: registerResult.id,
            email: registerInfo.email,
            response_time: performance.now(),
            correlation_id: correlationId,
            message: 'User registered successfully',
        });
        return registerResult;
    }
}

export async function emailVerificationStatus(): Promise<boolean> {
    const url = `${SERVER_URL}/user/verification/status`;
    const response = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (!response.ok) {
        const correlationId = response.headers.get('x-correlation-id');
        const errorData = await response.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'GET',
            action: 'email_verification_status',
            status_code: response.status,
            response: response,
            error_message: response.statusText || 'No status text provided',
            error_code: errorData?.errorCode || 'no error code',
            correlation_id: correlationId,
            message: `Failed to check email verification status: ${response.status}${response.statusText ? `, Message: ${response.statusText}` : ''}`,
        });
        switch (response.status) {
            case 401:
                return false;
            case 500: {
                const {message} = (await response.json()) as {message: string};
                throw new PlatformError(
                    `HttpStatus${response.status}`,
                    message,
                );
            }
            default:
                throw new PlatformError('HttpStatusOther', response.statusText);
        }
    }
    return true;
}

export async function sendVerificationEmail() {
    const url = `${SERVER_URL}/user/verification/send`;
    const response = await fetch(url, {
        method: 'POST',
        headers: await decorateHeaders(),
    });
    if (!response.ok) {
        const correlationId = response.headers.get('x-correlation-id');
        const errorData = await response.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'POST',
            action: 'send_verification_email',
            response: response,
            status_code: response.status,
            error_message: response.statusText || 'No status text provided',
            error_code: errorData?.errorCode || 'no error code',
            correlation_id: correlationId,
            message: `Failed to send verification email: ${response.status}${response.statusText ? `, Message: ${response.statusText}` : ''}`,
        });
        switch (response.status) {
            case 429: {
                throw new PlatformError(
                    `HttpStatus${response.status}`,
                    'Too many requests. Please wait before trying again.',
                );
            }
            case 409:
            case 400:
            case 500: {
                throw new PlatformError(
                    `HttpStatus${response.status}`,
                    response.statusText,
                );
            }
            default:
                throw new PlatformError('HttpStatusOther', response.statusText);
        }
    }
}

export async function getStripeManageSubscriptionLink(): Promise<
    {url: string} | undefined
> {
    const url = `${SERVER_URL}/stripe/manage-subscription`;
    const resp = await fetch(url, {
        method: 'POST',
        headers: await decorateHeaders(),
    });

    if (!resp.ok) {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'POST',
            action: 'create_stripe_manage_subscription_link',
            response: resp,
            status_code: resp.status,
            error_message: resp.statusText || 'No status text provided',
            error_code: errorData?.errorCode || 'no error code',
            correlation_id: correlationId,
            message: `Failed to create Stripe manage subscription link: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });
        throw new Error(
            'Failed to create Stripe manage subscription link: ' +
                resp.statusText,
        );
    }

    const json = await resp.json();
    if (json?.url) {
        return {url: json.url};
    } else {
        console.error(
            'Invalid response when creating Stripe manage subscription link:',
            json,
        );
        throw new Error('Failed to retrieve manage subscription link');
    }
}

export async function saveGithubInstallationInfo(
    code: string,
    installationID: string,
    setupAction: string,
) {
    const url = `${SERVER_URL}/user/github/install`;
    const resp = await fetch(url, {
        method: 'POST',
        headers: await decorateHeaders(),
        body: JSON.stringify({code, installationID, setupAction}),
    });

    if (!resp.ok) {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'POST',
            action: 'save_github_installation_info',
            response: resp,
            status_code: resp.status,
            error_message: resp.statusText || 'No status text provided',
            error_code: errorData?.errorCode || 'no error code',
            correlation_id: correlationId,
            message: `Failed to save Github installation info: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });
        throw new Error(
            'Failed to save Github installation info' + resp.statusText,
        );
    }
}

export async function getGithubInstallationStatus() {
    const url = `${SERVER_URL}/github`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });
    if (!resp.ok) {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'GET',
            action: 'get_github_installation_status',
            response: resp,
            status_code: resp.status,
            error_message: resp.statusText || 'No status text provided',
            error_code: errorData?.errorCode || 'no error code',
            correlation_id: correlationId,
            message: `Failed to get Github installation info: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });
        if (resp.status === 404) {
            return {results: [{status: '404'}]};
        }
        throw new Error(
            `Failed to get Github installation info: ${resp.statusText}`,
        );
    }
    const githubInstallationStatus =
        (await resp.json()) as GithubInstallationStatusResponse;
    return githubInstallationStatus;
}

export async function getGitInstallations() {
    const url = `${SERVER_URL}/git`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });
    if (!resp.ok) {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'GET',
            action: 'get_git_installations',
            response: resp,
            status_code: resp.status,
            error_message: resp.statusText || 'No status text provided',
            error_code: errorData?.errorCode || 'no error code',
            correlation_id: correlationId,
            message: `Failed to get git installations: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });
        throw new Error(`Failed to get git installations: ${resp.statusText}`);
    }
    const gitInstallations = (await resp.json()) as GetGitInstallationsOutput;
    return gitInstallations;
}

export async function downloadTechSpecPDF(
    projectId: string,
): Promise<{status: string; downloadUrl?: string}> {
    const url = `${SERVER_URL}/project/${projectId}/tech-spec/latest/document/pdf`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (!resp.ok) {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'GET',
            action: 'download_tech_spec_pdf',
            response: resp,
            status_code: resp.status,
            error_message: resp.statusText || 'No status text provided',
            error_code: errorData?.errorCode || 'no error code',
            project_id: projectId,
            correlation_id: correlationId,
            message: `Failed to download tech spec PDF: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });
        throw new Error(`Failed to download tech spec PDF: ${resp.statusText}`);
    }

    const response = await resp.json();

    if (resp.status === 202) {
        return {
            status: 'IN_PROGRESS',
        };
    }

    if (resp.status === 200) {
        return {
            status: 'COMPLETED',
            downloadUrl: response.preSignedURL,
        };
    }

    throw new Error('Unexpected response status');
}
/*
async function retrieveUserGeolocation(): Promise<
    GeoLocation | undefined
> {
    const url = `${SERVER_URL}/user/geolocation`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (!resp.ok) {
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'GET',
            action: 'retrieve_user_geolocation',
            response: resp,
            status_code: resp.status,
            error_message: resp.statusText,
            message: `Failed to retrieve user geolocation: ${resp.statusText}`,
        });
    }
    const jsonObj = await resp.json();
    // If the response is an empty object (no geolocation data)
    if (Object.keys(jsonObj).length === 0) {
        return undefined;
    }

    return jsonObj as GeoLocation;
}
*/
// back-prop project only
export async function onboardGitLocations(
    projectId: string,
    gitLocations: GitLocation[],
) {
    let errorMsg: string = '';

    const url = `${SERVER_URL}/project/${projectId}/github/repos`;
    const resp = await fetch(url, {
        method: 'POST',
        headers: await decorateHeaders(),
        body: JSON.stringify({
            input: gitLocations,
        }),
    });

    if (resp.ok) {
        return;
    } else {
        const errorData = await resp.json();
        const correlationId = resp.headers.get('x-correlation-id');
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'POST',
            action: 'onboard_git_locations',
            response: resp,
            status_code: resp.status,
            error_code: errorData.errorCode || 'no error code',
            error_message: resp.statusText || 'No status text provided',
            project_id: projectId,
            correlation_id: correlationId,
            message: `Failed to save git source: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });
        if (errorData.errorCode === 'BRANCH_LOCKED') {
            throw new PlatformError(
                'HttpStatus409',
                'This branch is currently being used by another project. Please select a different branch to continue.',
                {error_code: 'BRANCH_LOCK'},
            );
        } else {
            const errorMessage = errorData.error as string | undefined;
            errorMsg =
                errorMessage || `Failed to save git source: ${resp.statusText}`;
        }
    }

    throw new Error(errorMsg);
}

export async function onboardGitLocationsForNewProduct(
    projectId: string,
    gitLocation: GitLocation,
) {
    let errorMsg: string = '';

    const url = `${SERVER_URL}/project/${projectId}/new/product/github/repos`;
    const resp = await fetch(url, {
        method: 'POST',
        headers: await decorateHeaders(),
        body: JSON.stringify({
            input: gitLocation,
        }),
    });

    if (resp.ok) {
        const correlationId = resp.headers.get('x-correlation-id');
        logGAEvent('api_response_success', {
            endpoint: url,
            method: 'POST',
            action: 'onboard_git_locations_for_new_product',
            response: resp,
            status_code: resp.status,
            project_id: projectId,
            git_location: gitLocation,
            correlation_id: correlationId,
            message: 'Git location onboarded successfully',
        });
        return;
    } else {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'POST',
            action: 'onboard_git_locations_for_new_product',
            response: resp,
            status_code: resp.status,
            error_message: resp.statusText || 'No status text provided',
            error_code: errorData?.errorCode || 'no error code',
            project_id: projectId,
            correlation_id: correlationId,
            message: `Failed to save git source: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });
        const errorMessage = errorData.error as string | undefined;
        errorMsg =
            errorMessage || `Failed to save git source: ${resp.statusText}`;
    }

    throw new Error(errorMsg);
}

export async function getGithubAccounts(): Promise<GitAccount[]> {
    let errorMsg: string = '';

    const url = `${SERVER_URL}/github/accounts`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (resp.ok) {
        const {results: data} = (await resp.json()) as {results: GitAccount[]};
        return data;
    } else {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'GET',
            action: 'get_github_accounts',
            response: resp,
            status_code: resp.status,
            error_message: resp.statusText || 'No status text provided',
            error_code: errorData?.errorCode || 'no error code',
            correlation_id: correlationId,
            message: `Failed to fetch git organisations: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });
        errorMsg = `Failed to fetch git organisations: ${resp.statusText}`;
    }

    throw new Error(errorMsg);
}

export async function getGithubRepositories(
    accountName: string,
): Promise<GitRepo[]> {
    let errorMsg: string = '';

    const url = `${SERVER_URL}/github/accounts/${accountName}/repositories`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (resp.ok) {
        const {results: data} = (await resp.json()) as {results: GitRepo[]};
        return data;
    } else {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'GET',
            action: 'get_github_repositories',
            response: resp,
            status_code: resp.status,
            error_message: resp.statusText || 'No status text provided',
            error_code: errorData?.errorCode || 'no error code',
            account_name: accountName,
            correlation_id: correlationId,
            message: `Failed to fetch git repositories: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });
        errorMsg = `Failed to fetch git repositories: ${resp.statusText}`;
    }

    throw new Error(errorMsg);
}

export async function getGithubBranches(
    accountName: string,
    repoId: string,
): Promise<GitBranch[]> {
    let errorMsg: string = '';

    const url = `${SERVER_URL}/github/accounts/${accountName}/repositories/${repoId}/branches`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (resp.ok) {
        const {results: data} = (await resp.json()) as {results: GitBranch[]};
        return data;
    } else {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'GET',
            action: 'get_github_branches',
            response: resp,
            status_code: resp.status,
            error_message: resp.statusText || 'No status text provided',
            error_code: errorData?.errorCode || 'no error code',
            account_name: accountName,
            repo_id: repoId,
            correlation_id: correlationId,
            message: `Failed to fetch git branches: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });
        errorMsg = `Failed to fetch git branches: ${resp.statusText}`;
    }

    throw new Error(errorMsg);
}
/*
export async function getGitSink(projectId:string): Promise<GitLocation | undefined> {
    const url = `${SERVER_URL}/project/${projectId}/git-sync`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (!resp.ok) {
        logHttpError(url, 'GET', resp.status, resp.statusText);
        throw new Error(
            `Failed to get code generation commit status: ${resp.statusText}`,
        );
    }

    return await resp.json() as GitLocation;
}
*/
export async function getBranchDetailsForRepository(
    repositoryId: string,
    branchName: string,
): Promise<{isLocked: boolean; details?: any}> {
    const url = `${SERVER_URL}/repository/${repositoryId}/branches/${encodeURIComponent(branchName)}`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (!resp.ok) {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'GET',
            action: 'get_branch_details_for_repository',
            response: resp,
            status_code: resp.status,
            error_message: resp.statusText || 'No status text provided',
            error_code: errorData?.errorCode || 'no error code',
            repository_id: repositoryId,
            branch_name: branchName,
            correlation_id: correlationId,
            message: `Failed to get branch details: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });

        if (resp.status === 404) {
            return {isLocked: false, details: undefined};
        }
        throw new Error(`Failed to get branch details: ${resp.statusText}`);
    }

    const data = await resp.json();
    return {
        isLocked: data.locked || false,
        details: data,
    };
}

export async function getCodeGenCommitStatus(
    projectId: string,
    codeGenId: string,
): Promise<CodeGenCommitStatus> {
    const url = `${SERVER_URL}/project/${projectId}/code-gen/${codeGenId}/github/commit`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (resp.ok) {
        const data = (await resp.json()) as CodeGenCommitStatus;
        return data;
    } else {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'GET',
            action: 'get_code_gen_commit_status',
            response: resp,
            status_code: resp.status,
            error_message: resp.statusText || 'No status text provided',
            error_code: errorData?.errorCode || 'no error code',
            project_id: projectId,
            code_gen_id: codeGenId,
            correlation_id: correlationId,
            message: `Failed to get code generation commit status: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });
        throw new Error(
            `Failed to get code generation commit status: ${resp.statusText}`,
        );
    }
}

export async function fetchPRStatus(
    projectId: string,
    codeGenId: string,
): Promise<GithubPRStatus> {
    const url = `${SERVER_URL}/project/${projectId}/code-gen/${codeGenId}/github/pr/status`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (resp.ok) {
        const data = (await resp.json()) as {status: GithubPRStatus};
        return data.status;
    } else {
        const errorData = await resp.json();
        const errorMsg = errorData?.error || `Failed to fetch PR status`;
        throw new Error(errorMsg);
    }
}

export async function actOnGitHubPR(
    projectId: string,
    codeGenId: string,
    action: 'MERGE' | 'CLOSE',
) {
    const url = `${SERVER_URL}/project/${projectId}/code-gen/${codeGenId}/github/pr/action`;
    const resp = await fetch(url, {
        method: 'POST',
        headers: await decorateHeaders(),
        body: JSON.stringify({action}),
    });

    if (!resp.ok) {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'POST',
            action: 'act_on_github_pr',
            response: resp,
            status_code: resp.status,
            error_message: resp.statusText || 'No status text provided',
            error_code: errorData?.errorCode || 'no error code',
            project_id: projectId,
            code_gen_id: codeGenId,
            correlation_id: correlationId,
            message: `Failed to ${action} Github PR: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });
        if (resp.status === 409) {
            throw new PlatformError(
                'HttpStatus409',
                'Cannot perform this action at this time',
            );
        }
        if (resp.status === 404) {
            throw new PlatformError('HttpStatusOther', resp.statusText);
        }
        throw new Error(
            `Failed to ${action} Github: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        );
    }
}

export async function revertTechSpec(projectId: string): Promise<void> {
    const url = `${SERVER_URL}/project/${projectId}/tech-spec/revert`;
    const resp = await fetch(url, {
        method: 'POST',
        headers: await decorateHeaders(),
    });
    if (!resp.ok) {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'POST',
            action: 'revert_tech_spec',
            response: resp,
            status_code: resp.status,
            error_message: resp.statusText || 'No status text provided',
            error_code: errorData?.errorCode || 'no error code',
            project_id: projectId,
            correlation_id: correlationId,
            message: `Failed to revert tech spec: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });

        if (resp.status === 409) {
            throw new PlatformError(
                'HttpStatus409',
                'Cannot revert (no previous version exists)',
                {error_code: 'NO_PREVIOUS_VERSION'},
            );
        }
        throw new Error(`Failed to revert tech spec: ${resp.statusText}`);
    }

    const correlationId = resp.headers.get('x-correlation-id');
    logGAEvent('api_response_success', {
        endpoint: url,
        method: 'POST',
        action: 'revert_tech_spec',
        response: resp,
        status_code: resp.status,
        project_id: projectId,
        correlation_id: correlationId,
        message: 'Tech spec reverted successfully',
    });
}

export async function fetchUsage(): Promise<Usage> {
    const url = `${SERVER_URL}/user/usage`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (!resp.ok) {
        throw new Error(`Failed to revert tech spec: ${resp.statusText}`);
    }

    return (await resp.json()) as Usage;
}

export async function updateUserConfiguration(
    userConfiguration: UserConfiguration,
) {
    const url = `${SERVER_URL}/user/config`;
    const resp = await fetch(url, {
        method: 'PUT',
        headers: await decorateHeaders(),
        body: JSON.stringify(userConfiguration),
    });

    if (!resp.ok) {
        const errorResponse = (await resp.json()) as {message: string};
        const errorMessage = errorResponse.message;
        throw new Error(
            errorMessage ||
                `Failed to update user configuration: ${resp.statusText}`,
        );
    }
}

export async function fetchAzureDevOpsInstallUrl(): Promise<string> {
    const url = `${SERVER_URL}/azure/auth_url`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (!resp.ok) {
        const errorData = await resp.json();
        const errorMessage = errorData.error as string | undefined;
        throw new Error(
            errorMessage ||
                `Failed to fetch Azure DevOps install URL: ${resp.statusText}`,
        );
    }

    const data = (await resp.json()) as {authUrl: string};
    return data.authUrl;
}

export async function getAzureDevOpsAccounts(): Promise<GitAccount[]> {
    const url = `${SERVER_URL}/azure/accounts`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (!resp.ok) {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'GET',
            action: 'get_azure_devops_accounts',
            response: resp,
            status_code: resp.status,
            error_message: resp.statusText || 'No status text provided',
            error_code: errorData?.errorCode || 'no error code',
            correlation_id: correlationId,
            message: `Failed to get Azure DevOps accounts: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });
        throw new Error(
            `Failed to get Azure DevOps accounts: ${resp.statusText}`,
        );
    }

    const {results: data} = (await resp.json()) as {results: GitAccount[]};
    return data;
}

export async function getAzureDevOpsProjects(
    orgId: string,
): Promise<AzureProject[]> {
    const url = `${SERVER_URL}/azure/accounts/${orgId}/projects`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (!resp.ok) {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'GET',
            action: 'get_azure_devops_projects',
            response: resp,
            status_code: resp.status,
            error_message: resp.statusText || 'No status text provided',
            error_code: errorData?.errorCode || 'no error code',
            correlation_id: correlationId,
            org_id: orgId,
            message: `Failed to get Azure DevOps projects: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });
        throw new Error(
            `Failed to get Azure DevOps projects: ${resp.statusText}`,
        );
    }

    const {results: data} = (await resp.json()) as {results: AzureProject[]};
    return data;
}

export async function getAzureDevOpsRepositories(
    orgId: string,
    projectId: string,
): Promise<GitRepo[]> {
    const url = `${SERVER_URL}/azure/accounts/${orgId}/projects/${projectId}/repos`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (!resp.ok) {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'GET',
            action: 'get_azure_devops_repositories',
            response: resp,
            status_code: resp.status,
            error_message: resp.statusText || 'No status text provided',
            error_code: errorData?.errorCode || 'no error code',
            correlation_id: correlationId,
            org_id: orgId,
            project_id: projectId,
            message: `Failed to get Azure DevOps repositories: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });
        throw new Error(
            `Failed to get Azure DevOps repositories: ${resp.statusText}`,
        );
    }

    const {results: data} = (await resp.json()) as {results: GitRepo[]};
    return data;
}

export async function getAzureDevOpsBranches(
    orgId: string,
    projectId: string,
    repoId: string,
): Promise<GitBranch[]> {
    const url = `${SERVER_URL}/azure/accounts/${orgId}/projects/${projectId}/repos/${encodeURIComponent(repoId)}/branches`;
    const resp = await fetch(url, {
        method: 'GET',
        headers: await decorateHeaders(),
    });

    if (!resp.ok) {
        const correlationId = resp.headers.get('x-correlation-id');
        const errorData = await resp.json();
        logGAEvent('api_response_error', {
            endpoint: url,
            method: 'GET',
            action: 'get_azure_devops_branches',
            response: resp,
            status_code: resp.status,
            error_message: resp.statusText || 'No status text provided',
            error_code: errorData?.errorCode || 'no error code',
            correlation_id: correlationId,
            org_id: orgId,
            project_id: projectId,
            repo_id: repoId,
            message: `Failed to get Azure DevOps branches: ${resp.status}${resp.statusText ? `, Message: ${resp.statusText}` : ''}`,
        });
        throw new Error(
            `Failed to get Azure DevOps branches: ${resp.statusText}`,
        );
    }

    const {results: data} = (await resp.json()) as {results: GitBranch[]};
    return data;
}
