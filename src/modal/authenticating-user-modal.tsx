import IndeterminateLinearProgressIndicator from '@/components/custom/indeterminate-linear-progress-indicator';
import {Dialog, DialogContent, DialogTitle} from '@/components/ui/dialog';

export default function AuthenticatingUserModal() {
    return (
        <Dialog open>
            <DialogContent
                className="sm:max-w-[425px] min-h-dvh sm:min-h-min sm:p-[48px] flex flex-col justify-center items-center gap-[48px] rounded-none sm:rounded-[24px] outline-none [&>button]:hidden"
                tabIndex={1}>
                <DialogTitle className="sr-only">
                    Just a moment we’re signing you in...
                </DialogTitle>
                <div className="text-[20px] text-center text-[#333] font-semibold leading-[26px]">
                    <p className="bg-gradient-to-r from-[#B23AF2] via-[#5B39F3] to-[#94FAD5] text-transparent bg-clip-text">
                        Just a moment
                    </p>
                    <p>We’re signing you in...</p>
                </div>
                <div className="w-full">
                    <IndeterminateLinearProgressIndicator />
                </div>
                <div className="text-[14px] text-center text-[#999] leading-[21px]">
                    <p>Authenticating your details now.</p>
                </div>
            </DialogContent>
        </Dialog>
    );
}
