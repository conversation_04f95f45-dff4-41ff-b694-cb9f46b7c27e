import {useCallback} from 'react';
import {useNavigate} from 'react-router-dom';
import {useForm, SubmitHandler, Controller} from 'react-hook-form';
import {z} from 'zod';
import {zodResolver} from '@hookform/resolvers/zod';
import {Input} from '@/components/ui/input';
import {
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import {createProject} from '@/lib/backend';
import {useAlertContext} from '@/context/alert-context';
import {LoadingSubmitButton} from '@/components/custom/loading-submit-button';
import {logGAEvent} from '@/lib/utils';
interface Props {
    handleClose: () => void;
}

const Schema = z.object({
    name: z.string().min(1, {message: 'Project name is required'}).max(50, {
        message: 'Project name must not be longer than 50 characters.',
    }),
});

type SchemaType = z.infer<typeof Schema>;

export function ProjectNamePane({handleClose}: Props) {
    const navigateTo = useNavigate();
    const {showAlert} = useAlertContext();
    const {
        control,
        handleSubmit,
        formState: {errors, isSubmitting},
    } = useForm<SchemaType>({
        resolver: zodResolver(Schema),
        mode: 'onTouched',
        defaultValues: {
            name: '',
        },
    });

    const onSubmit: SubmitHandler<SchemaType> = useCallback(
        async data => {
            const {name} = data;
            logGAEvent('name_your_project', {projectName: name});
            try {
                const project = await createProject(name);
                if (project) {
                    // console.log(`Project created: ${JSON.stringify(project)}`);
                    handleClose();
                    navigateTo(`project/${project?.id}/prompt`, {
                        replace: true,
                    });
                } else {
                    showAlert?.({
                        type: 'HttpError',
                        message: 'Failed to create project. Please try again.',
                    });
                }
            } catch (error) {
                console.error(error);
                if (error instanceof Error) {
                    showAlert?.({
                        type: 'HttpError',
                        message: `Error creating project: ${error.message}`,
                    });
                }
            }
        },
        [handleClose, navigateTo, showAlert],
    );

    return (
        <>
            <DialogHeader>
                <DialogTitle>Name your project</DialogTitle>
                <DialogDescription>
                    Got a name? Perfect! Write it here. If not, make it
                    memorable.
                </DialogDescription>
            </DialogHeader>
            <form
                onSubmit={handleSubmit(onSubmit)}
                className="w-full flex flex-col flex-grow">
                <div className="flex-grow">
                    <Controller
                        name="name"
                        control={control}
                        render={({field}) => (
                            <Input
                                className={`w-full ${errors.name ? 'input-field-error' : 'input-field'}`}
                                placeholder="Project title"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.name && (
                        <div className="form-error-note pt-2 text-form-error-red">
                            {errors.name.message}
                        </div>
                    )}
                </div>
                <DialogFooter>
                    <LoadingSubmitButton
                        className="primary-button mt-6"
                        loadingText="Creating project"
                        loading={isSubmitting}>
                        Continue
                    </LoadingSubmitButton>
                </DialogFooter>
            </form>
        </>
    );
}
