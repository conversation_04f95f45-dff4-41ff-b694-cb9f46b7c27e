import {useCallback, useEffect, useState} from 'react';
import {useProjectContext} from '@/context/project-context';
import {Project} from '@/lib/entity-types';
import {AiSparkle} from './icons/ai-sparkle';
import {CodeBlock} from './icons/code-block';
// import {CoinStack} from './icons/coin-stack';
import {useSubscriptionContext} from '@/context/subscription-context';
import {logGAEvent} from '@/lib/utils';
import {useAlertContext} from '@/context/alert-context';
import {PlatformError} from '@/lib/platform-error';
import {LoadingSubmitButton} from '@/components/custom/loading-submit-button';
import {useParams} from 'react-router-dom';

interface Props {
    project?: Project;
    handleClose: () => void;
}

export function StartBuildingPane({handleClose}: Props) {
    // const [showBookMeeting, setShowBookMeeting] = useState<boolean>(false);
    const [isTechSpecSubmitting, setIsTechSpecSubmitting] =
        useState<boolean>(false);
    const {project, projectState, approveDocument, refreshProjectState} =
        useProjectContext();
    const {plan, isExpired, gotoPaymentLink, refreshSubscription} =
        useSubscriptionContext();
    const {showAlert} = useAlertContext();
    const {projectId} = useParams();

    useEffect(() => {
        if (refreshSubscription) {
            refreshSubscription();
        }
    }, [refreshSubscription]);

    const handleStartBuilding = useCallback(async () => {
        try {
            setIsTechSpecSubmitting(true);
            if (plan === 'FREE') {
                // Log event for upgrading to PRO
                logGAEvent('upgrade_to_pro_clicked', {
                    plan,
                    projectId: project?.id || 'N/A',
                });
                gotoPaymentLink?.(projectId);
            } else {
                if (project?.id && approveDocument) {
                    if (isExpired) {
                        alert(
                            'Your plan has expired. Please renew it to continue.',
                        );
                    } else {
                        // Log event for starting building
                        logGAEvent('start_building_clicked', {
                            plan,
                            projectId: project?.id || 'N/A',
                        });

                        await approveDocument('tech_spec');

                        if (refreshProjectState) {
                            refreshProjectState();
                        }

                        handleClose();

                        // navigateTo(
                        //     `/workspace/project/${project.id}/pending-code`,
                        //     {
                        //         replace: true,
                        //     },
                        // );
                    }
                }
            }
        } catch (error) {
            console.error(error);

            if (error instanceof Error) {
                if (
                    !(
                        error instanceof PlatformError &&
                        error.code === 'HttpStatus429'
                    )
                ) {
                    showAlert?.({
                        type: 'HttpError',
                        message: `Failed to approve project: ${error.message}`,
                    });
                }
            }
        } finally {
            setIsTechSpecSubmitting(false);
        }
    }, [
        projectId,
        approveDocument,
        gotoPaymentLink,
        handleClose,
        isExpired,
        plan,
        project?.id,
        refreshProjectState,
        showAlert,
    ]);

    useEffect(() => {
        if (projectState?.codeGeneration?.status !== 'TODO') {
            handleClose();
        }
    }, [handleClose, projectState?.codeGeneration?.status]);

    if (!project) return null;

    let buttonText: string | undefined = undefined;
    if (plan) {
        buttonText = plan === 'FREE' ? 'Upgrade to PRO' : 'Start building now';
    }

    return (
        <div className="h-full p-[8px] sm:p-[24px] flex flex-col sm:block">
            <div className="mb-[32px] sm:mb-[20px]">
                <h1 className="mb-[8px] text-[32px] sm:text-[40px] font-semibold">
                    Build{' '}
                    <span className="bg-gradient-to-r from-[#B23AF2] via-[#5B39F3] to-[#94FAD5] text-transparent bg-clip-text">
                        80%
                    </span>{' '}
                    of your product
                </h1>
                {/* <p className="text-[14px] text-[#999]">
                    Blitzy AI delivers in just 8 hours
                </p> */}
            </div>
            <div className="flex-grow">
                <div className="mb-[24px] h-[6px] bg-[#F2F0FE] rounded-full overflow-hidden">
                    <div className="w-4/5 h-[6px] bg-gradient-to-r from-[#B23AF2] via-[#5B39F3] to-[#94FAD5]"></div>
                </div>
                <div className="flex flex-col gap-[16px]">
                    <div className="flex justify-start items-start gap-[8px]">
                        <AiSparkle />
                        <div>
                            <h2 className="mb-[4px] text-[16px] font-semibold tracking-[-0.04px]">
                                Accelerate Development
                            </h2>
                            <p className="text-[16px] tracking-[-0.3px]">
                                Blitzy automates 80% of the development process.
                            </p>
                        </div>
                    </div>
                    <div className="flex justify-start items-start gap-[8px]">
                        <CodeBlock />
                        <div>
                            <h2 className="mb-[4px] text-[16px] font-semibold tracking-[-0.04px]">
                                Expert Support
                            </h2>
                            <p className="text-[16px] tracking-[-0.3px]">
                                Get our engineering guide to navigate the final
                                20% of your project.
                            </p>
                        </div>
                    </div>
                    {/* <div className="flex justify-start items-start gap-[8px]">
                        <CoinStack />
                        <div>
                            <h2 className="mb-[4px] text-[16px] font-semibold tracking-[-0.04px]">
                                Seamless Integration
                            </h2>
                            <p className="text-[16px] tracking-[-0.3px]">
                                Blitzy works seamlessly with your preferred
                                tools and workflows.
                            </p>
                        </div>
                    </div> */}
                </div>
            </div>
            {buttonText && (
                <div className="mt-[24px] w-full flex justify-end">
                    <div className="flex gap-[16px] sm:max-w-[280px]">
                        <div
                            role="button"
                            className="secondary-button"
                            onClick={handleClose}>
                            Cancel
                        </div>
                        <LoadingSubmitButton
                            loading={isTechSpecSubmitting}
                            onClick={handleStartBuilding}>
                            {buttonText}
                        </LoadingSubmitButton>
                    </div>
                </div>
            )}
        </div>
    );
}
