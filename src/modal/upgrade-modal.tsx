import {useCallback} from 'react';
import {useSubscriptionContext} from '@/context/subscription-context';
import iconCheck from './icons/check.svg';
import iconBlitzy from '/images/Blitzy-Icon.svg';
import iconClose from '/images/x-close.svg';
import {useParams} from 'react-router-dom';
interface UpgradeModalProps {
    isOpen: boolean;
    onClose: () => void;
}

export function UpgradeModal({isOpen, onClose}: UpgradeModalProps) {
    const {gotoPaymentLink} = useSubscriptionContext();
    const {projectId} = useParams();

    const features = [
        {
            title: 'Infinite code context — 100M+ lines of code',
            icon: iconCheck,
        },
        {
            title: 'Output up to 3M+ lines of code',
            icon: iconCheck,
        },
        {
            title: 'Increased code quality',
            icon: iconCheck,
        },
        {
            title: 'Enterprise-grade security',
            icon: iconCheck,
        },
    ];

    const handleUpgrade = useCallback(() => {
        gotoPaymentLink?.(projectId);
        if (onClose) onClose();
    }, [onClose, gotoPaymentLink, projectId]);

    const handleClose = useCallback(() => {
        if (onClose) onClose();
    }, [onClose]);

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black/40 flex items-center justify-center overflow-y-auto overflow-x-hidden z-50">
            <div className="relative min-h-[200px] w-full m-4 sm:m-auto sm:max-w-[760px]">
                <div className="bg-modal-feature-pane-custom-gradient feature-pane-enterprise-background bg-[#130342] px-[24px] pt-[56px] sm:p-[48px] pb-[36px] flex flex-col gap-[24px] rounded-none md:rounded-3xl relative">
                    <button
                        onClick={handleClose}
                        className="focus:outline-none focus-visible:ring-0 focus:ring-0 active:outline-none bg-transparent border-none mt-[24px] sm:mt-0 p-[10px] rounded-[8px] absolute right-3 top-3 flex justify-center items-center">
                        <img
                            className="w-[24px] h-[24px]"
                            src={iconClose}
                            alt="Close"
                        />
                    </button>
                    <div className="flex flex-col items-start gap-[24px]">
                        <div>
                            <img
                                src={iconBlitzy}
                                alt="Blitzy Logo"
                                className="w-[40px] h-[40px]"
                            />
                        </div>
                        <div>
                            <span className="text-white text-[40px] font-semibold leading-[44px]">
                                Upgrade plan
                            </span>
                        </div>
                    </div>
                    <div className="flex flex-col items-start gap-[16px]">
                        {features.map(({title, icon}, idx) => (
                            <div
                                key={idx}
                                className="flex items-start gap-[8px]">
                                <img
                                    className="block w-[24px] h-[24px]"
                                    src={icon}
                                    alt={`${title} Icon`}
                                />
                                <div>
                                    <span className="mb-[4px] text-white text-[16px] font-semibold">
                                        {title}
                                    </span>
                                </div>
                            </div>
                        ))}
                    </div>
                    <div className="flex flex-col sm:flex-row justify-start items-stretch sm:items-center gap-[12px]">
                        <div
                            role="button"
                            onClick={handleUpgrade}
                            className="px-[20px] py-[8px] bg-transparent text-white text-[16px] text-center font-semibold rounded-[32px] border border-white leading-[22px]">
                            Upgrade
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
