import {ReactNode, useCallback, useMemo} from 'react';
import {Dialog, DialogContent} from '@/components/ui/dialog';
import {PendingDocumentPane} from './pending-document-pane';
import {StartBuildingPane} from './start-building-pane';
import {useProjectContext} from '@/context/project-context';

const PROJECT_DIALOG_TYPES = ['pending-tech-spec', 'start-building'] as const;
export type ProjectDialogType = (typeof PROJECT_DIALOG_TYPES)[number];

function Modal({
    canClose = true,
    onClose,
    children,
}: {
    type: ProjectDialogType;
    canClose?: boolean;
    onClose: () => void;
    children: ReactNode;
}) {
    const handleClose = useCallback(() => {
        if (onClose) {
            onClose();
        }
    }, [onClose]);

    return (
        <Dialog open onOpenChange={handleClose}>
            <DialogContent
                className={`max-w-[760px] dialog-content ${canClose ? '' : '[&>button]:hidden'}`}>
                {children}
            </DialogContent>
        </Dialog>
    );
}

function Overlay({children}: {children: ReactNode}) {
    return (
        <div className="flex flex-col items-center h-full w-full">
            {children}
        </div>
    );
}

export function ProjectDialog({
    type,
    canClose = true,
    onClose,
    renderAs,
}: {
    type: ProjectDialogType;
    canClose?: boolean;
    onClose: () => void;
    renderAs: 'modal' | 'overlay';
}) {
    if (type === 'start-building') {
        renderAs = 'modal'; // 'start-building' must always be rendered as a modal
        canClose = false;
    }
    if (type === 'pending-tech-spec') {
        canClose = false;
    }

    const {project} = useProjectContext();
    const handleClose = useCallback(() => {
        if (onClose) {
            onClose();
        }
    }, [onClose]);

    const pane = useMemo(() => {
        return (
            <>
                {type === 'pending-tech-spec' && <PendingDocumentPane />}
                {type === 'start-building' && (
                    <StartBuildingPane handleClose={handleClose} />
                )}
            </>
        );
    }, [handleClose, type]);

    if (!project || !PROJECT_DIALOG_TYPES.includes(type)) return null;
    if (renderAs === 'modal') {
        return (
            <Modal type={type} canClose={canClose} onClose={handleClose}>
                {pane}
            </Modal>
        );
    } else if (renderAs === 'overlay') {
        return <Overlay>{pane}</Overlay>;
    }
}
