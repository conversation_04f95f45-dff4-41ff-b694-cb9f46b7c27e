import {useCallback, useEffect, useMemo, useState} from 'react';
import {useNavigate, useSearchParams} from 'react-router-dom';
import iconAzureDevOps from '../panel/workspace/icons/azure-devops.svg';

interface Props {
    handleClose: () => void;
}

export function AzureApprovalPane({handleClose}: Props) {
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();

    const error = searchParams.get('error');
    console.log('AzureApprovalPane error:', error);

    // State management
    const [installationStatus, setInstallationStatus] = useState<
        'SUCCESS' | 'FAILED' | null
    >(null);

    // Handle redirect after successful installation
    const handleSuccessfulInstallation = useCallback(() => {
        const success = !error;
        setInstallationStatus(success ? 'SUCCESS' : 'FAILED');

        if (success) {
            setTimeout(() => {
                handleClose();
                navigate('/workspace/projects', {replace: true});
            }, 3000); // delay to show success message
        } else {
            // Handle installation failure
            setTimeout(() => {
                handleClose();
            }, 3000); // delay to show error message
        }
    }, [handleClose, navigate, error]);

    // Watch for GitHub status changes after processing installation
    useEffect(() => {
        handleSuccessfulInstallation();
    }, [handleSuccessfulInstallation]);

    const displayText = useMemo(() => {
        if (installationStatus === null) return `Connecting Azure DevOps...`;
        if (installationStatus === 'SUCCESS') return `Connected successfully!`;
        if (installationStatus === 'FAILED')
            return `Connection failed. ${error}`;
    }, [installationStatus, error]);

    return (
        <div className="bg-black/20 fixed inset-0 flex flex-col items-center justify-center z-50">
            <div className="bg-white min-h-[100dvh] sm:min-h-[260px] p-[24px] w-full sm:w-[608px] flex flex-col justify-center items-center rounded-none sm:rounded-[24px] relative shadow-[0px_20px_24px_-4px_rgba(16,24,40,0.10),0px_8px_8px_-4px_rgba(16,24,40,0.04)]">
                <div className="flex flex-col items-center justify-center">
                    <img
                        src={iconAzureDevOps}
                        alt="Git platform"
                        className="mb-[24px] w-[64px] h-[64px]"
                    />

                    <h2 className="mb-[4px] text-[20px] sm:text-[24px] text-center font-semibold leading-[130%] text-black">
                        {displayText}
                    </h2>
                </div>
            </div>
        </div>
    );
}
