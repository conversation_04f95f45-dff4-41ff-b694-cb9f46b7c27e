import {useEffect} from 'react';

export default function BookMeetingPane() {
    useEffect(() => {
        // Dynamically load the script after the component mounts
        const script = document.createElement('script');
        script.src =
            'https://static.hsappstatic.net/MeetingsEmbed/ex/MeetingsEmbedCode.js';
        script.async = true;
        document.body.appendChild(script);

        // Cleanup on unmount
        return () => {
            document.body.removeChild(script);
        };
    }, []);

    return (
        <div
            className="meetings-iframe-container sm:mx-[40px] sm:mb-0"
            data-src="https://meetings.hubspot.com/brian1807/blitzy-meeting?embed=true"></div>
    );
}
