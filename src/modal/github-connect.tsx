import {useCallback} from 'react';
import iconCheck from '../panel/workspace/icons/check.svg';
import iconGithub from '../panel/workspace/icons/github.svg';
import {XClose} from '../modal/icons/x-close.tsx';

interface Props {
    handleClose: () => void;
}

export function GithubConnectPane({handleClose}: Props) {
    const handleConnectGithub = useCallback(() => {
        const url = import.meta.env.VITE_GITHUB_INSTALL_URL;
        if (url) {
            window.location.assign(url);
        }
    }, []);

    const handleCloseClick = useCallback(() => {
        handleClose();
    }, [handleClose]);

    return (
        <div className="bg-black/40 fixed inset-0  flex flex-col items-center justify-center z-50">
            <div className="bg-white h-[100dvh] sm:h-auto px-6 pt-16 sm:p-12 pb-9 flex flex-col w-full sm:m-0 sm:w-[500px] rounded-none sm:rounded-3xl relative shadow-[0px_20px_24px_-4px_rgba(16,24,40,0.10),0px_8px_8px_-4px_rgba(16,24,40,0.04)]">
                {/* Close Icon */}
                <button
                    onClick={handleCloseClick}
                    className="focus:outline-none focus-visible:ring-0 focus:ring-0 active:outline-none bg-transparent border-none p-3 rounded-lg absolute right-3 sm:right-3 sm:left-auto top-3 flex justify-center items-center">
                    <XClose />
                </button>

                {/* Content Container */}
                <div className="flex flex-col items-center justify-center flex-grow gap-[24px] min-h-[calc(100dvh-6rem)] sm:min-h-0">
                    {/* Main Content - Centered */}
                    <div className="flex flex-col items-center gap-0 justify-center">
                        <img
                            src={iconGithub}
                            alt="GitHub"
                            className="w-[64px] h-[64px]"
                        />

                        {/* Title and Subtitle */}
                        <div className="mt-[16px] mb-[24px] text-center">
                            <h2 className="text-[20px] sm:text-[24px] font-semibold leading-7 text-black">
                                Connect your GitHub account
                            </h2>
                            {/* <p className="text-sm leading-5 text-gray-600">
                                Sync your repo to auto-update code with document
                                changes.
                            </p> */}
                        </div>

                        {/* Features List */}
                        <div className="flex flex-col w-full text-[16px] leading-[150%] p-[24px] sm:p-[32px] gap-[24px] rounded-[24px] bg-[#F5F5F5]">
                            {[
                                'Create technical specs from your codebase',
                                'Add new features with natural language',
                                'Refactor outdated technologies easily',
                                'Sync your repo with your existing product',
                            ].map((text, index) => (
                                <div
                                    key={index}
                                    className="flex items-start gap-[8px]">
                                    <img
                                        src={iconCheck}
                                        alt="Check"
                                        className="w-[24px] h-[24px] flex-shrink-0"
                                    />
                                    <span className="text-[16px] leading-[150%] tracking-tight text-[#333]">
                                        {text}
                                    </span>
                                </div>
                            ))}
                        </div>
                    </div>

                    <button
                        onClick={handleConnectGithub}
                        className="primary-button w-fit">
                        Connect
                    </button>
                </div>
            </div>
        </div>
    );
}
