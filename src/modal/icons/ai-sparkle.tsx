interface AiSparkleProps {
    fill?: string;
    width?: number;
    height?: number;
}

export function AiSparkle({
    fill = '#5B39F3',
    width = 25,
    height = 24,
}: AiSparkleProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 25 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
                d="M9.86639 4.4412C10.11 3.78277 11.0413 3.78277 11.2849 4.4412L13.2776 9.82631C13.3542 10.0333 13.5174 10.1965 13.7244 10.2731L19.1095 12.2658C19.768 12.5094 19.768 13.4407 19.1095 13.6844L13.7244 15.677C13.5174 15.7536 13.3542 15.9168 13.2776 16.1238L11.2849 21.509C11.0413 22.1674 10.11 22.1674 9.86639 21.509L7.87372 16.1238C7.79712 15.9168 7.63391 15.7536 7.4269 15.677L2.04178 13.6844C1.38336 13.4407 1.38336 12.5094 2.04178 12.2658L7.4269 10.2731C7.63391 10.1965 7.79712 10.0333 7.87372 9.82631L9.86639 4.4412Z"
                fill={fill}
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M21.5121 1.69064C21.5121 1.03307 20.9791 0.5 20.3215 0.5C19.6639 0.5 19.1309 1.03307 19.1309 1.69064V3.11327H17.7082C17.0506 3.11327 16.5176 3.64634 16.5176 4.30391C16.5176 4.96148 17.0506 5.49454 17.7082 5.49454H19.1309V6.91718C19.1309 7.57475 19.6639 8.10782 20.3215 8.10782C20.9791 8.10782 21.5121 7.57475 21.5121 6.91718V5.49454H22.9348C23.5923 5.49454 24.1254 4.96148 24.1254 4.30391C24.1254 3.64634 23.5923 3.11327 22.9348 3.11327H21.5121V1.69064Z"
                fill={fill}
            />
        </svg>
    );
}
