interface XCloseProps {
    width?: number;
    height?: number;
    fill?: string;
}

export function XClose({
    width = 25,
    height = 24,
    fill = '#999999',
}: XCloseProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 25 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
                d="M18.125 6L6.125 18M6.125 6L18.125 18"
                stroke={fill}
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </svg>
    );
}
