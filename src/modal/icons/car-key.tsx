interface CarKeyProps {
    fill?: string;
    width?: number;
    height?: number;
}

export function CarKey({
    fill = '#333333',
    width = 24,
    height = 24,
}: CarKeyProps) {
    return (
        <svg
            width={width}
            height={height}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
                d="M15.6998 2H15.5198C13.3298 2 11.2598 3.21 9.98977 5.25C9.17977 6.55 8.86977 7.87 9.05977 9.28L2.89977 15.37C2.32977 15.93 2.00977 16.71 2.00977 17.5V19C2.00977 20.65 3.35977 22 5.00977 22H6.59977C7.39977 22 8.14977 21.69 8.71977 21.12L9.27977 20.56C9.53977 20.3 9.73977 19.98 9.85977 19.64C10.1998 19.52 10.5098 19.32 10.7798 19.06L11.2798 18.56C11.5398 18.3 11.7398 17.98 11.8598 17.64C12.1998 17.52 12.5098 17.32 12.7798 17.06L13.2798 16.56C13.5698 16.27 13.7898 15.91 13.8998 15.53C14.2498 15.42 14.5598 15.22 14.8298 14.96C15.0598 14.99 15.2798 15 15.5098 15C16.6498 15 17.7598 14.65 18.8098 13.97C20.9398 12.59 22.1298 10.41 21.9898 8.12C21.7898 4.74 19.0898 2.1 15.6998 2ZM17.7198 12.29C16.9198 12.81 16.1798 13 15.4998 13C15.0098 13 14.5498 12.9 14.1098 12.76L13.4298 13.52C13.3498 13.61 13.2398 13.65 13.1198 13.65C12.9698 13.65 12.8098 13.59 12.6398 13.46L11.9998 13V14.79C11.9998 14.92 11.9498 15.05 11.8498 15.14L11.3498 15.64C11.3045 15.6872 11.2502 15.7247 11.19 15.7504C11.1299 15.776 11.0652 15.7893 10.9998 15.7893C10.9344 15.7893 10.8697 15.776 10.8095 15.7504C10.7494 15.7247 10.695 15.6872 10.6498 15.64L9.99977 14.99V16.78C9.99977 16.91 9.94977 17.04 9.84977 17.13L9.34977 17.63C9.3045 17.6772 9.25016 17.7147 9.19001 17.7404C9.12987 17.766 9.06515 17.7793 8.99977 17.7793C8.93438 17.7793 8.86967 17.766 8.80952 17.7404C8.74937 17.7147 8.69503 17.6772 8.64977 17.63L7.99977 16.98V18.77C7.99977 18.9 7.94977 19.03 7.84977 19.12L7.28977 19.68C7.19633 19.7727 7.08551 19.846 6.96367 19.8958C6.84184 19.9455 6.71137 19.9708 6.57977 19.97H4.98977C4.43977 19.97 3.98977 19.52 3.98977 18.97V17.47C3.99031 17.3377 4.01711 17.2068 4.06861 17.0849C4.12011 16.9631 4.19528 16.8526 4.28977 16.76L11.2398 9.88C10.8898 8.82 10.8098 7.64 11.6698 6.27C12.5198 4.92 13.9198 3.96 15.5098 3.96H15.6298C17.9598 4.03 19.8498 5.88 19.9798 8.19C20.0798 9.85 19.0998 11.34 17.6998 12.24L17.7198 12.29Z"
                fill={fill}
            />
            <path
                d="M14 6.68982L17.31 9.99982C18.23 9.07982 18.23 7.59982 17.31 6.68982C16.39 5.77982 14.91 5.77982 14 6.68982Z"
                fill={fill}
            />
        </svg>
    );
}
