import {useCallback, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import {CheckBox} from './icons/checkbox';
import {UnCheckBox} from './icons/un-checkbox';
import {LayerPlus} from './icons/layer-plus';
import {CodeAlt} from './icons/code-alt';
import {logGAEvent} from '@/lib/utils';

export function ProjectTypePane() {
    const navigateTo = useNavigate();
    const [projectType, setProjectType] = useState<null | 'new' | 'existing'>(
        null,
    );

    const handleNewProject = useCallback(() => {
        navigateTo(
            {
                pathname: '/workspace/projects',
                search: '?popup=project-name',
            },
            {replace: true},
        );
    }, [navigateTo]);

    const handleCreateProject = useCallback(() => {
        if (projectType !== null && projectType === 'new') {
            logGAEvent('create_new_project', {projectType: 'new product'}); // Log event for new project
            handleNewProject();
        } else if (projectType !== null && projectType === 'existing') {
            logGAEvent('create_existing_project', {
                projectType: 'existing product',
            }); // Log event for existing project
            navigateTo('/workspace/back-prop/test-page-one', {replace: false});
        }
    }, [projectType, handleNewProject, navigateTo]);

    return (
        <>
            <DialogHeader>
                <DialogTitle>
                    What kind of project are you working on?
                </DialogTitle>
                <DialogDescription>Select the best fit.</DialogDescription>
            </DialogHeader>
            <div className="flex flex-col gap-6 flex-grow">
                <div
                    className={`p-6 border ${projectType === 'new' ? 'border-brand-purple bg-[#F2F0FE]' : 'border-[#999]'} rounded-3xl flex justify-normal items-start gap-4 cursor-pointer`}
                    onClick={() => setProjectType('new')}>
                    <div
                        className={`p-3 bg-[#F2F0FE] ${projectType === 'new' && 'bg-white'} rounded-2xl`}>
                        <LayerPlus />
                    </div>
                    <div className="flex-grow">
                        <span className="mb-2 px-2 py-1 font-semibold bg-secondary text-secondary-foreground rounded-full inline-block">
                            Free
                        </span>
                        <span className="mb-2 text-lg font-semibold block">
                            New product
                        </span>
                        <span className="block text-sm">
                            Create a brand new software solution tailored to
                            your business needs.
                        </span>
                    </div>
                    {projectType === 'new' ? <CheckBox /> : <UnCheckBox />}
                </div>
                <div
                    className={`p-6 border ${projectType === 'existing' ? 'border-brand-purple bg-[#F2F0FE]' : 'border-[#999]'} rounded-3xl flex justify-normal items-start gap-4 cursor-pointer`}
                    onClick={() => setProjectType('existing')}>
                    <div
                        className={`p-3 bg-[#F2F0FE] ${projectType === 'existing' && 'bg-white'} rounded-2xl`}>
                        <CodeAlt />
                    </div>
                    <div className="flex-grow">
                        <span className="mb-2 px-2 py-1 font-semibold bg-brand-purple text-primary-foreground rounded-full inline-block">
                            Enterprise Plan
                        </span>
                        <span className="mb-2 text-lg font-semibold block">
                            Existing product
                        </span>
                        <span className="block text-sm">
                            Add features to your existing products or refactor
                            your codebase.
                        </span>
                    </div>
                    {projectType === 'existing' ? <CheckBox /> : <UnCheckBox />}
                </div>
            </div>
            <DialogFooter>
                <div
                    role="button"
                    className="primary-button"
                    onClick={handleCreateProject}>
                    Create Project
                </div>
            </DialogFooter>
        </>
    );
}
