import {useCallback} from 'react';
import {useNavigate} from 'react-router-dom';
import iconBlitzy from '/images/Blitzy-Icon.svg';
// import iconCheck from '../icons/check.svg';
// import iconClose from '/images/x-close.svg';
// import {FeaturePaneProps} from '.';
import {useSubscriptionContext} from '@/context/subscription-context';
import {logGAEvent} from '@/lib/utils';
import {useGitInstallationsContext} from '@/context/git-installations-context';

// const features = [
//     {
//         title: 'Add features to existing code bases',
//         description: 'Expand your product’s capabilities with unlimited runs',
//         icon: iconCheck,
//     },
//     {
//         title: 'Refactor existing code bases',
//         description:
//             'Modernize your code for increased efficiency and scalability',
//         icon: iconCheck,
//     },
//     {
//         title: 'Dedicated instance of the platform',
//         description: 'Tailored to your business, with expert support',
//         icon: iconCheck,
//     },
// ];

const FeaturePaneTeam = ({onClose}: {onClose?: () => void}) => {
    const navigateTo = useNavigate();
    const {plan} = useSubscriptionContext();
    const {githubStatus, adoStatus} = useGitInstallationsContext();

    const handleNewProject = useCallback(() => {
        logGAEvent('lets_go_clicked', {
            plan,
            button_type: 'lets_go',
        });
        if (
            githubStatus !== 'ACTIVE' &&
            githubStatus !== 'PENDING' &&
            adoStatus !== 'ACTIVE' &&
            adoStatus !== 'PENDING'
        ) {
            navigateTo({
                pathname: '/workspace/projects',
                search: '?popup=github-enable',
            });
            return;
        }
        // navigateTo(
        //     {
        //         pathname: `/workspace/project-name`,
        //     },
        //     {replace: true},
        // );
        if (onClose) onClose();
    }, [navigateTo, plan, githubStatus, adoStatus, onClose]);

    return (
        <div className="bg-black/40 fixed inset-0  flex flex-col items-center justify-center z-50">
            <div className="bg-modal-feature-pane-custom-gradient feature-pane-enterprise-background bg-[#130342] px-[24px] pt-[56px] sm:p-[48px] pb-[36px] sm:pb-[36px] flex flex-col gap-[48px] grow sm:grow-0 w-full md:w-[760px] md:h-auto rounded-none md:rounded-3xl relative">
                {/* <button
                    onClick={handleClose}
                    className="focus:outline-none focus-visible:ring-0 focus:ring-0 active:outline-none  bg-transparent border-none mt-[24px] sm:mt-0 p-[10px] rounded-[8px] absolute right-3 top-3 flex justify-center items-center">
                    <img
                        className="w-[24px] h-[24px]"
                        src={iconClose}
                        alt="Close"
                    />
                </button> */}
                <div className="flex flex-col items-start gap-[24px]">
                    <div>
                        <img
                            src={iconBlitzy}
                            alt="Blitzy Logo"
                            className="w-[40px] h-[40px]"
                        />
                    </div>
                    <div>
                        <span className="text-white text-[40px] font-semibold leading-[44px]">
                            Welcome to
                        </span>
                        <br />
                        <span className="text-white text-[40px] font-semibold leading-[44px] mt-4">
                            Blitzy Team
                        </span>
                    </div>
                </div>
                {/* <div className="flex flex-col items-start gap-[16px]">
                    {features.map(({title, description, icon}, idx) => (
                        <div key={idx} className="flex items-start gap-[8px]">
                            <img
                                className="block w-[24px] h-[24px]"
                                src={icon}
                                alt={`${title} Icon`}
                            />
                            <div>
                                <span className="mb-[4px] text-white text-[16px] font-semibold">
                                    {title}
                                </span>
                                <br />
                                <span className="text-white text-[16px]">
                                    {description
                                        .split('\n')
                                        .map((line, index) => (
                                            <React.Fragment key={index}>
                                                {line}
                                                <br />
                                            </React.Fragment>
                                        ))}
                                </span>
                            </div>
                        </div>
                    ))}
                </div> */}
                <div className="flex flex-col sm:flex-row justify-start items-stretch sm:items-center gap-[12px]">
                    <div
                        role="button"
                        onClick={handleNewProject}
                        className="px-[20px] py-[8px] bg-transparent text-white text-[16px] text-center font-semibold rounded-[32px] border border-white leading-[22px]">
                        Let’s Go
                    </div>
                </div>
            </div>
        </div>
    );
};

export default FeaturePaneTeam;
