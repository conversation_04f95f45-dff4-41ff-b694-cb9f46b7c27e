import FeaturePaneFree from './feature-pane-free';
import FeaturePanePro from './feature-pane-pro';
import FeaturePaneEnterprise from './feature-pane-enterprise';
// import {useCallback} from 'react';
// import {useNavigate} from 'react-router-dom';
// import {useGithubInstallation} from '../../panel/workspace/hooks/github-status-hook';
import {useSubscriptionContext} from '@/context/subscription-context';
import FeaturePaneTeam from './feature-pane-team';

interface FeaturePaneProps {
    handleClose?: () => void;
}

export function FeaturePane({handleClose}: FeaturePaneProps) {
    // const navigateTo = useNavigate();
    const {plan} = useSubscriptionContext();
    // const {status} = useGithubInstallation();
    // const customHandleClose = useCallback(() => {
    //     const githubRedirect =
    //         import.meta.env.VITE_FORCE_REDIRECT_TO_GITHUB === 'true' &&
    //         plan === 'PRO' &&
    //         status === '404';
    //     if (githubRedirect) {
    //         navigateTo({
    //             pathname: '/workspace/projects',
    //             search: '?popup=github-connect',
    //         });
    //     } else {
    //         handleClose?.();
    //     }
    // }, [handleClose, navigateTo, plan, status]);

    if (plan === 'FREE') {
        return <FeaturePaneFree onClose={handleClose} />;
    } else if (plan === 'PRO') {
        return <FeaturePanePro onClose={handleClose} />;
    } else if (plan === 'TEAMS') {
        return <FeaturePaneTeam onClose={handleClose} />;
    } else if (plan === 'ENTERPRISE') {
        return <FeaturePaneEnterprise onClose={handleClose} />;
    } else return null;
}
