import {useCallback} from 'react';
import {useNavigate} from 'react-router-dom';
import iconBlitzy from '/images/Blitzy-Icon.svg';

// import iconSparkle from '/images/sparkle.svg';
// import iconVscode from '/images/vscode.svg';
// import iconCodeeye from '/images/codeeye.svg';
// import iconClose from '/images/x-close.svg';
// import {FeaturePaneProps} from '.';
// import {useSubscription} from '@/panel/workspace/hooks/subscription-hook';
import {logGAEvent} from '@/lib/utils';
// import {useGeolocation} from '@/panel/workspace/hooks/geolocation-hook';
import {useGitInstallationsContext} from '@/context/git-installations-context';

// const features = [
//     {
//         title: 'Autonomous Development',
//         description:
//             'Submit product requirements and get an advanced codebase, up to 300,000 lines of code ,\n in 8-12 hours.',
//         icon: iconSparkle,
//     },
//     {
//         title: 'Compute-Intensive Inference',
//         description:
//             'AI tools rush to generate code; Blitz<PERSON> meticulously plans, generates, and validates code before you see it.',
//         icon: iconVscode,
//     },
//     {
//         title: 'New AI and Human Collaboration',
//         description:
//             'Blitzy delivers ~80%,  providing a human project guide for the final 20% where Agents need support.',
//         icon: iconCodeeye,
//     },
// ];

const FeaturePaneFree = ({onClose}: {onClose?: () => void}) => {
    const navigateTo = useNavigate();
    // const {plan, gotoPaymentLink} = useSubscriptionContext();
    // const {userGeolocation} = useGeolocation();
    const {githubStatus, adoStatus} = useGitInstallationsContext();

    // const handleUpgrade = useCallback(() => {
    //     logGAEvent('start_trial_clicked', {
    //         plan,
    //         button_type: 'start_trial',
    //     });
    //     gotoPaymentLink();
    //     logSuccess('start_trial_clicked', {
    //         plan,
    //     });
    //     gotoPaymentLink();
    // }, [gotoPaymentLink, plan]);

    const handleNewProject = useCallback(() => {
        logGAEvent('continue_free_clicked', {
            // plan,
            button_type: 'continue_free',
        });
        if (
            githubStatus !== 'ACTIVE' &&
            githubStatus !== 'PENDING' &&
            adoStatus !== 'ACTIVE' &&
            adoStatus !== 'PENDING'
        ) {
            navigateTo({
                pathname: '/workspace/projects',
                search: '?popup=github-enable',
            });
            return;
        }
        if (onClose) onClose();
    }, [navigateTo, githubStatus, adoStatus, onClose]);

    return (
        // <div className="bg-black/40 fixed inset-0  flex flex-col items-center justify-center z-50">
        //     <div className="bg-modal-feature-pane-custom-gradient feature-pane-free-background px-[24px] pt-[48px] sm:p-[48px] pb-[36px] flex flex-col gap-[24px] grow sm:grow-0 md:w-[760px] md:h-auto rounded-none md:rounded-3xl relative">
        <div className="bg-black/40 fixed inset-0  flex flex-col items-center justify-center z-50">
            <div className="bg-modal-feature-pane-custom-gradient feature-pane-pro-background bg-[#130342] px-[24px] pt-[56px] sm:p-[48px] pb-[36px] sm:pb-[36px] flex flex-col gap-[48px] grow sm:grow-0 w-full md:w-[760px] md:h-auto rounded-none md:rounded-3xl relative">
                {/* <button
                    onClick={handleClose}
                    className="focus:outline-none focus-visible:ring-0 focus:ring-0 active:outline-none  bg-transparent border-none mt-[24px] sm:mt-0 p-[10px] rounded-[8px] absolute right-3 top-3 flex justify-center items-center">
                    <img
                        className="w-[24px] h-[24px]"
                        src={iconClose}
                        alt="Close"
                    />
                </button> */}
                <div className="flex flex-col items-start gap-[24px]">
                    <div>
                        <img
                            src={iconBlitzy}
                            alt="Blitzy Logo"
                            className="w-[40px] h-[40px]"
                        />
                    </div>
                    <div>
                        <span className="text-white text-[40px] font-semibold leading-[44px]">
                            Welcome to
                        </span>
                        <br />
                        <span className="text-white text-[40px] font-semibold leading-[44px]">
                            Blitzy Free
                        </span>
                    </div>
                </div>
                {/* <div className="flex flex-col items-start gap-[12px]">
                    {features.map(({title, description, icon}, idx) => (
                        <div key={idx} className="flex items-start gap-[8px]">
                            <img
                                className="block w-[24px] h-[24px]"
                                src={icon}
                                alt={`${title} Icon`}
                            />
                            <div>
                                <h2 className="mb-[4px] text-white text-[16px] font-semibold">
                                    {title}
                                </h2>
                                <p className="text-white text-[14px]">
                                    {description
                                        .split('\n')
                                        .map((line, index) => (
                                            <React.Fragment key={index}>
                                                {line}
                                                <br />
                                            </React.Fragment>
                                        ))}
                                </p>
                            </div>
                        </div>
                    ))}
                </div> */}
                <div className="flex flex-col sm:flex-row justify-start items-stretch sm:items-center gap-[12px]">
                    {/* <div
                        role="button"
                        onClick={handleUpgrade}
                        className="px-[20px] py-[8px] bg-white text-[#333] text-[16px] text-center font-semibold rounded-[32px] border border-white leading-[22px]">
                        {/* {userGeolocation?.country_code === 'US'
                            ? 'Start 7-day free trial'
                            : 'Upgrade to Pro'} */}

                    {/* Upgrade to Pro */}
                    {/* </div> */}

                    <div
                        role="button"
                        onClick={handleNewProject}
                        className="px-[20px] py-[8px] bg-transparent text-white text-[16px] text-center font-semibold rounded-[32px] border border-white leading-[22px]">
                        Let's Go
                    </div>
                </div>
            </div>
        </div>
    );
};

export default FeaturePaneFree;
