import {useCallback, useState} from 'react';
import {<PERSON>} from 'react-router-dom';
import iconGithub from '../panel/workspace/icons/github.svg';
import {UnCheckBox} from '@/modal/icons/un-checkbox.tsx';
import {CheckBox} from '@/modal/icons/checkbox.tsx';
import iconAzureDevOps from '../panel/workspace/icons/azure-devops.svg';
import {fetchAzureDevOpsInstallUrl} from '@/lib/backend';

interface Props {
    handleClose: () => void;
}
type GitPlatform = 'github' | 'azure-devops';

const gitPlatforms: {label: string; value: GitPlatform; icon: string}[] = [
    {label: 'GitHub', value: 'github', icon: iconGithub},
    {label: 'Azure DevOps', value: 'azure-devops', icon: iconAzureDevOps},
];
export function GithubEnablePane({handleClose}: Props) {
    const [selectedGitPlatform, setSelectedGitPlatform] =
        useState<GitPlatform>('github');
    const handleContinue = useCallback(async () => {
        if (!selectedGitPlatform) {
            return;
        }

        try {
            if (selectedGitPlatform === 'github') {
                const url = import.meta.env.VITE_GITHUB_INSTALL_URL;
                if (url) {
                    localStorage.setItem(
                        'github-connect-redirect-to-integration-tab',
                        'true',
                    );
                    window.location.assign(url);
                }
            } else if (selectedGitPlatform === 'azure-devops') {
                const url = await fetchAzureDevOpsInstallUrl();
                window.location.assign(url);
            }
        } catch (error) {
            console.error('Error connecting to Git platform:', error);
        }

        handleClose();
    }, [handleClose, selectedGitPlatform]);

    return (
        <div className="fixed bg-black/40 inset-0  flex flex-col items-center justify-center z-50">
            <div className="bg-gray-50 min-h-dvh sm:min-h-0 sm:h-auto flex flex-col w-full sm:m-0 sm:w-[608px] rounded-none sm:rounded-3xl relative shadow-[0px_20px_24px_-4px_rgba(16,24,40,0.10),0px_8px_8px_-4px_rgba(16,24,40,0.04)]">
                {/* Content Container */}
                <div className="flex flex-col items-start gap-[24px]  self-stretch">
                    <div className="flex flex-col p-[24px] pb-[12px] flex-grow gap-6 w-full">
                        <div className="text-black font-inter text-[24px] font-semibold leading-[130%] mb-2">
                            Connect a Git platform to generate code
                        </div>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-[12px]">
                            {gitPlatforms.map(platform => {
                                const isSelected =
                                    selectedGitPlatform === platform.value;
                                return (
                                    <div
                                        key={platform.value}
                                        className={`flex-1 p-[16px] sm:p-[24px] flex justify-between items-center gap-[16px] border ${isSelected ? 'bg-[#F2F0FE] border-[#5B39F3]' : 'bg-[#FFF] border-[#999]'} rounded-[24px] cursor-pointer`}
                                        onClick={() =>
                                            setSelectedGitPlatform(
                                                platform.value,
                                            )
                                        }>
                                        <div className="flex items-center gap-[16px]">
                                            <img
                                                src={platform.icon}
                                                alt={platform.label}
                                                className="w-[32px] h-[32px]"
                                            />
                                            <span className="text-[18px] font-semibold leading-[28px]">
                                                {platform.label}
                                            </span>
                                        </div>
                                        {isSelected ? (
                                            <CheckBox />
                                        ) : (
                                            <UnCheckBox />
                                        )}
                                    </div>
                                );
                            })}
                        </div>
                        <div className="w-full flex justify-start">
                            <Link
                                to="/workspace/settings/integrations"
                                replace={true}
                                onClick={handleClose}
                                className="cursor-pointer justify-start text-[#5B39F3] text-[16px] font-normal leading-[150%] tracking-[-0.3px] hover:underline">
                                Integration settings
                            </Link>
                        </div>
                    </div>

                    <div className="w-full flex flex-col sm:flex-row justify-end space-y-[12px] sm:space-y-0 sm:gap-[12px] p-[24px] pt-0 mt-auto sm:mt-2">
                        <div
                            role="button"
                            className="secondary-button"
                            onClick={handleClose}>
                            Cancel
                        </div>
                        <button
                            onClick={handleContinue}
                            className="primary-button">
                            Continue
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}
