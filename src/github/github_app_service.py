from typing import Optional

from blitzy_utils.common import blitzy_exponential_retry
from blitzy_utils.logger import logger
from src.api.models import CreateCommitInput, CreatePullRequestInput
from src.github.github_app_connection import GithubAppConnection
from src.github.github_repository import GithubRepository
from src.scm_base.base_classes import BaseAppService, BaseOrganization, BaseRepository
from src.scm_base.base_objects import BlitzyFileContent


class GithubAppService(BaseAppService):
    def __init__(self):
        self.connection = GithubAppConnection()

    @blitzy_exponential_retry()
    def create_branch(
        self, installation_id, repo_id, new_branch_name, base_branch="main"
    ):
        """
        Creates a new branch in the specified repository.

        :param installation_id: The installation ID used to get the GitHub client.
        :param repo_id: The ID of the repository where the branch will be created.
        :param new_branch_name: The name of the new branch to be created.
        :param base_branch: The name of the base branch to create the new branch from,
            defaults to "main".
        :return: A dictionary containing the status of the branch creation operation.
        """
        client = self.connection.get_client(installation_id)
        repo = GithubRepository(client, repo_id=repo_id)
        logger.info(f"Creating branch {new_branch_name} on repo {repo_id}")
        output = repo.create_branch(new_branch_name, base_branch)
        logger.info(f"Branch created status: {output['status']}")
        return output

    @blitzy_exponential_retry()
    def create_commit(
        self, repo_id, branch_name, installation_id: str, payload: CreateCommitInput
    ):
        """
        Create a new commit in a GitHub repository.

        :param repo_id: Identifier of the repository where the commit will be made.
        :param branch_name: Name of the branch on which the commit will be created.
        :param installation_id: Identifier of the GitHub App installation.
        :param payload: Input data containing commit message, files to commit.
        :return: Result of the commit operation.
        """
        client = self.connection.get_client(int(installation_id))
        repo = GithubRepository(client, repo_id=repo_id)
        output = repo.commit_changes(branch_name, payload.message, payload.files)
        logger.info(f"Total files committed: {len(payload.files)}")
        return output

    def create_pull_request(
        self, repo_id, installation_id: str, create_pr_request: CreatePullRequestInput
    ):
        """
        Creates a pull request in the specified repository.

        :param repo_id: Unique identifier of the repository where the pull request will
            be created.
        :param installation_id: Unique identifier of the GitHub App installation.
        :param create_pr_request: Object containing the details of the pull request to be created.
        :return: The created pull request object.
        """
        client = self.connection.get_client(int(installation_id))
        repo = GithubRepository(client, repo_id=repo_id)
        return repo.create_pull_request(create_pr_request)

    @blitzy_exponential_retry()
    def get_default_branch(self, repo_id, installation_id: str):
        """
        Fetches the default branch name of a specified GitHub repository. This function retrieves the client
        associated with the given installation ID, interacts with the repository using its ID, and fetches
        the name of the default branch.

        :param repo_id: The ID of the repository for which the default branch is to be retrieved.
        :param installation_id: The installation ID used to get the associated client.
        :return: The name of the default branch.
        """
        client = self.connection.get_client(int(installation_id))
        repo = GithubRepository(client, repo_id=repo_id)
        return repo.get_default_branch()

    @blitzy_exponential_retry()
    def get_branch_head_commit(self, repo_id, installation_id: str, branch_name: str):
        """
        Fetches the head commit of a specific branch from a given repository using the
        provided installation ID. This function utilizes the connection's client to
        retrieve the branch information and returns the latest commit of the branch.

        :param repo_id: The unique identifier for the repository.
        :param installation_id: The installation ID linked to the repository.
        :param branch_name: The name of the branch to retrieve the head commit from.
        :return: The latest commit of the specified branch.
        """
        client = self.connection.get_client(int(installation_id))
        repo = GithubRepository(client, repo_id=repo_id)
        branch_info = repo.get_branch_head_commit(branch_name)
        return branch_info

    @blitzy_exponential_retry()
    def manage_pull_request(
        self,
        user_id: str,
        pr_identifier: int,
        action: str,
        merge_method: str = "merge",
        **kwargs,
    ):
        """
        Manage a GitHub pull request.

        Required kwargs:
        - installation_id: GitHub App installation ID
        - repo_id: Repository ID
        """
        installation_id = kwargs.get("installation_id")
        repo_id = kwargs.get("repo_id")

        if not installation_id or not repo_id:
            raise ValueError(
                "installation_id and repo_id are required for GitHub operations"
            )

        client = self.connection.get_client(int(installation_id))
        repo = GithubRepository(client, repo_id=repo_id)
        return repo.manage_pull_request(user_id, pr_identifier, action, merge_method)

    @blitzy_exponential_retry()
    def get_file_content_by_path(self,
                                 installation_id: str,
                                 repo_id: str,
                                 path: str,
                                 ref: str | None = None
                                 ) -> BlitzyFileContent:
        """
        Retrieves the content of a file from a specified repository and branch.

        :param installation_id: The installation ID used to get the GitHub client.
        :param repo_id: The ID of the repository where the file is located.
        :param path: The path to the file within the repository.
        :param branch: The branch from which to retrieve the file.
        :return: The content of the file as a string.
        """
        client = self.connection.get_client(int(installation_id))
        repo = GithubRepository(client, repo_id=repo_id)
        return repo.get_file_content(path, ref)

    @blitzy_exponential_retry()
    def get_pr_status_by_repo_id_and_pr_number(self, repo_id: str, pr_number: int, installation_id: str):
        """
        Get PR status from GitHub API using repository ID and pull request number.
        This function fetches real-time data from GitHub and returns only the status.

        Args:
            repo_id: Repository ID
            pr_number: Pull request number
            installation_id: GitHub App installation ID

        Returns:
            PR status string (PENDING, MERGED, REJECTED, CLOSED, DONE) or None if not found
        """
        try:
            client = self.connection.get_client(int(installation_id))
            repo = GithubRepository(client, repo_id=repo_id)

            return repo.get_pr_status(pr_number)

        except Exception as e:
            logger.error(
                f"Error fetching PR status from GitHub for repo {repo_id}, PR {pr_number}: {str(e)}"
            )
            return None

    def get_organization(self, org_name: str, **kwargs) -> Optional[BaseOrganization]:
        """
        Get an organization instance.

        :param org_name: Name of the organization
        :return: Organization instance
        """
        pass

    def get_repository(self, repo_identifier: str, **kwargs) -> Optional[BaseRepository]:
        """
        Get a repository instance.

        :param repo_identifier: Repository identifier (ID, name, etc.)
        :return: Repository instance
        """
        pass

    def update_access_token(self, new_access_token: str):
        """
        Update the access token for the connection.

        :param new_access_token: Fresh access token
        """
        self.connection.update_access_token(new_access_token)
