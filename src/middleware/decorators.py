import base64
import json
import re
from functools import lru_cache, wraps
from typing import Any, Dict

from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import User
from firebase_admin import auth
from firebase_admin.auth import ExpiredIdTokenError, InvalidIdTokenError
from flask import request
from google.auth import jwt
from sqlalchemy.exc import IntegrityError
from tenacity import (retry, retry_if_exception_type, stop_after_attempt,
                      wait_exponential)

from src.api.models import CreateUserInput
from src.consts import AUTHORIZATION_HEADER_NAME
from src.error.errors import (AuthorizationTokenMissing, UserCreationError,
                              UserError)
from src.service.user_service import (create_user_using_firebase_user_id,
                                      save_user_geolocation)

API_GATEWAY_CUSTOM_HEADER = "X-Apigateway-Api-Userinfo"


def get_user_info(f=None, *, skip_user_verification=False):
    """
    A decorator function to get user information.
    :param f: Function which is passed to the decorator function.
    :return: returns a decorator
    """
    if f is None:
        return lambda f: get_user_info(f, skip_user_verification=skip_user_verification)

    @wraps(f)
    def decorated(*args, **kwargs):
        auth_header = request.headers.get(AUTHORIZATION_HEADER_NAME)
        if not auth_header:
            return {
                "message": "JWT Token not found.",
                "error": "Unauthorized"
            }, 401

        try:
            user_id = extract_user_id_factory()
            user_info = fetch_user_info(user_id)

            project_id = None
            if request.path:
                project_id_match = re.search(r'/v1/project/([^/]+)', request.path)
                if project_id_match:
                    project_id = project_id_match.group(1)

            logger_context = {"user_id": user_info.get("id")}
            if project_id:
                logger_context["project_id"] = project_id
            logger.set_context(**logger_context)

            if not skip_user_verification:
                verified = user_info.get("is_verified")
                if not verified:
                    return {
                        "message": "User is not verified.",
                        "error": "Unauthorized"
                    }, 401

        except AuthorizationTokenMissing as e:
            logger.warning(f"Failed to get user information with error: {e}")
            return {
                "message": "Token is expired.",
                "error": f"{e}"
            }, 401
        except ExpiredIdTokenError as e:
            logger.warning(f"Failed to get user information with error: {e}")
            return {
                "message": "Token is expired.",
                "error": "Unauthorized"
            }, 401
        except InvalidIdTokenError as e:
            logger.warning(f"Failed to get user information with error: {e}")
            return {
                "message": "Token is invalid.",
                "error": "Unauthorized"
            }, 401
        except UserError as e:
            logger.warning(f"Failed to get user information with error: {e}")
            return {
                "message": "Token is invalid.",
                "error": e
            }, 401
        except Exception as e:
            logger.warning(f"Failed to get user information with error: {e}")
            return {
                "message": "Something went wrong.",
                "data": None,
                "error": str(e)
            }, 500

        return f(user_info, *args, **kwargs)

    return decorated


def extract_user_id_from_token(token: str) -> str:
    """
    Extracts user id from Authentication token.
    :param token: Token received in Authorization header.
    """
    split_token = token.split(" ")
    decoded = jwt.decode(split_token[1], verify=False)
    user_id = decoded.get("uid")
    if not user_id:
        logger.warning(f"User ID not found in the token, decoded token: {decoded}.")
        raise Exception("User ID not found in the token")
    return user_id


@lru_cache(maxsize=128)
@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10),
    retry=retry_if_exception_type(UserCreationError)
)
def fetch_user_info(user_id: str) -> Dict[str, Any]:
    """
    Fetch user info from database else get it from auth provider and populate database.
    :param user_id: User ID.
    """
    # Here's how following code works.
    # First we check whether user exists in the database, if it exists we return the result.
    # If not we fetch user from auth provider(Firebase) and create user in the database and then return the result.
    try:
        with get_db_session() as session:
            # TODO (Chaitanya): This information should be cached.
            user = session.query(User).filter(User.user_id == user_id).first()
            if user:
                save_user_geolocation(user.id, session)
                return user.to_dict()

        logger.info(f"User with ID {user_id} does not exists, will export user data from firebase and create them.")
        user = register_user(user_id)
        return user
    except IntegrityError:
        raise UserCreationError(f"User creation failed for user with id {user_id}")
    except Exception as e:
        logger.warning(f"Failed to fetch user info for user with id {user_id}: {e}")
        raise Exception("Something went wrong.")


def extract_user_id_factory() -> str:
    custom_header = request.headers.get(API_GATEWAY_CUSTOM_HEADER)
    if custom_header:
        return extract_user_id_from_custom_token(custom_header)

    auth_header = request.headers.get(AUTHORIZATION_HEADER_NAME)
    if auth_header:
        return extract_user_id_from_token(auth_header)

    raise AuthorizationTokenMissing("Authentication token is missing.")


def extract_user_id_from_custom_token(custom_header: str) -> str:
    """
    Extracts user id from custom API gateway header `X-Apigateway-Api-Userinfo`.
    :param custom_header: Base64 string received in header.
    :return: User id.
    """
    padding = 4 - (len(custom_header) % 4)
    if padding != 4:
        custom_header += '=' * padding
    decoded_bytes = base64.b64decode(custom_header)
    decoded_string = decoded_bytes.decode('utf-8')
    json_dict = json.loads(decoded_string)

    if json_dict.get("uid"):
        return json_dict["uid"]

    raise Exception(f"UID is not available in custom header {API_GATEWAY_CUSTOM_HEADER}")


def register_user(firebase_user_id: str) -> Dict[str, Any]:
    """
    Registers a user in the system using Firebase user details. Fetches user
    information from Firebase, extracts necessary details, and creates a corresponding
    user in the system.

    :param firebase_user_id: The Firebase user ID of the user to be registered.
    :return: A User object corresponding to the registered Firebase user.
    """
    try:
        user_record = auth.get_user(firebase_user_id)
        user_payload = CreateUserInput(email=user_record.email, userId=firebase_user_id)

        with get_db_session() as session:
            user = create_user_using_firebase_user_id(user_payload, session)
            session.commit()
            return user
    except Exception as e:
        logger.warning(f"Failed to get user record for firebase user id {firebase_user_id}: {e}")
        raise Exception("Something went wrong.")
