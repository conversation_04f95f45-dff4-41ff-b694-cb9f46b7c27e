# Simplified Markdown Editor with TOC Design

## Overview

This design focuses on a frontend-centric approach where:
1. Frontend handles all markdown parsing, TOC generation, and editing
2. Changes are cached locally in the frontend
3. Full document is saved to backend only when user triggers save
4. Backend treats markdown as a simple text blob

## Architecture Philosophy

**Frontend-Heavy Approach:**
- Leverage existing TipTap editor with markdown extensions
- Use markdown AST parsing in frontend for TOC generation
- Local state management for all editing operations
- Periodic or manual save to backend

**Backend Simplicity:**
- Treat markdown as plain text content
- Simple CRUD operations for document storage
- No complex block-level parsing or management
- Existing tech spec document APIs can be reused

## Data Models

### Frontend TypeScript Interfaces

```typescript
// Enhanced TOC structure for frontend use
export interface TOCItem {
    id: string;
    title: string;
    level: number; // 1-6 for heading levels
    anchor: string; // URL fragment identifier
    children: TOCItem[];
    position: number; // Character position in document
    element?: HTMLElement; // DOM reference for scrolling
}

export interface MarkdownEditorState {
    documentId: string;
    content: string; // Full markdown content
    tableOfContents: TOCItem[];
    isDirty: boolean; // Has unsaved changes
    lastSaved: Date;
    isAutoSaveEnabled: boolean;
    selectedTOCItem?: string;
    editingSection?: {
        startPos: number;
        endPos: number;
        content: string;
    };
}

export interface DocumentMetadata {
    id: string;
    title: string;
    projectId: string;
    lastModified: Date;
    version: number;
    checksum: string; // For conflict detection
}
```

### Backend Models (Minimal Changes)

```python
# Reuse existing TechnicalSpec model or create simple MarkdownDocument
class MarkdownDocument(Base):
    __tablename__ = 'markdown_documents'
    
    id = Column(String, primary_key=True)
    project_id = Column(String, ForeignKey('projects.id'), nullable=False)
    title = Column(String, nullable=False)
    content = Column(Text, nullable=False)  # Full markdown content
    version = Column(Integer, default=1)
    checksum = Column(String, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
```

## Frontend Implementation

### 1. TOC Generator Service

```typescript
// src/services/TOCGeneratorService.ts
import { marked } from 'marked';

export class TOCGeneratorService {
    
    generateTOC(markdownContent: string): TOCItem[] {
        const tokens = marked.lexer(markdownContent);
        const tocItems: TOCItem[] = [];
        const stack: TOCItem[] = [];
        let position = 0;
        
        for (const token of tokens) {
            if (token.type === 'heading') {
                const tocItem: TOCItem = {
                    id: this.generateId(),
                    title: token.text,
                    level: token.depth,
                    anchor: this.generateAnchor(token.text),
                    children: [],
                    position: position
                };
                
                // Build hierarchy
                while (stack.length > 0 && stack[stack.length - 1].level >= token.depth) {
                    stack.pop();
                }
                
                if (stack.length === 0) {
                    tocItems.push(tocItem);
                } else {
                    stack[stack.length - 1].children.push(tocItem);
                }
                
                stack.push(tocItem);
            }
            
            position += this.calculateTokenLength(token);
        }
        
        return tocItems;
    }
    
    private generateAnchor(text: string): string {
        return text
            .toLowerCase()
            .replace(/[^\w\s-]/g, '')
            .replace(/\s+/g, '-')
            .trim();
    }
    
    private generateId(): string {
        return `toc-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    
    private calculateTokenLength(token: any): number {
        // Calculate approximate character length of token in original markdown
        switch (token.type) {
            case 'heading':
                return `${'#'.repeat(token.depth)} ${token.text}\n\n`.length;
            case 'paragraph':
                return `${token.text}\n\n`.length;
            case 'code':
                return `\`\`\`${token.lang || ''}\n${token.text}\n\`\`\`\n\n`.length;
            default:
                return token.raw?.length || 0;
        }
    }
}
```

### 2. Enhanced Markdown Editor Component

```typescript
// src/components/markdown/EnhancedMarkdownEditor.tsx
import { useState, useEffect, useCallback, useRef } from 'react';
import { TiptapEditor, IEditor } from '@/components/editor/TiptapEditor';
import { TOCNavigationPanel } from './TOCNavigationPanel';
import { TOCGeneratorService } from '@/services/TOCGeneratorService';

interface EnhancedMarkdownEditorProps {
    documentId: string;
    initialContent?: string;
    onSave?: (content: string) => Promise<void>;
    autoSaveInterval?: number; // milliseconds
}

export const EnhancedMarkdownEditor: React.FC<EnhancedMarkdownEditorProps> = ({
    documentId,
    initialContent = '',
    onSave,
    autoSaveInterval = 30000 // 30 seconds
}) => {
    const [editorState, setEditorState] = useState<MarkdownEditorState>({
        documentId,
        content: initialContent,
        tableOfContents: [],
        isDirty: false,
        lastSaved: new Date(),
        isAutoSaveEnabled: true,
        selectedTOCItem: undefined,
        editingSection: undefined
    });
    
    const [isLoading, setIsLoading] = useState(false);
    const [saveStatus, setSaveStatus] = useState<'saved' | 'saving' | 'error'>('saved');
    
    const editorRef = useRef<IEditor>(null);
    const tocGenerator = useRef(new TOCGeneratorService());
    const autoSaveTimer = useRef<NodeJS.Timeout>();
    
    // Generate TOC when content changes
    const updateTOC = useCallback((content: string) => {
        const toc = tocGenerator.current.generateTOC(content);
        setEditorState(prev => ({
            ...prev,
            tableOfContents: toc
        }));
    }, []);
    
    // Handle content changes
    const handleContentChange = useCallback((newContent: string) => {
        setEditorState(prev => ({
            ...prev,
            content: newContent,
            isDirty: true
        }));
        
        // Debounced TOC update
        clearTimeout(autoSaveTimer.current);
        autoSaveTimer.current = setTimeout(() => {
            updateTOC(newContent);
        }, 500);
        
        // Auto-save setup
        if (editorState.isAutoSaveEnabled && onSave) {
            clearTimeout(autoSaveTimer.current);
            autoSaveTimer.current = setTimeout(() => {
                handleSave(newContent);
            }, autoSaveInterval);
        }
    }, [editorState.isAutoSaveEnabled, onSave, autoSaveInterval, updateTOC]);
    
    // Save function
    const handleSave = useCallback(async (content?: string) => {
        const contentToSave = content || editorState.content;
        
        if (!onSave || !editorState.isDirty) return;
        
        setSaveStatus('saving');
        try {
            await onSave(contentToSave);
            setEditorState(prev => ({
                ...prev,
                isDirty: false,
                lastSaved: new Date()
            }));
            setSaveStatus('saved');
        } catch (error) {
            console.error('Save failed:', error);
            setSaveStatus('error');
        }
    }, [editorState.content, editorState.isDirty, onSave]);
    
    // TOC navigation
    const handleTOCItemClick = useCallback((tocItem: TOCItem) => {
        setEditorState(prev => ({
            ...prev,
            selectedTOCItem: tocItem.id
        }));
        
        // Scroll to position in editor
        if (editorRef.current) {
            editorRef.current.commands.focus();
            // Find and scroll to the heading
            const editor = editorRef.current;
            const doc = editor.state.doc;
            
            // Simple approach: search for heading text
            let pos = 0;
            doc.descendants((node, nodePos) => {
                if (node.type.name === 'heading' && 
                    node.textContent === tocItem.title) {
                    editor.commands.setTextSelection(nodePos);
                    return false; // Stop iteration
                }
            });
        }
    }, []);
    
    // Initialize TOC
    useEffect(() => {
        if (initialContent) {
            updateTOC(initialContent);
        }
    }, [initialContent, updateTOC]);
    
    // Keyboard shortcuts
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                handleSave();
            }
        };
        
        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [handleSave]);
    
    return (
        <div className="enhanced-markdown-editor">
            {/* Header with save controls */}
            <div className="editor-header">
                <div className="save-controls">
                    <button 
                        onClick={() => handleSave()}
                        disabled={!editorState.isDirty || saveStatus === 'saving'}
                        className="save-btn"
                    >
                        {saveStatus === 'saving' ? 'Saving...' : 'Save'}
                    </button>
                    
                    <span className="save-status">
                        {saveStatus === 'saved' && editorState.isDirty && 'Unsaved changes'}
                        {saveStatus === 'saved' && !editorState.isDirty && 'All changes saved'}
                        {saveStatus === 'saving' && 'Saving...'}
                        {saveStatus === 'error' && 'Save failed'}
                    </span>
                    
                    <label className="auto-save-toggle">
                        <input
                            type="checkbox"
                            checked={editorState.isAutoSaveEnabled}
                            onChange={(e) => setEditorState(prev => ({
                                ...prev,
                                isAutoSaveEnabled: e.target.checked
                            }))}
                        />
                        Auto-save
                    </label>
                </div>
            </div>
            
            <div className="editor-content">
                {/* TOC Sidebar */}
                <div className="toc-sidebar">
                    <TOCNavigationPanel
                        tocItems={editorState.tableOfContents}
                        selectedItemId={editorState.selectedTOCItem}
                        onItemClick={handleTOCItemClick}
                    />
                </div>
                
                {/* Main Editor */}
                <div className="editor-main">
                    <TiptapEditor
                        ref={editorRef}
                        markdown={editorState.content}
                        onChange={handleContentChange}
                        editable={true}
                        docType="markdown"
                    />
                </div>
            </div>
        </div>
    );
};
```

### 3. TOC Navigation Panel Component

```typescript
// src/components/markdown/TOCNavigationPanel.tsx
import { useState, useMemo } from 'react';
import { ChevronRight, ChevronDown, Search } from 'lucide-react';

interface TOCNavigationPanelProps {
    tocItems: TOCItem[];
    selectedItemId?: string;
    onItemClick: (item: TOCItem) => void;
}

export const TOCNavigationPanel: React.FC<TOCNavigationPanelProps> = ({
    tocItems,
    selectedItemId,
    onItemClick
}) => {
    const [searchTerm, setSearchTerm] = useState('');
    const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

    // Filter TOC items based on search
    const filteredTOC = useMemo(() => {
        if (!searchTerm) return tocItems;

        const filterItems = (items: TOCItem[]): TOCItem[] => {
            return items.reduce((acc, item) => {
                const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase());
                const filteredChildren = filterItems(item.children);

                if (matchesSearch || filteredChildren.length > 0) {
                    acc.push({
                        ...item,
                        children: filteredChildren
                    });
                }

                return acc;
            }, [] as TOCItem[]);
        };

        return filterItems(tocItems);
    }, [tocItems, searchTerm]);

    const toggleExpanded = (itemId: string) => {
        setExpandedItems(prev => {
            const newSet = new Set(prev);
            if (newSet.has(itemId)) {
                newSet.delete(itemId);
            } else {
                newSet.add(itemId);
            }
            return newSet;
        });
    };

    const renderTOCItem = (item: TOCItem, depth = 0) => {
        const hasChildren = item.children.length > 0;
        const isExpanded = expandedItems.has(item.id);
        const isSelected = selectedItemId === item.id;

        return (
            <div key={item.id} className="toc-item-container">
                <div
                    className={`toc-item level-${item.level} depth-${depth} ${isSelected ? 'selected' : ''}`}
                    style={{ paddingLeft: `${depth * 16 + 8}px` }}
                >
                    {hasChildren && (
                        <button
                            className="expand-toggle"
                            onClick={() => toggleExpanded(item.id)}
                        >
                            {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                        </button>
                    )}

                    <button
                        className="toc-link"
                        onClick={() => onItemClick(item)}
                        title={item.title}
                    >
                        <span className="toc-title">{item.title}</span>
                        <span className="toc-level">H{item.level}</span>
                    </button>
                </div>

                {hasChildren && isExpanded && (
                    <div className="toc-children">
                        {item.children.map(child => renderTOCItem(child, depth + 1))}
                    </div>
                )}
            </div>
        );
    };

    return (
        <div className="toc-navigation-panel">
            <div className="toc-header">
                <h3>Table of Contents</h3>

                <div className="toc-search">
                    <Search size={16} />
                    <input
                        type="text"
                        placeholder="Search sections..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="search-input"
                    />
                </div>
            </div>

            <div className="toc-content">
                {filteredTOC.length > 0 ? (
                    filteredTOC.map(item => renderTOCItem(item))
                ) : (
                    <div className="no-results">
                        {searchTerm ? 'No matching sections found' : 'No headings found'}
                    </div>
                )}
            </div>
        </div>
    );
};
```

## Backend Implementation (Minimal)

### 1. Simple Document API

```python
# src/api/routes/markdown_documents.py
from flask import Blueprint, request, jsonify
from flask_utils.decorators import flask_pydantic_response, validate_request
from src.api.models.markdown_models import *

markdown_bp = Blueprint('markdown', __name__)

@markdown_bp.route("/<project_id>/documents/<document_id>", methods=["GET"])
@flask_pydantic_response
def get_markdown_document(project_id: str, document_id: str):
    """Get full markdown document content"""
    document = get_document_by_id(document_id, project_id)
    if not document:
        raise DocumentNotFoundError(f"Document {document_id} not found")

    return GetDocumentResponse(
        id=document.id,
        title=document.title,
        content=document.content,
        version=document.version,
        lastModified=document.updated_at,
        checksum=document.checksum
    ), 200

@markdown_bp.route("/<project_id>/documents/<document_id>", methods=["PUT"])
@validate_request(UpdateDocumentRequest)
@flask_pydantic_response
def update_markdown_document(project_id: str, document_id: str, payload: UpdateDocumentRequest):
    """Update full markdown document content"""
    document = get_document_by_id(document_id, project_id)
    if not document:
        raise DocumentNotFoundError(f"Document {document_id} not found")

    # Simple conflict detection
    if payload.version != document.version:
        raise ConflictError("Document has been modified by another user")

    # Update document
    new_checksum = calculate_checksum(payload.content)
    document.content = payload.content
    document.version += 1
    document.checksum = new_checksum
    document.updated_at = datetime.utcnow()

    db.session.commit()

    return UpdateDocumentResponse(
        success=True,
        version=document.version,
        lastModified=document.updated_at
    ), 200

@markdown_bp.route("/<project_id>/documents", methods=["POST"])
@validate_request(CreateDocumentRequest)
@flask_pydantic_response
def create_markdown_document(project_id: str, payload: CreateDocumentRequest):
    """Create new markdown document"""
    document = MarkdownDocument(
        id=generate_uuid(),
        project_id=project_id,
        title=payload.title,
        content=payload.content or "",
        version=1,
        checksum=calculate_checksum(payload.content or "")
    )

    db.session.add(document)
    db.session.commit()

    return CreateDocumentResponse(
        id=document.id,
        version=document.version
    ), 201
```

### 2. API Models

```python
# src/api/models/markdown_models.py
from pydantic import BaseModel
from datetime import datetime
from typing import Optional

class GetDocumentResponse(BaseModel):
    id: str
    title: str
    content: str
    version: int
    lastModified: datetime
    checksum: str

class UpdateDocumentRequest(BaseModel):
    content: str
    version: int

class UpdateDocumentResponse(BaseModel):
    success: bool
    version: int
    lastModified: datetime

class CreateDocumentRequest(BaseModel):
    title: str
    content: Optional[str] = ""

class CreateDocumentResponse(BaseModel):
    id: str
    version: int
```

## Data Flow Diagram

```mermaid
sequenceDiagram
    participant U as User
    participant TOC as TOC Panel
    participant E as Editor
    participant LS as Local State
    participant API as Backend API
    participant DB as Database

    Note over U,DB: Document Loading
    U->>E: Open Document
    E->>API: GET /documents/{id}
    API->>DB: Fetch document
    DB-->>API: Return content
    API-->>E: Return markdown content
    E->>E: Parse markdown & generate TOC
    E->>TOC: Update TOC structure
    TOC-->>U: Display navigation

    Note over U,DB: Editing Flow
    U->>TOC: Click section
    TOC->>E: Navigate to section
    U->>E: Edit content
    E->>LS: Cache changes locally
    E->>E: Regenerate TOC (debounced)
    E->>TOC: Update TOC structure

    Note over U,DB: Save Flow
    alt Auto-save or Manual Save
        E->>API: PUT /documents/{id}
        API->>API: Validate version
        API->>DB: Update full content
        DB-->>API: Confirm update
        API-->>E: Return new version
        E->>LS: Mark as saved
    end
```

## Styling (CSS)

```css
/* src/components/markdown/markdown-editor.css */
.enhanced-markdown-editor {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.editor-header {
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
}

.save-controls {
    display: flex;
    align-items: center;
    gap: 16px;
}

.save-btn {
    px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50;
}

.save-status {
    font-size: 14px;
    color: #6b7280;
}

.editor-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.toc-sidebar {
    width: 300px;
    border-right: 1px solid #e5e7eb;
    background: #f9fafb;
    overflow-y: auto;
}

.editor-main {
    flex: 1;
    overflow: hidden;
}

/* TOC Styles */
.toc-navigation-panel {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.toc-header {
    padding: 16px;
    border-bottom: 1px solid #e5e7eb;
}

.toc-search {
    position: relative;
    margin-top: 12px;
}

.toc-search input {
    width: 100%;
    padding: 8px 12px 8px 32px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
}

.toc-search svg {
    position: absolute;
    left: 8px;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
}

.toc-content {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;
}

.toc-item {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    margin: 1px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.15s;
}

.toc-item:hover {
    background: #e5e7eb;
}

.toc-item.selected {
    background: #dbeafe;
    color: #1d4ed8;
}

.toc-link {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-align: left;
    border: none;
    background: none;
    padding: 4px 0;
    font-size: 14px;
}

.toc-level {
    font-size: 12px;
    color: #9ca3af;
    font-weight: 500;
}

.expand-toggle {
    margin-right: 4px;
    padding: 2px;
    border: none;
    background: none;
    color: #6b7280;
}
```
