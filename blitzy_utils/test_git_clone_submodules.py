#!/usr/bin/env python3
"""
Test script to verify the Azure DevOps GIT CLONE submodule fix is working.
This specifically tests the git clone implementation, not the API-based approach.
"""

import os
import sys
import tempfile
import shutil
import logging

def test_git_clone_submodules():
    """Test that git clone submodules are properly handled."""
    
    # Set up logging to see what's happening
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)
    
    try:
        # Change to the blitzy_utils directory to handle relative imports
        original_cwd = os.getcwd()
        blitzy_utils_dir = os.path.join(os.path.dirname(__file__), 'blitzy_utils')
        os.chdir(blitzy_utils_dir)

        # Import the git clone implementation (not the API-based one)
        from blitzy_utils.azure_git_clone import clone_azure_devops_repository_with_auth
        
        logger.info("🧪 Testing Azure DevOps GIT CLONE submodule fix...")
        
        # Test data - using the repository with submodules
        test_data = {
            "organization": "blitzy-qa",
            "project": "hello-world-qa",
            "repo_name": "19July_Lakshya_back_prop",
            "branch_name": "ADO_Branch_10AM", 
            "commit_hash": "34fcbe813b071f8fb8e264ca54075bef38594700",  # Latest commit with .gitmodules
        }
        
        logger.info(f"📦 Testing with repo: {test_data['repo_name']}")
        logger.info(f"🌿 Branch: {test_data['branch_name']}")
        logger.info(f"📝 Commit: {test_data['commit_hash']}")
        
        # Clean up any existing clone
        clone_path = f"blitzy/{test_data['repo_name']}/{test_data['branch_name']}"
        if os.path.exists(clone_path):
            logger.info(f"🧹 Cleaning up existing clone: {clone_path}")
            shutil.rmtree(clone_path)
        
        # Call the git clone function directly
        logger.info("🚀 Calling clone_azure_devops_repository_with_auth...")
        
        # Get access token from environment or service
        from blitzy_utils.service_client import get_access_token
        access_token = get_access_token()

        # Change back to original directory for file operations
        os.chdir(original_cwd)
        
        result_path = clone_azure_devops_repository_with_auth(
            organization=test_data['organization'],
            project=test_data['project'],
            repo_name=test_data['repo_name'],
            branch_name=test_data['branch_name'],
            commit_hash=test_data['commit_hash'],
            access_token=access_token
        )
        
        logger.info(f"✅ Git clone completed! Repository cloned to: {result_path}")
        
        # Verify the clone exists and has .git directory
        if os.path.exists(result_path):
            logger.info(f"✅ Cloned repository found at: {result_path}")
            
            # Check if it's a proper git repository
            git_dir = os.path.join(result_path, '.git')
            if os.path.exists(git_dir):
                logger.info("✅ Repository is a proper git repository (.git directory exists)")
            else:
                logger.warning("⚠️  No .git directory found - this might not be a git clone")
            
            # Check if .gitmodules exists
            gitmodules_path = os.path.join(result_path, '.gitmodules')
            if os.path.exists(gitmodules_path):
                logger.info("✅ .gitmodules file exists in cloned repository")
                
                # Read the .gitmodules content
                with open(gitmodules_path, 'r') as f:
                    gitmodules_content = f.read()
                logger.info(f"📄 .gitmodules content:\n{gitmodules_content}")
                
                # Check if submodule directory exists
                if 'path = libs/mysubmodule' in gitmodules_content:
                    submodule_path = os.path.join(result_path, 'libs', 'mysubmodule')
                    if os.path.exists(submodule_path):
                        logger.info("🎉 SUCCESS: Submodule directory exists!")
                        
                        # Check if submodule has content
                        submodule_files = os.listdir(submodule_path)
                        if submodule_files:
                            logger.info(f"📁 Submodule contains {len(submodule_files)} files: {submodule_files}")
                            
                            # Check if it's a proper git repository
                            if '.git' in submodule_files:
                                logger.info("✅ Submodule is a proper git repository")
                                
                                # Check for actual content files
                                content_files = [f for f in submodule_files if f != '.git']
                                if content_files:
                                    logger.info(f"📄 Submodule content files: {content_files}")
                                    
                                    # Read a sample file to verify content
                                    if 'README.md' in content_files:
                                        readme_path = os.path.join(submodule_path, 'README.md')
                                        with open(readme_path, 'r') as f:
                                            readme_content = f.read()[:200]
                                        logger.info(f"📄 Submodule README.md preview: {readme_content}...")
                                    
                                    logger.info("🎯 GIT CLONE SUBMODULE TEST PASSED!")
                                    return True
                                else:
                                    logger.warning("⚠️  Submodule directory exists but has no content files")
                            else:
                                logger.warning("⚠️  Submodule directory exists but is not a git repository")
                        else:
                            logger.warning("⚠️  Submodule directory exists but is empty")
                    else:
                        logger.warning("❌ Submodule directory does not exist")
                        logger.info("🔧 This indicates the submodule update step needs improvement")
                        return False
            else:
                logger.warning("❌ .gitmodules file not found in cloned repository")
        else:
            logger.error(f"❌ Cloned repository not found at: {result_path}")
            return False
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ Failed to import git clone functions: {e}")
        return False
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Azure DevOps Git Clone Submodule Test")
    print("=" * 50)
    
    success = test_git_clone_submodules()
    if success:
        print("\n🎉 GIT CLONE SUBMODULE TEST PASSED!")
        print("✅ Azure DevOps git clone submodule handling is working correctly")
    else:
        print("\n💥 GIT CLONE SUBMODULE TEST FAILED!")
        print("❌ Azure DevOps git clone submodule handling needs attention")
        sys.exit(1)
