{"tests": [{"test": "Real Azure DevOps Download", "status": "PASS", "files_count": 7, "total_size": 11046, "file_types": {"xml": 1, "gitmodules": 1, "md": 2, "gitignore": 1, "gitattributes": 1, "no_ext": 1}, "sample_files": [{"path": "pom.xml", "size": 3209}, {"path": ".git<PERSON><PERSON><PERSON>", "size": 133}, {"path": "README.md", "size": 5892}, {"path": ".giti<PERSON>re", "size": 302}, {"path": ".gitattributes", "size": 32}]}, {"test": "Direct Azure DevOps Download", "status": "PASS", "files_count": 7}, {"test": "Disk Files Check", "status": "PASS", "clone_dirs_found": 3}, {"test": "Direct Azure DevOps Download", "status": "PASS", "files_count": 7}, {"test": "Disk Files and Submodule Check", "status": "PASS", "clone_dirs_found": 57, "submodule_support_detected": false}, {"test": "Git Clone Submodule Support", "status": "PASS", "recurse_submodules_flag": true, "submodule_references": true}], "summary": {"total": 6, "passed": 6, "failed": 0, "errors": 0}}