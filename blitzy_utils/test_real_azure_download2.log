🧪 Testing REAL Azure DevOps Repository Download
======================================================================
📦 Importing REAL Azure DevOps download functions...
✅ Successfully imported REAL download functions!

1️⃣ Testing REAL download_repository_to_disk for Azure DevOps
🔄 Testing with repo: 19July_Lakshya_back_prop
🔄 Branch: ADO_Branch_10AM
🔄 Commit: 4a30f0519248898f6e2f0cb7ab457cd19179f25e
🔄 Git Project Repo ID: 36c8d0c2-4d87-4658-920e-68d9c9754ef6
{"timestamp": "2025-07-24T22:10:36.934634+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Getting head commit hash for 19July_La<PERSON>hya_back_prop with git_project_repo_id 36c8d0c2-4d87-4658-920e-68d9c9754ef6", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:36.934734+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Fetching service type from https://archie-github-handler-480762617400.us-central1.run.app/v1/users/2887a75e-9b01-4787-a4a0-bee4e919f4f8/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/svc-type", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:36.989184+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): oauth2.googleapis.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.098632+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://oauth2.googleapis.com:443 \"POST /token HTTP/1.1\" 200 None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.101154+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): archie-github-handler-480762617400.us-central1.run.app:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.416514+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://archie-github-handler-480762617400.us-central1.run.app:443 \"GET /v1/users/2887a75e-9b01-4787-a4a0-bee4e919f4f8/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/svc-type HTTP/1.1\" 200 27", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.416985+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Service type: AZURE_DEVOPS", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.417318+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Processing repository 19July_Lakshya_back_prop with service type: AZURE_DEVOPS", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.417362+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Setting up repository access", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.417392+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Fetching Azure DevOps credentials from URL: https://archie-github-handler-480762617400.us-central1.run.app/v1/github/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/access-token", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.471268+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): oauth2.googleapis.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.539667+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://oauth2.googleapis.com:443 \"POST /token HTTP/1.1\" 200 None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.540498+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): archie-github-handler-480762617400.us-central1.run.app:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.748166+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://archie-github-handler-480762617400.us-central1.run.app:443 \"GET /v1/github/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/access-token HTTP/1.1\" 200 2458", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.748428+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Successfully fetched Azure DevOps credentials by repo id", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.748468+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Received response from fetching Azure DevOps credentials", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.748487+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Organization name: blitzy-qa, Organization ID: 4b316c82-e2a7-42d3-bd0d-7d2c5933b791", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.748642+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Azure DevOps organization: 4b316c82-e2a7-42d3-bd0d-7d2c5933b791, project_id: 1810cd4a-6912-4f05-b39f-b93d75822f2d", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.748659+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Getting repository information for repo_id: d9db696a-b353-40f3-9e42-07ccd5f6c947, repo_name: 19July_Lakshya_back_prop, to check if repo exists", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.748728+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.767094+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.771374+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.771592+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops._file_cache", "message": "Loading cache file: /Users/<USER>/.azure-devops/python-sdk/cache/resources.json", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.771664+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops._file_cache", "message": "attempting to read file /Users/<USER>/.azure-devops/python-sdk/cache/resources.json as utf-8-sig", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.772080+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.774395+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.774503+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops._file_cache", "message": "Loading cache file: /Users/<USER>/.azure-devops/python-sdk/cache/options.json", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.774561+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops._file_cache", "message": "attempting to read file /Users/<USER>/.azure-devops/python-sdk/cache/options.json as utf-8-sig", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.775396+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "File cache hit for options on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.786620+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: {project}/_apis/{area}/{resource}/{repositoryId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.786855+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.786884+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.786930+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.786994+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.787013+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.787031+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.787059+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.787631+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.963684+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947 HTTP/1.1\" 200 690", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.963993+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"id\":\"d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"name\":\"19July_Lakshya_back_prop\",\"url\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"project\":{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"},\"defaultBranch\":\"refs/heads/main\",\"size\":4989,\"remoteUrl\":\"https://<EMAIL>/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"sshUrl\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\",\"webUrl\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"_links\":{\"self\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\"},\"project\":{\"href\":\"vstfs:///Classification/TeamProject/1810cd4a-6912-4f05-b39f-b93d75822f2d\"},\"web\":{\"href\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\"},\"ssh\":{\"href\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\"},\"commits\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/commits\"},\"refs\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/refs\"},\"pullRequests\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pullRequests\"},\"items\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/items\"},\"pushes\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pushes\"}},\"isDisabled\":false,\"isInMaintenance\":false}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.965262+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Found repository: {'id': 'd9db696a-b353-40f3-9e42-07ccd5f6c947', 'name': '19July_Lakshya_back_prop', 'url': 'https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947', 'default_branch': 'main'}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.965291+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Attempting to download Azure DevOps repository using git clone: 19July_Lakshya_back_prop", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.965335+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.966499+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.967672+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.967719+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.969841+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.969916+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: {project}/_apis/{area}/{resource}/{repositoryId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.969962+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.969980+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.969996+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.970024+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.970038+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.970053+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.970064+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:37.970499+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.129617+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947 HTTP/1.1\" 200 690", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.130007+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"id\":\"d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"name\":\"19July_Lakshya_back_prop\",\"url\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"project\":{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"},\"defaultBranch\":\"refs/heads/main\",\"size\":4989,\"remoteUrl\":\"https://<EMAIL>/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"sshUrl\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\",\"webUrl\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"_links\":{\"self\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\"},\"project\":{\"href\":\"vstfs:///Classification/TeamProject/1810cd4a-6912-4f05-b39f-b93d75822f2d\"},\"web\":{\"href\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\"},\"ssh\":{\"href\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\"},\"commits\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/commits\"},\"refs\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/refs\"},\"pullRequests\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pullRequests\"},\"items\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/items\"},\"pushes\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pushes\"}},\"isDisabled\":false,\"isInMaintenance\":false}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.130381+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.132891+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.135633+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.135679+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.138624+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.138713+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: _apis/{resource}/{*projectId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.138797+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.138830+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/_apis/projects", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.138852+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.138895+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.138918+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.138953+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.138973+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.139729+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.249911+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/_apis/projects HTTP/1.1\" 200 334", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.250194+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"count\":1,\"value\":[{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"}]}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.250441+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Found project ID '1810cd4a-6912-4f05-b39f-b93d75822f2d' with name: hello-world-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.250552+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Removing existing directory: blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.258828+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Cloning Azure DevOps repository: https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop to blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.777982+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Azure DevOps repository cloned successfully to: blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.874205+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Checking out specific commit: 4a30f0519248898f6e2f0cb7ab457cd19179f25e", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.887230+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Checked out commit: 4a30f0519248898f6e2f0cb7ab457cd19179f25e", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.887619+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 1: pom.xml", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.887717+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 2: README.md", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.887786+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 3: .gitignore", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.887850+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 4: .gitattributes", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.887911+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 5: Jenkins", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.888437+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Total files read from cloned repository: 5", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.888468+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Successfully downloaded 5 files from Azure DevOps repository using git clone", "service": "archie-service-backend"}
✅ download_repository_to_disk completed!
📊 Files downloaded: 5
📄 Sample files:
   1. pom.xml (3209 bytes)
   2. README.md (5892 bytes)
   3. .gitignore (302 bytes)
   4. .gitattributes (32 bytes)
   5. Jenkins (512 bytes)
📊 File types: {'gitattributes': 1, 'gitignore': 1, 'md': 1, 'no_ext': 1, 'xml': 1}
📊 Total content size: 9947 bytes

2️⃣ Testing REAL _download_all_git_files_to_disk_azure_devops
🔄 Testing direct Azure DevOps download function...
{"timestamp": "2025-07-24T22:10:38.888549+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Setting up repository access", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.888569+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Fetching Azure DevOps credentials from URL: https://archie-github-handler-480762617400.us-central1.run.app/v1/github/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/access-token", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:38.935208+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): oauth2.googleapis.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.001123+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://oauth2.googleapis.com:443 \"POST /token HTTP/1.1\" 200 None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.002011+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): archie-github-handler-480762617400.us-central1.run.app:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.204209+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://archie-github-handler-480762617400.us-central1.run.app:443 \"GET /v1/github/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/access-token HTTP/1.1\" 200 2458", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.204503+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Successfully fetched Azure DevOps credentials by repo id", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.204549+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Received response from fetching Azure DevOps credentials", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.204572+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Organization name: blitzy-qa, Organization ID: 4b316c82-e2a7-42d3-bd0d-7d2c5933b791", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.204751+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Azure DevOps organization: 4b316c82-e2a7-42d3-bd0d-7d2c5933b791, project_id: 1810cd4a-6912-4f05-b39f-b93d75822f2d", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.204770+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Getting repository information for repo_id: d9db696a-b353-40f3-9e42-07ccd5f6c947, repo_name: 19July_Lakshya_back_prop, to check if repo exists", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.204822+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.206386+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.207748+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.207806+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.210218+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.210319+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: {project}/_apis/{area}/{resource}/{repositoryId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.210369+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.210388+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.210407+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.210455+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.210485+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.210525+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.210537+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.211068+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.344004+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947 HTTP/1.1\" 200 690", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.344350+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"id\":\"d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"name\":\"19July_Lakshya_back_prop\",\"url\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"project\":{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"},\"defaultBranch\":\"refs/heads/main\",\"size\":4989,\"remoteUrl\":\"https://<EMAIL>/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"sshUrl\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\",\"webUrl\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"_links\":{\"self\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\"},\"project\":{\"href\":\"vstfs:///Classification/TeamProject/1810cd4a-6912-4f05-b39f-b93d75822f2d\"},\"web\":{\"href\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\"},\"ssh\":{\"href\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\"},\"commits\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/commits\"},\"refs\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/refs\"},\"pullRequests\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pullRequests\"},\"items\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/items\"},\"pushes\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pushes\"}},\"isDisabled\":false,\"isInMaintenance\":false}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.344635+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Found repository: {'id': 'd9db696a-b353-40f3-9e42-07ccd5f6c947', 'name': '19July_Lakshya_back_prop', 'url': 'https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947', 'default_branch': 'main'}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.344677+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Attempting to download Azure DevOps repository using git clone: 19July_Lakshya_back_prop", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.344732+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.346655+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.348313+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.348363+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.351401+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.351492+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: {project}/_apis/{area}/{resource}/{repositoryId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.351553+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.351578+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.351598+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.351635+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.351654+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.351716+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.351762+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.352596+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.469580+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947 HTTP/1.1\" 200 690", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.469769+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"id\":\"d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"name\":\"19July_Lakshya_back_prop\",\"url\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"project\":{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"},\"defaultBranch\":\"refs/heads/main\",\"size\":4989,\"remoteUrl\":\"https://<EMAIL>/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"sshUrl\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\",\"webUrl\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"_links\":{\"self\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\"},\"project\":{\"href\":\"vstfs:///Classification/TeamProject/1810cd4a-6912-4f05-b39f-b93d75822f2d\"},\"web\":{\"href\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\"},\"ssh\":{\"href\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\"},\"commits\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/commits\"},\"refs\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/refs\"},\"pullRequests\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pullRequests\"},\"items\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/items\"},\"pushes\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pushes\"}},\"isDisabled\":false,\"isInMaintenance\":false}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.469945+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.471148+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.472238+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.472268+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.475441+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.475535+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: _apis/{resource}/{*projectId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.475573+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.475589+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/_apis/projects", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.475603+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.475624+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.475640+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.475652+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.475663+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.475988+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.587759+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/_apis/projects HTTP/1.1\" 200 334", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.588051+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"count\":1,\"value\":[{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"}]}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.588316+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Found project ID '1810cd4a-6912-4f05-b39f-b93d75822f2d' with name: hello-world-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.588419+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Removing existing directory: blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:39.593083+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Cloning Azure DevOps repository: https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop to blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:40.027718+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Azure DevOps repository cloned successfully to: blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:40.113709+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Checking out specific commit: 4a30f0519248898f6e2f0cb7ab457cd19179f25e", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:40.125960+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Checked out commit: 4a30f0519248898f6e2f0cb7ab457cd19179f25e", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:40.126387+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 1: pom.xml", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:40.126521+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 2: README.md", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:40.126601+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 3: .gitignore", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:40.126666+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 4: .gitattributes", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:40.126730+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 5: Jenkins", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:40.127249+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Total files read from cloned repository: 5", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:10:40.127280+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Successfully downloaded 5 files from Azure DevOps repository using git clone", "service": "archie-service-backend"}
✅ _download_all_git_files_to_disk_azure_devops completed!
📊 Files downloaded: 5
📄 Sample files:
   1. pom.xml
      Content preview: <?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         x...
   2. README.md
      Content preview:  # :fallen_leaf: :leaves: Testinium-QA :leaves: :fallen_leaf:
Automating the Testinium browser  (JAV...
   3. .gitignore
      Content preview: # Compiled class file
*.class
configuration.properties

# Log file
*.log

# BlueJ files
*.ctxt

# Mo...

3️⃣ Testing for actual cloned repository files on disk
🔍 Looking for cloned repositories in: /var/folders/m3/nysj737d3bx2tp1525d0ln0m0000gn/T
📁 Found 3 potential clone directories:
   📁 /var/folders/m3/nysj737d3bx2tp1525d0ln0m0000gn/T/azure_real_test_m2nk39_i
      📊 Contains 0 files
   📁 /var/folders/m3/nysj737d3bx2tp1525d0ln0m0000gn/T/azure_real_test_m2nk39_i
      📊 Contains 0 files
   📁 /var/folders/m3/nysj737d3bx2tp1525d0ln0m0000gn/T/real_azure_test_e6lrpm3z
      📊 Contains 0 files

======================================================================
🎯 REAL AZURE DEVOPS DOWNLOAD TEST SUMMARY
======================================================================
📊 Total Tests: 3
✅ Passed: 3
❌ Failed: 0
💥 Errors: 0

🎉 ALL REAL DOWNLOAD TESTS PASSED!
✅ Real Azure DevOps repository download is working correctly

💾 Results saved to: real_azure_download_results.json
