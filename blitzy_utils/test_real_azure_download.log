🧪 Testing REAL Azure DevOps Repository Download
======================================================================
📦 Importing REAL Azure DevOps download functions...
✅ Successfully imported REAL download functions!

1️⃣ Testing REAL download_repository_to_disk for Azure DevOps
🔄 Testing with repo: 19July_Lakshya_back_prop
🔄 Branch: ADO_Branch_10AM
🔄 Commit: 4a30f0519248898f6e2f0cb7ab457cd19179f25e
🔄 Git Project Repo ID: 36c8d0c2-4d87-4658-920e-68d9c9754ef6
{"timestamp": "2025-07-24T21:57:26.244926+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Getting head commit hash for 19July_La<PERSON>hya_back_prop with git_project_repo_id 36c8d0c2-4d87-4658-920e-68d9c9754ef6", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:26.245024+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Fetching service type from https://archie-github-handler-480762617400.us-central1.run.app/v1/users/2887a75e-9b01-4787-a4a0-bee4e919f4f8/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/svc-type", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:26.302398+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): oauth2.googleapis.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:26.401916+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://oauth2.googleapis.com:443 \"POST /token HTTP/1.1\" 200 None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:26.403451+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): archie-github-handler-480762617400.us-central1.run.app:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:26.654040+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://archie-github-handler-480762617400.us-central1.run.app:443 \"GET /v1/users/2887a75e-9b01-4787-a4a0-bee4e919f4f8/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/svc-type HTTP/1.1\" 200 27", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:26.654229+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Service type: AZURE_DEVOPS", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:26.654406+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Processing repository 19July_Lakshya_back_prop with service type: AZURE_DEVOPS", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:26.654431+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Setting up repository access", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:26.654447+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Fetching Azure DevOps credentials from URL: https://archie-github-handler-480762617400.us-central1.run.app/v1/github/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/access-token", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:26.702227+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): oauth2.googleapis.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:26.902270+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://oauth2.googleapis.com:443 \"POST /token HTTP/1.1\" 200 None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:26.904184+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): archie-github-handler-480762617400.us-central1.run.app:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.119437+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://archie-github-handler-480762617400.us-central1.run.app:443 \"GET /v1/github/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/access-token HTTP/1.1\" 200 2458", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.119757+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Successfully fetched Azure DevOps credentials by repo id", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.119806+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Received response from fetching Azure DevOps credentials", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.119830+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Organization name: blitzy-qa, Organization ID: 4b316c82-e2a7-42d3-bd0d-7d2c5933b791", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.119998+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Azure DevOps organization: 4b316c82-e2a7-42d3-bd0d-7d2c5933b791, project_id: 1810cd4a-6912-4f05-b39f-b93d75822f2d", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.120020+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Getting repository information for repo_id: d9db696a-b353-40f3-9e42-07ccd5f6c947, repo_name: 19July_Lakshya_back_prop, to check if repo exists", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.120088+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.155275+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.160242+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.160454+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops._file_cache", "message": "Loading cache file: /Users/<USER>/.azure-devops/python-sdk/cache/resources.json", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.160544+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops._file_cache", "message": "attempting to read file /Users/<USER>/.azure-devops/python-sdk/cache/resources.json as utf-8-sig", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.160976+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.163507+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.163643+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops._file_cache", "message": "Loading cache file: /Users/<USER>/.azure-devops/python-sdk/cache/options.json", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.163726+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops._file_cache", "message": "attempting to read file /Users/<USER>/.azure-devops/python-sdk/cache/options.json as utf-8-sig", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.164596+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "File cache hit for options on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.177700+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: {project}/_apis/{area}/{resource}/{repositoryId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.177985+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.178018+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.178166+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.178305+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.178350+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.178375+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.178426+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.181105+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.407106+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947 HTTP/1.1\" 200 690", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.407505+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"id\":\"d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"name\":\"19July_Lakshya_back_prop\",\"url\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"project\":{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"},\"defaultBranch\":\"refs/heads/main\",\"size\":4989,\"remoteUrl\":\"https://<EMAIL>/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"sshUrl\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\",\"webUrl\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"_links\":{\"self\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\"},\"project\":{\"href\":\"vstfs:///Classification/TeamProject/1810cd4a-6912-4f05-b39f-b93d75822f2d\"},\"web\":{\"href\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\"},\"ssh\":{\"href\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\"},\"commits\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/commits\"},\"refs\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/refs\"},\"pullRequests\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pullRequests\"},\"items\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/items\"},\"pushes\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pushes\"}},\"isDisabled\":false,\"isInMaintenance\":false}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.409731+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Found repository: {'id': 'd9db696a-b353-40f3-9e42-07ccd5f6c947', 'name': '19July_Lakshya_back_prop', 'url': 'https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947', 'default_branch': 'main'}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.409776+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Attempting to download Azure DevOps repository using git clone: 19July_Lakshya_back_prop", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.409837+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.411420+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.412889+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.412937+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.415589+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.415675+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: {project}/_apis/{area}/{resource}/{repositoryId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.415741+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.415769+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.415786+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.415821+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.415838+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.415858+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.415873+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.416461+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.539114+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947 HTTP/1.1\" 200 690", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.539379+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"id\":\"d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"name\":\"19July_Lakshya_back_prop\",\"url\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"project\":{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"},\"defaultBranch\":\"refs/heads/main\",\"size\":4989,\"remoteUrl\":\"https://<EMAIL>/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"sshUrl\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\",\"webUrl\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"_links\":{\"self\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\"},\"project\":{\"href\":\"vstfs:///Classification/TeamProject/1810cd4a-6912-4f05-b39f-b93d75822f2d\"},\"web\":{\"href\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\"},\"ssh\":{\"href\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\"},\"commits\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/commits\"},\"refs\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/refs\"},\"pullRequests\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pullRequests\"},\"items\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/items\"},\"pushes\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pushes\"}},\"isDisabled\":false,\"isInMaintenance\":false}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.539608+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.540953+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.543970+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.544018+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.546254+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.546311+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: _apis/{resource}/{*projectId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.546360+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.546380+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/_apis/projects", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.546393+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.546420+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.546433+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.546447+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.546459+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.546962+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.663746+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/_apis/projects HTTP/1.1\" 200 334", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.664055+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"count\":1,\"value\":[{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"}]}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.664326+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Found project ID '1810cd4a-6912-4f05-b39f-b93d75822f2d' with name: hello-world-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.664450+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Removing existing directory: blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:27.672871+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Cloning Azure DevOps repository: https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop to blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.189390+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Azure DevOps repository cloned successfully to: blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.198608+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "No .gitmodules file found, skipping submodule auth configuration", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.198682+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Checking out specific commit: 4a30f0519248898f6e2f0cb7ab457cd19179f25e", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.209530+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Checked out commit: 4a30f0519248898f6e2f0cb7ab457cd19179f25e", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.209878+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 1: pom.xml", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.209956+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 2: README.md", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.210028+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 3: .gitignore", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.210094+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 4: .gitattributes", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.210153+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 5: Jenkins", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.210589+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Total files read from cloned repository: 5", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.210613+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Successfully downloaded 5 files from Azure DevOps repository using git clone", "service": "archie-service-backend"}
✅ download_repository_to_disk completed!
📊 Files downloaded: 5
📄 Sample files:
   1. pom.xml (3209 bytes)
   2. README.md (5892 bytes)
   3. .gitignore (302 bytes)
   4. .gitattributes (32 bytes)
   5. Jenkins (512 bytes)
📊 File types: {'gitattributes': 1, 'gitignore': 1, 'md': 1, 'no_ext': 1, 'xml': 1}
📊 Total content size: 9947 bytes

2️⃣ Testing REAL _download_all_git_files_to_disk_azure_devops
🔄 Testing direct Azure DevOps download function...
{"timestamp": "2025-07-24T21:57:28.210687+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Setting up repository access", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.210708+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Fetching Azure DevOps credentials from URL: https://archie-github-handler-480762617400.us-central1.run.app/v1/github/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/access-token", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.258901+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): oauth2.googleapis.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.330913+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://oauth2.googleapis.com:443 \"POST /token HTTP/1.1\" 200 None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.331901+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): archie-github-handler-480762617400.us-central1.run.app:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.633477+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://archie-github-handler-480762617400.us-central1.run.app:443 \"GET /v1/github/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/access-token HTTP/1.1\" 200 2458", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.633722+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Successfully fetched Azure DevOps credentials by repo id", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.633770+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Received response from fetching Azure DevOps credentials", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.633797+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Organization name: blitzy-qa, Organization ID: 4b316c82-e2a7-42d3-bd0d-7d2c5933b791", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.633949+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Azure DevOps organization: 4b316c82-e2a7-42d3-bd0d-7d2c5933b791, project_id: 1810cd4a-6912-4f05-b39f-b93d75822f2d", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.633969+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Getting repository information for repo_id: d9db696a-b353-40f3-9e42-07ccd5f6c947, repo_name: 19July_Lakshya_back_prop, to check if repo exists", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.634023+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.635707+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.637245+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.637337+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.640190+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.640316+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: {project}/_apis/{area}/{resource}/{repositoryId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.640377+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.640400+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.640421+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.640479+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.640500+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.640519+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.640533+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.641047+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.760302+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947 HTTP/1.1\" 200 690", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.760515+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"id\":\"d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"name\":\"19July_Lakshya_back_prop\",\"url\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"project\":{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"},\"defaultBranch\":\"refs/heads/main\",\"size\":4989,\"remoteUrl\":\"https://<EMAIL>/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"sshUrl\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\",\"webUrl\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"_links\":{\"self\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\"},\"project\":{\"href\":\"vstfs:///Classification/TeamProject/1810cd4a-6912-4f05-b39f-b93d75822f2d\"},\"web\":{\"href\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\"},\"ssh\":{\"href\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\"},\"commits\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/commits\"},\"refs\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/refs\"},\"pullRequests\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pullRequests\"},\"items\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/items\"},\"pushes\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pushes\"}},\"isDisabled\":false,\"isInMaintenance\":false}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.760690+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Found repository: {'id': 'd9db696a-b353-40f3-9e42-07ccd5f6c947', 'name': '19July_Lakshya_back_prop', 'url': 'https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947', 'default_branch': 'main'}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.760712+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Attempting to download Azure DevOps repository using git clone: 19July_Lakshya_back_prop", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.760742+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.761861+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.762970+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.763009+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.765129+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.765208+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: {project}/_apis/{area}/{resource}/{repositoryId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.765256+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.765274+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.765288+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.765316+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.765329+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.765342+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.765353+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.765794+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.879878+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947 HTTP/1.1\" 200 690", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.880261+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"id\":\"d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"name\":\"19July_Lakshya_back_prop\",\"url\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"project\":{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"},\"defaultBranch\":\"refs/heads/main\",\"size\":4989,\"remoteUrl\":\"https://<EMAIL>/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"sshUrl\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\",\"webUrl\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"_links\":{\"self\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\"},\"project\":{\"href\":\"vstfs:///Classification/TeamProject/1810cd4a-6912-4f05-b39f-b93d75822f2d\"},\"web\":{\"href\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\"},\"ssh\":{\"href\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\"},\"commits\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/commits\"},\"refs\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/refs\"},\"pullRequests\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pullRequests\"},\"items\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/items\"},\"pushes\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pushes\"}},\"isDisabled\":false,\"isInMaintenance\":false}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.880631+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.883146+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.885458+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.885531+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.889830+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.890000+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: _apis/{resource}/{*projectId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.890084+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.890120+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/_apis/projects", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.890151+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.890197+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.890222+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.890248+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.890270+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:28.891016+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:29.006627+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/_apis/projects HTTP/1.1\" 200 334", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:29.006814+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"count\":1,\"value\":[{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"}]}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:29.006966+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Found project ID '1810cd4a-6912-4f05-b39f-b93d75822f2d' with name: hello-world-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:29.007026+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Removing existing directory: blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:29.010746+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Cloning Azure DevOps repository: https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop to blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:29.554129+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Azure DevOps repository cloned successfully to: blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:29.564286+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "No .gitmodules file found, skipping submodule auth configuration", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:29.564378+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Checking out specific commit: 4a30f0519248898f6e2f0cb7ab457cd19179f25e", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:29.577097+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Checked out commit: 4a30f0519248898f6e2f0cb7ab457cd19179f25e", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:29.577529+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 1: pom.xml", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:29.577613+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 2: README.md", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:29.577682+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 3: .gitignore", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:29.577751+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 4: .gitattributes", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:29.577814+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 5: Jenkins", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:29.578366+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Total files read from cloned repository: 5", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T21:57:29.578395+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Successfully downloaded 5 files from Azure DevOps repository using git clone", "service": "archie-service-backend"}
✅ _download_all_git_files_to_disk_azure_devops completed!
📊 Files downloaded: 5
📄 Sample files:
   1. pom.xml
      Content preview: <?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         x...
   2. README.md
      Content preview:  # :fallen_leaf: :leaves: Testinium-QA :leaves: :fallen_leaf:
Automating the Testinium browser  (JAV...
   3. .gitignore
      Content preview: # Compiled class file
*.class
configuration.properties

# Log file
*.log

# BlueJ files
*.ctxt

# Mo...

3️⃣ Testing for actual cloned repository files on disk
🔍 Looking for cloned repositories in: /var/folders/m3/nysj737d3bx2tp1525d0ln0m0000gn/T
📁 Found 3 potential clone directories:
   📁 /var/folders/m3/nysj737d3bx2tp1525d0ln0m0000gn/T/azure_real_test_m2nk39_i
      📊 Contains 0 files
   📁 /var/folders/m3/nysj737d3bx2tp1525d0ln0m0000gn/T/azure_real_test_m2nk39_i
      📊 Contains 0 files
   📁 /var/folders/m3/nysj737d3bx2tp1525d0ln0m0000gn/T/real_azure_test_e6lrpm3z
      📊 Contains 0 files

======================================================================
🎯 REAL AZURE DEVOPS DOWNLOAD TEST SUMMARY
======================================================================
📊 Total Tests: 3
✅ Passed: 3
❌ Failed: 0
💥 Errors: 0

🎉 ALL REAL DOWNLOAD TESTS PASSED!
✅ Real Azure DevOps repository download is working correctly

💾 Results saved to: real_azure_download_results.json
