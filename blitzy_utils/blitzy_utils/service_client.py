from dataclasses import dataclass
from typing import Optional, Dict, Any, Callable
from google.auth import default
from google.auth.transport.requests import Request
from google.oauth2 import id_token
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import os

from blitzy_utils.logger import logger


@dataclass
class ServiceConfig:
    """
    Configuration for a service endpoint.

    :param url: Service base URL
    :param timeout: Request timeout in seconds (default is 30)
    :param max_retries: Maximum number of retry attempts (default is 3)
    :param retry_status_codes: HTTP status codes to trigger retry (default is (408, 429, 500, 502, 503, 504))
    """
    url: str
    timeout: int = 30
    max_retries: int = 3
    retry_status_codes: tuple = (408, 429, 500, 502, 503, 504)


class ServiceClient:
    """Client for making authenticated requests to Cloud Run services."""

    def __init__(self, env_prefix: str = "SERVICE_URL_"):
        """
        Initialize the client and auto-register services from environment variables.

        :param env_prefix: Prefix for environment variables containing service URLs.
                         Example: If prefix is "SERVICE_URL_", looks for env vars like
                         SERVICE_URL_SECRETS, SERVICE_URL_AUTH, etc.
        :type env_prefix: str

        :raises google.auth.exceptions.DefaultCredentialsError: If default credentials cannot be obtained
        :raises ValueError: If environment variables with the specified prefix are malformed
        """
        self._services: Dict[str, ServiceConfig] = {}
        self._session = None
        self._credentials, self._project = default()
        self._env_prefix = env_prefix
        self._register_services_from_env()

    def _register_services_from_env(self) -> None:
        """Automatically register services from environment variables."""
        for env_var, url in os.environ.items():
            if env_var.startswith(self._env_prefix):
                service_name = env_var[len(self._env_prefix):].lower()
                self.add_service(service_name, ServiceConfig(url=url))
                logger.debug(f"Auto-registered service {service_name} from environment variable {env_var}")

    def _create_session(self) -> requests.Session:
        """Create a session with retry configuration."""
        if self._session is None:
            session = requests.Session()
            retry_strategy = Retry(
                total=3,
                backoff_factor=1,
                status_forcelist=[408, 429, 500, 502, 503, 504]
            )
            adapter = HTTPAdapter(max_retries=retry_strategy)
            session.mount("http://", adapter)
            session.mount("https://", adapter)
            self._session = session
        return self._session

    def __enter__(self):
        """Context manager entry."""
        self._create_session()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()

    def add_service(self, service_name: str, config: ServiceConfig) -> None:
        """
        Register a new service configuration.

        :param service_name: Name of the service
        :param config: Service configuration
        """
        self._services[service_name] = config
        logger.debug(f"Added service configuration for {service_name}")

    def get_auth_token(self, target_url: str) -> str:
        """Generate authentication token for a service."""
        try:
            auth_req = Request()
            token = id_token.fetch_id_token(auth_req, target_url)
            return token
        except Exception as e:
            logger.error(f"Failed to get auth token: {str(e)}")
            raise

    def _get_service_config(self, service_name: str) -> ServiceConfig:
        """Get service configuration or raise error if not found."""
        config = self._services.get(service_name)
        if not config:
            raise ValueError(
                f"Service '{service_name}' not configured. Check environment variable {self._env_prefix}{service_name.upper()}")
        return config

    def _ensure_session(self):
        """Ensure session exists."""
        if self._session is None:
            self._create_session()

    def _make_request(
            self,
            method: str,
            service_name: str,
            endpoint: str = "",
            json: Optional[Dict] = None,
            params: Optional[Dict] = None,
            headers: Optional[Dict] = None,
            timeout: Optional[int] = None
    ) -> requests.Response:
        """
        Make an authenticated request to the service.

        :param timeout: Optional timeout override for this specific request
        """
        self._ensure_session()
        config = self._get_service_config(service_name)
        url = f"{config.url.rstrip('/')}/{endpoint.lstrip('/')}"

        # Get fresh token and add to headers
        token = self.get_auth_token(config.url)
        headers = headers or {}
        headers["Authorization"] = f"Bearer {token}"

        # Add correlation ID from Flask context if available.
        try:
            from flask import g
            if hasattr(g, 'correlation_id'):
                headers['X-Correlation-ID'] = g.correlation_id
        except (ImportError, RuntimeError):
            # Handle cases where Flask is not installed or outside request context
            pass

        # Use provided timeout or fall back to config timeout
        request_timeout = timeout or config.timeout

        try:
            response = self._session.request(
                method=method,
                url=url,
                json=json,
                params=params,
                headers=headers,
                timeout=request_timeout
            )
            return response
        except requests.exceptions.RequestException as e:
            logger.error(f"Request failed: {str(e)}")
            raise

    def get(self, service_name: str, endpoint: str = "", params: Optional[Dict] = None,
            timeout: Optional[int] = None) -> requests.Response:
        """
        Make GET request to service.

        :param service_name: Name of the registered service
        :param endpoint: Endpoint path
        :param params: Query parameters
        :param timeout: Optional timeout override for this request
        :return: Response from the service
        """
        return self._make_request("GET", service_name, endpoint, params=params, timeout=timeout)

    def post(self, service_name: str, endpoint: str = "", json: Optional[Dict] = None,
             timeout: Optional[int] = None) -> requests.Response:
        """
        Make POST request to service.

        :param service_name: Name of the registered service
        :param endpoint: Endpoint path
        :param json: JSON body data
        :param timeout: Optional timeout override for this request
        :return: Response from the service
        """
        return self._make_request("POST", service_name, endpoint, json=json, timeout=timeout)

    def put(self, service_name: str, endpoint: str = "", json: Optional[Dict] = None,
            timeout: Optional[int] = None) -> requests.Response:
        """
        Make PUT request to service.

        :param service_name: Name of the registered service
        :param endpoint: Endpoint path
        :param json: JSON body data
        :param timeout: Optional timeout override for this request
        :return: Response from the service
        """
        return self._make_request("PUT", service_name, endpoint, json=json, timeout=timeout)

    def delete(self, service_name: str, endpoint: str = "",
               timeout: Optional[int] = None) -> requests.Response:
        """
        Make DELETE request to service.

        :param service_name: Name of the registered service
        :param endpoint: Endpoint path
        :param timeout: Optional timeout override for this request
        :return: Response from the service
        """
        return self._make_request("DELETE", service_name, endpoint, timeout=timeout)

    def close(self) -> None:
        """Close the session."""
        if self._session:
            self._session.close()
            self._session = None
            logger.debug("Service client session closed")


def create_service_error_handler(base_error_class: type) -> Callable:
    """
    Create an error handler function with the BaseError class in closure.

    :param base_error_class: Your BaseError class from the main app
    :return: Error handler function
    """

    ERROR_MAPPINGS = {
        400: ("Invalid request", "SERVICE_VALIDATION_ERROR"),
        401: ("Authentication failed", "SERVICE_AUTH_ERROR"),
        403: ("Access denied", "SERVICE_FORBIDDEN_ERROR"),
        404: ("Resource not found", "SERVICE_NOT_FOUND_ERROR"),
        408: ("Service timeout", "SERVICE_TIMEOUT_ERROR"),
        409: ("Resource conflict", "SERVICE_CONFLICT_ERROR"),
        422: ("Unprocessable entity", "SERVICE_UNPROCESSABLE_ERROR"),
        429: ("Too many requests", "SERVICE_RATE_LIMIT_ERROR"),
        500: ("Internal service error", "SERVICE_INTERNAL_ERROR"),
        502: ("Bad gateway", "SERVICE_BAD_GATEWAY_ERROR"),
        503: ("Service temporarily unavailable", "SERVICE_UNAVAILABLE_ERROR"),
        504: ("Gateway timeout", "SERVICE_GATEWAY_TIMEOUT_ERROR"),
    }

    def handle_service_response(response: requests.Response) -> None:
        """
        Handle service response and raise the appropriate error using BaseError. As BaseError is part of `blitzy_utils`
        and we need to add this workaround.

        :param response: The response object from requests
        :raises: Instance of base_error_class with appropriate message and status code
        """
        if response.status_code < 400:
            return

        try:
            error_data = response.json()
        except Exception:
            error_data = {"raw_response": response.text}

        logger.debug(f"Service response: {error_data} with status code {response.status_code}")

        error_message = (
            error_data.get("error") or
            error_data.get("message") or
            error_data.get("detail") or
            error_data.get("error_description") or
            error_data.get("raw_response")
        )

        default_message, error_code = ERROR_MAPPINGS.get(
            response.status_code,
            (f"Service error: {response.status_code}", "SERVICE_ERROR")
        )

        final_message = error_message if error_message else default_message

        detailed_message = f"{final_message}"

        error = base_error_class(
            message=detailed_message,
            status_code=response.status_code,
            error_code=error_code
        )

        error.response_data = error_data

        raise error

    return handle_service_response
