"""
Test suite for Azure DevOps pull request functions in blitzy_utils.

This module tests the Azure DevOps pull request creation and management functionality,
including both single and bulk pull request operations.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, List, Optional

# Import the functions we want to test
try:
    from blitzy_utils.azure import (
        create_single_pull_request_azure_devops,
        create_all_pull_requests_azure_devops,
        _get_azure_devops_credentials_by_repo_id,
        _get_azure_devops_repo,
        GitProjectRepo,
    )
    from blitzy_utils.consts import SvcType
except ImportError as e:
    pytest.skip(f"Could not import Azure DevOps functions: {e}", allow_module_level=True)


class TestAzureDevOpsPullRequests:
    """Test cases for Azure DevOps pull request operations."""

    @pytest.fixture
    def mock_git_project_repo(self):
        """Mock GitProjectRepo object for testing."""
        return GitProjectRepo(
            access_token="mock_token",
            azure_org_id="mock_org_id",
            azure_org_name="mock_org",
            azure_project_id="mock_project_id",
            repo_id="mock_repo_id"
        )

    @pytest.fixture
    def mock_azure_connection(self):
        """Mock Azure DevOps connection."""
        mock_connection = Mock()
        mock_git_client = Mock()
        mock_connection.clients.get_git_client.return_value = mock_git_client
        return mock_connection, mock_git_client

    @pytest.fixture
    def mock_existing_pr(self):
        """Mock existing pull request object."""
        mock_pr = Mock()
        mock_pr.pull_request_id = 123
        mock_pr.title = "Existing PR"
        mock_pr.description = "Existing PR description"
        mock_pr.url = "https://dev.azure.com/org/project/_git/repo/pullrequest/123"
        mock_pr.status = "active"
        mock_pr.source_ref_name = "refs/heads/feature-branch"
        mock_pr.target_ref_name = "refs/heads/main"
        mock_pr.creation_date = Mock()
        mock_pr.creation_date.isoformat.return_value = "2023-01-01T00:00:00Z"
        return mock_pr

    @pytest.fixture
    def mock_created_pr(self):
        """Mock newly created pull request object."""
        mock_pr = Mock()
        mock_pr.pull_request_id = 456
        mock_pr.title = "New PR"
        mock_pr.description = "New PR description"
        mock_pr.url = "https://dev.azure.com/org/project/_git/repo/pullrequest/456"
        mock_pr.status = "active"
        mock_pr.source_ref_name = "refs/heads/feature-branch"
        mock_pr.target_ref_name = "refs/heads/main"
        mock_pr.creation_date = Mock()
        mock_pr.creation_date.isoformat.return_value = "2023-01-01T00:00:00Z"
        return mock_pr

    @patch('blitzy_utils.azure._create_azure_connection')
    def test_create_single_pull_request_existing_pr(
        self, 
        mock_create_connection, 
        mock_azure_connection, 
        mock_existing_pr
    ):
        """Test creating a single pull request when one already exists."""
        mock_connection, mock_git_client = mock_azure_connection
        mock_create_connection.return_value = mock_connection

        # Mock existing PR search
        mock_git_client.get_pull_requests.return_value = [mock_existing_pr]

        result = create_single_pull_request_azure_devops(
            organization="test_org",
            project_id="test_project",
            repo_id="test_repo",
            access_token="test_token",
            head_branch="feature-branch",
            base_branch="main",
            pr_title="Test PR",
            pr_body="Test PR body"
        )

        # Verify the result
        assert result is not None
        assert result["id"] == 123
        assert result["title"] == "Existing PR"
        assert result["url"] == "https://dev.azure.com/org/project/_git/repo/pullrequest/123"

        # Verify that create_pull_request was not called
        mock_git_client.create_pull_request.assert_not_called()

    @patch('blitzy_utils.azure._create_azure_connection')
    def test_create_single_pull_request_new_pr(
        self, 
        mock_create_connection, 
        mock_azure_connection, 
        mock_created_pr
    ):
        """Test creating a new single pull request."""
        mock_connection, mock_git_client = mock_azure_connection
        mock_create_connection.return_value = mock_connection

        # Mock no existing PRs
        mock_git_client.get_pull_requests.return_value = []
        mock_git_client.create_pull_request.return_value = mock_created_pr

        result = create_single_pull_request_azure_devops(
            organization="test_org",
            project_id="test_project",
            repo_id="test_repo",
            access_token="test_token",
            head_branch="feature-branch",
            base_branch="main",
            pr_title="Test PR",
            pr_body="Test PR body"
        )

        # Verify the result
        assert result is not None
        assert result["id"] == 456
        assert result["title"] == "New PR"
        assert result["url"] == "https://dev.azure.com/org/project/_git/repo/pullrequest/456"

        # Verify create_pull_request was called with correct parameters
        mock_git_client.create_pull_request.assert_called_once()
        call_args = mock_git_client.create_pull_request.call_args
        assert call_args[1]["git_pull_request_to_create"]["sourceRefName"] == "refs/heads/feature-branch"
        assert call_args[1]["git_pull_request_to_create"]["targetRefName"] == "refs/heads/main"
        assert call_args[1]["git_pull_request_to_create"]["title"] == "Test PR"
        assert call_args[1]["git_pull_request_to_create"]["description"] == "Test PR body"

    @patch('blitzy_utils.azure._create_azure_connection')
    def test_create_single_pull_request_branch_ref_formatting(
        self, 
        mock_create_connection, 
        mock_azure_connection, 
        mock_created_pr
    ):
        """Test that branch names are properly formatted with refs/ prefix."""
        mock_connection, mock_git_client = mock_azure_connection
        mock_create_connection.return_value = mock_connection

        mock_git_client.get_pull_requests.return_value = []
        mock_git_client.create_pull_request.return_value = mock_created_pr

        # Test with branch names that already have refs/ prefix
        create_single_pull_request_azure_devops(
            organization="test_org",
            project_id="test_project",
            repo_id="test_repo",
            access_token="test_token",
            head_branch="refs/heads/feature-branch",
            base_branch="refs/heads/main",
            pr_title="Test PR",
            pr_body="Test PR body"
        )

        # Verify refs/ prefix is preserved
        call_args = mock_git_client.create_pull_request.call_args
        assert call_args[1]["git_pull_request_to_create"]["sourceRefName"] == "refs/heads/feature-branch"
        assert call_args[1]["git_pull_request_to_create"]["targetRefName"] == "refs/heads/main"

    @patch('blitzy_utils.azure._get_azure_devops_credentials_by_repo_id')
    @patch('blitzy_utils.azure._get_azure_devops_repo')
    @patch('blitzy_utils.azure.create_single_pull_request_azure_devops')
    @patch('blitzy_utils.azure._get_gitmodules_content')
    def test_create_all_pull_requests_no_submodules(
        self,
        mock_get_gitmodules,
        mock_create_single_pr,
        mock_get_repo,
        mock_get_credentials,
        mock_git_project_repo
    ):
        """Test creating all pull requests when there are no submodules."""
        # Setup mocks
        mock_get_credentials.return_value = mock_git_project_repo
        mock_get_repo.return_value = {
            "id": "repo_id",
            "name": "test_repo",
            "default_branch": "refs/heads/main"
        }
        mock_get_gitmodules.return_value = None  # No .gitmodules file
        mock_create_single_pr.return_value = {
            "id": 123,
            "title": "Test PR",
            "url": "https://dev.azure.com/org/project/_git/repo/pullrequest/123"
        }

        result = create_all_pull_requests_azure_devops(
            git_project_repo_id="test_repo_id",
            head_branch="feature-branch",
            base_branch="",
            pr_title="Test PR",
            pr_body="Test PR body"
        )

        # Verify results
        assert len(result) == 1
        assert result[0]["id"] == 123

        # Verify single PR creation was called
        mock_create_single_pr.assert_called_once_with(
            organization="mock_org",
            project_id="mock_project_id",
            repo_id="mock_repo_id",
            access_token="mock_token",
            head_branch="feature-branch",
            base_branch="main",  # Should strip refs/heads/ prefix
            pr_title="Test PR",
            pr_body="Test PR body"
        )

    @patch('blitzy_utils.azure._create_azure_connection')
    def test_create_single_pull_request_azure_error(self, mock_create_connection):
        """Test handling of Azure DevOps service errors."""
        mock_connection = Mock()
        mock_git_client = Mock()
        mock_connection.clients.get_git_client.return_value = mock_git_client
        mock_create_connection.return_value = mock_connection

        # Mock Azure DevOps service error - use a generic exception since AzureDevOpsServiceError
        # constructor is complex and we just want to test error handling
        mock_git_client.get_pull_requests.side_effect = Exception("Service error")

        with pytest.raises(Exception):
            create_single_pull_request_azure_devops(
                organization="test_org",
                project_id="test_project",
                repo_id="test_repo",
                access_token="test_token",
                head_branch="feature-branch",
                base_branch="main",
                pr_title="Test PR",
                pr_body="Test PR body"
            )

    def test_create_single_pull_request_invalid_parameters(self):
        """Test validation of input parameters."""
        # Test with empty organization
        with pytest.raises(Exception):
            create_single_pull_request_azure_devops(
                organization="",
                project_id="test_project",
                repo_id="test_repo",
                access_token="test_token",
                head_branch="feature-branch",
                base_branch="main",
                pr_title="Test PR",
                pr_body="Test PR body"
            )


class TestAzureDevOpsUnifiedFunctions:
    """Test cases for unified GitHub/Azure DevOps pull request functions."""

    @patch('blitzy_utils.github._get_service_type')
    @patch('blitzy_utils.azure.create_all_pull_requests_azure_devops')
    def test_create_all_pull_requests_azure_devops_detection(
        self,
        mock_azure_create_all,
        mock_get_service_type
    ):
        """Test that unified function correctly detects Azure DevOps and calls Azure implementation."""
        from blitzy_utils.github import create_all_pull_requests

        # Mock service type detection
        mock_get_service_type.return_value = SvcType.AZURE_DEVOPS
        mock_azure_create_all.return_value = [{"id": 123, "title": "Test PR"}]

        result = create_all_pull_requests(
            repo="test_repo",
            head_branch="feature-branch",
            user_id="test_user",
            server="test_server",
            git_project_repo_id="test_repo_id"
        )

        # Verify Azure DevOps implementation was called
        mock_azure_create_all.assert_called_once_with(
            git_project_repo_id="test_repo_id",
            head_branch="feature-branch",
            base_branch="",
            pr_title=None,
            pr_body=None,
            is_new_repo=False
        )

        assert len(result) == 1
        assert result[0]["id"] == 123

    @patch('blitzy_utils.github._get_service_type')
    @patch('blitzy_utils.azure.create_single_pull_request_azure_devops')
    @patch('blitzy_utils.azure._get_azure_devops_credentials_by_repo_id')
    def test_create_single_pull_request_azure_devops_detection(
        self,
        mock_get_credentials,
        mock_azure_create_single,
        mock_get_service_type
    ):
        """Test that unified function correctly detects Azure DevOps and calls Azure implementation."""
        from blitzy_utils.github import create_single_pull_request

        # Mock service type detection
        mock_get_service_type.return_value = SvcType.AZURE_DEVOPS
        mock_get_credentials.return_value = GitProjectRepo(
            access_token="mock_token",
            azure_org_id="mock_org_id",
            azure_org_name="mock_org",
            azure_project_id="mock_project_id",
            repo_id="mock_repo_id"
        )
        mock_azure_create_single.return_value = {"id": 123, "title": "Test PR"}

        result = create_single_pull_request(
            repo="test_repo",
            head_branch="feature-branch",
            base_branch="main",
            pr_title="Test PR",
            pr_body="Test PR body",
            user_id="test_user",
            git_project_repo_id="test_repo_id"
        )

        # Verify Azure DevOps implementation was called
        mock_azure_create_single.assert_called_once_with(
            organization="mock_org",
            project_id="mock_project_id",
            repo_id="mock_repo_id",
            access_token="mock_token",
            head_branch="feature-branch",
            base_branch="main",
            pr_title="Test PR",
            pr_body="Test PR body"
        )

        assert result["id"] == 123

    def test_create_single_pull_request_github_fallback(self):
        """Test that unified function falls back to GitHub when no git_project_repo_id is provided."""
        from blitzy_utils.github import create_single_pull_request
        from github.Repository import Repository

        # Mock GitHub repository
        mock_repo = Mock(spec=Repository)
        mock_repo.get_pulls.return_value = []
        mock_pr = Mock()
        mock_pr.number = 123
        mock_pr.html_url = "https://github.com/owner/repo/pull/123"
        mock_repo.create_pull.return_value = mock_pr
        mock_repo.owner.login = "owner"

        result = create_single_pull_request(
            repo=mock_repo,
            head_branch="feature-branch",
            base_branch="main",
            pr_title="Test PR",
            pr_body="Test PR body"
        )

        # Verify GitHub implementation was used
        assert result == mock_pr
        mock_repo.create_pull.assert_called_once_with(
            title="Test PR",
            body="Test PR body",
            head="feature-branch",
            base="main"
        )


class TestAzureDevOpsIntegrationScenarios:
    """Integration test scenarios for Azure DevOps pull request operations."""

    @patch('blitzy_utils.azure._get_azure_devops_credentials_by_repo_id')
    @patch('blitzy_utils.azure._get_azure_devops_repo')
    @patch('blitzy_utils.azure.create_single_pull_request_azure_devops')
    @patch('blitzy_utils.azure._get_gitmodules_content')
    @patch('blitzy_utils.azure._parse_gitmodules')
    @patch('blitzy_utils.azure._parse_submodule_url_azure_devops')
    @patch('blitzy_utils.azure._get_project_id_by_name')
    @patch('blitzy_utils.azure._get_azure_repo_id_by_name')
    def test_create_all_pull_requests_with_submodules(
        self,
        mock_get_repo_id,
        mock_get_project_id,
        mock_parse_url,
        mock_parse_gitmodules,
        mock_get_gitmodules,
        mock_create_single_pr,
        mock_get_repo,
        mock_get_credentials
    ):
        """Test creating pull requests for main repo and submodules."""
        # Setup main repo mocks
        mock_git_project_repo = GitProjectRepo(
            access_token="mock_token",
            azure_org_id="mock_org_id",
            azure_org_name="mock_org",
            azure_project_id="mock_project_id",
            repo_id="mock_repo_id"
        )
        mock_get_credentials.return_value = mock_git_project_repo
        mock_get_repo.side_effect = [
            {"id": "main_repo_id", "name": "main_repo", "default_branch": "refs/heads/main"},
            {"id": "sub_repo_id", "name": "sub_repo", "default_branch": "refs/heads/main"}
        ]

        # Setup submodule mocks
        mock_get_gitmodules.return_value = "[submodule \"sub\"]\n\tpath = sub\n\turl = https://dev.azure.com/org/project/_git/subrepo"
        mock_parse_gitmodules.return_value = [{"path": "sub", "url": "https://dev.azure.com/org/project/_git/subrepo"}]
        mock_parse_url.return_value = {"organization": "org", "project_name": "project", "repo_name": "subrepo"}
        mock_get_project_id.return_value = "sub_project_id"
        mock_get_repo_id.return_value = "sub_repo_id"

        # Mock PR creation
        mock_create_single_pr.side_effect = [
            {"id": 123, "title": "Main PR", "url": "https://dev.azure.com/org/project/_git/repo/pullrequest/123"},
            {"id": 456, "title": "Sub PR", "url": "https://dev.azure.com/org/project/_git/subrepo/pullrequest/456"}
        ]

        result = create_all_pull_requests_azure_devops(
            git_project_repo_id="test_repo_id",
            head_branch="feature-branch",
            base_branch="",
            pr_title="Test PR",
            pr_body="Test PR body"
        )

        # Verify results
        assert len(result) == 2
        assert result[0]["id"] == 123  # Main repo PR
        assert result[1]["id"] == 456  # Submodule PR

        # Verify both PRs were created
        assert mock_create_single_pr.call_count == 2
