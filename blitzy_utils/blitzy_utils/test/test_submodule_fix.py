#!/usr/bin/env python3
"""
Test script to verify the Azure DevOps submodule fix is working.
"""

import os
import sys
import tempfile
import shutil
import logging

# Add the blitzy_utils directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'blitzy_utils'))

def test_submodule_fix():
    """Test that submodules are properly handled after commit checkout."""
    
    # Set up logging to see what's happening
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)
    
    try:
        # Import the real Azure DevOps download function
        from blitzy_utils.azure import _download_all_git_files_to_disk_azure_devops
        
        logger.info("🧪 Testing Azure DevOps submodule fix...")
        
        # Test data - using the repository with submodules
        test_data = {
            "repo_name": "19July_<PERSON><PERSON><PERSON>_back_prop",
            "branch_name": "ADO_Branch_10AM", 
            "head_commit_hash": "34fcbe813b071f8fb8e264ca54075bef38594700",  # Latest commit with .gitmodules
            "git_project_repo_id": "36c8d0c2-4d87-4658-920e-68d9c9754ef6"
        }
        
        logger.info(f"📦 Testing with repo: {test_data['repo_name']}")
        logger.info(f"🌿 Branch: {test_data['branch_name']}")
        logger.info(f"📝 Commit: {test_data['head_commit_hash']}")
        
        # Call the download function
        git_files = _download_all_git_files_to_disk_azure_devops(
            repo_name=test_data['repo_name'],
            branch_name=test_data['branch_name'],
            commit_hash=test_data['head_commit_hash'],
            git_project_repo_id=test_data['git_project_repo_id']
        )
        
        logger.info(f"✅ Download completed! Files downloaded: {len(git_files)}")
        
        # Check if .gitmodules file is present
        gitmodules_file = None
        for git_file in git_files:
            if git_file.path == '.gitmodules':
                gitmodules_file = git_file
                break
        
        if gitmodules_file:
            logger.info("✅ .gitmodules file found in downloaded files")
            logger.info(f"📄 .gitmodules content preview:")
            logger.info(f"   {gitmodules_file.text[:200]}...")
        else:
            logger.warning("⚠️  .gitmodules file not found in downloaded files")
        
        # Check if the cloned repository exists on disk
        clone_path = f"blitzy/{test_data['repo_name']}/{test_data['branch_name']}"
        if os.path.exists(clone_path):
            logger.info(f"✅ Cloned repository found at: {clone_path}")
            
            # Check if .gitmodules exists in the cloned repo
            gitmodules_path = os.path.join(clone_path, '.gitmodules')
            if os.path.exists(gitmodules_path):
                logger.info("✅ .gitmodules file exists in cloned repository")
                
                # Read the .gitmodules content
                with open(gitmodules_path, 'r') as f:
                    gitmodules_content = f.read()
                logger.info(f"📄 .gitmodules content:\n{gitmodules_content}")
                
                # Check if submodule directory exists
                if 'path = libs/mysubmodule' in gitmodules_content:
                    submodule_path = os.path.join(clone_path, 'libs', 'mysubmodule')
                    if os.path.exists(submodule_path):
                        logger.info("🎉 SUCCESS: Submodule directory exists!")
                        
                        # Check if submodule has content
                        submodule_files = os.listdir(submodule_path)
                        if submodule_files:
                            logger.info(f"📁 Submodule contains {len(submodule_files)} files: {submodule_files[:5]}")
                        else:
                            logger.warning("⚠️  Submodule directory exists but is empty")
                    else:
                        logger.warning("❌ Submodule directory does not exist")
                        logger.info("🔧 This indicates the submodule update step needs improvement")
                        
                        # Check git submodule status
                        import subprocess
                        try:
                            result = subprocess.run(
                                ["git", "-C", clone_path, "submodule", "status"],
                                capture_output=True, text=True, check=False
                            )
                            logger.info(f"📊 Git submodule status: {result.stdout.strip()}")
                            if result.stderr:
                                logger.info(f"📊 Git submodule stderr: {result.stderr.strip()}")
                        except Exception as e:
                            logger.warning(f"Failed to check submodule status: {e}")
            else:
                logger.warning("❌ .gitmodules file not found in cloned repository")
        else:
            logger.warning(f"❌ Cloned repository not found at: {clone_path}")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ Failed to import Azure DevOps functions: {e}")
        return False
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_submodule_fix()
    if success:
        print("\n🎯 Test completed - check logs above for submodule status")
    else:
        print("\n💥 Test failed")
        sys.exit(1)
