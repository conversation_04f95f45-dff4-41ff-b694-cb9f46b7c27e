from enum import Enum


class ProjectPhase(Enum):
    SOFTWARE_REQUIREMENTS = "SOFTWARE_REQUIREMENTS"
    TECHNICAL_SPECIFICATION = "TECHNICAL_SPECIFICATION"
    THINKING = "THINKING"
    REVERSE_THINKING = "REVERSE_THINKING"
    CODE_GENERATION = "CODE_GENERATION"
    VALIDATION = "VALIDATION"
    CODE_DOWNLOAD = "CODE_DOWNLOAD"
    CODE_DELETE = "CODE_DELETE"
    CODE_UPLOAD = "CODE_UPLOAD"
    CODE_GRAPH = "CODE_GRAPH"
    FILE_MAPPING = "FILE_MAPPING"
    REVERSE_CODE_GENERATION = "REVERSE_CODE_GENERATION"


class ProjectPhaseAction(Enum):
    GENERATE_PDF = "GENERATE_PDF"


class JobStatus(Enum):
    IN_PROGRESS = "IN_PROGRESS"
    DONE = "DONE"
    FAILED = "FAILED"


class SourceMode(Enum):
    GCP = "GCP"
    GITHUB = "GITHUB"


class UploadMode(Enum):
    REPO = "REPO"
    PR = "PR"


class BackpropChangeMode(str, Enum):
    GENERATE = "GENERATE",
    UPDATE = "UPDATE"


class BackpropCommand(str, Enum):
    ADD_FEATURE = "ADD_FEATURE",
    REFACTOR_CODE = "REFACTOR_CODE",
    FIX_BUGS = "FIX_BUGS",
    FIX_CVES = "FIX_CVES",
    ADD_TESTING = "ADD_TESTING",
    DOCUMENT_CODE = "DOCUMENT_CODE",
    CUSTOM = "CUSTOM"
