# blitzy_utils/logger/providers/base.py

import uuid
from typing import Optional, Dict, Any
from dataclasses import dataclass


@dataclass
class LogContext:
    request_id: Optional[str] = None
    correlation_id: Optional[str] = None
    additional_context: Dict[str, Any] = None


class BaseContextProvider:
    def get_context(self) -> LogContext:
        """Get current logging context"""
        return LogContext()

    def generate_id(self) -> str:
        """Generate a unique ID for request/correlation"""
        return str(uuid.uuid4())


class DefaultContextProvider(BaseContextProvider):
    """Default provider that generates new IDs if none exist"""
    _context: Optional[LogContext] = None

    def get_context(self) -> LogContext:
        if not self._context:
            self._context = LogContext(
                request_id=self.generate_id(),
                correlation_id=self.generate_id()
            )
        return self._context