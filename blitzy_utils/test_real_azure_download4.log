✅ Successfully imported real Azure DevOps git clone functions
============================= test session starts ==============================
platform darwin -- Python 3.12.11, pytest-8.4.1, pluggy-1.6.0
rootdir: /Users/<USER>/git/work/blitzy-utils-python/blitzy_utils
configfile: pyproject.toml
plugins: anyio-4.9.0, typeguard-4.4.4, langsmith-0.4.8
collected 15 items

blitzy_utils/test/test_azure_git_clone.py ...............                [100%]

============================== 15 passed in 0.02s ==============================
