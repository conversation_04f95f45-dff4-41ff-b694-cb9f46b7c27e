2025-07-24 18:53:41,383 - INFO - 🔍 Found blitzy_utils at: /Users/<USER>/git/work/blitzy-utils-python/blitzy_utils/blitzy_utils
2025-07-24 18:53:41,383 - INFO - 📦 Importing Azure DevOps functions...
2025-07-24 18:53:41,464 - ERROR - ❌ Failed to import Azure DevOps functions: No module named 'azure.core'; 'azure' is not a package
2025-07-24 18:53:41,464 - ERROR - 💡 Make sure you're running from the correct directory with blitzy_utils available
2025-07-24 18:54:04,637 - INFO - 🔍 Found blitzy_utils at: /Users/<USER>/git/work/blitzy-utils-python/blitzy_utils/blitzy_utils
2025-07-24 18:54:04,637 - INFO - 📦 Importing Azure DevOps functions...
2025-07-24 18:54:04,737 - ERROR - ❌ Failed to import Azure DevOps functions: attempted relative import with no known parent package
2025-07-24 18:54:04,737 - ERROR - 💡 Make sure you're running from the correct directory with blitzy_utils available
2025-07-24 18:54:39,189 - INFO - 🔍 Found blitzy_utils at: /Users/<USER>/git/work/blitzy-utils-python/blitzy_utils/blitzy_utils
2025-07-24 18:54:39,189 - INFO - 📦 Importing Azure DevOps functions...
2025-07-24 18:54:39,281 - ERROR - ❌ Failed to import Azure DevOps functions: No module named 'azure.core'; 'azure' is not a package
2025-07-24 18:54:39,281 - ERROR - 💡 Make sure you're running from the correct directory with blitzy_utils available
