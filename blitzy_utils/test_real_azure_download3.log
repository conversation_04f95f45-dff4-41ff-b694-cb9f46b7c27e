🧪 Testing REAL Azure DevOps Repository Download
======================================================================
📦 Importing REAL Azure DevOps download functions...
✅ Successfully imported REAL download functions!

1️⃣ Testing REAL download_repository_to_disk for Azure DevOps
🔄 Testing with repo: 19July_Lakshya_back_prop
🔄 Branch: ADO_Branch_10AM
🔄 Commit: 34fcbe813b071f8fb8e264ca54075bef38594700
🔄 Git Project Repo ID: 36c8d0c2-4d87-4658-920e-68d9c9754ef6
{"timestamp": "2025-07-24T22:29:28.355661+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Getting head commit hash for 19July_<PERSON><PERSON>hya_back_prop with git_project_repo_id 36c8d0c2-4d87-4658-920e-68d9c9754ef6", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.355753+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Fetching service type from https://archie-github-handler-480762617400.us-central1.run.app/v1/users/2887a75e-9b01-4787-a4a0-bee4e919f4f8/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/svc-type", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.406467+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): oauth2.googleapis.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.480268+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://oauth2.googleapis.com:443 \"POST /token HTTP/1.1\" 200 None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.481483+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): archie-github-handler-480762617400.us-central1.run.app:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.621170+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://archie-github-handler-480762617400.us-central1.run.app:443 \"GET /v1/users/2887a75e-9b01-4787-a4a0-bee4e919f4f8/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/svc-type HTTP/1.1\" 200 27", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.621497+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Service type: AZURE_DEVOPS", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.621803+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Processing repository 19July_Lakshya_back_prop with service type: AZURE_DEVOPS", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.621866+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Setting up repository access", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.621900+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Fetching Azure DevOps credentials from URL: https://archie-github-handler-480762617400.us-central1.run.app/v1/github/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/access-token", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.685872+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): oauth2.googleapis.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.753223+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://oauth2.googleapis.com:443 \"POST /token HTTP/1.1\" 200 None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.754291+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): archie-github-handler-480762617400.us-central1.run.app:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.962232+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://archie-github-handler-480762617400.us-central1.run.app:443 \"GET /v1/github/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/access-token HTTP/1.1\" 200 2458", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.962463+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Successfully fetched Azure DevOps credentials by repo id", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.962497+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Received response from fetching Azure DevOps credentials", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.962516+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Organization name: blitzy-qa, Organization ID: 4b316c82-e2a7-42d3-bd0d-7d2c5933b791", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.962641+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Azure DevOps organization: 4b316c82-e2a7-42d3-bd0d-7d2c5933b791, project_id: 1810cd4a-6912-4f05-b39f-b93d75822f2d", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.962656+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Getting repository information for repo_id: d9db696a-b353-40f3-9e42-07ccd5f6c947, repo_name: 19July_Lakshya_back_prop, to check if repo exists", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.962705+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.981080+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.985297+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.985494+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops._file_cache", "message": "Loading cache file: /Users/<USER>/.azure-devops/python-sdk/cache/resources.json", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.985574+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops._file_cache", "message": "attempting to read file /Users/<USER>/.azure-devops/python-sdk/cache/resources.json as utf-8-sig", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.986056+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.988180+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.988303+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops._file_cache", "message": "Loading cache file: /Users/<USER>/.azure-devops/python-sdk/cache/options.json", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.988389+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops._file_cache", "message": "attempting to read file /Users/<USER>/.azure-devops/python-sdk/cache/options.json as utf-8-sig", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:28.991319+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "File cache hit for options on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.001824+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: {project}/_apis/{area}/{resource}/{repositoryId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.002476+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.002555+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.002632+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.002750+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.002804+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.002842+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.003137+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.004675+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.132552+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947 HTTP/1.1\" 200 690", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.132902+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"id\":\"d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"name\":\"19July_Lakshya_back_prop\",\"url\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"project\":{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"},\"defaultBranch\":\"refs/heads/main\",\"size\":4989,\"remoteUrl\":\"https://<EMAIL>/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"sshUrl\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\",\"webUrl\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"_links\":{\"self\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\"},\"project\":{\"href\":\"vstfs:///Classification/TeamProject/1810cd4a-6912-4f05-b39f-b93d75822f2d\"},\"web\":{\"href\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\"},\"ssh\":{\"href\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\"},\"commits\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/commits\"},\"refs\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/refs\"},\"pullRequests\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pullRequests\"},\"items\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/items\"},\"pushes\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pushes\"}},\"isDisabled\":false,\"isInMaintenance\":false}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.133904+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Found repository: {'id': 'd9db696a-b353-40f3-9e42-07ccd5f6c947', 'name': '19July_Lakshya_back_prop', 'url': 'https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947', 'default_branch': 'main'}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.133931+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Attempting to download Azure DevOps repository using git clone: 19July_Lakshya_back_prop", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.133980+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.134987+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.135970+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.136003+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.137794+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.137847+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: {project}/_apis/{area}/{resource}/{repositoryId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.137886+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.137901+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.137912+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.137935+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.137947+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.137960+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.137970+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.138491+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.335847+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947 HTTP/1.1\" 200 690", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.336122+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"id\":\"d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"name\":\"19July_Lakshya_back_prop\",\"url\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"project\":{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"},\"defaultBranch\":\"refs/heads/main\",\"size\":4989,\"remoteUrl\":\"https://<EMAIL>/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"sshUrl\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\",\"webUrl\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"_links\":{\"self\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\"},\"project\":{\"href\":\"vstfs:///Classification/TeamProject/1810cd4a-6912-4f05-b39f-b93d75822f2d\"},\"web\":{\"href\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\"},\"ssh\":{\"href\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\"},\"commits\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/commits\"},\"refs\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/refs\"},\"pullRequests\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pullRequests\"},\"items\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/items\"},\"pushes\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pushes\"}},\"isDisabled\":false,\"isInMaintenance\":false}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.336369+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.337950+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.342479+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.342529+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.345008+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.345074+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: _apis/{resource}/{*projectId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.345131+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.345152+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/_apis/projects", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.345167+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.345197+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.345212+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.345228+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.345240+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.345749+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.471228+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/_apis/projects HTTP/1.1\" 200 334", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.471508+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"count\":1,\"value\":[{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"}]}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.471751+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Found project ID '1810cd4a-6912-4f05-b39f-b93d75822f2d' with name: hello-world-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.471852+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Removing existing directory: blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.477973+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Cloning Azure DevOps repository: https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop to blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:29.913671+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Azure DevOps repository cloned successfully to: blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.003510+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Checking out specific commit: 34fcbe813b071f8fb8e264ca54075bef38594700", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.015964+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Checked out commit: 34fcbe813b071f8fb8e264ca54075bef38594700", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.016426+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 1: pom.xml", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.016513+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 2: .gitmodules", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.016589+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 3: README.md", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.016654+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 4: .gitignore", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.016721+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 5: .gitattributes", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.016778+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 6: Jenkins", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.017340+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Total files read from cloned repository: 6", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.017371+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Successfully downloaded 6 files from Azure DevOps repository using git clone", "service": "archie-service-backend"}
✅ download_repository_to_disk completed!
📊 Files downloaded: 6
📄 Sample files:
   1. pom.xml (3209 bytes)
   2. .gitmodules (133 bytes)
   3. README.md (5892 bytes)
   4. .gitignore (302 bytes)
   5. .gitattributes (32 bytes)
📊 File types: {'gitattributes': 1, 'gitignore': 1, 'gitmodules': 1, 'md': 1, 'no_ext': 1, 'xml': 1}
📊 Total content size: 10080 bytes

2️⃣ Testing REAL _download_all_git_files_to_disk_azure_devops
🔄 Testing direct Azure DevOps download function...
{"timestamp": "2025-07-24T22:29:30.017455+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Setting up repository access", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.017476+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Fetching Azure DevOps credentials from URL: https://archie-github-handler-480762617400.us-central1.run.app/v1/github/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/access-token", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.065076+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): oauth2.googleapis.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.131729+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://oauth2.googleapis.com:443 \"POST /token HTTP/1.1\" 200 None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.132796+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): archie-github-handler-480762617400.us-central1.run.app:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.334330+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://archie-github-handler-480762617400.us-central1.run.app:443 \"GET /v1/github/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/access-token HTTP/1.1\" 200 2458", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.334602+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Successfully fetched Azure DevOps credentials by repo id", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.334646+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Received response from fetching Azure DevOps credentials", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.334675+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Organization name: blitzy-qa, Organization ID: 4b316c82-e2a7-42d3-bd0d-7d2c5933b791", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.334819+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Azure DevOps organization: 4b316c82-e2a7-42d3-bd0d-7d2c5933b791, project_id: 1810cd4a-6912-4f05-b39f-b93d75822f2d", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.334835+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Getting repository information for repo_id: d9db696a-b353-40f3-9e42-07ccd5f6c947, repo_name: 19July_Lakshya_back_prop, to check if repo exists", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.334886+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.336082+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.337120+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.337174+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.339244+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.339362+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: {project}/_apis/{area}/{resource}/{repositoryId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.339411+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.339428+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.339444+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.339485+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.339498+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.339510+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.339520+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.340062+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.512043+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947 HTTP/1.1\" 200 690", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.512279+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"id\":\"d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"name\":\"19July_Lakshya_back_prop\",\"url\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"project\":{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"},\"defaultBranch\":\"refs/heads/main\",\"size\":4989,\"remoteUrl\":\"https://<EMAIL>/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"sshUrl\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\",\"webUrl\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"_links\":{\"self\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\"},\"project\":{\"href\":\"vstfs:///Classification/TeamProject/1810cd4a-6912-4f05-b39f-b93d75822f2d\"},\"web\":{\"href\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\"},\"ssh\":{\"href\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\"},\"commits\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/commits\"},\"refs\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/refs\"},\"pullRequests\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pullRequests\"},\"items\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/items\"},\"pushes\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pushes\"}},\"isDisabled\":false,\"isInMaintenance\":false}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.512463+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Found repository: {'id': 'd9db696a-b353-40f3-9e42-07ccd5f6c947', 'name': '19July_Lakshya_back_prop', 'url': 'https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947', 'default_branch': 'main'}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.512488+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Attempting to download Azure DevOps repository using git clone: 19July_Lakshya_back_prop", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.512520+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.513624+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.514671+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.514745+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.516730+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.516795+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: {project}/_apis/{area}/{resource}/{repositoryId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.516840+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.516858+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.516871+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.516897+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.516910+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.516925+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.516935+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.517478+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.717795+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947 HTTP/1.1\" 200 690", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.718120+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"id\":\"d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"name\":\"19July_Lakshya_back_prop\",\"url\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"project\":{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"},\"defaultBranch\":\"refs/heads/main\",\"size\":4989,\"remoteUrl\":\"https://<EMAIL>/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"sshUrl\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\",\"webUrl\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"_links\":{\"self\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\"},\"project\":{\"href\":\"vstfs:///Classification/TeamProject/1810cd4a-6912-4f05-b39f-b93d75822f2d\"},\"web\":{\"href\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\"},\"ssh\":{\"href\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\"},\"commits\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/commits\"},\"refs\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/refs\"},\"pullRequests\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pullRequests\"},\"items\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/items\"},\"pushes\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pushes\"}},\"isDisabled\":false,\"isInMaintenance\":false}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.718535+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.720413+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.721948+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.722003+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.724881+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.725014+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: _apis/{resource}/{*projectId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.725079+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.725104+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/_apis/projects", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.725125+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.725162+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.725183+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.725206+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.725222+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.725850+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.925869+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/_apis/projects HTTP/1.1\" 200 334", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.926231+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"count\":1,\"value\":[{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"}]}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.926506+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Found project ID '1810cd4a-6912-4f05-b39f-b93d75822f2d' with name: hello-world-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.926644+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Removing existing directory: blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:30.932890+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Cloning Azure DevOps repository: https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop to blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.424362+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Azure DevOps repository cloned successfully to: blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.515429+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Checking out specific commit: 34fcbe813b071f8fb8e264ca54075bef38594700", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.527551+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Checked out commit: 34fcbe813b071f8fb8e264ca54075bef38594700", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.527998+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 1: pom.xml", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.528087+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 2: .gitmodules", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.528165+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 3: README.md", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.528233+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 4: .gitignore", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.528299+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 5: .gitattributes", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.528363+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 6: Jenkins", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.528974+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Total files read from cloned repository: 6", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.529023+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Successfully downloaded 6 files from Azure DevOps repository using git clone", "service": "archie-service-backend"}
✅ _download_all_git_files_to_disk_azure_devops completed!
📊 Files downloaded: 6
📄 Sample files:
   1. pom.xml
      Content preview: <?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         x...
   2. .gitmodules
      Content preview: [submodule "libs/mysubmodule"]
  path = libs/mysubmodule
  url = https://dev.azure.com/blitzy-qa/hel...
   3. README.md
      Content preview:  # :fallen_leaf: :leaves: Testinium-QA :leaves: :fallen_leaf:
Automating the Testinium browser  (JAV...

3️⃣ Testing for actual cloned repository files on disk
🔍 Looking for cloned repositories in: /var/folders/m3/nysj737d3bx2tp1525d0ln0m0000gn/T
📁 Found 3 potential clone directories:
   📁 /var/folders/m3/nysj737d3bx2tp1525d0ln0m0000gn/T/azure_real_test_m2nk39_i
      📊 Contains 0 files
   📁 /var/folders/m3/nysj737d3bx2tp1525d0ln0m0000gn/T/azure_real_test_m2nk39_i
      📊 Contains 0 files
   📁 /var/folders/m3/nysj737d3bx2tp1525d0ln0m0000gn/T/real_azure_test_e6lrpm3z
      📊 Contains 0 files

======================================================================
🎯 REAL AZURE DEVOPS DOWNLOAD TEST SUMMARY
======================================================================
📊 Total Tests: 3
✅ Passed: 3
❌ Failed: 0
💥 Errors: 0

🎉 ALL REAL DOWNLOAD TESTS PASSED!
✅ Real Azure DevOps repository download is working correctly

2️⃣ Testing REAL _download_all_git_files_to_disk_azure_devops
🔄 Testing direct Azure DevOps download function...
{"timestamp": "2025-07-24T22:29:31.531196+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Setting up repository access", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.531231+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Fetching Azure DevOps credentials from URL: https://archie-github-handler-480762617400.us-central1.run.app/v1/github/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/access-token", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.579073+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): oauth2.googleapis.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.643389+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://oauth2.googleapis.com:443 \"POST /token HTTP/1.1\" 200 None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.644333+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): archie-github-handler-480762617400.us-central1.run.app:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.859760+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://archie-github-handler-480762617400.us-central1.run.app:443 \"GET /v1/github/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/access-token HTTP/1.1\" 200 2458", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.860945+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Successfully fetched Azure DevOps credentials by repo id", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.861122+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Received response from fetching Azure DevOps credentials", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.861224+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Organization name: blitzy-qa, Organization ID: 4b316c82-e2a7-42d3-bd0d-7d2c5933b791", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.861857+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Azure DevOps organization: 4b316c82-e2a7-42d3-bd0d-7d2c5933b791, project_id: 1810cd4a-6912-4f05-b39f-b93d75822f2d", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.861964+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Getting repository information for repo_id: d9db696a-b353-40f3-9e42-07ccd5f6c947, repo_name: 19July_Lakshya_back_prop, to check if repo exists", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.862231+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.865753+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.868722+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.868852+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.873433+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.873608+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: {project}/_apis/{area}/{resource}/{repositoryId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.873711+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.873749+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.873781+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.873860+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.873889+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.873919+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.873942+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.874687+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.991962+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947 HTTP/1.1\" 200 690", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.992283+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"id\":\"d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"name\":\"19July_Lakshya_back_prop\",\"url\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"project\":{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"},\"defaultBranch\":\"refs/heads/main\",\"size\":4989,\"remoteUrl\":\"https://<EMAIL>/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"sshUrl\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\",\"webUrl\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"_links\":{\"self\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\"},\"project\":{\"href\":\"vstfs:///Classification/TeamProject/1810cd4a-6912-4f05-b39f-b93d75822f2d\"},\"web\":{\"href\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\"},\"ssh\":{\"href\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\"},\"commits\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/commits\"},\"refs\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/refs\"},\"pullRequests\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pullRequests\"},\"items\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/items\"},\"pushes\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pushes\"}},\"isDisabled\":false,\"isInMaintenance\":false}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.992572+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Found repository: {'id': 'd9db696a-b353-40f3-9e42-07ccd5f6c947', 'name': '19July_Lakshya_back_prop', 'url': 'https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947', 'default_branch': 'main'}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.992604+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Attempting to download Azure DevOps repository using git clone: 19July_Lakshya_back_prop", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.992649+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.994603+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.996539+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.996592+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.999801+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.999887+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: {project}/_apis/{area}/{resource}/{repositoryId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.999949+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.999980+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:31.999997+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.000031+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.000047+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.000064+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.000079+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.002145+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.125685+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947 HTTP/1.1\" 200 690", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.125940+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"id\":\"d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"name\":\"19July_Lakshya_back_prop\",\"url\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"project\":{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"},\"defaultBranch\":\"refs/heads/main\",\"size\":4989,\"remoteUrl\":\"https://<EMAIL>/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"sshUrl\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\",\"webUrl\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"_links\":{\"self\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\"},\"project\":{\"href\":\"vstfs:///Classification/TeamProject/1810cd4a-6912-4f05-b39f-b93d75822f2d\"},\"web\":{\"href\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\"},\"ssh\":{\"href\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\"},\"commits\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/commits\"},\"refs\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/refs\"},\"pullRequests\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pullRequests\"},\"items\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/items\"},\"pushes\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pushes\"}},\"isDisabled\":false,\"isInMaintenance\":false}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.126168+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.127703+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.129078+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.129132+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.131648+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.131707+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: _apis/{resource}/{*projectId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.131769+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.131793+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/_apis/projects", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.131808+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.131838+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.131856+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.131872+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.131886+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.132436+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.248254+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/_apis/projects HTTP/1.1\" 200 334", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.248578+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"count\":1,\"value\":[{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"}]}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.248836+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Found project ID '1810cd4a-6912-4f05-b39f-b93d75822f2d' with name: hello-world-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.248940+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Removing existing directory: blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.254777+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Cloning Azure DevOps repository: https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop to blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.683361+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Azure DevOps repository cloned successfully to: blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.766188+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Checking out specific commit: 34fcbe813b071f8fb8e264ca54075bef38594700", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.776542+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Checked out commit: 34fcbe813b071f8fb8e264ca54075bef38594700", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.776880+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 1: pom.xml", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.776967+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 2: .gitmodules", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.777039+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 3: README.md", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.777097+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 4: .gitignore", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.777156+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 5: .gitattributes", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.777212+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 6: Jenkins", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.777754+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Total files read from cloned repository: 6", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T22:29:32.777779+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Successfully downloaded 6 files from Azure DevOps repository using git clone", "service": "archie-service-backend"}
✅ _download_all_git_files_to_disk_azure_devops completed!
📊 Files downloaded: 6
📄 Sample files:
   1. pom.xml
      Content preview: <?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         x...
   2. .gitmodules
      Content preview: [submodule "libs/mysubmodule"]
  path = libs/mysubmodule
  url = https://dev.azure.com/blitzy-qa/hel...
   3. README.md
      Content preview:  # :fallen_leaf: :leaves: Testinium-QA :leaves: :fallen_leaf:
Automating the Testinium browser  (JAV...

3️⃣ Testing for actual cloned repository files and submodule support
🔍 Looking for cloned repositories in: /var/folders/m3/nysj737d3bx2tp1525d0ln0m0000gn/T
🔍 Also checking current directory for blitzy/ structure
📁 Found 36 potential clone directories:
   📁 /var/folders/m3/nysj737d3bx2tp1525d0ln0m0000gn/T/azure_real_test_m2nk39_i
      📊 Contains 0 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
   📁 /var/folders/m3/nysj737d3bx2tp1525d0ln0m0000gn/T/azure_real_test_m2nk39_i
      📊 Contains 0 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
   📁 /var/folders/m3/nysj737d3bx2tp1525d0ln0m0000gn/T/real_azure_test_e6lrpm3z
      📊 Contains 0 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
   📁 blitzy/19July_Lakshya_back_prop
      🔗 Found .gitmodules file: ADO_Branch_10AM/.gitmodules
      📄 .gitmodules content preview:
         [submodule "libs/mysubmodule"]
  path = libs/mysubmodule
  url = https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_FWD_Prop_...
      📊 Contains 45 total files
      📊 Git-related files: 42
      📊 Submodule files: 1
      📄 Sample files: ['ADO_Branch_10AM/pom.xml', 'ADO_Branch_10AM/.gitmodules', 'ADO_Branch_10AM/README.md', 'ADO_Branch_10AM/.gitignore', 'ADO_Branch_10AM/.gitattributes']
      🔍 Repository analysis:
         Has .git directory: True
         Has .gitmodules: True
      ✅ Repository appears to support submodules!
      📊 Submodule directories found: 0
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM
      🔗 Found .gitmodules file: .gitmodules
      📄 .gitmodules content preview:
         [submodule "libs/mysubmodule"]
  path = libs/mysubmodule
  url = https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_FWD_Prop_...
      📊 Contains 45 total files
      📊 Git-related files: 42
      📊 Submodule files: 1
      📄 Sample files: ['pom.xml', '.gitmodules', 'README.md', '.gitignore', '.gitattributes']
      🔍 Repository analysis:
         Has .git directory: True
         Has .gitmodules: True
      ✅ Repository appears to support submodules!
      📊 Submodule directories found: 0
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git
      📊 Contains 39 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['config', 'HEAD', 'description', 'index', 'packed-refs']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects
      📊 Contains 15 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['0c/a446aab9d09eac8625b53e3df8da661976c458', '6f/8ca8dc4e56ebdcafb48ae9ed819a81c17f5223', '34/fcbe813b071f8fb8e264ca54075bef38594700', '5f/da9eff7ad139c4f394a6d2218af5ac167fea96', 'ae/afa313113c53b39cb2393e7a8a3e5556d1c119']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/0c
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['a446aab9d09eac8625b53e3df8da661976c458']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/6f
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['8ca8dc4e56ebdcafb48ae9ed819a81c17f5223']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/34
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['fcbe813b071f8fb8e264ca54075bef38594700']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/5f
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['da9eff7ad139c4f394a6d2218af5ac167fea96']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/ae
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['afa313113c53b39cb2393e7a8a3e5556d1c119']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/fb
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['3ee9ff7b4b10b273741720df8d5e755edf0716']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/pack
      📊 Contains 0 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/2f
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['f9b16078bee35cc59d2bd3f08823202dd80c8d']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/07
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['c963fb6de5521059f9511fe73c53a4efbae23b']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/00
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['50632a1377956d68fb9c4d11a5ab3422ff3263']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/info
      📊 Contains 0 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/98
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['04b758e84cac41a6acc4d011f57310a1f63102']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/f0
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['d299ed5c4a3779654b488ac9f892f125474c31']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/41
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['af03230029b5e4b54c962f73a595f71f45906b']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/4a
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['30f0519248898f6e2f0cb7ab457cd19179f25e']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/15
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['10f0725d77f28f9b161e95f8bde56b1caf9fba']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/7a
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['9014a30331be90f02502c4e87b0134689115b9']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/info
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['exclude']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs
      📊 Contains 3 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['HEAD', 'refs/heads/main', 'refs/remotes/origin/HEAD']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs/refs
      📊 Contains 2 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['heads/main', 'remotes/origin/HEAD']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs/refs/heads
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['main']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs/refs/remotes
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['origin/HEAD']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs/refs/remotes/origin
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['HEAD']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/hooks
      📊 Contains 13 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['commit-msg.sample', 'pre-rebase.sample', 'pre-commit.sample', 'applypatch-msg.sample', 'fsmonitor-watchman.sample']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/refs
      📊 Contains 2 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['heads/main', 'remotes/origin/HEAD']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/refs/heads
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['main']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/refs/tags
      📊 Contains 0 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/refs/remotes
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['origin/HEAD']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/refs/remotes/origin
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['HEAD']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False

4️⃣ Testing Git Clone Command Submodule Support
🔍 Analyzing git clone implementation:
   ✅ Contains '--recurse-submodules' flag: True
   ✅ Contains submodule references: True
   🎉 CONFIRMED: Azure DevOps git clone includes submodule support!

📁 Keeping 30 directories for inspection:
   1. blitzy/19July_Lakshya_back_prop
   2. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM
   3. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git
   4. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects
   5. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/0c
   6. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/6f
   7. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/34
   8. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/5f
   9. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/ae
   10. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/fb
   11. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/2f
   12. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/07
   13. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/00
   14. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/98
   15. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/f0
   16. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/41
   17. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/4a
   18. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/15
   19. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/7a
   20. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/info
   21. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs
   22. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs/refs
   23. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs/refs/heads
   24. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs/refs/remotes
   25. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs/refs/remotes/origin
   26. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/hooks
   27. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/refs
   28. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/refs/heads
   29. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/refs/remotes
   30. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/refs/remotes/origin

💡 To clean up: rm -rf blitzy/19July_Lakshya_back_prop blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/0c blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/6f blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/34 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/5f blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/ae blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/fb blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/2f blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/07 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/00 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/98 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/f0 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/41 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/4a blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/15 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/7a blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/info blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs/refs blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs/refs/heads blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs/refs/remotes blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs/refs/remotes/origin blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/hooks blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/refs blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/refs/heads blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/refs/remotes blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/refs/remotes/origin
💡 Or set CLEANUP_AFTER_TEST = True in the script

💾 Results saved to: real_azure_download_results.json
