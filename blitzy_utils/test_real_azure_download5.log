🧪 Testing REAL Azure DevOps Repository Download
======================================================================
📦 Importing REAL Azure DevOps download functions...
✅ Successfully imported REAL download functions!

1️⃣ Testing REAL download_repository_to_disk for Azure DevOps
🔄 Testing with repo: 19July_Lakshya_back_prop
🔄 Branch: ADO_Branch_10AM
🔄 Commit: 34fcbe813b071f8fb8e264ca54075bef38594700
🔄 Git Project Repo ID: 36c8d0c2-4d87-4658-920e-68d9c9754ef6
{"timestamp": "2025-07-24T23:19:41.953622+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Getting head commit hash for 19July_<PERSON><PERSON><PERSON>_back_prop with git_project_repo_id 36c8d0c2-4d87-4658-920e-68d9c9754ef6", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:41.953717+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Fetching service type from https://archie-github-handler-480762617400.us-central1.run.app/v1/users/2887a75e-9b01-4787-a4a0-bee4e919f4f8/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/svc-type", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.008283+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): oauth2.googleapis.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.098274+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://oauth2.googleapis.com:443 \"POST /token HTTP/1.1\" 200 None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.099950+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): archie-github-handler-480762617400.us-central1.run.app:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.307111+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://archie-github-handler-480762617400.us-central1.run.app:443 \"GET /v1/users/2887a75e-9b01-4787-a4a0-bee4e919f4f8/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/svc-type HTTP/1.1\" 200 27", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.307811+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Service type: AZURE_DEVOPS", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.308459+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Processing repository 19July_Lakshya_back_prop with service type: AZURE_DEVOPS", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.308612+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Setting up repository access", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.308711+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Fetching Azure DevOps credentials from URL: https://archie-github-handler-480762617400.us-central1.run.app/v1/github/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/access-token", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.379876+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): oauth2.googleapis.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.451021+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://oauth2.googleapis.com:443 \"POST /token HTTP/1.1\" 200 None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.452085+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): archie-github-handler-480762617400.us-central1.run.app:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.761851+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://archie-github-handler-480762617400.us-central1.run.app:443 \"GET /v1/github/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/access-token HTTP/1.1\" 200 2459", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.762198+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Successfully fetched Azure DevOps credentials by repo id", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.762245+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Received response from fetching Azure DevOps credentials", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.762270+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Organization name: blitzy-qa, Organization ID: 4b316c82-e2a7-42d3-bd0d-7d2c5933b791", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.762474+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Azure DevOps organization: 4b316c82-e2a7-42d3-bd0d-7d2c5933b791, project_id: 1810cd4a-6912-4f05-b39f-b93d75822f2d", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.762494+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Getting repository information for repo_id: d9db696a-b353-40f3-9e42-07ccd5f6c947, repo_name: 19July_Lakshya_back_prop, to check if repo exists", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.762564+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.783680+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.788402+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.788711+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops._file_cache", "message": "Loading cache file: /Users/<USER>/.azure-devops/python-sdk/cache/resources.json", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.788806+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops._file_cache", "message": "attempting to read file /Users/<USER>/.azure-devops/python-sdk/cache/resources.json as utf-8-sig", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.789397+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.792059+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.792267+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops._file_cache", "message": "Loading cache file: /Users/<USER>/.azure-devops/python-sdk/cache/options.json", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.792343+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops._file_cache", "message": "attempting to read file /Users/<USER>/.azure-devops/python-sdk/cache/options.json as utf-8-sig", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.793466+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "File cache hit for options on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.805846+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: {project}/_apis/{area}/{resource}/{repositoryId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.806153+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.806188+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.806241+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.806313+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.806333+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.806351+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.806382+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.806956+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.916325+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947 HTTP/1.1\" 200 690", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.916633+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"id\":\"d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"name\":\"19July_Lakshya_back_prop\",\"url\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"project\":{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"},\"defaultBranch\":\"refs/heads/main\",\"size\":4989,\"remoteUrl\":\"https://<EMAIL>/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"sshUrl\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\",\"webUrl\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"_links\":{\"self\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\"},\"project\":{\"href\":\"vstfs:///Classification/TeamProject/1810cd4a-6912-4f05-b39f-b93d75822f2d\"},\"web\":{\"href\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\"},\"ssh\":{\"href\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\"},\"commits\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/commits\"},\"refs\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/refs\"},\"pullRequests\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pullRequests\"},\"items\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/items\"},\"pushes\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pushes\"}},\"isDisabled\":false,\"isInMaintenance\":false}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.918744+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Found repository: {'id': 'd9db696a-b353-40f3-9e42-07ccd5f6c947', 'name': '19July_Lakshya_back_prop', 'url': 'https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947', 'default_branch': 'main'}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.918779+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Attempting to download Azure DevOps repository using git clone: 19July_Lakshya_back_prop", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.918835+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.920236+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.921556+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.921616+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.923905+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.923985+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: {project}/_apis/{area}/{resource}/{repositoryId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.924047+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.924070+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.924087+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.924121+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.924136+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.924153+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.924167+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:42.924710+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.043065+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947 HTTP/1.1\" 200 690", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.043428+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"id\":\"d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"name\":\"19July_Lakshya_back_prop\",\"url\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"project\":{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"},\"defaultBranch\":\"refs/heads/main\",\"size\":4989,\"remoteUrl\":\"https://<EMAIL>/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"sshUrl\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\",\"webUrl\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"_links\":{\"self\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\"},\"project\":{\"href\":\"vstfs:///Classification/TeamProject/1810cd4a-6912-4f05-b39f-b93d75822f2d\"},\"web\":{\"href\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\"},\"ssh\":{\"href\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\"},\"commits\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/commits\"},\"refs\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/refs\"},\"pullRequests\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pullRequests\"},\"items\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/items\"},\"pushes\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pushes\"}},\"isDisabled\":false,\"isInMaintenance\":false}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.043771+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.045866+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.050964+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.051034+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.054112+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.054203+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: _apis/{resource}/{*projectId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.054285+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.054313+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/_apis/projects", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.054331+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.054368+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.054387+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.054406+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.054420+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.055040+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.177190+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/_apis/projects HTTP/1.1\" 200 334", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.177474+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"count\":1,\"value\":[{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"}]}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.177726+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Found project ID '1810cd4a-6912-4f05-b39f-b93d75822f2d' with name: hello-world-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.177840+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Removing existing directory: blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.189401+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Cloning Azure DevOps repository: https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop to blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.677472+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Azure DevOps repository cloned successfully to: blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.686523+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Found .gitmodules file, configuring submodule authentication", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.686650+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": ".gitmodules content: [submodule \"libs/mysubmodule\"]\n  path = libs/mysubmodule\n  url = https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_FWD_Prop_", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.694737+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Found submodule path: libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.694799+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Configuring authentication for 1 submodules: ['libs/mysubmodule']", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.702407+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Configuring auth for submodule: libs/mysubmodule (path: libs/mysubmodule)", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.710010+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Submodule libs/mysubmodule URL: https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_FWD_Prop_", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.710104+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Setting authenticated URL for submodule libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.718210+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Configured auth for Azure DevOps submodule: libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.718270+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Checking out specific commit: 34fcbe813b071f8fb8e264ca54075bef38594700", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.727904+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Checked out commit: 34fcbe813b071f8fb8e264ca54075bef38594700", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.727970+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Updating submodules to match the checked out commit", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.727989+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Re-configuring submodule authentication after commit checkout", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.728015+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Found .gitmodules file, configuring submodule authentication", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.728076+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": ".gitmodules content: [submodule \"libs/mysubmodule\"]\n  path = libs/mysubmodule\n  url = https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_FWD_Prop_", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.736636+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Found submodule path: libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.736713+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Configuring authentication for 1 submodules: ['libs/mysubmodule']", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.745271+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Configuring auth for submodule: libs/mysubmodule (path: libs/mysubmodule)", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.757591+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Submodule libs/mysubmodule URL: https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_FWD_Prop_", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.757714+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Setting authenticated URL for submodule libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.768215+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Configured auth for Azure DevOps submodule: libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.768296+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Step 1: Initializing submodules", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.839643+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Submodules initialized successfully", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.839738+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Step 2: Re-configuring submodule authentication after init", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.839774+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Found .gitmodules file, configuring submodule authentication", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.839848+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": ".gitmodules content: [submodule \"libs/mysubmodule\"]\n  path = libs/mysubmodule\n  url = https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_FWD_Prop_", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.848559+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Found submodule path: libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.848610+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Configuring authentication for 1 submodules: ['libs/mysubmodule']", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.857621+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Configuring auth for submodule: libs/mysubmodule (path: libs/mysubmodule)", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.865972+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Submodule libs/mysubmodule URL: https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_FWD_Prop_", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.866079+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Setting authenticated URL for submodule libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.874767+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Configured auth for Azure DevOps submodule: libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.874833+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Step 3: Updating submodules", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.949479+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Submodules updated successfully to match the checked out commit", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.949556+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Step 4: Verifying submodules were actually cloned", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.959018+00:00", "severity": "WARNING", "level": "WARNING", "name": "root", "message": "Submodules appear to be defined in .gitmodules but not actually committed/cloned", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.959088+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Attempting manual submodule clone for uncommitted submodules", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.959122+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Attempting manual submodule cloning", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:43.968585+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Manually cloning submodule libs/mysubmodule to libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.310706+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Successfully cloned submodule libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.311142+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 1: pom.xml", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.311234+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 2: .gitmodules", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.311362+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 3: README.md", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.311433+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 4: .gitignore", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.311498+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 5: .gitattributes", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.311564+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 6: Jenkins", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.311685+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 7: libs/mysubmodule/README.md", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.312615+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Total files read from cloned repository: 7", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.312649+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Successfully downloaded 7 files from Azure DevOps repository using git clone", "service": "archie-service-backend"}
✅ download_repository_to_disk completed!
📊 Files downloaded: 7
📄 Sample files:
   1. pom.xml (3209 bytes)
   2. .gitmodules (133 bytes)
   3. README.md (5892 bytes)
   4. .gitignore (302 bytes)
   5. .gitattributes (32 bytes)
📊 File types: {'gitattributes': 1, 'gitignore': 1, 'gitmodules': 1, 'md': 2, 'no_ext': 1, 'xml': 1}
📊 Total content size: 11046 bytes

2️⃣ Testing REAL _download_all_git_files_to_disk_azure_devops
🔄 Testing direct Azure DevOps download function...
{"timestamp": "2025-07-24T23:19:44.312738+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Setting up repository access", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.312759+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Fetching Azure DevOps credentials from URL: https://archie-github-handler-480762617400.us-central1.run.app/v1/github/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/access-token", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.361979+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): oauth2.googleapis.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.422863+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://oauth2.googleapis.com:443 \"POST /token HTTP/1.1\" 200 None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.423783+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): archie-github-handler-480762617400.us-central1.run.app:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.641256+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://archie-github-handler-480762617400.us-central1.run.app:443 \"GET /v1/github/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/access-token HTTP/1.1\" 200 2459", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.641463+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Successfully fetched Azure DevOps credentials by repo id", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.641501+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Received response from fetching Azure DevOps credentials", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.641520+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Organization name: blitzy-qa, Organization ID: 4b316c82-e2a7-42d3-bd0d-7d2c5933b791", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.641645+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Azure DevOps organization: 4b316c82-e2a7-42d3-bd0d-7d2c5933b791, project_id: 1810cd4a-6912-4f05-b39f-b93d75822f2d", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.641659+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Getting repository information for repo_id: d9db696a-b353-40f3-9e42-07ccd5f6c947, repo_name: 19July_Lakshya_back_prop, to check if repo exists", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.641700+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.642781+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.643859+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.643913+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.646174+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.646307+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: {project}/_apis/{area}/{resource}/{repositoryId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.646366+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.646386+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.646410+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.646461+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.646477+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.646494+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.646505+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.646949+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.796099+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947 HTTP/1.1\" 200 690", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.796344+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"id\":\"d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"name\":\"19July_Lakshya_back_prop\",\"url\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"project\":{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"},\"defaultBranch\":\"refs/heads/main\",\"size\":4989,\"remoteUrl\":\"https://<EMAIL>/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"sshUrl\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\",\"webUrl\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"_links\":{\"self\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\"},\"project\":{\"href\":\"vstfs:///Classification/TeamProject/1810cd4a-6912-4f05-b39f-b93d75822f2d\"},\"web\":{\"href\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\"},\"ssh\":{\"href\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\"},\"commits\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/commits\"},\"refs\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/refs\"},\"pullRequests\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pullRequests\"},\"items\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/items\"},\"pushes\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pushes\"}},\"isDisabled\":false,\"isInMaintenance\":false}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.796600+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Found repository: {'id': 'd9db696a-b353-40f3-9e42-07ccd5f6c947', 'name': '19July_Lakshya_back_prop', 'url': 'https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947', 'default_branch': 'main'}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.796641+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Attempting to download Azure DevOps repository using git clone: 19July_Lakshya_back_prop", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.796681+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.797910+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.798901+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.798949+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.800838+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.800900+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: {project}/_apis/{area}/{resource}/{repositoryId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.800944+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.800961+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.800973+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.800998+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.801011+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.801024+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.801034+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.801483+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.915894+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947 HTTP/1.1\" 200 690", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.916153+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"id\":\"d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"name\":\"19July_Lakshya_back_prop\",\"url\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"project\":{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"},\"defaultBranch\":\"refs/heads/main\",\"size\":4989,\"remoteUrl\":\"https://<EMAIL>/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"sshUrl\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\",\"webUrl\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"_links\":{\"self\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\"},\"project\":{\"href\":\"vstfs:///Classification/TeamProject/1810cd4a-6912-4f05-b39f-b93d75822f2d\"},\"web\":{\"href\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\"},\"ssh\":{\"href\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\"},\"commits\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/commits\"},\"refs\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/refs\"},\"pullRequests\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pullRequests\"},\"items\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/items\"},\"pushes\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pushes\"}},\"isDisabled\":false,\"isInMaintenance\":false}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.916396+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.917956+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.919425+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.919471+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.922518+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.922606+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: _apis/{resource}/{*projectId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.922673+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.922700+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/_apis/projects", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.922737+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.922769+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.922786+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.922804+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.922819+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:44.923334+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.056442+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/_apis/projects HTTP/1.1\" 200 334", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.056715+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"count\":1,\"value\":[{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"}]}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.056934+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Found project ID '1810cd4a-6912-4f05-b39f-b93d75822f2d' with name: hello-world-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.057027+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Removing existing directory: blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.066008+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Cloning Azure DevOps repository: https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop to blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.521690+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Azure DevOps repository cloned successfully to: blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.531038+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Found .gitmodules file, configuring submodule authentication", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.531170+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": ".gitmodules content: [submodule \"libs/mysubmodule\"]\n  path = libs/mysubmodule\n  url = https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_FWD_Prop_", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.540797+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Found submodule path: libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.540859+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Configuring authentication for 1 submodules: ['libs/mysubmodule']", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.549466+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Configuring auth for submodule: libs/mysubmodule (path: libs/mysubmodule)", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.558648+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Submodule libs/mysubmodule URL: https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_FWD_Prop_", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.558744+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Setting authenticated URL for submodule libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.567811+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Configured auth for Azure DevOps submodule: libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.567917+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Checking out specific commit: 34fcbe813b071f8fb8e264ca54075bef38594700", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.579089+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Checked out commit: 34fcbe813b071f8fb8e264ca54075bef38594700", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.579162+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Updating submodules to match the checked out commit", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.579184+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Re-configuring submodule authentication after commit checkout", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.579214+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Found .gitmodules file, configuring submodule authentication", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.579288+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": ".gitmodules content: [submodule \"libs/mysubmodule\"]\n  path = libs/mysubmodule\n  url = https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_FWD_Prop_", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.588712+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Found submodule path: libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.588787+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Configuring authentication for 1 submodules: ['libs/mysubmodule']", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.598101+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Configuring auth for submodule: libs/mysubmodule (path: libs/mysubmodule)", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.606322+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Submodule libs/mysubmodule URL: https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_FWD_Prop_", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.606406+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Setting authenticated URL for submodule libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.614635+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Configured auth for Azure DevOps submodule: libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.614706+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Step 1: Initializing submodules", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.712411+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Submodules initialized successfully", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.712501+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Step 2: Re-configuring submodule authentication after init", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.712550+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Found .gitmodules file, configuring submodule authentication", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.712649+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": ".gitmodules content: [submodule \"libs/mysubmodule\"]\n  path = libs/mysubmodule\n  url = https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_FWD_Prop_", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.722807+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Found submodule path: libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.722914+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Configuring authentication for 1 submodules: ['libs/mysubmodule']", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.732500+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Configuring auth for submodule: libs/mysubmodule (path: libs/mysubmodule)", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.742550+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Submodule libs/mysubmodule URL: https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_FWD_Prop_", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.742670+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Setting authenticated URL for submodule libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.752447+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Configured auth for Azure DevOps submodule: libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.752567+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Step 3: Updating submodules", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.832745+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Submodules updated successfully to match the checked out commit", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.832827+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Step 4: Verifying submodules were actually cloned", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.842242+00:00", "severity": "WARNING", "level": "WARNING", "name": "root", "message": "Submodules appear to be defined in .gitmodules but not actually committed/cloned", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.842315+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Attempting manual submodule clone for uncommitted submodules", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.842348+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Attempting manual submodule cloning", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:45.851900+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Manually cloning submodule libs/mysubmodule to libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.179512+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Successfully cloned submodule libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.179967+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 1: pom.xml", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.180106+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 2: .gitmodules", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.180198+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 3: README.md", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.180266+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 4: .gitignore", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.180330+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 5: .gitattributes", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.180392+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 6: Jenkins", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.180527+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 7: libs/mysubmodule/README.md", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.181442+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Total files read from cloned repository: 7", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.181482+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Successfully downloaded 7 files from Azure DevOps repository using git clone", "service": "archie-service-backend"}
✅ _download_all_git_files_to_disk_azure_devops completed!
📊 Files downloaded: 7
📄 Sample files:
   1. pom.xml
      Content preview: <?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         x...
   2. .gitmodules
      Content preview: [submodule "libs/mysubmodule"]
  path = libs/mysubmodule
  url = https://dev.azure.com/blitzy-qa/hel...
   3. README.md
      Content preview:  # :fallen_leaf: :leaves: Testinium-QA :leaves: :fallen_leaf:
Automating the Testinium browser  (JAV...

3️⃣ Testing for actual cloned repository files on disk
🔍 Looking for cloned repositories in: /var/folders/m3/nysj737d3bx2tp1525d0ln0m0000gn/T
📁 Found 3 potential clone directories:
   📁 /var/folders/m3/nysj737d3bx2tp1525d0ln0m0000gn/T/azure_real_test_m2nk39_i
      📊 Contains 0 files
   📁 /var/folders/m3/nysj737d3bx2tp1525d0ln0m0000gn/T/azure_real_test_m2nk39_i
      📊 Contains 0 files
   📁 /var/folders/m3/nysj737d3bx2tp1525d0ln0m0000gn/T/real_azure_test_e6lrpm3z
      📊 Contains 0 files

======================================================================
🎯 REAL AZURE DEVOPS DOWNLOAD TEST SUMMARY
======================================================================
📊 Total Tests: 3
✅ Passed: 3
❌ Failed: 0
💥 Errors: 0

🎉 ALL REAL DOWNLOAD TESTS PASSED!
✅ Real Azure DevOps repository download is working correctly

2️⃣ Testing REAL _download_all_git_files_to_disk_azure_devops
🔄 Testing direct Azure DevOps download function...
{"timestamp": "2025-07-24T23:19:46.184917+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Setting up repository access", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.184949+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Fetching Azure DevOps credentials from URL: https://archie-github-handler-480762617400.us-central1.run.app/v1/github/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/access-token", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.232667+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): oauth2.googleapis.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.307987+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://oauth2.googleapis.com:443 \"POST /token HTTP/1.1\" 200 None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.308868+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): archie-github-handler-480762617400.us-central1.run.app:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.493622+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://archie-github-handler-480762617400.us-central1.run.app:443 \"GET /v1/github/repositories/36c8d0c2-4d87-4658-920e-68d9c9754ef6/access-token HTTP/1.1\" 200 2459", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.493829+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Successfully fetched Azure DevOps credentials by repo id", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.493865+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Received response from fetching Azure DevOps credentials", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.493882+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Organization name: blitzy-qa, Organization ID: 4b316c82-e2a7-42d3-bd0d-7d2c5933b791", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.494003+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Azure DevOps organization: 4b316c82-e2a7-42d3-bd0d-7d2c5933b791, project_id: 1810cd4a-6912-4f05-b39f-b93d75822f2d", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.494027+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Getting repository information for repo_id: d9db696a-b353-40f3-9e42-07ccd5f6c947, repo_name: 19July_Lakshya_back_prop, to check if repo exists", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.494077+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.495396+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.496508+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.496562+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.498636+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.498731+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: {project}/_apis/{area}/{resource}/{repositoryId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.498781+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.498799+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.498816+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.498863+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.498878+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.498892+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.498903+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.499311+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.620647+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947 HTTP/1.1\" 200 690", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.620866+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"id\":\"d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"name\":\"19July_Lakshya_back_prop\",\"url\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"project\":{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"},\"defaultBranch\":\"refs/heads/main\",\"size\":4989,\"remoteUrl\":\"https://<EMAIL>/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"sshUrl\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\",\"webUrl\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"_links\":{\"self\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\"},\"project\":{\"href\":\"vstfs:///Classification/TeamProject/1810cd4a-6912-4f05-b39f-b93d75822f2d\"},\"web\":{\"href\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\"},\"ssh\":{\"href\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\"},\"commits\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/commits\"},\"refs\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/refs\"},\"pullRequests\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pullRequests\"},\"items\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/items\"},\"pushes\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pushes\"}},\"isDisabled\":false,\"isInMaintenance\":false}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.621049+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Found repository: {'id': 'd9db696a-b353-40f3-9e42-07ccd5f6c947', 'name': '19July_Lakshya_back_prop', 'url': 'https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947', 'default_branch': 'main'}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.621068+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Attempting to download Azure DevOps repository using git clone: 19July_Lakshya_back_prop", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.621094+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.622149+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.623191+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.623226+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.625083+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.625138+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: {project}/_apis/{area}/{resource}/{repositoryId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.625178+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.625194+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.625205+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.625228+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.625240+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.625252+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.625262+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.626619+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.741816+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947 HTTP/1.1\" 200 690", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.742038+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"id\":\"d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"name\":\"19July_Lakshya_back_prop\",\"url\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\",\"project\":{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"},\"defaultBranch\":\"refs/heads/main\",\"size\":4989,\"remoteUrl\":\"https://<EMAIL>/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"sshUrl\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\",\"webUrl\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\",\"_links\":{\"self\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947\"},\"project\":{\"href\":\"vstfs:///Classification/TeamProject/1810cd4a-6912-4f05-b39f-b93d75822f2d\"},\"web\":{\"href\":\"https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop\"},\"ssh\":{\"href\":\"*********************:v3/blitzy-qa/hello-world-qa/19July_Lakshya_back_prop\"},\"commits\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/commits\"},\"refs\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/refs\"},\"pullRequests\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pullRequests\"},\"items\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/items\"},\"pushes\":{\"href\":\"https://dev.azure.com/blitzy-qa/1810cd4a-6912-4f05-b39f-b93d75822f2d/_apis/git/repositories/d9db696a-b353-40f3-9e42-07ccd5f6c947/pushes\"}},\"isDisabled\":false,\"isInMaintenance\":false}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.742220+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Creating Azure DevOps connection to: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.743321+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.744427+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.744480+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.connection", "message": "File cache hit for resources on: https://dev.azure.com/blitzy-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.746505+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http.requests", "message": "Configuring retry: max_retries=3, backoff_factor=0.8, max_backoff=90", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.746549+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Route template: _apis/{resource}/{*projectId}", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.746591+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Api version '7.0'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.746609+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "GET https://dev.azure.com/blitzy-qa/_apis/projects", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.746621+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Request content: None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.746645+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring redirects: allow=True, max=30", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.746658+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring request: timeout=100, verify=True, cert=None", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.746671+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Configuring proxies: ''", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.746682+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "msrest.universal_http", "message": "Evaluate proxies against ENV settings: True", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.747086+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "Starting new HTTPS connection (1): dev.azure.com:443", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.863965+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "urllib3.connectionpool", "message": "https://dev.azure.com:443 \"GET /blitzy-qa/_apis/projects HTTP/1.1\" 200 334", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.864203+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "azure.devops.client", "message": "Response content: b'{\"count\":1,\"value\":[{\"id\":\"1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"name\":\"hello-world-qa\",\"url\":\"https://dev.azure.com/blitzy-qa/_apis/projects/1810cd4a-6912-4f05-b39f-b93d75822f2d\",\"state\":\"wellFormed\",\"revision\":11,\"visibility\":\"private\",\"lastUpdateTime\":\"2025-07-07T13:27:17.14Z\"}]}'", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.864392+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Found project ID '1810cd4a-6912-4f05-b39f-b93d75822f2d' with name: hello-world-qa", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.864473+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Removing existing directory: blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:46.871648+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Cloning Azure DevOps repository: https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_Lakshya_back_prop to blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.359195+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Azure DevOps repository cloned successfully to: blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.368808+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Found .gitmodules file, configuring submodule authentication", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.368917+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": ".gitmodules content: [submodule \"libs/mysubmodule\"]\n  path = libs/mysubmodule\n  url = https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_FWD_Prop_", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.376910+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Found submodule path: libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.376962+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Configuring authentication for 1 submodules: ['libs/mysubmodule']", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.385486+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Configuring auth for submodule: libs/mysubmodule (path: libs/mysubmodule)", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.393937+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Submodule libs/mysubmodule URL: https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_FWD_Prop_", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.394048+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Setting authenticated URL for submodule libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.402806+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Configured auth for Azure DevOps submodule: libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.402884+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Checking out specific commit: 34fcbe813b071f8fb8e264ca54075bef38594700", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.412645+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Checked out commit: 34fcbe813b071f8fb8e264ca54075bef38594700", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.412711+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Updating submodules to match the checked out commit", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.412728+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Re-configuring submodule authentication after commit checkout", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.412756+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Found .gitmodules file, configuring submodule authentication", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.412817+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": ".gitmodules content: [submodule \"libs/mysubmodule\"]\n  path = libs/mysubmodule\n  url = https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_FWD_Prop_", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.421397+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Found submodule path: libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.421453+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Configuring authentication for 1 submodules: ['libs/mysubmodule']", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.429721+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Configuring auth for submodule: libs/mysubmodule (path: libs/mysubmodule)", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.437462+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Submodule libs/mysubmodule URL: https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_FWD_Prop_", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.437540+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Setting authenticated URL for submodule libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.445178+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Configured auth for Azure DevOps submodule: libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.445245+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Step 1: Initializing submodules", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.516017+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Submodules initialized successfully", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.516085+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Step 2: Re-configuring submodule authentication after init", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.516117+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Found .gitmodules file, configuring submodule authentication", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.516169+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": ".gitmodules content: [submodule \"libs/mysubmodule\"]\n  path = libs/mysubmodule\n  url = https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_FWD_Prop_", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.524440+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Found submodule path: libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.524486+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Configuring authentication for 1 submodules: ['libs/mysubmodule']", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.532290+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Configuring auth for submodule: libs/mysubmodule (path: libs/mysubmodule)", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.539616+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Submodule libs/mysubmodule URL: https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_FWD_Prop_", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.539704+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Setting authenticated URL for submodule libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.547464+00:00", "severity": "DEBUG", "level": "DEBUG", "name": "root", "message": "Configured auth for Azure DevOps submodule: libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.547522+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Step 3: Updating submodules", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.617857+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Submodules updated successfully to match the checked out commit", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.617934+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Step 4: Verifying submodules were actually cloned", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.627379+00:00", "severity": "WARNING", "level": "WARNING", "name": "root", "message": "Submodules appear to be defined in .gitmodules but not actually committed/cloned", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.627442+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Attempting manual submodule clone for uncommitted submodules", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.627468+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Attempting manual submodule cloning", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.636457+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Manually cloning submodule libs/mysubmodule to libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.983040+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Successfully cloned submodule libs/mysubmodule", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.983627+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 1: pom.xml", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.983708+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 2: .gitmodules", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.983777+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 3: README.md", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.983834+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 4: .gitignore", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.983892+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 5: .gitattributes", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.988623+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 6: Jenkins", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.988787+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Read file 7: libs/mysubmodule/README.md", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.989852+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Total files read from cloned repository: 7", "service": "archie-service-backend"}
{"timestamp": "2025-07-24T23:19:47.989903+00:00", "severity": "INFO", "level": "INFO", "name": "root", "message": "Successfully downloaded 7 files from Azure DevOps repository using git clone", "service": "archie-service-backend"}
✅ _download_all_git_files_to_disk_azure_devops completed!
📊 Files downloaded: 7
📄 Sample files:
   1. pom.xml
      Content preview: <?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         x...
   2. .gitmodules
      Content preview: [submodule "libs/mysubmodule"]
  path = libs/mysubmodule
  url = https://dev.azure.com/blitzy-qa/hel...
   3. README.md
      Content preview:  # :fallen_leaf: :leaves: Testinium-QA :leaves: :fallen_leaf:
Automating the Testinium browser  (JAV...

3️⃣ Testing for actual cloned repository files and submodule support
🔍 Looking for cloned repositories in: /var/folders/m3/nysj737d3bx2tp1525d0ln0m0000gn/T
🔍 Also checking current directory for blitzy/ structure
📁 Found 57 potential clone directories:
   📁 /var/folders/m3/nysj737d3bx2tp1525d0ln0m0000gn/T/azure_real_test_m2nk39_i
      📊 Contains 0 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
   📁 /var/folders/m3/nysj737d3bx2tp1525d0ln0m0000gn/T/azure_real_test_m2nk39_i
      📊 Contains 0 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
   📁 /var/folders/m3/nysj737d3bx2tp1525d0ln0m0000gn/T/real_azure_test_e6lrpm3z
      📊 Contains 0 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
   📁 blitzy/19July_Lakshya_back_prop
      🔗 Found .gitmodules file: ADO_Branch_10AM/.gitmodules
      📄 .gitmodules content preview:
         [submodule "libs/mysubmodule"]
  path = libs/mysubmodule
  url = https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_FWD_Prop_...
      📊 Contains 73 total files
      📊 Git-related files: 69
      📊 Submodule files: 1
      📄 Sample files: ['ADO_Branch_10AM/pom.xml', 'ADO_Branch_10AM/.gitmodules', 'ADO_Branch_10AM/README.md', 'ADO_Branch_10AM/.gitignore', 'ADO_Branch_10AM/.gitattributes']
      🔍 Repository analysis:
         Has .git directory: True
         Has .gitmodules: True
      ✅ Repository appears to support submodules!
      📊 Submodule directories found: 0
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM
      🔗 Found .gitmodules file: .gitmodules
      📄 .gitmodules content preview:
         [submodule "libs/mysubmodule"]
  path = libs/mysubmodule
  url = https://dev.azure.com/blitzy-qa/hello-world-qa/_git/19July_FWD_Prop_...
      📊 Contains 73 total files
      📊 Git-related files: 69
      📊 Submodule files: 1
      📄 Sample files: ['pom.xml', '.gitmodules', 'README.md', '.gitignore', '.gitattributes']
      🔍 Repository analysis:
         Has .git directory: True
         Has .gitmodules: True
      ✅ Repository appears to support submodules!
      📊 Submodule directories found: 0
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs
      📊 Contains 28 total files
      📊 Git-related files: 27
      📊 Submodule files: 0
      📄 Sample files: ['mysubmodule/README.md', 'mysubmodule/.git/config', 'mysubmodule/.git/HEAD', 'mysubmodule/.git/description', 'mysubmodule/.git/index']
      🔍 Repository analysis:
         Has .git directory: True
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule
      📊 Contains 28 total files
      📊 Git-related files: 27
      📊 Submodule files: 0
      📄 Sample files: ['README.md', '.git/config', '.git/HEAD', '.git/description', '.git/index']
      🔍 Repository analysis:
         Has .git directory: True
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git
      📊 Contains 27 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['config', 'HEAD', 'description', 'index', 'packed-refs']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/objects
      📊 Contains 3 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['0c/a446aab9d09eac8625b53e3df8da661976c458', '98/04b758e84cac41a6acc4d011f57310a1f63102', 'c4/139fcef7a82b9204cde6ab01b139760d881817']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/objects/0c
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['a446aab9d09eac8625b53e3df8da661976c458']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/objects/pack
      📊 Contains 0 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/objects/info
      📊 Contains 0 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/objects/98
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['04b758e84cac41a6acc4d011f57310a1f63102']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/objects/c4
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['139fcef7a82b9204cde6ab01b139760d881817']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/info
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['exclude']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/logs
      📊 Contains 3 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['HEAD', 'refs/heads/main', 'refs/remotes/origin/HEAD']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/logs/refs
      📊 Contains 2 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['heads/main', 'remotes/origin/HEAD']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/logs/refs/heads
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['main']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/logs/refs/remotes
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['origin/HEAD']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/logs/refs/remotes/origin
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['HEAD']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/hooks
      📊 Contains 13 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['commit-msg.sample', 'pre-rebase.sample', 'pre-commit.sample', 'applypatch-msg.sample', 'fsmonitor-watchman.sample']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/refs
      📊 Contains 2 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['heads/main', 'remotes/origin/HEAD']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/refs/heads
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['main']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/refs/tags
      📊 Contains 0 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/refs/remotes
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['origin/HEAD']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/refs/remotes/origin
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['HEAD']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git
      📊 Contains 39 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['config', 'HEAD', 'description', 'index', 'packed-refs']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects
      📊 Contains 15 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['0c/a446aab9d09eac8625b53e3df8da661976c458', '6f/8ca8dc4e56ebdcafb48ae9ed819a81c17f5223', '34/fcbe813b071f8fb8e264ca54075bef38594700', '5f/da9eff7ad139c4f394a6d2218af5ac167fea96', 'ae/afa313113c53b39cb2393e7a8a3e5556d1c119']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/0c
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['a446aab9d09eac8625b53e3df8da661976c458']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/6f
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['8ca8dc4e56ebdcafb48ae9ed819a81c17f5223']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/34
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['fcbe813b071f8fb8e264ca54075bef38594700']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/5f
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['da9eff7ad139c4f394a6d2218af5ac167fea96']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/ae
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['afa313113c53b39cb2393e7a8a3e5556d1c119']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/fb
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['3ee9ff7b4b10b273741720df8d5e755edf0716']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/pack
      📊 Contains 0 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/2f
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['f9b16078bee35cc59d2bd3f08823202dd80c8d']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/07
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['c963fb6de5521059f9511fe73c53a4efbae23b']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/00
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['50632a1377956d68fb9c4d11a5ab3422ff3263']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/info
      📊 Contains 0 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/98
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['04b758e84cac41a6acc4d011f57310a1f63102']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/f0
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['d299ed5c4a3779654b488ac9f892f125474c31']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/41
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['af03230029b5e4b54c962f73a595f71f45906b']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/4a
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['30f0519248898f6e2f0cb7ab457cd19179f25e']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/15
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['10f0725d77f28f9b161e95f8bde56b1caf9fba']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/7a
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['9014a30331be90f02502c4e87b0134689115b9']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/info
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['exclude']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs
      📊 Contains 3 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['HEAD', 'refs/heads/main', 'refs/remotes/origin/HEAD']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs/refs
      📊 Contains 2 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['heads/main', 'remotes/origin/HEAD']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs/refs/heads
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['main']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs/refs/remotes
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['origin/HEAD']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs/refs/remotes/origin
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['HEAD']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/hooks
      📊 Contains 13 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['commit-msg.sample', 'pre-rebase.sample', 'pre-commit.sample', 'applypatch-msg.sample', 'fsmonitor-watchman.sample']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/refs
      📊 Contains 2 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['heads/main', 'remotes/origin/HEAD']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/refs/heads
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['main']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/refs/tags
      📊 Contains 0 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/refs/remotes
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['origin/HEAD']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False
   📁 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/refs/remotes/origin
      📊 Contains 1 total files
      📊 Git-related files: 0
      📊 Submodule files: 0
      📄 Sample files: ['HEAD']
      🔍 Repository analysis:
         Has .git directory: False
         Has .gitmodules: False

4️⃣ Testing Git Clone Command Submodule Support
🔍 Analyzing git clone implementation:
   ✅ Contains '--recurse-submodules' flag: True
   ✅ Contains submodule references: True
   🎉 CONFIRMED: Azure DevOps git clone includes submodule support!

📁 Keeping 48 directories for inspection:
   1. blitzy/19July_Lakshya_back_prop
   2. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM
   3. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs
   4. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule
   5. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git
   6. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/objects
   7. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/objects/0c
   8. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/objects/98
   9. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/objects/c4
   10. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/info
   11. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/logs
   12. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/logs/refs
   13. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/logs/refs/heads
   14. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/logs/refs/remotes
   15. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/logs/refs/remotes/origin
   16. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/hooks
   17. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/refs
   18. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/refs/heads
   19. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/refs/remotes
   20. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/refs/remotes/origin
   21. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git
   22. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects
   23. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/0c
   24. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/6f
   25. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/34
   26. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/5f
   27. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/ae
   28. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/fb
   29. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/2f
   30. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/07
   31. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/00
   32. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/98
   33. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/f0
   34. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/41
   35. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/4a
   36. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/15
   37. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/7a
   38. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/info
   39. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs
   40. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs/refs
   41. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs/refs/heads
   42. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs/refs/remotes
   43. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs/refs/remotes/origin
   44. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/hooks
   45. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/refs
   46. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/refs/heads
   47. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/refs/remotes
   48. blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/refs/remotes/origin

💡 To clean up: rm -rf blitzy/19July_Lakshya_back_prop blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/objects blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/objects/0c blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/objects/98 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/objects/c4 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/info blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/logs blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/logs/refs blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/logs/refs/heads blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/logs/refs/remotes blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/logs/refs/remotes/origin blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/hooks blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/refs blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/refs/heads blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/refs/remotes blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/libs/mysubmodule/.git/refs/remotes/origin blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/0c blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/6f blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/34 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/5f blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/ae blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/fb blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/2f blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/07 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/00 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/98 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/f0 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/41 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/4a blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/15 blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/objects/7a blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/info blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs/refs blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs/refs/heads blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs/refs/remotes blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/logs/refs/remotes/origin blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/hooks blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/refs blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/refs/heads blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/refs/remotes blitzy/19July_Lakshya_back_prop/ADO_Branch_10AM/.git/refs/remotes/origin
💡 Or set CLEANUP_AFTER_TEST = True in the script

💾 Results saved to: real_azure_download_results.json
