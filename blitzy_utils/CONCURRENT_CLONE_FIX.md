# Concurrent Clone Fix for Reverse Code Generator

## Problem Description

The reverse-code-generator container/job was experiencing failures when attempting to clone the same repository multiple times concurrently. The error was:

```
ERROR 2025-07-21T22:31:47.033648Z Error cloning repository: fatal: destination path 'blitzy/cobol-casualty-claims/main' already exists and is not an empty directory.
```

This occurred because:
1. Multiple concurrent processes were trying to clone to the same destination path
2. The directory existence check and removal wasn't atomic
3. Race conditions caused git clone to fail when another process had already created the directory

## Root Cause Analysis

The issue was in both `clone_repository_with_auth()` (GitHub) and `clone_azure_devops_repository_with_auth()` (Azure DevOps) functions. While they did check for existing directories and remove them, the operations weren't synchronized across concurrent processes.

**Race Condition Scenario:**
1. Process A checks if directory exists → No
2. Process B checks if directory exists → No  
3. Process A starts git clone, creates directory
4. Process B starts git clone → **FAILS** because directory now exists

## Solution Implemented

### 1. File-Based Locking Mechanism

Added exclusive file locking using `fcntl.LOCK_EX` to ensure only one process can clone to a specific path at a time:

```python
import fcntl

# Create a lock file path based on the clone path
lock_file_path = f"{clone_path}.lock"
lock_file = open(lock_file_path, 'w')
fcntl.flock(lock_file.fileno(), fcntl.LOCK_EX)
```

### 2. Smart Repository Reuse

Enhanced the logic to detect and reuse existing valid git repositories:

```python
if os.path.exists(clone_path):
    git_dir = os.path.join(clone_path, '.git')
    if os.path.exists(git_dir):
        # Verify it's the correct repository by checking remote URL
        result = subprocess.run(
            ["git", "-C", clone_path, "remote", "get-url", "origin"],
            capture_output=True, text=True, check=False
        )
        if result.returncode == 0:
            existing_url = result.stdout.strip()
            # Check if this matches our expected repository
            if expected_repo_matches(existing_url):
                logger.info("Existing repository matches, skipping clone")
                return True
```

### 3. Proper Cleanup

Added `finally` blocks to ensure locks are always released:

```python
finally:
    if lock_file:
        try:
            fcntl.flock(lock_file.fileno(), fcntl.LOCK_UN)
            lock_file.close()
            if os.path.exists(lock_file_path):
                os.remove(lock_file_path)
        except Exception as e:
            logger.warning(f"Error releasing lock: {e}")
```

## Files Modified

### 1. `blitzy_utils/github.py`
- Enhanced `clone_repository_with_auth()` function
- Added file locking mechanism
- Added repository URL verification for reuse
- Added proper lock cleanup

### 2. `blitzy_utils/azure_git_clone.py`  
- Enhanced `clone_azure_devops_repository_with_auth()` function
- Added file locking mechanism
- Added Azure DevOps repository URL verification
- Added proper lock cleanup

## Benefits of the Fix

### 1. **Eliminates Race Conditions**
- Only one process can clone to a specific path at a time
- Atomic directory operations prevent conflicts

### 2. **Improves Performance**
- Reuses existing valid repositories instead of re-cloning
- Reduces unnecessary network traffic and disk I/O

### 3. **Better Resource Management**
- Proper lock cleanup prevents resource leaks
- Handles edge cases and error scenarios gracefully

### 4. **Maintains Backward Compatibility**
- No changes to function signatures
- Existing code continues to work without modifications

## Testing

Created comprehensive tests to verify the fix:

### 1. `test_concurrent_clone_fix.py`
- Tests concurrent clones to the same path
- Verifies proper locking behavior
- Confirms lock cleanup

### 2. `test_directory_reuse.py`
- Tests repository reuse scenarios
- Verifies URL matching logic
- Tests handling of non-git directories

## Test Results

✅ **Concurrent Clone Test**: All workers properly acquire/release locks sequentially
✅ **Directory Reuse Test**: Correctly identifies and reuses matching repositories  
✅ **Lock Cleanup Test**: All lock files are properly cleaned up
✅ **Error Handling Test**: Graceful handling of authentication and network errors

## Impact on Reverse Code Generator

This fix directly addresses the original error in the reverse-code-generator:

**Before Fix:**
```
ERROR: fatal: destination path 'blitzy/cobol-casualty-claims/main' already exists and is not an empty directory.
```

**After Fix:**
- Multiple concurrent clones work without conflicts
- Existing repositories are reused when appropriate
- No more "directory already exists" errors
- Improved reliability and performance

## Deployment Notes

1. **No Breaking Changes**: The fix is backward compatible
2. **No Configuration Required**: Works automatically with existing code
3. **Cross-Platform**: Uses standard POSIX file locking (works on Linux containers)
4. **Memory Efficient**: Minimal overhead from locking mechanism

## Monitoring

The fix includes enhanced logging to monitor:
- Lock acquisition and release
- Repository reuse decisions
- Error conditions and recovery

Look for these log messages:
- `"Acquired lock for cloning to: {path}"`
- `"Released lock for: {path}"`
- `"Existing repository matches, skipping clone"`
- `"Removing existing directory: {path}"`
