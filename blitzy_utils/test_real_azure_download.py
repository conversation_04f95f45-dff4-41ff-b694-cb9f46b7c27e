#!/usr/bin/env python3
"""
Real Azure DevOps Repository Download Test

This script tests the REAL download_repository_to_disk function for Azure DevOps
without any mocking. It will attempt actual git clone operations.

Usage: python test_real_azure_download.py
"""

import os
import sys
import tempfile
import shutil
import json
from typing import Dict, Any, List

# Configuration
CLEANUP_AFTER_TEST = False  # Set to True to clean up files, False to keep for inspection

# Add the parent directory to path for relative imports
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

def test_real_azure_devops_download():
    """Test the real Azure DevOps download_repository_to_disk function."""
    print("🧪 Testing REAL Azure DevOps Repository Download")
    print("=" * 70)
    
    temp_dirs = []
    results = {
        'tests': [],
        'summary': {'total': 0, 'passed': 0, 'failed': 0, 'errors': 0}
    }
    
    try:
        # Import the REAL implementation
        print("📦 Importing REAL Azure DevOps download functions...")
        
        from blitzy_utils.github import download_repository_to_disk
        from blitzy_utils.azure import _download_all_git_files_to_disk_azure_devops
        from blitzy_utils.common import BlitzyGitFile
        
        print("✅ Successfully imported REAL download functions!")
        
        # Test data using your exact format
        test_data = {
            "job_id": "939a3370-387d-427d-b169-77579fd57cd9",
            "resume": True,
            "repo_id": "d9db696a-b353-40f3-9e42-07ccd5f6c947",
            "team_id": "0e930d20-17c0-4301-9a55-49b8694339d8",
            "user_id": "2887a75e-9b01-4787-a4a0-bee4e919f4f8",
            "branch_id": "df042e05-d8b4-47df-97a1-44932e0c4698",
            "propagate": True,
            "repo_name": "19July_Lakshya_back_prop",
            "company_id": "4b01c459-254e-4f28-8b5d-923584f0bf82",
            "project_id": "6877cbe0-bbfc-442c-975a-0052937b353e",
            "branch_name": "ADO_Branch_10AM",
            "tech_spec_id": "22b39eb2-657d-4e0e-8cd2-32386a514842",
            # "head_commit_hash": "4a30f0519248898f6e2f0cb7ab457cd19179f25e",
            "head_commit_hash" : "34fcbe813b071f8fb8e264ca54075bef38594700",
            "git_project_repo_id": "36c8d0c2-4d87-4658-920e-68d9c9754ef6",
            "prev_head_commit_hash": ""
        }
        
        # Test 1: Real download_repository_to_disk for Azure DevOps
        print("\n1️⃣ Testing REAL download_repository_to_disk for Azure DevOps")
        try:
            print(f"🔄 Testing with repo: {test_data['repo_name']}")
            print(f"🔄 Branch: {test_data['branch_name']}")
            print(f"🔄 Commit: {test_data['head_commit_hash']}")
            print(f"🔄 Git Project Repo ID: {test_data['git_project_repo_id']}")
            
            # This will call the REAL function which should:
            # 1. Detect it's Azure DevOps (due to git_project_repo_id)
            # 2. Call _download_all_git_files_to_disk_azure_devops
            # 3. Get proper access token from the system
            # 4. Perform actual git clone
            git_files = download_repository_to_disk(
                repo_name=test_data['repo_name'],
                branch_name=test_data['branch_name'],
                user_id=test_data['user_id'],
                server="azure-devops-server",  # This will be used for token lookup
                commit_hash=test_data['head_commit_hash'],
                repo_id=test_data['repo_id'],
                git_project_repo_id=test_data['git_project_repo_id']
            )
            
            print(f"✅ download_repository_to_disk completed!")
            print(f"📊 Files downloaded: {len(git_files)}")
            
            if len(git_files) > 0:
                print(f"📄 Sample files:")
                for i, git_file in enumerate(git_files[:5]):  # Show first 5 files
                    print(f"   {i+1}. {git_file.path} ({len(git_file.text)} bytes)")
                
                # Analyze file types
                file_types = {}
                total_size = 0
                for git_file in git_files:
                    ext = git_file.path.split('.')[-1] if '.' in git_file.path else 'no_ext'
                    file_types[ext] = file_types.get(ext, 0) + 1
                    total_size += len(git_file.text)
                
                print(f"📊 File types: {dict(sorted(file_types.items()))}")
                print(f"📊 Total content size: {total_size} bytes")
                
                results['tests'].append({
                    'test': 'Real Azure DevOps Download',
                    'status': 'PASS',
                    'files_count': len(git_files),
                    'total_size': total_size,
                    'file_types': file_types,
                    'sample_files': [{'path': f.path, 'size': len(f.text)} for f in git_files[:5]]
                })
                results['summary']['passed'] += 1
            else:
                print("⚠️  No files downloaded - this might indicate an issue")
                results['tests'].append({
                    'test': 'Real Azure DevOps Download',
                    'status': 'FAIL',
                    'reason': 'No files downloaded'
                })
                results['summary']['failed'] += 1
                
            results['summary']['total'] += 1
            
        except Exception as e:
            print(f"❌ download_repository_to_disk error: {e}")
            print(f"Error type: {type(e).__name__}")
            import traceback
            traceback.print_exc()
            
            results['tests'].append({
                'test': 'Real Azure DevOps Download',
                'status': 'ERROR',
                'error': str(e),
                'error_type': type(e).__name__
            })
            results['summary']['errors'] += 1
            results['summary']['total'] += 1
        
        # Test 2: Direct Azure DevOps download function
        print("\n2️⃣ Testing REAL _download_all_git_files_to_disk_azure_devops")
        try:
            print(f"🔄 Testing direct Azure DevOps download function...")
            
            # This will call the Azure DevOps specific function directly
            git_files_direct = _download_all_git_files_to_disk_azure_devops(
                repo_name=test_data['repo_name'],
                branch_name=test_data['branch_name'],
                commit_hash=test_data['head_commit_hash'],
                git_project_repo_id=test_data['git_project_repo_id']
            )
            
            print(f"✅ _download_all_git_files_to_disk_azure_devops completed!")
            print(f"📊 Files downloaded: {len(git_files_direct)}")
            
            if len(git_files_direct) > 0:
                print(f"📄 Sample files:")
                for i, git_file in enumerate(git_files_direct[:3]):  # Show first 3 files
                    print(f"   {i+1}. {git_file.path}")
                    print(f"      Content preview: {git_file.text[:100]}...")
                
                results['tests'].append({
                    'test': 'Direct Azure DevOps Download',
                    'status': 'PASS',
                    'files_count': len(git_files_direct)
                })
                results['summary']['passed'] += 1
            else:
                results['tests'].append({
                    'test': 'Direct Azure DevOps Download',
                    'status': 'FAIL',
                    'reason': 'No files downloaded'
                })
                results['summary']['failed'] += 1
                
            results['summary']['total'] += 1
            
        except Exception as e:
            print(f"❌ _download_all_git_files_to_disk_azure_devops error: {e}")
            print(f"Error type: {type(e).__name__}")
            
            results['tests'].append({
                'test': 'Direct Azure DevOps Download',
                'status': 'ERROR',
                'error': str(e),
                'error_type': type(e).__name__
            })
            results['summary']['errors'] += 1
            results['summary']['total'] += 1
        
        # Test 3: Check for actual cloned files on disk
        print("\n3️⃣ Testing for actual cloned repository files on disk")
        try:
            # Look for any Azure DevOps clone directories
            temp_base = tempfile.gettempdir()
            import glob
            
            azure_clone_dirs = []
            for pattern in ['azure_*', '*azure*', f'*{test_data["repo_name"]}*']:
                azure_clone_dirs.extend(glob.glob(os.path.join(temp_base, pattern)))
            
            print(f"🔍 Looking for cloned repositories in: {temp_base}")
            
            if azure_clone_dirs:
                print(f"📁 Found {len(azure_clone_dirs)} potential clone directories:")
                for clone_dir in azure_clone_dirs:
                    print(f"   📁 {clone_dir}")
                    
                    if os.path.exists(clone_dir):
                        # List contents
                        try:
                            files = []
                            for root, dirs, filenames in os.walk(clone_dir):
                                for filename in filenames:
                                    file_path = os.path.join(root, filename)
                                    rel_path = os.path.relpath(file_path, clone_dir)
                                    files.append(rel_path)
                            
                            print(f"      📊 Contains {len(files)} files")
                            if files:
                                print(f"      📄 Sample files: {files[:5]}")
                                temp_dirs.append(clone_dir)  # Track for cleanup
                                
                        except Exception as e:
                            print(f"      ❌ Error reading directory: {e}")
                
                results['tests'].append({
                    'test': 'Disk Files Check',
                    'status': 'PASS',
                    'clone_dirs_found': len(azure_clone_dirs)
                })
                results['summary']['passed'] += 1
            else:
                print("📁 No clone directories found")
                results['tests'].append({
                    'test': 'Disk Files Check',
                    'status': 'FAIL',
                    'reason': 'No clone directories found'
                })
                results['summary']['failed'] += 1
                
            results['summary']['total'] += 1
            
        except Exception as e:
            print(f"❌ Disk files check error: {e}")
            results['tests'].append({
                'test': 'Disk Files Check',
                'status': 'ERROR',
                'error': str(e)
            })
            results['summary']['errors'] += 1
            results['summary']['total'] += 1
        
        # Summary
        print("\n" + "=" * 70)
        print("🎯 REAL AZURE DEVOPS DOWNLOAD TEST SUMMARY")
        print("=" * 70)
        
        total = results['summary']['total']
        passed = results['summary']['passed']
        failed = results['summary']['failed']
        errors = results['summary']['errors']
        
        print(f"📊 Total Tests: {total}")
        print(f"✅ Passed: {passed}")
        print(f"❌ Failed: {failed}")
        print(f"💥 Errors: {errors}")
        
        if failed == 0 and errors == 0:
            print("\n🎉 ALL REAL DOWNLOAD TESTS PASSED!")
            print("✅ Real Azure DevOps repository download is working correctly")
        else:
            print(f"\n⚠️  Some tests had issues. Check details above.")
            print("💡 This might be due to authentication or network issues")
            print("💡 Check that the git_project_repo_id and credentials are valid")
        
        # Test 2: Direct Azure DevOps download function
        print("\n2️⃣ Testing REAL _download_all_git_files_to_disk_azure_devops")
        try:
            print(f"🔄 Testing direct Azure DevOps download function...")

            # This will call the Azure DevOps specific function directly
            git_files_direct = _download_all_git_files_to_disk_azure_devops(
                repo_name=test_data['repo_name'],
                branch_name=test_data['branch_name'],
                commit_hash=test_data['head_commit_hash'],
                git_project_repo_id=test_data['git_project_repo_id']
            )

            print(f"✅ _download_all_git_files_to_disk_azure_devops completed!")
            print(f"📊 Files downloaded: {len(git_files_direct)}")

            if len(git_files_direct) > 0:
                print(f"📄 Sample files:")
                for i, git_file in enumerate(git_files_direct[:3]):  # Show first 3 files
                    print(f"   {i+1}. {git_file.path}")
                    print(f"      Content preview: {git_file.text[:100]}...")

                results['tests'].append({
                    'test': 'Direct Azure DevOps Download',
                    'status': 'PASS',
                    'files_count': len(git_files_direct)
                })
                results['summary']['passed'] += 1
            else:
                results['tests'].append({
                    'test': 'Direct Azure DevOps Download',
                    'status': 'FAIL',
                    'reason': 'No files downloaded'
                })
                results['summary']['failed'] += 1

            results['summary']['total'] += 1

        except Exception as e:
            print(f"❌ _download_all_git_files_to_disk_azure_devops error: {e}")
            print(f"Error type: {type(e).__name__}")

            results['tests'].append({
                'test': 'Direct Azure DevOps Download',
                'status': 'ERROR',
                'error': str(e),
                'error_type': type(e).__name__
            })
            results['summary']['errors'] += 1
            results['summary']['total'] += 1

        # Test 3: Check for actual cloned files on disk and submodule support
        print("\n3️⃣ Testing for actual cloned repository files and submodule support")
        try:
            # Look for any Azure DevOps clone directories
            temp_base = tempfile.gettempdir()

            azure_clone_dirs = []
            for pattern in ['azure_*', '*azure*', f'*{test_data["repo_name"]}*', 'blitzy*']:
                azure_clone_dirs.extend(glob.glob(os.path.join(temp_base, pattern)))

            # Also check for the specific clone directory structure used by the implementation
            current_dir = os.getcwd()
            blitzy_dirs = []
            if os.path.exists('blitzy'):
                for root, dirs, files in os.walk('blitzy'):
                    if test_data['repo_name'] in root:
                        blitzy_dirs.append(root)

            print(f"🔍 Looking for cloned repositories in: {temp_base}")
            print(f"🔍 Also checking current directory for blitzy/ structure")

            all_clone_dirs = azure_clone_dirs + blitzy_dirs

            if all_clone_dirs:
                print(f"📁 Found {len(all_clone_dirs)} potential clone directories:")
                for clone_dir in all_clone_dirs:
                    print(f"   📁 {clone_dir}")

                    if os.path.exists(clone_dir):
                        # List contents and check for submodules
                        try:
                            files = []
                            submodule_files = []
                            git_files = []

                            for root, dirs, filenames in os.walk(clone_dir):
                                for filename in filenames:
                                    file_path = os.path.join(root, filename)
                                    rel_path = os.path.relpath(file_path, clone_dir)
                                    files.append(rel_path)

                                    # Check for submodule indicators
                                    if filename == '.gitmodules':
                                        submodule_files.append(rel_path)
                                        print(f"      🔗 Found .gitmodules file: {rel_path}")

                                        # Read .gitmodules content
                                        try:
                                            with open(file_path, 'r') as f:
                                                gitmodules_content = f.read()
                                                print(f"      📄 .gitmodules content preview:")
                                                print(f"         {gitmodules_content[:200]}...")
                                        except Exception as e:
                                            print(f"      ❌ Error reading .gitmodules: {e}")

                                    # Check for git-related files
                                    if '.git' in rel_path:
                                        git_files.append(rel_path)

                            print(f"      📊 Contains {len(files)} total files")
                            print(f"      📊 Git-related files: {len(git_files)}")
                            print(f"      📊 Submodule files: {len(submodule_files)}")

                            if files:
                                print(f"      📄 Sample files: {files[:5]}")
                                temp_dirs.append(clone_dir)  # Track for cleanup

                                # Check if this looks like a repository with submodules
                                has_git_dir = any('.git/' in f for f in files)
                                has_gitmodules = len(submodule_files) > 0

                                print(f"      🔍 Repository analysis:")
                                print(f"         Has .git directory: {has_git_dir}")
                                print(f"         Has .gitmodules: {has_gitmodules}")

                                if has_gitmodules:
                                    print(f"      ✅ Repository appears to support submodules!")

                                    # Check for actual submodule directories
                                    submodule_dirs = []
                                    for root, dirs, filenames in os.walk(clone_dir):
                                        for dir_name in dirs:
                                            dir_path = os.path.join(root, dir_name)
                                            # Check if this directory has its own .git file (submodule indicator)
                                            git_file = os.path.join(dir_path, '.git')
                                            if os.path.exists(git_file):
                                                try:
                                                    with open(git_file, 'r') as f:
                                                        git_content = f.read().strip()
                                                        if git_content.startswith('gitdir:'):
                                                            submodule_dirs.append(os.path.relpath(dir_path, clone_dir))
                                                            print(f"      🔗 Found submodule directory: {os.path.relpath(dir_path, clone_dir)}")
                                                except:
                                                    pass

                                    print(f"      📊 Submodule directories found: {len(submodule_dirs)}")

                        except Exception as e:
                            print(f"      ❌ Error reading directory: {e}")

                results['tests'].append({
                    'test': 'Disk Files and Submodule Check',
                    'status': 'PASS',
                    'clone_dirs_found': len(all_clone_dirs),
                    'submodule_support_detected': any('.gitmodules' in str(d) for d in all_clone_dirs)
                })
                results['summary']['passed'] += 1
            else:
                print("📁 No clone directories found")
                results['tests'].append({
                    'test': 'Disk Files and Submodule Check',
                    'status': 'FAIL',
                    'reason': 'No clone directories found'
                })
                results['summary']['failed'] += 1

            results['summary']['total'] += 1

        except Exception as e:
            print(f"❌ Disk files and submodule check error: {e}")
            results['tests'].append({
                'test': 'Disk Files and Submodule Check',
                'status': 'ERROR',
                'error': str(e)
            })
            results['summary']['errors'] += 1
            results['summary']['total'] += 1

        # Test 4: Verify git clone command includes submodule support
        print("\n4️⃣ Testing Git Clone Command Submodule Support")
        try:
            # Import the real Azure git clone implementation to check the command
            from blitzy_utils.azure_git_clone import clone_azure_devops_repository_with_auth

            # Check if we can inspect the function to verify submodule support
            import inspect
            source_code = inspect.getsource(clone_azure_devops_repository_with_auth)

            has_recurse_submodules = '--recurse-submodules' in source_code
            has_submodule_support = 'submodule' in source_code.lower()

            print(f"🔍 Analyzing git clone implementation:")
            print(f"   ✅ Contains '--recurse-submodules' flag: {has_recurse_submodules}")
            print(f"   ✅ Contains submodule references: {has_submodule_support}")

            if has_recurse_submodules:
                print(f"   🎉 CONFIRMED: Azure DevOps git clone includes submodule support!")
                results['tests'].append({
                    'test': 'Git Clone Submodule Support',
                    'status': 'PASS',
                    'recurse_submodules_flag': True,
                    'submodule_references': has_submodule_support
                })
                results['summary']['passed'] += 1
            else:
                print(f"   ⚠️  WARNING: No submodule support detected in git clone command")
                results['tests'].append({
                    'test': 'Git Clone Submodule Support',
                    'status': 'FAIL',
                    'reason': 'No --recurse-submodules flag found'
                })
                results['summary']['failed'] += 1

            results['summary']['total'] += 1

        except Exception as e:
            print(f"❌ Git clone submodule support check error: {e}")
            results['tests'].append({
                'test': 'Git Clone Submodule Support',
                'status': 'ERROR',
                'error': str(e)
            })
            results['summary']['errors'] += 1
            results['summary']['total'] += 1

        return results

    except ImportError as e:
        print(f"❌ Failed to import REAL implementation: {e}")
        return {'error': f'Import failed: {e}'}

    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return {'error': f'Unexpected error: {e}'}
        
    finally:
        # Cleanup based on flag
        if CLEANUP_AFTER_TEST:
            print(f"\n🧹 Cleaning up {len(temp_dirs)} temporary directories...")
            for temp_dir in temp_dirs:
                try:
                    if os.path.exists(temp_dir):
                        shutil.rmtree(temp_dir)
                        print(f"✅ Cleaned: {temp_dir}")
                except Exception as e:
                    print(f"❌ Failed to clean {temp_dir}: {e}")
        else:
            if temp_dirs:
                print(f"\n📁 Keeping {len(temp_dirs)} directories for inspection:")
                for i, temp_dir in enumerate(temp_dirs, 1):
                    print(f"   {i}. {temp_dir}")
                print(f"\n💡 To clean up: rm -rf {' '.join(temp_dirs)}")
                print(f"💡 Or set CLEANUP_AFTER_TEST = True in the script")

if __name__ == "__main__":
    results = test_real_azure_devops_download()
    
    # Save results
    with open('real_azure_download_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Results saved to: real_azure_download_results.json")
    
    # Exit with appropriate code
    if 'error' in results:
        sys.exit(1)
    elif results['summary']['failed'] > 0 or results['summary']['errors'] > 0:
        sys.exit(1)
    else:
        sys.exit(0)
