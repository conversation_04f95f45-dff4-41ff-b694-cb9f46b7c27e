import argparse
import logging
import os
import re
import shutil
import subprocess
from pathlib import Path

import toml

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    parser = argparse.ArgumentParser(description='Update package version in pyproject.toml')
    parser.add_argument(
        '--repository',
        help='Repository URL',
        default=os.getenv('REPOSITORY_NAME', 'python-us-east1')
    )
    parser.add_argument(
        '--package',
        help='Package name',
        default=os.getenv('PACKAGE_NAME')
    )
    parser.add_argument(
        '--location',
        help='Repository location',
        default=os.getenv('REPOSITORY_LOCATION', 'us-east1')
    )
    parser.add_argument(
        '--path',
        help='Path to the utility directory',
        required=True
    )
    return parser.parse_args()


def get_latest_version_from_repository(repository_url, package_name, location):
    """
    Get the latest deployed version of the package from the repository.
    """
    if not repository_url or not package_name:
        raise ValueError("Repository URL and package name must be provided")

    logger.info(f"Fetching latest version for {package_name} from {repository_url}")

    try:
        result = subprocess.run(
            [
                "gcloud",
                "artifacts",
                "versions",
                "list",
                f"--repository={repository_url}",
                f"--package={package_name}",
                f"--location={location}",
                "--format=value(name)"
            ],
            capture_output=True,
            text=True,
            check=True
        )

        # Parse the returned versions
        versions = [v.strip() for v in result.stdout.strip().split("\n") if v.strip()]

        if not versions:
            logger.info(f"No versions found for package: {package_name}. Starting with 0.0.0.")
            return "0.0.0"

        latest_version = sorted(versions, key=semantic_version_key)[-1]
        clean_version = latest_version.split("/")[-1]
        return clean_version

    except subprocess.CalledProcessError as e:
        logger.error(f"Error executing gcloud command: {e.stderr}")
        return "0.0.0"
    except Exception as e:
        logger.error(f"An error occurred while querying the repository: {str(e)}")
        return "0.0.0"


def semantic_version_key(version):
    """
    Helper function to sort semantic versions correctly.
    """
    numbers = tuple(map(int, re.findall(r"(\d+)", version)))
    logger.debug(f"Version: {version}, Parsed numbers: {numbers}")
    return numbers


def increment_version(version, part="patch"):
    """
    Increment the version number.
    """
    try:
        major, minor, patch = map(int, version.split("."))
    except ValueError as e:
        logger.error(f"Error parsing version {version}: {e}")
        return "0.0.1"

    if part == "patch":
        patch += 1
    elif part == "minor":
        minor += 1
        patch = 0
    elif part == "major":
        major += 1
        minor = 0
        patch = 0
    else:
        raise ValueError("Invalid version part. Use 'major', 'minor', or 'patch'.")

    return f"{major}.{minor}.{patch}"


def update_pyproject_version(utility_path, new_version):
    """
    Update the version field in pyproject.toml with backup.
    """
    file_path = Path(utility_path) / "pyproject.toml"
    backup_path = file_path.with_suffix('.toml.bak')

    # Create backup
    shutil.copy2(file_path, backup_path)

    try:
        with open(file_path, "r") as f:
            content = f.read()
            pyproject_data = toml.loads(content)

        if "project" not in pyproject_data:
            raise KeyError(f"No [project] section found in {file_path}")

        # Only update the version
        old_version = pyproject_data["project"]["version"]
        content = content.replace(
            f'version = "{old_version}"',
            f'version = "{new_version}"'
        )

        with open(file_path, "w") as f:
            f.write(content)

        logger.info(f"Updated {file_path} version to: {new_version}")
        backup_path.unlink()  # Remove backup if successful

    except Exception as e:
        logger.error(f"Error updating version: {e}")
        if backup_path.exists():
            shutil.move(backup_path, file_path)  # Restore backup
        raise


def get_package_name_from_pyproject(utility_path):
    """Get package name from pyproject.toml"""
    try:
        with open(Path(utility_path) / "pyproject.toml", "r") as f:
            pyproject_data = toml.load(f)
            return pyproject_data["project"]["name"]
    except Exception as e:
        logger.error(f"Error reading package name from pyproject.toml: {e}")
        raise


if __name__ == "__main__":
    try:
        args = parse_args()
        utility_path = Path(args.path)

        if not utility_path.is_dir():
            raise ValueError(f"Utility path does not exist: {utility_path}")

        # If package name not provided, try to get it from pyproject.toml
        if not args.package:
            args.package = get_package_name_from_pyproject(utility_path)

        if not args.package:
            raise ValueError("Package name must be provided either via argument or in pyproject.toml")

        # Fetch the latest version from the repository
        latest_version = get_latest_version_from_repository(
            args.repository,
            args.package,
            args.location
        )

        # Increment the version
        new_version = increment_version(latest_version, part="patch")
        logger.info(f"Incrementing version from {latest_version} to {new_version}")

        # Update the version in pyproject.toml
        update_pyproject_version(utility_path, new_version)

        logger.info("Version update completed successfully")

    except Exception as e:
        logger.error(f"Script failed: {str(e)}")
        raise
