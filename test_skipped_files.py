import json
import re


def extract_skipped_file_paths(json_file_path):
    """
    Extract file paths from JSON log entries where textPayload contains 'skipping file'.

    Args:
        json_file_path (str): Path to the JSON file

    Returns:
        list: List of file paths that were skipped
    """
    skipped_files = []

    try:
        # Read the JSON file
        with open(json_file_path, 'r') as file:
            # Handle both single objects and arrays of objects
            data = json.load(file)

            # If data is not a list, make it a list
            if not isinstance(data, list):
                data = [data]

            # Process each entry
            for entry in data:
                # Check if entry has textPayload
                if 'textPayload' in entry:
                    payload = entry['textPayload']

                    # Check if payload contains "skipping file"
                    if 'skipping file:' in payload.lower():
                        # Extract the file path using regex
                        # Pattern matches "skipping file: " followed by the path
                        match = re.search(r'skipping file:\s*(.+)', payload, re.IGNORECASE)
                        if match:
                            file_path = match.group(1).strip()
                            skipped_files.append(file_path)

    except FileNotFoundError:
        print(f"Error: File '{json_file_path}' not found")
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON format - {e}")
    except Exception as e:
        print(f"Error: {e}")

    return skipped_files


def main():
    # Replace with your JSON file path
    json_file_path = 'logs.json'

    # Extract skipped file paths
    skipped_files = extract_skipped_file_paths(json_file_path)

    # Print results
    if skipped_files:
        print(f"Found {len(skipped_files)} skipped files:")
        print("-" * 50)
        for i, file_path in enumerate(skipped_files, 1):
            print(f"{i}. {file_path}")
    else:
        print("No skipped files found in the logs.")

    # Optionally save to a text file
    if skipped_files:
        with open('skipped_files.txt', 'w') as f:
            for file_path in skipped_files:
                f.write(file_path + '\n')
        print(f"\nSkipped files saved to 'skipped_files.txt'")


if __name__ == "__main__":
    main()
