/** @type {import('tailwindcss').Config} */
export default {
    content: ['./src/**/*.{js,jsx,ts,tsx}'],
    theme: {
        extend: {
            screens: {
                sm: '768px',
            },
            borderRadius: {
                lg: 'var(--radius)',
                md: 'calc(var(--radius) - 2px)',
                sm: 'calc(var(--radius) - 4px)',
            },
            colors: {
                'bg-primary': 'var(--bg-primary)',
                'bg-secondary': 'var(--bg-secondary)',
                'bg-tertiary': 'var(--bg-tertiary)',
                'bg-brand-purple': 'var(--bg-brand-purple)',
                'bg-success': 'var(--bg-success)',
                'bg-success-light': 'var(--bg-success-light)',
                'bg-error': 'var(--bg-error)',
                'bg-primary-invert': 'var(--bg-primary-invert)',
                'bg-secondary-invert': 'var(--bg-secondary-invert)',
                'bg-tertiary-invert': 'var(--bg-tertiary-invert)',
                'bg-brand-50': 'var(--bg-brand-50)',
                'bg-brand-100': 'var(--bg-brand-100)',
                'bg-purple': 'var(--bg-purple)',
                'bg-tertiary-hover': 'var(--bg-tertiary-hover)',
                'bg-tertiary-hover-invert': 'var(--bg-tertiary-hover-invert)',
                'bg-brand-hover': 'var(--bg-brand-hover)',
                'bg-brand-hover-invert': 'var(--bg-brand-hover-invert)',
                'text-primary': 'var(--text-primary)',
                'text-secondary': 'var(--text-secondary)',
                'text-tertiary': 'var(--text-tertiary)',
                'text-brand-purple': 'var(--text-brand-purple)',
                'text-success': 'var(--text-success)',
                'text-error-100': 'var(--text-error-100)',
                'text-error-200': 'var(--text-error-200)',
                'text-primary-invert': 'var(--text-primary-invert)',
                'text-brand-hover': 'var(--text-brand-hover)',
                'border-primary': 'var(--border-primary)',
                'border-secondary': 'var(--border-secondary)',
                'border-brand-purple': 'var(--border-brand-purple)',
                'border-primary-invert': 'var(--border-primary-invert)',
                'border-brand-invert': 'var(--border-brand-invert)',
                'border-brand-hover': 'var(--border-brand-hover)',
                'border-error': 'var(--border-error)',
                'border-tertiary-hover-invert':
                    'var(--border-tertiary-hover-invert)',

                'brand-purple': 'var(--brand-purple)',
                'form-error-red': 'var(--form-error-red)',
                background: 'hsl(var(--background))',
                foreground: 'hsl(var(--foreground))',
                card: {
                    DEFAULT: 'hsl(var(--card))',
                    foreground: 'hsl(var(--card-foreground))',
                },
                popover: {
                    DEFAULT: 'hsl(var(--popover))',
                    foreground: 'hsl(var(--popover-foreground))',
                },
                primary: {
                    DEFAULT: 'hsl(var(--primary))',
                    foreground: 'hsl(var(--primary-foreground))',
                },
                secondary: {
                    DEFAULT: 'hsl(var(--secondary))',
                    foreground: 'hsl(var(--secondary-foreground))',
                },
                muted: {
                    DEFAULT: 'hsl(var(--muted))',
                    foreground: 'hsl(var(--muted-foreground))',
                },
                accent: {
                    DEFAULT: 'hsl(var(--accent))',
                    foreground: 'hsl(var(--accent-foreground))',
                },
                destructive: {
                    DEFAULT: 'hsl(var(--destructive))',
                    foreground: 'hsl(var(--destructive-foreground))',
                },
                border: 'hsl(var(--border))',
                input: 'hsl(var(--input))',
                ring: 'hsl(var(--ring))',
                chart: {
                    1: 'hsl(var(--chart-1))',
                    2: 'hsl(var(--chart-2))',
                    3: 'hsl(var(--chart-3))',
                    4: 'hsl(var(--chart-4))',
                    5: 'hsl(var(--chart-5))',
                },
            },
            backgroundImage: {
                'modal-feature-pane-custom-gradient':
                    'radial-gradient(760.85% 462.4% at -63.56% 37.38%, #1A105F 11.53%, #372ABA 32.55%, #94FAD5 56.3%), linear-gradient(180deg, #1A105F 0%, #372ABA 100%)',
            },
        },
    },
    plugins: [require('tailwindcss-animate')],
};

// radial-gradient(at left top, #4101DB 30%, #5B39F3 40%, #77C8AB 90%)
//radial-gradient(at -63.56% 37.38%, #1A105F 11.53%, #372ABA 32.55%, #94FAD5CC 56.3%)
