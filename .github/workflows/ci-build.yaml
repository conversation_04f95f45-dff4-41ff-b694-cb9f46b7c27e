name: Docker Build and Push to GCP Artifactory

on:
  push:
    branches:
      - qa

permissions:
  contents: write

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    env:
      ARTIFACTORY_DOMAIN: us-east1-docker.pkg.dev
      ARTIFACTORY_PYTHON_DOMAIN: us-east1-python.pkg.dev
      REPOSITORY_NAME: python-us-east1

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.BOT_TOKEN_GITHUB }}
          fetch-depth: 0

      - name: Set Up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.12'

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.SERVICE_ACCOUNT_SERVICE_KEY_STAGE }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Use gcloud CLI
        run: gcloud info

      - name: Build & upload Package
        run: |
          make

      - name: Configure Git
        run: |
          git config --global --unset credential.helper || true
          git config --global user.name "Blitzy Automation"
          git config --global user.email "<EMAIL>"
          # Use GH_TOKEN instead of GITHUB_TOKEN to avoid confusion with default token
          git remote set-url origin "https://x-access-token:$<EMAIL>/${{ github.repository }}.git"

      - name: Push new versions to GitHub using Bot Token
        run: |
          git config --global user.name "Blitzy Automation"
          git config --global user.email "<EMAIL>"
          git add **/pyproject.toml
          git diff --staged --quiet || git commit -m "[skip ci] Update package versions"
          git push

      - name: Notify Slack
        if: always()
        uses: slackapi/slack-github-action@v1.24.0
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
        with:
          channel-id: ${{ vars.SLACK_BOT_CHANNEL }}
          slack-message: |
            >>> *Build Status:* ${{ job.status == 'success' && '✅ Success' || '❌ Failure' }}
            *Repository:* ${{ github.repository }}
            *Branch:* `${{ github.ref_name }}`
            *Triggered by:* ${{ github.actor }}
            *Commit:* <${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }}|${{ github.sha }}>
            *Action:* <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Build>
