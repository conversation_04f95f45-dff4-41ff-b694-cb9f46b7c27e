name: Docker Build and Push to GCP Artifactory

on:
  push:
    branches:
    - main

  workflow_dispatch:
    inputs:
      environment:
        description: Environment to run tests against
        required: true
        default: staging
        type: choice
        options:
        - staging
        - production
      debug_enabled:
        description: Run with debug logging
        required: false
        type: boolean
        default: false

permissions:
  contents: write

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    env:
      ARTIFACTORY_DOMAIN: us-east1-docker.pkg.dev
      ARTIFACTORY_IMAGE_DOMAIN: us-east1-docker.pkg.dev/${{ vars.PROJECT_ID }}/${{ vars.REPOSITORY }}
      IMAGE_NAME: archie-db-migration
      IMAGE_TAG: latest
      CLOUD_RUN_SERVICE: archie-db-migration
      CLOUD_RUN_REGION: us-east1
      PROJECT_ID: ${{ vars.PROJECT_ID }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - uses: webfactory/ssh-agent@v0.9.0
      with:
        ssh-private-key: ${{ secrets.SSH_KEY }}

    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v1
      with:
        credentials_json: ${{ secrets.SERVICE_ACCOUNT_SERVICE_KEY }}

    - name: Configure Docker for GCP Artifactory
      run: |
        gcloud auth configure-docker $ARTIFACTORY_DOMAIN

    - name: Build Docker image
      run: |
        make

    - name: Install deployment utils
      run: make install-deployment-utils

    - name: Tag Docker image
      run: |
        docker tag $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:$IMAGE_TAG $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:${{ github.sha }}

    - name: Push Docker image
      run: |
        echo "Pushing $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:$IMAGE_TAG"
        echo "Pushing image with SHA $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:${{ github.sha }}"
        docker push $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:${{ github.sha }}
        docker push $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:$IMAGE_TAG

    - name: Deploy to Cloud Run
      run: make deploy YAML_FILE=service.yaml tag=${{ github.sha }}

    - name: Execute Migration Job
      run: |
        gcloud run jobs execute ${CLOUD_RUN_SERVICE} \
          --region ${CLOUD_RUN_REGION} \
          --wait

    - name: Notify Slack
      if: always()
      uses: slackapi/slack-github-action@v1.24.0
      env:
        SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
      with:
        channel-id: ${{ vars.SLACK_BOT_CHANNEL }}
        slack-message: |
          >>> *Migration Status:* ${{ job.status == 'success' && '✅ Success' || '❌ Failure' }}
          *Repository:* blitzy-ai/${{ github.repository }}
          *Branch:* `${{ github.ref_name }}`
          *Triggered by:* ${{ github.actor }}
          *Commit:* <${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }}|${{ github.sha }}>
          *Action:* <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Build>

  publish-package:
    runs-on: ubuntu-latest
    permissions:
      contents: write

    env:
      PROJECT_ID: ${{ vars.PROJECT_ID }}
      REPOSITORY_NAME: python-us-east1
      REPOSITORY_LOCATION: us-east1
      ARTIFACTORY_PYTHON_DOMAIN: us-east1-python.pkg.dev

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.BOT_TOKEN_GITHUB }}
        fetch-depth: 0

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.12'

    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.SERVICE_ACCOUNT_SERVICE_KEY }}

    - name: Update, Build & Upload Package
      run: make upload

    - name: Configure Git
      run: |
        git config --global --unset credential.helper || true
        git config --global user.name "Blitzy Automation"
        git config --global user.email "<EMAIL>"
        # Use GH_TOKEN instead of GITHUB_TOKEN to avoid confusion with default token
        git remote set-url origin "https://x-access-token:$<EMAIL>/${{ github.repository }}.git"

    - name: Push new versions to GitHub using Bot Token
      run: |
        git config --global user.name "Blitzy Automation"
        git config --global user.email "<EMAIL>"
        git add pyproject.toml
        git diff --staged --quiet || git commit -m "[skip ci] Update package versions"
        git push

    - name: Notify Slack
      if: always()
      uses: slackapi/slack-github-action@v1.24.0
      env:
        SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
      with:
        channel-id: ${{ vars.SLACK_BOT_CHANNEL }}
        slack-message: |
          >>> *Package Publish Status:* ${{ job.status == 'success' && '✅ Success' || '❌ Failure' }}
          *Repository:* blitzy-ai/${{ github.repository }}
          *Branch:* `${{ github.ref_name }}`
          *Triggered by:* ${{ github.actor }}
          *Commit:* <${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }}|${{ github.sha }}>
          *Action:* <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Build>
