name: Build, Push and Deploy

on:
  push:
    branches:
    - prod

  workflow_dispatch:
    inputs:
      environment:
        description: Environment to run tests against
        required: true
        default: staging
        type: choice
        options:
        - staging
        - production
      debug_enabled:
        description: Run with debug logging
        required: false
        type: boolean
        default: false

jobs:
  check_changes:
    runs-on: ubuntu-latest
    outputs:
      swagger: ${{ steps.filter.outputs.swagger }}
    steps:
    - uses: actions/checkout@v3
      with:
        fetch-depth: 2

    - uses: dorny/paths-filter@v2
      id: filter
      with:
        filters: |
          swagger:
            - 'swagger.yaml'

  build-and-push:
    runs-on: ubuntu-latest
    env:
      ARTIFACTORY_DOMAIN: us-east1-docker.pkg.dev
      ARTIFACTORY_IMAGE_DOMAIN: us-east1-docker.pkg.dev/${{ vars.PROJECT_ID_PROD }}/${{ vars.REPOSITORY }}
      IMAGE_NAME: archie-service-backend
      IMAGE_TAG: latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v1
      with:
        credentials_json: ${{ secrets.SERVICE_ACCOUNT_SERVICE_KEY_PROD }}

    - name: Configure Docker for GCP Artifactory
      run: |
        gcloud auth configure-docker $ARTIFACTORY_DOMAIN

    - name: Build Docker image
      run: |
        make

    - name: Tag Docker image
      run: |
        docker tag $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:$IMAGE_TAG $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:${{ github.sha }}

    - name: Push Docker image
      run: |
        echo "Pushing $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:$IMAGE_TAG"
        echo "Pushing image with SHA $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:${{ github.sha }}"
        docker push $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:${{ github.sha }}
        docker push $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:$IMAGE_TAG

    - name: Deploy to Cloud Run
      run: make deploy YAML_FILE=service.yaml tag=${{ github.sha }}

    - name: Notify Slack
      if: always()
      uses: slackapi/slack-github-action@v1.24.0
      env:
        SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
      with:
        channel-id: ${{ vars.SLACK_BOT_CHANNEL }}
        slack-message: |
          >>> *Job:* `Cloud Run Deploy`
          *Build Status:* ${{ job.status == 'success' && '✅ Success' || '❌ Failure' }}
          *Repository:* blitzy-ai/${{ github.repository }}
          *Branch:* `${{ github.ref_name }}`
          *Triggered by:* ${{ github.actor }}
          *Commit:* <${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }}|${{ github.sha }}>
          *Action:* <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Build>

  api-gateway-push:
    needs: check_changes
    if: needs.check_changes.outputs.swagger == 'true'
    runs-on: ubuntu-latest
    env:
      ARTIFACTORY_DOMAIN: us-east1-docker.pkg.dev
      ARTIFACTORY_IMAGE_DOMAIN: us-east1-docker.pkg.dev/${{ vars.PROJECT_ID_PROD }}/${{ vars.REPOSITORY }}
      IMAGE_NAME: archie-service-backend
      IMAGE_TAG: latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v1
      with:
        credentials_json: ${{ secrets.SERVICE_ACCOUNT_SERVICE_KEY_PROD }}

    - name: Deploy api gateway
      run: |
        make deploy-gateway

    - name: Notify Slack
      if: always()
      uses: slackapi/slack-github-action@v1.24.0
      env:
        SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
      with:
        channel-id: ${{ vars.SLACK_BOT_CHANNEL }}
        slack-message: |
          >>> *Job:* `Api Gateway Deploy`
          *Build Status:* ${{ job.status == 'success' && '✅ Success' || '❌ Failure' }}
          *Repository:* blitzy-ai/${{ github.repository }}
          *Branch:* `${{ github.ref_name }}`
          *Triggered by:* ${{ github.actor }}
          *Commit:* <${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }}|${{ github.sha }}>
          *Action:* <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Build>
