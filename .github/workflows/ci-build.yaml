name: Docker Build and Push to GCP Artifactory

on:
    push:
        branches:
            - main

jobs:
    build-and-push:
        runs-on: ubuntu-latest

        env:
            ARTIFACTORY_DOMAIN: us-east1-docker.pkg.dev
            ARTIFACTORY_IMAGE_DOMAIN: us-east1-docker.pkg.dev/${{ vars.PROJECT_ID }}/${{ vars.REPOSITORY }}
            IMAGE_NAME: blitzy-os-ui
            IMAGE_TAG: latest
            CLOUD_RUN_SERVICE: blitzy-os-ui-service

        steps:
            - name: Checkout code
              uses: actions/checkout@v3

            - name: Authenticate to Google Cloud
              uses: google-github-actions/auth@v1
              with:
                  credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

            - name: Configure Docker for GCP Artifactory
              run: |
                  gcloud auth configure-docker $ARTIFACTORY_DOMAIN

            - name: Retrieve secret from GCP Secret Manager
              run: |
                  gcloud secrets versions access latest --secret=$IMAGE_NAME > .env

            - name: Build Docker image
              run: |
                  make build

            - name: Tag Docker image
              run: |
                  docker tag $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:$IMAGE_TAG $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:${{ github.sha }}

            - name: Push Docker image
              run: |
                  echo "Pushing $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:$IMAGE_TAG"
                  echo "Pushing image with SHA $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:${{ github.sha }}"
                  docker push $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:${{ github.sha }}
                  docker push $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:$IMAGE_TAG

            - name: Deploy to Cloud Run
              uses: google-github-actions/deploy-cloudrun@v1
              with:
                  service: ${{ env.CLOUD_RUN_SERVICE }}
                  image: ${{ env.ARTIFACTORY_IMAGE_DOMAIN }}/${{ env.IMAGE_NAME }}:${{ env.IMAGE_TAG }}
                  region: ${{ env.CLOUD_RUN_REGION }}
