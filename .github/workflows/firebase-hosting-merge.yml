# This file was auto-generated by the Firebase CLI
# https://github.com/firebase/firebase-tools

name: Deploy to Firebase Hosting on merge

on:
    push:
        branches:
            - qa

jobs:
    build_and_deploy:
        runs-on: ubuntu-latest
        environment: dev
        steps:
            - uses: actions/checkout@v4

            - name: Create .env file
              run: |
                  echo "VITE_FIREBASE_API_KEY=${{ secrets.VITE_FIREBASE_API_KEY }}" >> .env
                  echo "VITE_FIREBASE_PROJECT_ID=${{ secrets.VITE_FIREBASE_PROJECT_ID }}" >> .env
                  echo "VITE_FIREBASE_AUTH_DOMAIN=${{ secrets.VITE_FIREBASE_AUTH_DOMAIN }}" >> .env
                  echo "VITE_FIREBASE_STORAGE_BUCKET=${{ secrets.VITE_FIREBASE_STORAGE_BUCKET }}" >> .env
                  echo "VITE_FIREBASE_MESSAGING_SENDER_ID=${{ secrets.VITE_FIREBASE_MESSAGING_SENDER_ID }}" >> .env
                  echo "VITE_FIREBASE_APPID=${{ secrets.VITE_FIREBASE_APPID }}" >> .env
                  echo "VITE_FIREBASE_MEASUREMENT_ID=${{ secrets.VITE_FIREBASE_MEASUREMENT_ID }}" >> .env
                  echo "VITE_BLITZY_OS_SERVER_URL=${{ vars.VITE_BLITZY_OS_SERVER_URL }}" >> .env
                  echo "VITE_BLITZY_OS_MOCK_SERVER_URL=${{ vars.VITE_BLITZY_OS_MOCK_SERVER_URL }}" >> .env

            - run: npm ci && npm run build

            - uses: FirebaseExtended/action-hosting-deploy@v0
              with:
                  repoToken: ${{ secrets.GITHUB_TOKEN }}
                  firebaseServiceAccount: ${{ secrets.FIREBASE_SERVICE_ACCOUNT_BLITZY_OS_DEV }}
                  channelId: live
                  projectId: blitzy-os-dev
              env:
                  FIREBASE_CLI_EXPERIMENTS: webframeworks
