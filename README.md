# .env.local

Firebase authentication configuation is saved in .env.local. Please create your own local .env.local pointing to the correct values.

By default, vite server will start on 5173 port. If you want to start server on different port setup `VITE_PORT` config.  
Link to [sample.env](sample.env).

```shell
VITE_FIREBASE_API_KEY=
VITE_FIREBASE_PROJECT_ID=
VITE_FIREBASE_AUTH_DOMAIN=
VITE_FIREBASE_STORAGE_BUCKET=
VITE_FIREBASE_MESSAGING_SENDER_ID=
VITE_FIREBASE_APPID=
VITE_FIREBASE_MEASUREMENT_ID=

VITE_BLITZY_OS_SERVER_URL=
VITE_BLITZY_OS_MOCK_SERVER_URL=http://localhost:8091
```

# Some coding convention

## home flow

Home flow is the flow of screens before a user signs in. Url of all screens in home flow starts with `/home`.

## workspace flow

Workspace flow is the flow of screens after a user signs in. Url of all screens in the workflow flow starts with `/workspace`.

## Panel

A panel such as `getting-started-panel.tsx` is the engire right section of a screen either in the home flow or in the workspace flow.

## Pane

A pane represents a smaller container than a panel. A pane such as `ingest-prompt-pane.tsx` only appears in a Modal (dialog box). In the future, a pane is a _resuable_ container that may appear in multiple places.

## lib/entity-types.ts

All typescript types for entities used in the project should be defined in `lib/entity-types.ts`

## lib/backend.ts

All REST API calls should be wrapped into utility functions in `lib/backend.ts` which will be consumed by various components.

# Mock backend server (blitzy-ai/blitzy-os-mock)

To power UI development using a mock server, make sure this following entry is in your .env.local file. The value of VITE_BLITZY_OS_MOCK_SERVER_URL is the nodejs mock server. The mock server should be started independently.

```shell
VITE_BLITZY_OS_SERVER_URL=http://localhost:8091
```

# to run in dev mode

```shell
npm run dev
```

# to build for production

```shell
npm run build
```

# unit testing

We use vitest and testinglibrary/react for unit testing. See **test** folder for example unit test cases. To run unit test cases:

```shell
npm test
```

## Expanding the ESLint configuration

If you are developing a production application, we recommend updating the configuration to enable type aware lint rules:

- Configure the top-level `parserOptions` property like this:

```js
export default tseslint.config({
    languageOptions: {
        // other options...
        parserOptions: {
            project: ['./tsconfig.node.json', './tsconfig.app.json'],
            tsconfigRootDir: import.meta.dirname,
        },
    },
});
```

- Replace `tseslint.configs.recommended` to `tseslint.configs.recommendedTypeChecked` or `tseslint.configs.strictTypeChecked`
- Optionally add `...tseslint.configs.stylisticTypeChecked`
- Install [eslint-plugin-react](https://github.com/jsx-eslint/eslint-plugin-react) and update the config:

```js
// eslint.config.js
import react from 'eslint-plugin-react';

export default tseslint.config({
    // Set the react version
    settings: {react: {version: '18.3'}},
    plugins: {
        // Add the react plugin
        react,
    },
    rules: {
        // other rules...
        // Enable its recommended rules
        ...react.configs.recommended.rules,
        ...react.configs['jsx-runtime'].rules,
    },
});
```
