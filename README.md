# Cloud Run Template Microservice

A template repository for a Cloud Run microservice, written in Python

[![Run on Google Cloud](https://deploy.cloud.run/button.svg)](https://deploy.cloud.run)

## Prerequisite

* Enable the Cloud Run API via
  the [console](https://console.cloud.google.com/apis/library/run.googleapis.com?_ga=2.124941642.1555267850.1615248624-203055525.1615245957)
  or CLI:

```bash
gcloud services enable run.googleapis.com
```

## Features

* **Flask**: Web server framework.
* **Buildpack support** Tooling to build production-ready container images from source code and without a Dockerfile
* **Dockerfile**: Container build instructions, if needed to replace buildpack for custom build
* **SIGTERM handler**: Catch termination signal for cleanup before Cloud Run stops the container
* **Service metadata**: Access service metadata, project ID and region, at runtime
* **Local development utilities**: Auto-restart with changes and prettify logs
* **Structured logging w/ Log Correlation** <PERSON><PERSON><PERSON> formatted logger, parsable by Cloud Logging,
  with [automatic correlation of container logs to a request log](https://cloud.google.com/run/docs/logging#correlate-logs).
* **Unit and System tests**: Basic unit and system tests setup for the microservice
* **Task definition and execution**: Uses [invoke](http://www.pyinvoke.org/) to execute defined tasks in `tasks.py`.

## Pre-Setup

Run following command before starting development.

```shell
make pre-setup
```

## Local Development

### To install dependencies

```shell
make init
```

We have few dependencies hosted on gcp artifact repository. For that to work on your local, follow below steps

1. pip install keyrings.google-artifactregistry-auth
2. gcloud auth application-default login

Once you are logged in. You should be able to run following commands

```shell
pip install -r requirements.txt
# or
pip install --extra-index-url https://us-east1-python.pkg.dev/blitzy-platform-stage/python-us-east1/simple/ blitzy-utils==0.0.10
```

### To build image on local

To build image on local. Execute following commands. Here it's already considered that you already have SSH key setup
and key is added to the Github.

```shell
eval "$(ssh-agent -s)"
ssh-add <PATH_TO_YOUR_KEY> # E.g. ssh-add ~/.ssh/id_ed25519
```

To build image on local you need to export variable `SERVICE_ACCOUNT_KEY_PATH` with the service account credential

```shell
# To generate service account json.
gcloud iam service-accounts keys create <FILE_PATH> \
    --iam-account "SERVICE_ACCOUNT_EMAIL_ADDRESS"
```

These commands can be part of your ~/.zshrc, ~/.bashrc or ~/.bash_profile script.
On every run these settings will be automatically loaded.

Once this is done run following command

```shell
make
```

### Cloud Code

This template works with [Cloud Code](https://cloud.google.com/code), an IDE extension
to let you rapidly iterate, debug, and run code on Kubernetes and Cloud Run.

Learn how to use Cloud Code for:

* Local
  development - [VSCode](https://cloud.google.com/code/docs/vscode/developing-a-cloud-run-service), [IntelliJ](https://cloud.google.com/code/docs/intellij/developing-a-cloud-run-service)

* Local
  debugging - [VSCode](https://cloud.google.com/code/docs/vscode/debugging-a-cloud-run-service), [IntelliJ](https://cloud.google.com/code/docs/intellij/debugging-a-cloud-run-service)

* Deploying a Cloud Run
  service - [VSCode](https://cloud.google.com/code/docs/vscode/deploying-a-cloud-run-service), [IntelliJ](https://cloud.google.com/code/docs/intellij/deploying-a-cloud-run-service)
* Creating a new application from a custom template (`.template/templates.json` allows for use as an app
  template) - [VSCode](https://cloud.google.com/code/docs/vscode/create-app-from-custom-template), [IntelliJ](https://cloud.google.com/code/docs/intellij/create-app-from-custom-template)

### CLI tooling

To run the `invoke` commands below, install [`invoke`](https://www.pyinvoke.org/index.html) system wide:

```bash
pip install invoke
```

Invoke will handle establishing local virtual environments, etc. Task definitions can be found in `tasks.py`.

#### Local development

1. Set Project Id:
    ```bash
    export GOOGLE_CLOUD_PROJECT=<GCP_PROJECT_ID>
    ```
2. Start the server with hot reload:
    ```bash
    invoke dev
    ```

#### Deploying a Cloud Run service

1. Set Project Id:
    ```bash
    export GOOGLE_CLOUD_PROJECT=<GCP_PROJECT_ID>
    ```

1. Enable the Artifact Registry API:
    ```bash
    gcloud services enable artifactregistry.googleapis.com
    ```

1. Create an Artifact Registry repo:
    ```bash
    export REPOSITORY="samples"
    export REGION=us-central1
    gcloud artifacts repositories create $REPOSITORY --location $REGION --repository-format "docker"
    ```

1. Use the gcloud credential helper to authorize Docker to push to your Artifact Registry:
    ```bash
    gcloud auth configure-docker
    ```

2. Build the container using a buildpack:
    ```bash
    invoke build
    ```
3. Deploy to Cloud Run:
    ```bash
    invoke deploy
    ```

### Run sample tests

1. [Pass credentials via
   `GOOGLE_APPLICATION_CREDENTIALS` env var](https://cloud.google.com/docs/authentication/production#passing_variable):
    ```bash
    export GOOGLE_APPLICATION_CREDENTIALS="[PATH]"
    ```

2. Set Project Id:
    ```bash
    export GOOGLE_CLOUD_PROJECT=<GCP_PROJECT_ID>
    ```
3. Run unit tests
    ```bash
    invoke test
    ```

4. Run system tests
    ```bash
    gcloud builds submit \
        --config test/advance.cloudbuild.yaml \
        --substitutions 'COMMIT_SHA=manual,REPO_NAME=manual'
    ```
   The Cloud Build configuration file will build and deploy the containerized service
   to Cloud Run, run tests managed by pytest, then clean up testing resources. This configuration restricts public
   access to the test service. Therefore, service accounts need to have the permission to issue ID tokens for request
   authorization:
    * Enable Cloud Run, Cloud Build, Artifact Registry, and IAM APIs:
        ```bash
        gcloud services enable run.googleapis.com cloudbuild.googleapis.com iamcredentials.googleapis.com artifactregistry.googleapis.com
        ```

    * Set environment variables.
        ```bash
        export PROJECT_ID="$(gcloud config get-value project)"
        export PROJECT_NUMBER="$(gcloud projects describe $(gcloud config get-value project) --format='value(projectNumber)')"
        ```

    * Create an Artifact Registry repo (or use another already created repo):
        ```bash
        export REPOSITORY="samples"
        export REGION=us-central1
        gcloud artifacts repositories create $REPOSITORY --location $REGION --repository-format "docker"
        ```

    * Create service account `token-creator` with `Service Account Token Creator` and `Cloud Run Invoker` roles.
        ```bash
        gcloud iam service-accounts create token-creator

        gcloud projects add-iam-policy-binding $PROJECT_ID \
            --member="serviceAccount:token-creator@$PROJECT_ID.iam.gserviceaccount.com" \
            --role="roles/iam.serviceAccountTokenCreator"
        gcloud projects add-iam-policy-binding $PROJECT_ID \
            --member="serviceAccount:token-creator@$PROJECT_ID.iam.gserviceaccount.com" \
            --role="roles/run.invoker"
        ```

    * Add `Service Account Token Creator` role to the Cloud Build service account.
        ```bash
        gcloud projects add-iam-policy-binding $PROJECT_ID \
            --member="serviceAccount:$<EMAIL>" \
            --role="roles/iam.serviceAccountTokenCreator"
        ```

    * Cloud Build also requires permission to deploy Cloud Run services and administer artifacts:

        ```bash
        gcloud projects add-iam-policy-binding $PROJECT_ID \
            --member="serviceAccount:$<EMAIL>" \
            --role="roles/run.admin"
        gcloud projects add-iam-policy-binding $PROJECT_ID \
            --member="serviceAccount:$<EMAIL>" \
            --role="roles/iam.serviceAccountUser"
        gcloud projects add-iam-policy-binding $PROJECT_ID \
            --member="serviceAccount:$<EMAIL>" \
            --role="roles/artifactregistry.repoAdmin"
        ```

## To deploy code

Run `make install-deployment-utils` to install deployment utils. This will install util required to deploy cloud run.

### On local env

```shell
# Deploy to dev (default)
make deploy tag=v1.0.0

# Deploy to prod
make deploy tag=v1.0.0 ENV=prod
```

## Environment Variables

This section documents the required environment variables for the application.

- **`PLATFORM_URL`** (Required) - The base URL of your platform/application. Used to construct full redirect URIs

### Azure OAuth Integration

The following environment variables are required for Azure DevOps OAuth2 authentication:

- **`AZURE_CLIENT_ID`** (Required) - The client ID of your Azure application registration. Used for OAuth2 authentication with Azure DevOps.

- **`AZURE_REDIRECT_URI`** (Required) - The redirect URI path (relative to PLATFORM_URL) where user will redirect after azure authentication. This should match the redirect URI configured in your Azure app registration.

- **`AZURE_SUCCESS_AUTH_REDIRECT_URI`** (Required) - The URI path (relative to PLATFORM_URL) where users will be redirected after successful azure auth and code exchange(means we have access and refresh tokens for this user).

- **`AZURE_STATE_SECRET`** (Required) - A secret key used for generating and validating HMAC-signed 'state' parameters in Azure OAuth2 flows. Should be a strong, random string to ensure security.


## Maintenance & Support

This repo performs basic periodic testing for maintenance. Please use the issue tracker for bug reports, features
requests and submitting pull requests..

## Contributions

Please see the [contributing guidelines](CONTRIBUTING.md)

## License

This library is licensed under Apache 2.0. Full license text is available in [LICENSE](LICENSE)..
