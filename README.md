# Python Utilities

This repository contains various Python utilities used across Blitzy projects. Each utility is maintained as a separate
package and published to Google Artifact Registry.

## Repository Structure

```shell
blitzy-utils-python/
├── blitzy_utils/        # Core utilities
├── blitzy_flask_utils/         # Flask related utilities
├── update_version.py    # Version management script
├── Makefile            # Root level makefile
└── README.md
```

## Setup

1. Ensure you have Python 3.12+ installed
2. Install required dependencies:

```shell
make install-deps
```

3. Configure Google Artifact Registry authentication:

```shell
export PROJECT_ID=your-project-id
```

## Build Commands

### Building All Utilities

```shell
make all                # Complete process (install, update version, build, upload)
make build             # Build all utilities
make upload            # Upload all utilities
make update-version    # Update version for all utilities
make clean             # Clean all utilities
```

### Working with Individual Utilities

You can work with specific utilities using the -C flag:

```shell
make -C blitzy_flask_utils all           # Complete process for blitzy_flask_utils
make -C blitzy_flask_utils build         # Only build blitzy_flask_utils
make -C blitzy_flask_utils upload        # Only upload blitzy_flask_utils
make -C blitzy_flask_utils update-version   # Only update version
make -C blitzy_flask_utils clean         # Only clean build artifacts
```

## Version Management

The `update_version.py` script automatically manages package versions:

- Fetches the latest version from Google Artifact Registry
- Increments the patch version
- Updates pyproject.toml before building

## Package Configuration

Each utility should have:

1. `pyproject.toml` - Package metadata and dependencies
2. `Makefile` - Build and publish commands
3. `README.md` - Package documentation

Example pyproject.toml:

```toml
[project]
name = "flask-utils"
version = "0.0.1"
description = "Flask utilities for Blitzy projects"
requires-python = ">=3.8"
dependencies = [
    "Flask==3.0.3",
    "Flask-Cors==5.0.0"
]
```

## Adding New Utility

1. Create a new directory for your utility
2. Add required files:
    - pyproject.toml
    - Makefile (copy from existing utility)
    - README.md
3. Add utility name to UTILS in root Makefile
4. Build and publish:

```shell
make -C your_new_utils all
```

## Development Workflow

1. Make changes to utility code
2. Update version:

```shell
make -C utility_name update-version
```

3. Build and test:

```shell
make -C utility_name build
```

4. Publish to artifact registry:

```shell
make -C utility_name upload
```

## Troubleshooting

1. Version not updating?
    - Check if package name in pyproject.toml matches artifact registry
    - Ensure Google Cloud authentication is configured
2. Upload failing?
    - Verify PROJECT_ID is set
    - Check Google Cloud authentication
    - Ensure package version doesn't already exist

