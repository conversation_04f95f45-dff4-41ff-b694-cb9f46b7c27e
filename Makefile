# Configuration
REPOSITORY_NAME ?= python-us-east1
REPOSITORY_LOCATION ?= us-east1
UTILS := blitzy_flask_utils blitzy_utils deployment notification_utils# List all your utilities here

.PHONY: all install-deps clean build upload update-version $(UTILS)

# Default target to build and upload all utilities
all: install-deps update-version build upload

# Install global dependencies
install-deps:
	python -m pip install --upgrade pip setuptools wheel build twine keyrings.google-artifactregistry-auth toml
	@echo "Installed/updated required dependencies."

# Update version for all utilities
update-version:
	@for util in $(UTILS); do \
		echo "Updating version for $$util..."; \
		$(MAKE) -C $$util update-version; \
	done

# Build all utilities
build:
	@for util in $(UTILS); do \
		echo "Building $$util..."; \
		$(MAKE) -C $$util build; \
	done

# Upload all utilities
upload:
	@for util in $(UTILS); do \
		echo "Uploading $$util..."; \
		$(MAKE) -C $$util upload; \
	done

# Clean all utilities
clean:
	@for util in $(UTILS); do \
		echo "Cleaning $$util..."; \
		$(MAKE) -C $$util clean; \
	done
