PORT ?= 8080
IMAGE_TAG ?= latest
REGION ?= us-east1
PROJECT_ID ?= blitzy-os-dev
REPOSITORY ?= gcf-artifacts
IMAGE_NAME ?= blitzy-os-ui
TAG ?= latest

# Full image path
IMAGE_PATH := $(REGION)-docker.pkg.dev/$(PROJECT_ID)/$(REPOSITORY)/$(IMAGE_NAME):$(IMAGE_TAG)

build:
	docker build --build-arg APP_PORT=$(PORT) -t $(IMAGE_PATH) .
	@echo "Building docker image with tag $(IMAGE_PATH)"

clean:
	docker rmi -f $(docker images -f "dangling=true" -q)
