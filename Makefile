PORT ?= 8080
IMAGE_TAG ?= latest
REGION ?= us-east1
PROJECT_ID ?= blitzy-os-dev
REPOSITORY ?= gcf-artifacts
IMAGE_NAME ?= archie-service-admin
ENV ?= dev
SERVICE_ACCOUNT_NAME ?= <EMAIL>

# Full image path
IMAGE_PATH := $(REGION)-docker.pkg.dev/$(PROJECT_ID)/$(REPOSITORY)/$(IMAGE_NAME):$(IMAGE_TAG)

all: pre-setup build

install-deployment-utils:
	@echo "Installing deployment utils"
	pip install keyrings.google-artifactregistry-auth
	pip install --extra-index-url https://us-east1-python.pkg.dev/${PROJECT_ID}/python-us-east1/simple deployment-utils

pre-setup: install-deployment-utils
	@echo "Generating models from swagger"
	generate-pydantic-models -i swagger.yaml -o src/api/models.py -b "src.models_config.base_class.BlitzyBaseModel"

init:
	pip install -r requirements.txt

build:
	@if [ "$(GITHUB_ACTIONS)" = "true" ]; then \
		DOCKER_BUILDKIT=1 docker build \
			--secret id=google_credentials,src=$$GOOGLE_APPLICATION_CREDENTIALS \
			-t $(IMAGE_PATH) .; \
	else \
		@echo "Building locally..."; \
		if [ -z "$$SERVICE_ACCOUNT_KEY_PATH" ]; then \
			echo "ERROR: SERVICE_ACCOUNT_KEY_PATH environment variable not set"; \
			echo "Please export SERVICE_ACCOUNT_KEY_PATH=/path/to/your/service-account-key.json"; \
			exit 1; \
		fi; \
		DOCKER_BUILDKIT=1 docker build \
			--secret id=google_credentials,src=$$SERVICE_ACCOUNT_KEY_PATH \
			-t $(IMAGE_PATH) .; \
	fi
	@echo "Building docker image with tag $(IMAGE_PATH)"

clean:
	docker rmi -f $(docker images -f "dangling=true" -q)

deploy:
	@if [ -n "$(tag)" ]; then \
		echo "Deploying with tag from command line: $(tag)"; \
		deploy-to-cloud-run --image-tag=$(tag) --yaml-file=$(YAML_FILE) --vars-file=env_config/env-$(ENV).yaml --skip-confirmation; \
	else \
		echo "Deploying with IMAGE_PATH: $(IMAGE_PATH)"; \
		deploy-to-cloud-run --image-tag=$(IMAGE_PATH) --yaml-file=$(YAML_FILE) --vars-file=env_config/env-$(ENV).yaml --skip-confirmation; \
	fi

.PHONY: all deploy
