# Migration configuration
PORT ?= 8080
IMAGE_TAG ?= latest
REGION ?= us-east1
PROJECT_ID ?= blitzy-os-dev
REPOSITORY ?= gcf-artifacts
IMAGE_NAME ?= archie-db-migration
ENV ?= dev

# Package configuration
DIST_DIR := dist
PACKAGE_NAME := db-common-model
REPOSITORY_NAME ?= python-us-east1
REPOSITORY_LOCATION ?= us-east1
ARTIFACTORY_PYTHON_DOMAIN ?= us-east1-python.pkg.dev

# Docker configuration
IMAGE_PATH := $(REGION)-docker.pkg.dev/$(PROJECT_ID)/$(REPOSITORY)/$(IMAGE_NAME):$(IMAGE_TAG)

# Construct the repository URL
TWINE_REPOSITORY_URL ?= https://$(ARTIFACTORY_PYTHON_DOMAIN)/$(PROJECT_ID)/$(REPOSITORY_NAME)

.PHONY: all init install-deps build deploy update-version upload test-upload

all: build

init:
	pip install -r requirements.txt

install-deps:
	python -m pip install --upgrade pip setuptools wheel build twine keyrings.google-artifactregistry-auth toml
	@echo "Installed/updated required dependencies."

install-deployment-utils:
	@echo "Installing deployment utils"
	pip install git+ssh://**************/blitzy-ai/python-utils.git@main#subdirectory=deployment

build:
	@if [ "$(GITHUB_ACTIONS)" = "true" ]; then \
		 DOCKER_BUILDKIT=1 docker build \
			--ssh default \
			--secret id=google_credentials,src=$$GOOGLE_APPLICATION_CREDENTIALS \
			-t $(IMAGE_PATH) .; \
	else \
		DOCKER_BUILDKIT=1 docker build \
			--ssh default \
			--secret id=google_credentials,src=$$SERVICE_ACCOUNT_KEY_PATH \
			--no-cache \
			-t $(IMAGE_PATH) .; \
	fi
	@echo "Building docker image with tag $(IMAGE_PATH)"

deploy:
	@if [ -n "$(tag)" ]; then \
		echo "Deploying with tag from command line: $(tag)"; \
		deploy-to-cloud-run --type job --image-tag=$(tag) --yaml-file=$(YAML_FILE) --vars-file=env_config/env-$(ENV).yaml --skip-confirmation; \
	else \
		echo "Deploying with IMAGE_PATH: $(IMAGE_PATH)"; \
		deploy-to-cloud-run --type job --image-tag=$(IMAGE_PATH) --yaml-file=$(YAML_FILE) --vars-file=env_config/env-$(ENV).yaml --skip-confirmation; \
	fi

# Package publishing targets
clean-repo:
	rm -rf $(DIST_DIR) *.egg-info build
	@echo "Cleaned build artifacts."

update-version: install-deps
	python update_version.py \
		--repository $(REPOSITORY_NAME) \
		--package $(PACKAGE_NAME) \
		--location $(REPOSITORY_LOCATION) \
		--path .
	@echo "Version updated in pyproject.toml."

build-package: clean-repo update-version
	python -m build
	@echo "Package built successfully."

upload: build-package
	@if [ -z "$(PROJECT_ID)" ]; then \
		echo "Error: PROJECT_ID is not set"; \
		exit 1; \
	fi
	python -m twine upload --repository-url $(TWINE_REPOSITORY_URL) "$(DIST_DIR)/*"
	@echo "Package uploaded successfully to $(TWINE_REPOSITORY_URL)."

test-upload: build-package
	python -m twine upload --repository-url https://test.pypi.org/legacy/ "$(DIST_DIR)/*"
	@echo "Package uploaded successfully to test PyPI."
