{"name": "blitzy-os-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:ipv4": "vite --host 127.0.0.1", "build": "tsc -b && vite build", "test": "vitest run", "test:watch": "vitest --watch", "preview": "vite preview", "lint": "eslint .", "lint:fix": "eslint --fix .", "prettier": "prettier --write .", "prepare": "husky", "knip": "knip"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.2.6", "@tiptap/extension-blockquote": "^2.14.1", "@tiptap/extension-bullet-list": "^2.14.1", "@tiptap/extension-code-block": "^2.14.1", "@tiptap/extension-code-block-lowlight": "^2.14.1", "@tiptap/extension-color": "^2.14.1", "@tiptap/extension-dropcursor": "^2.14.1", "@tiptap/extension-gapcursor": "^2.14.1", "@tiptap/extension-heading": "^2.14.1", "@tiptap/extension-highlight": "^2.14.1", "@tiptap/extension-history": "^2.14.1", "@tiptap/extension-horizontal-rule": "^2.14.1", "@tiptap/extension-image": "^2.14.1", "@tiptap/extension-link": "^2.14.1", "@tiptap/extension-list-item": "^2.14.1", "@tiptap/extension-ordered-list": "^2.14.1", "@tiptap/extension-subscript": "^2.14.1", "@tiptap/extension-superscript": "^2.14.1", "@tiptap/extension-table": "^2.14.1", "@tiptap/extension-table-cell": "^2.14.1", "@tiptap/extension-table-header": "^2.14.1", "@tiptap/extension-table-row": "^2.14.1", "@tiptap/extension-task-item": "^2.14.1", "@tiptap/extension-task-list": "^2.14.1", "@tiptap/extension-text-align": "^2.14.1", "@tiptap/extension-text-style": "^2.14.1", "@tiptap/extension-typography": "^2.14.1", "@tiptap/extension-underline": "^2.14.1", "@tiptap/react": "^2.14.1", "@tiptap/starter-kit": "^2.14.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "dayjs": "^1.11.13", "dompurify": "^3.2.4", "firebase": "^11.2.0", "form-data": "^4.0.4", "highlight.js": "^11.11.1", "html-escaper": "^3.0.3", "jwt-decode": "^4.0.0", "lottie-react": "^2.4.0", "lowlight": "^3.3.0", "lucide-react": "^0.441.0", "marked": "^14.1.2", "mermaid": "^11.7.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-router-dom": "^6.26.2", "react-router-prompt": "^0.7.2", "react-virtuoso": "^4.12.6", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "tiptap-markdown": "^0.8.10", "zod": "^3.23.8"}, "overrides": {"nanoid": "3.3.8", "undici": "6.21.1", "esbuild": "0.25.0", "brace-expansion": "2.0.2"}, "devDependencies": {"@eslint/js": "^9.9.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.0.1", "@types/html-escaper": "^3.0.4", "@types/mermaid": "^9.1.0", "@types/node": "^22.14.1", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.15.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "husky": "^9.1.6", "jsdom": "^25.0.1", "knip": "^5.50.4", "postcss": "^8.4.47", "prettier": "^3.3.3", "tailwindcss": "^3.4.11", "typescript": "^5.8.3", "typescript-eslint": "^8.0.1", "vite": "5.4.19", "vitest": "^3.0.5"}}