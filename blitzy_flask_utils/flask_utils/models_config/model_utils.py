import re
from datetime import datetime
from enum import Enum
from functools import lru_cache
from typing import (Any, Callable, Dict, Optional, Type, TypeVar, Union,
                    get_args, get_origin)

from sqlalchemy.orm import DeclarativeBase
from sqlalchemy.orm.exc import DetachedInstanceError

from src.models_config.base_class import BlitzyBaseModel

from blitzy_utils.logger import logger

ModelT = TypeVar("ModelT", bound=BlitzyBaseModel)
DBModelT = TypeVar("DBModelT", bound=DeclarativeBase)
TransformerT = Callable[[Any], Any]


def datetime_to_epoch(dt: Optional[datetime]) -> Optional[int]:
    """
    Convert datetime to Unix epoch timestamp.

    :param dt: Datetime object to convert.
    :return: Unix timestamp as integer or None if input is None.
    """
    if dt is None:
        return None
    return int(dt.timestamp())


def camel_to_snake(name: str) -> str:
    """
    Convert camelCase to snake_case.

    :param name: String in camelCase format.
    :return: String converted to snake_case.
    """
    name = re.sub("(.)([A-Z][a-z]+)", r"\1_\2", name)
    return re.sub("([a-z0-9])([A-Z])", r"\1_\2", name).lower()


def snake_to_camel(name: str) -> str:
    """Convert snake_case to camelCase."""
    components = name.split('_')
    return components[0] + ''.join(x.title() for x in components[1:])


@lru_cache(maxsize=128)
def create_default_mapping(
        model_class: Type[ModelT],
        db_class: Type[DBModelT]
) -> Dict[str, str]:
    """
    Creates a mapping between Pydantic and SQLAlchemy fields.

    :param model_class: Pydantic model class.
    :param db_class: SQLAlchemy model class.
    :return: Dictionary mapping Pydantic field names to SQLAlchemy field names.
    """
    db_fields = set(db_class.__table__.columns.keys())
    relationships = {
        key for key, value in db_class.__mapper__.relationships.items()
    }
    available_fields = db_fields | relationships

    mapping = {}
    for field_name in model_class.model_fields.keys():
        snake_name = camel_to_snake(field_name)
        if snake_name in available_fields:
            mapping[field_name] = snake_name

    return mapping


def safe_get_nested_attr(obj: Any, nested_path: str) -> Any:
    """
    Safely get nested attribute using dot notation.

    :param obj: Source object to get attribute from.
    :param nested_path: Dot-separated path to the attribute (e.g., "user.email").
    :return: Attribute value or None if not found or error occurs.
    """
    try:
        if "." not in nested_path:
            return getattr(obj, nested_path, None)

        current = obj
        for part in nested_path.split("."):
            if current is None:
                return None
            current = getattr(current, part)
        return current
    except (AttributeError, DetachedInstanceError):
        return None


def get_field_transformer(field_type: Any) -> Optional[TransformerT]:
    """
    Get default transformer for a field type.

    :param field_type: Type annotation of the field.
    :return: Transformer function if available.
    """
    if get_origin(field_type) is Optional:
        field_type = get_args(field_type)[0]

    if get_origin(field_type) is Union:
        field_type = get_args(field_type)[0]

    # Default transformers mapping
    type_transformers = {
        # For datetime to int conversion
        int: lambda v: int(v.timestamp()) if isinstance(v, datetime) else (
            int(v) if isinstance(v, (int, float, str)) else v),
        datetime: lambda v: v.replace(tzinfo=None) if hasattr(v, "replace") else v,
        str: str,
        float: float,
    }

    # Handle enums
    if isinstance(field_type, type) and issubclass(field_type, Enum):
        def enum_transformer(v):
            if isinstance(v, Enum):
                return v.value
            if isinstance(v, str):
                try:
                    return field_type[v].value
                except KeyError:
                    return v
            return v

        return enum_transformer

    return type_transformers.get(field_type)


def get_nested_field_transformers(field_info: Any) -> Dict[str, TransformerT]:
    """
    Get transformers for nested model fields.

    :param field_info: Field info from Pydantic model
    :return: Dictionary of field transformers for nested model
    """
    if not hasattr(field_info.annotation, 'model_fields'):
        return {}

    nested_transformers = {}
    for field_name, nested_field in field_info.annotation.model_fields.items():
        transformer = get_field_transformer(nested_field.annotation)
        if transformer:
            nested_transformers[field_name] = transformer

    return nested_transformers


def convert_dict_keys_to_camel(data: Dict) -> Dict:
    """
    Convert dictionary keys from snake_case to camelCase.

    :param data: Dictionary with snake_case keys
    :return: Dictionary with camelCase keys
    """
    if not isinstance(data, dict):
        return data

    return {
        snake_to_camel(key): convert_dict_keys_to_camel(value) if isinstance(value, dict) else value
        for key, value in data.items()
    }


def convert_sqlalchemy_obj_to_dict(obj: Any) -> Dict:
    """
    Convert SQLAlchemy object to dictionary with proper case and type conversion.

    :param obj: SQLAlchemy model instance
    :return: Dictionary representation with camelCase keys
    """
    if obj is None:
        return None

    data = {}
    for column in obj.__table__.columns:
        value = getattr(obj, column.name)
        camel_name = snake_to_camel(column.name)

        # Handle enum values
        if isinstance(value, Enum):
            value = value.name
        # Handle datetime values
        elif isinstance(value, datetime):
            value = int(value.timestamp()) if value else None
        # Handle dictionary values
        elif isinstance(value, dict):
            value = convert_dict_keys_to_camel(value)

        data[camel_name] = value

    return data


def map_to_model(
        db_instance: DBModelT,
        model_class: Type[ModelT],
        custom_mapping: Optional[Dict[str, str]] = None,
        field_transformers: Optional[Dict[str, TransformerT]] = None
) -> ModelT:
    """
    Convert SQLAlchemy model instance to Pydantic model instance.

    :param db_instance: Source SQLAlchemy model instance.
    :param model_class: Target Pydantic model class.
    :param custom_mapping: Optional dictionary mapping target field names to source field paths.
    :param field_transformers: Optional dictionary mapping field names to transformer functions.
    :return: Instance of the target Pydantic model.

    Example:
        Field mapping is optional.
        >>> field_mapping = {
        ...     "firstName": "first_name",
        ...     "createdAt": "created_at"
        ... }
        >>> field_transformers = {
        ...     "createdAt": datetime_to_epoch
        ... }
        >>> user_model = map_to_model(user, UserModel, field_mapping, field_transformers)
    """
    default_mapping = create_default_mapping(model_class, type(db_instance))
    field_mapping = {**default_mapping, **(custom_mapping or {})}

    # Get default transformers based on field types
    default_transformers = {}
    for field_name, field_info in model_class.model_fields.items():
        transformer = get_field_transformer(field_info.annotation)
        if transformer:
            default_transformers[field_name] = transformer

    transformers = {**default_transformers, **(field_transformers or {})}

    data = {}
    for model_field, db_field in field_mapping.items():
        value = safe_get_nested_attr(db_instance, db_field)

        if value is not None:
            # Handle SQLAlchemy model instances
            if hasattr(value, "__table__"):
                value = convert_sqlalchemy_obj_to_dict(value)
            # Handle enum values
            elif isinstance(value, Enum):
                value = value.name
            # Handle datetime values
            elif isinstance(value, datetime):
                value = int(value.timestamp()) if value else None
            # Handle dictionary values
            elif isinstance(value, dict):
                value = convert_dict_keys_to_camel(value)

            # Apply any transformers
            transformer = transformers.get(model_field)
            if transformer:
                try:
                    value = transformer(value)
                except Exception as exc:
                    logger.error(f"Error applying transformer to field {model_field}: {exc}")
                    continue

            data[model_field] = value

    return model_class.model_validate(data)
