from datetime import datetime
from enum import Enum
from typing import Any, Dict

from pydantic import BaseModel, ConfigDict


class BlitzyCommonPydanticModel(BaseModel):
    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True
    )

    def model_dump_json(obj: Any) -> Dict:
        """
        Custom dump function that properly handles enums and other special types.

        :param obj: Pydantic model instance
        :return: JSON serializable dictionary
        """

        def _serialize(value: Any) -> Any:
            if isinstance(value, Enum):
                return value.name  # or value.value based on your needs
            elif isinstance(value, datetime):
                return value.isoformat()
            elif isinstance(value, dict):
                return {k: _serialize(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [_serialize(item) for item in value]
            return value

        # Use model_dump to get dict and then serialize special types
        data = obj.model_dump(warnings=False)
        return {k: _serialize(v) for k, v in data.items()}
