"""
Integration tests to verify the race condition fix works in realistic scenarios.
"""

import os
import threading
import time
import unittest
from unittest.mock import Mock, patch
from concurrent.futures import ThreadPoolExecutor

from common_models.db_client import SpannerClientSingleton, get_db_session


class TestIntegration(unittest.TestCase):
    """Integration tests for the race condition fix."""

    def setUp(self):
        """Set up test environment."""
        # Reset the singleton instance before each test
        SpannerClientSingleton._instance = None
        
        # Mock environment variables
        self.env_patcher = patch.dict(os.environ, {
            'SPANNER_DATABASE_NAME': 'test_db',
            'DATABASE_HOST': 'localhost',
            'DATABASE_PORT': '5432',
            'ALLOW_TABLE_CREATION': 'false'
        })
        self.env_patcher.start()

    def tearDown(self):
        """Clean up after each test."""
        self.env_patcher.stop()
        SpannerClientSingleton._instance = None

    @patch('common_models.db_client.create_engine')
    @patch('common_models.db_client.sessionmaker')
    @patch('common_models.db_client.event')
    def test_get_db_session_concurrent_access(self, mock_event, mock_sessionmaker, mock_create_engine):
        """Test that get_db_session() works correctly under concurrent access."""
        # Mock the engine and sessionmaker
        mock_engine = Mock()
        mock_create_engine.return_value = mock_engine
        mock_session_instance = Mock()
        mock_session_class = Mock(return_value=mock_session_instance)
        mock_sessionmaker.return_value = mock_session_class
        # Mock the event system
        mock_event.listens_for.return_value = lambda func: func

        successful_sessions = []
        errors = []

        def worker():
            """Worker function that simulates the original error scenario."""
            try:
                # This simulates the exact call pattern from company_service.py
                with get_db_session() as session:
                    # Simulate some database work
                    time.sleep(0.001)
                    successful_sessions.append(session)
            except Exception as e:
                errors.append(e)

        # Run multiple workers concurrently
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(worker) for _ in range(20)]
            
            # Wait for all to complete
            for future in futures:
                future.result()

        # Verify no errors occurred
        self.assertEqual(len(errors), 0, f"Errors occurred: {errors}")
        self.assertEqual(len(successful_sessions), 20)

    @patch('common_models.db_client.create_engine')
    @patch('common_models.db_client.sessionmaker')
    @patch('common_models.db_client.event')
    def test_singleton_initialization_robustness(self, mock_event, mock_sessionmaker, mock_create_engine):
        """Test that singleton initialization is robust under stress."""
        # Mock the engine and sessionmaker
        mock_engine = Mock()
        mock_create_engine.return_value = mock_engine
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        # Mock the event system
        mock_event.listens_for.return_value = lambda func: func

        # Add some delay to initialization to increase chance of race conditions
        original_init = SpannerClientSingleton._initialize_client
        
        def slow_init(self):
            time.sleep(0.01)  # Small delay
            return original_init(self)
        
        with patch.object(SpannerClientSingleton, '_initialize_client', slow_init):
            instances = []
            errors = []

            def create_and_use_singleton():
                try:
                    singleton = SpannerClientSingleton()
                    session = singleton.get_session()
                    instances.append((singleton, session))
                except Exception as e:
                    errors.append(e)

            # Create many threads that simultaneously create and use singleton
            threads = []
            for _ in range(30):
                thread = threading.Thread(target=create_and_use_singleton)
                threads.append(thread)

            # Start all threads
            for thread in threads:
                thread.start()

            # Wait for all to complete
            for thread in threads:
                thread.join()

            # Verify no errors occurred
            self.assertEqual(len(errors), 0, f"Errors occurred: {errors}")
            
            # Verify all instances are the same singleton
            self.assertEqual(len(instances), 30)
            for singleton, session in instances:
                self.assertIs(singleton, instances[0][0])

    def test_error_message_clarity(self):
        """Test that error messages are clear when SessionLocal is not initialized."""
        # Reset singleton
        SpannerClientSingleton._instance = None
        
        # Create a singleton instance but prevent proper initialization
        with patch('common_models.db_client.sessionmaker') as mock_sessionmaker:
            mock_sessionmaker.side_effect = Exception("Mock initialization failure")
            
            try:
                singleton = SpannerClientSingleton()
            except:
                pass  # Expected to fail
            
            # Get the singleton instance
            singleton = SpannerClientSingleton._instance
            
            # Try to get a session - should get clear error message
            with self.assertRaises(RuntimeError) as context:
                singleton.get_session()
            
            error_message = str(context.exception)
            self.assertIn("not properly initialized", error_message)
            self.assertIn("SessionLocal is not available", error_message)


if __name__ == '__main__':
    unittest.main()
