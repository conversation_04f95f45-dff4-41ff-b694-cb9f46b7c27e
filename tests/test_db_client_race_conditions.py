"""
Unit tests for SpannerClientSingleton race condition robustness.

This module contains comprehensive tests to verify that the SpannerClientSingleton
class handles concurrent access correctly and prevents race conditions that could
lead to AttributeError: 'SpannerClientSingleton' object has no attribute 'SessionLocal'.
"""

import os
import threading
import time
import unittest
from unittest.mock import Mock, patch
from concurrent.futures import ThreadPoolExecutor, as_completed

from common_models.db_client import SpannerClientSingleton


class TestSpannerClientSingletonRaceConditions(unittest.TestCase):
    """Test cases for SpannerClientSingleton race condition scenarios."""

    def setUp(self):
        """Set up test environment before each test."""
        # Reset the singleton instance before each test
        SpannerClientSingleton._instance = None
        
        # Mock environment variables to avoid real database connections
        self.env_patcher = patch.dict(os.environ, {
            'SPANNER_DATABASE_NAME': 'test_db',
            'DATABASE_HOST': 'localhost',
            'DATABASE_PORT': '5432',
            'ALLOW_TABLE_CREATION': 'false'
        })
        self.env_patcher.start()

    def tearDown(self):
        """Clean up after each test."""
        self.env_patcher.stop()
        # Reset singleton instance
        SpannerClientSingleton._instance = None

    @patch('common_models.db_client.create_engine')
    @patch('common_models.db_client.sessionmaker')
    @patch('common_models.db_client.event')
    def test_concurrent_singleton_creation(self, mock_event, mock_sessionmaker, mock_create_engine):
        """Test that multiple threads creating singleton instances work correctly."""
        # Mock the engine and sessionmaker
        mock_engine = Mock()
        mock_create_engine.return_value = mock_engine
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        # Mock the event system
        mock_event.listens_for.return_value = lambda func: func
        
        instances = []
        errors = []
        
        def create_singleton():
            try:
                instance = SpannerClientSingleton()
                instances.append(instance)
            except Exception as e:
                errors.append(e)
        
        # Create multiple threads that try to create singleton instances
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=create_singleton)
            threads.append(thread)
        
        # Start all threads simultaneously
        for thread in threads:
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Verify no errors occurred
        self.assertEqual(len(errors), 0, f"Errors occurred during concurrent creation: {errors}")
        
        # Verify all instances are the same (singleton behavior)
        self.assertEqual(len(instances), 10)
        for instance in instances:
            self.assertIs(instance, instances[0])

    @patch('common_models.db_client.create_engine')
    @patch('common_models.db_client.sessionmaker')
    @patch('common_models.db_client.event')
    def test_concurrent_get_session_calls(self, mock_event, mock_sessionmaker, mock_create_engine):
        """Test that concurrent get_session() calls don't cause race conditions."""
        # Mock the engine and sessionmaker
        mock_engine = Mock()
        mock_create_engine.return_value = mock_engine
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        # Mock the event system
        mock_event.listens_for.return_value = lambda func: func
        
        # Create singleton instance first
        singleton = SpannerClientSingleton()
        
        sessions = []
        errors = []
        
        def get_session():
            try:
                session = singleton.get_session()
                sessions.append(session)
            except Exception as e:
                errors.append(e)
        
        # Create multiple threads that try to get sessions
        threads = []
        for _ in range(20):
            thread = threading.Thread(target=get_session)
            threads.append(thread)
        
        # Start all threads simultaneously
        for thread in threads:
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Verify no errors occurred
        self.assertEqual(len(errors), 0, f"Errors occurred during concurrent get_session calls: {errors}")
        
        # Verify all sessions were created
        self.assertEqual(len(sessions), 20)

    @patch('common_models.db_client.create_engine')
    @patch('common_models.db_client.sessionmaker')
    @patch('common_models.db_client.event')
    def test_get_session_before_initialization_complete(self, mock_event, mock_sessionmaker, mock_create_engine):
        """Test the specific race condition scenario from the error log."""
        # Mock the engine and sessionmaker with a delay to simulate slow initialization
        mock_engine = Mock()
        mock_create_engine.return_value = mock_engine
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        # Mock the event system
        mock_event.listens_for.return_value = lambda func: func
        
        # Add delay to sessionmaker to simulate slow initialization
        def slow_sessionmaker(*args, **kwargs):
            time.sleep(0.1)  # Simulate slow initialization
            return mock_session_class

        mock_sessionmaker.side_effect = slow_sessionmaker
        
        errors = []
        sessions = []
        
        def create_and_get_session():
            try:
                # This simulates the scenario where one thread creates the singleton
                # and another thread immediately tries to get a session
                singleton = SpannerClientSingleton()
                session = singleton.get_session()
                sessions.append(session)
            except Exception as e:
                errors.append(e)
        
        # Create multiple threads that simultaneously create singleton and get session
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(create_and_get_session) for _ in range(5)]
            
            # Wait for all to complete
            for future in as_completed(futures):
                future.result()  # This will raise any exceptions that occurred
        
        # Verify no AttributeError occurred
        attribute_errors = [e for e in errors if isinstance(e, AttributeError)]
        self.assertEqual(len(attribute_errors), 0, 
                        f"AttributeError occurred: {attribute_errors}")

    @patch('common_models.db_client.create_engine')
    @patch('common_models.db_client.event')
    def test_get_session_with_uninitialized_sessionlocal(self, mock_event, mock_create_engine):
        """Test that get_session() properly handles uninitialized SessionLocal."""
        # Mock the engine
        mock_engine = Mock()
        mock_create_engine.return_value = mock_engine
        # Mock the event system
        mock_event.listens_for.return_value = lambda func: func

        # Create singleton but don't let it complete initialization
        with patch('common_models.db_client.sessionmaker') as mock_sessionmaker:
            # Make sessionmaker fail to simulate incomplete initialization
            mock_sessionmaker.side_effect = Exception("Initialization failed")

            singleton = None
            try:
                singleton = SpannerClientSingleton()
            except:
                pass  # Expected to fail during initialization

            # Get the singleton instance even if initialization failed
            if singleton is None:
                singleton = SpannerClientSingleton._instance

            # Now try to get a session - should raise RuntimeError, not AttributeError
            with self.assertRaises(RuntimeError) as context:
                singleton.get_session()

            self.assertIn("not properly initialized", str(context.exception))
            self.assertIn("SessionLocal is not available", str(context.exception))

    @patch('common_models.db_client.create_engine')
    @patch('common_models.db_client.sessionmaker')
    @patch('common_models.db_client.event')
    def test_stress_test_concurrent_access(self, mock_event, mock_sessionmaker, mock_create_engine):
        """Stress test with many concurrent threads accessing the singleton."""
        # Mock the engine and sessionmaker
        mock_engine = Mock()
        mock_create_engine.return_value = mock_engine
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        # Mock the event system
        mock_event.listens_for.return_value = lambda func: func
        
        num_threads = 50
        operations_per_thread = 10
        
        errors = []
        successful_operations = []
        
        def worker():
            for _ in range(operations_per_thread):
                try:
                    singleton = SpannerClientSingleton()
                    session = singleton.get_session()
                    successful_operations.append(1)
                    # Small delay to increase chance of race conditions
                    time.sleep(0.001)
                except Exception as e:
                    errors.append(e)
        
        # Create and start many threads
        threads = []
        for _ in range(num_threads):
            thread = threading.Thread(target=worker)
            threads.append(thread)
        
        start_time = time.time()
        
        # Start all threads
        for thread in threads:
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        end_time = time.time()
        
        # Verify results
        self.assertEqual(len(errors), 0, f"Errors occurred during stress test: {errors}")
        expected_operations = num_threads * operations_per_thread
        self.assertEqual(len(successful_operations), expected_operations)
        
        print(f"Stress test completed: {expected_operations} operations in {end_time - start_time:.2f} seconds")


if __name__ == '__main__':
    unittest.main()
