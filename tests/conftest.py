"""
Pytest configuration for db-common-model tests.
"""

import pytest
import os
from unittest.mock import patch


@pytest.fixture(autouse=True)
def mock_environment():
    """Mock environment variables for all tests."""
    with patch.dict(os.environ, {
        'SPANNER_DATABASE_NAME': 'test_db',
        'DATABASE_HOST': 'localhost',
        'DATABASE_PORT': '5432',
        'ALLOW_TABLE_CREATION': 'false'
    }):
        yield


@pytest.fixture(autouse=True)
def reset_singleton():
    """Reset SpannerClientSingleton before each test."""
    from common_models.db_client import SpannerClientSingleton
    SpannerClientSingleton._instance = None
    yield
    SpannerClientSingleton._instance = None
