apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: {{SERVICE_NAME}}
  labels:
    managed-by: github-actions
    cloud.googleapis.com/location: {{DEPLOYMENT_REGION}}
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/minScale: '1'
        run.googleapis.com/container-dependencies: '{"archie-service-admin-1":["pgadapter"]}'
        run.googleapis.com/vpc-access-connector: {{CONNECTOR_NAME}}
        run.googleapis.com/vpc-access-egress: {{EGRESS_SETTING}}
    spec:
      containerConcurrency: 50
      timeoutSeconds: 3600
      serviceAccountName: {{SERVICE_ACCOUNT}}
      volumes:
      - name: sockets-dir
        emptyDir:
          sizeLimit: 50Mi
          medium: Memory
      containers:
      - name: archie-service-admin-1
        image: {{ARTIFACTORY_REGION}}-docker.pkg.dev/{{PROJECT_ID}}/{{REPOSITORY}}/{{IMAGE_NAME}}:{{IMAGE_TAG}}
        ports:
        - name: http1
          containerPort: {{PORT}}
        env:
        - name: SPANNER_DATABASE_NAME
          value: {{SPANNER_DATABASE_NAME}}
        - name: PROJECT_ID
          value: {{PROJECT_ID}}
        - name: LOG_LEVEL
          value: {{LOG_LEVEL}}
        - name: SERVICE_NAME
          value: {{SERVICE_NAME}}
        - name: GCS_BUCKET_NAME
          value: {{GCS_BUCKET_NAME}}
        - name: JOB_CPU_REQUEST
          value: '{{JOB_CPU_REQUEST}}'
        - name: JOB_CPU_LIMIT
          value: '{{JOB_CPU_LIMIT}}'
        - name: JOB_MEMORY_REQUEST
          value: {{JOB_MEMORY_REQUEST}}
        - name: JOB_MEMORY_LIMIT
          value: {{JOB_MEMORY_LIMIT}}
        - name: GKE_ZONE
          value: {{GKE_ZONE}}
        - name: GKE_CLUSTER
          value: {{GKE_CLUSTER}}
        - name: ENVIRONMENT
          value: {{ENVIRONMENT}}
        - name: WINDOWS_JOB_IMAGE
          value: {{WINDOWS_JOB_IMAGE}}
        - name: WINDOWS_JOB_REQUESTS_CPU
          value: '{{WINDOWS_JOB_REQUESTS_CPU}}'
        - name: WINDOWS_JOB_REQUESTS_MEMORY
          value: '{{WINDOWS_JOB_REQUESTS_MEMORY}}'
        - name: WINDOWS_JOB_LIMITS_CPU
          value: '{{WINDOWS_JOB_LIMITS_CPU}}'
        - name: WINDOWS_JOB_LIMITS_MEMORY
          value: '{{WINDOWS_JOB_LIMITS_MEMORY}}'
        - name: WINDOWS_JOB_NAMESPACE
          value: {{WINDOWS_JOB_NAMESPACE}}
        - name: GCP_SERVICE_ACCOUNT_SECRET_NAME
          value: {{GCP_SERVICE_ACCOUNT_SECRET_NAME}}
        resources:
          limits:
            cpu: 1000m
            memory: 512Mi
        startupProbe:
          timeoutSeconds: 10
          periodSeconds: 10
          failureThreshold: 1
          tcpSocket:
            port: 8080
        volumeMounts:
        - mountPath: /sockets
          name: sockets-dir
      - name: pgadapter
        image: gcr.io/cloud-spanner-pg-adapter/pgadapter
        volumeMounts:
        - mountPath: /sockets
          name: sockets-dir
        args:
        - -dir /sockets
        - -x
        startupProbe:
          initialDelaySeconds: 10
          timeoutSeconds: 10
          periodSeconds: 10
          failureThreshold: 3
          tcpSocket:
            port: 5432
  traffic:
  - percent: 100
    latestRevision: true
