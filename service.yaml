apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: {{SERVICE_NAME}}
  labels:
    managed-by: github-actions
    cloud.googleapis.com/location: {{DEPLOYMENT_REGION}}
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/minScale: '1'
        run.googleapis.com/container-dependencies: '{"archie-service-backend-1":["pgadapter"]}'
        run.googleapis.com/vpc-access-connector: {{CONNECTOR_NAME}}
        run.googleapis.com/vpc-access-egress: {{EGRESS_SETTING}}
    spec:
      containerConcurrency: 50
      timeoutSeconds: 3600
      serviceAccountName: {{SERVICE_ACCOUNT_NAME}}
      volumes:
      - name: sockets-dir
        emptyDir:
          sizeLimit: 50Mi
          medium: Memory
      - name: service-account-key
        secret:
          secretName: service-account-key
          items:
            - key: latest
              path: key.json
      containers:
      - name: archie-service-backend-1
        image: {{ARTIFACTORY_REGION}}-docker.pkg.dev/{{PROJECT_ID}}/{{REPOSITORY}}/{{IMAGE_NAME}}:{{IMAGE_TAG}}
        ports:
        - name: http1
          containerPort: {{PORT}}
        env:
        - name: SERVICE_NAME
          value: {{SERVICE_NAME}}
        - name: LOG_LEVEL
          value: {{LOG_LEVEL}}
        - name: ID_TOKEN_HEADER_NAME
          value: {{ID_TOKEN_HEADER_NAME}}
        - name: PROJECT_ID
          value: {{PROJECT_ID}}
        - name: UI_AUTH_SECRET_ID
          value: {{UI_AUTH_SECRET_ID}}
        - name: UI_SA_EMAIL
          value: {{UI_SA_EMAIL}}
        - name: JWT_AUDIENCE
          value: {{JWT_AUDIENCE}}
        - name: FIREBASE_API_KEY
          value: {{FIREBASE_API_KEY}}
        - name: BASIC_AUTH_HEADER_NAME
          value: {{BASIC_AUTH_HEADER_NAME}}
        - name: GENERATE_DOCUMENT_TOPIC
          value: {{GENERATE_DOCUMENT_TOPIC}}
        - name: GCS_BUCKET_NAME
          value: {{GCS_BUCKET_NAME}}
        - name: BLOB_NAME
          value: {{BLOB_NAME}}
        - name: GENERATE_CODE_TOPIC
          value: {{GENERATE_CODE_TOPIC}}
        - name: GITHUB_TOKEN
          value: {{GITHUB_TOKEN}}
        - name: SPANNER_DATABASE_INSTANCE_ID
          value: {{SPANNER_DATABASE_INSTANCE_ID}}
        - name: SPANNER_DATABASE_NAME
          value: {{SPANNER_DATABASE_NAME}}
        - name: STRIPE_API_KEY
          value: {{STRIPE_API_KEY}}
        - name: STRIPE_ENDPOINT_SECRET
          value: {{STRIPE_ENDPOINT_SECRET}}
        - name: STRIPE_BLITZY_PRO_PRICE_ID
          value: {{STRIPE_BLITZY_PRO_PRICE_ID}}
        - name: ALLOW_TABLE_CREATION
          value: '{{ALLOW_TABLE_CREATION}}'
        - name: GOOGLE_APPLICATION_CREDENTIALS
          value: /secrets/key.json
        - name: SERVICE_URL_SECRET
          value: {{SERVICE_URL_SECRET}}
        - name: FREE_USER_DOCUMENT_SUBMISSION_LIMIT
          value: '{{FREE_USER_DOCUMENT_SUBMISSION_LIMIT}}'
        - name: FREE_USER_LIMIT_RESETTING_TIME_HR
          value: '{{FREE_USER_LIMIT_RESETTING_TIME_HR}}'
        - name: PRO_USER_TRIAL_SUBMISSION_LIMIT
          value: '{{PRO_USER_TRIAL_SUBMISSION_LIMIT}}'
        - name: PRO_USER_ALLOWED_JOB_RUNS
          value: '{{PRO_USER_ALLOWED_JOB_RUNS}}'
        - name: GITHUB_CLIENT_ID
          value: {{GITHUB_CLIENT_ID}}
        - name: GITHUB_CLIENT_SECRET
          value: {{GITHUB_CLIENT_SECRET}}
        - name: HUBSPOT_API_KEY
          value: {{HUBSPOT_API_KEY}}
        - name: IPINFO_TOKEN
          value: {{IPINFO_TOKEN}}
        - name: IPINFO_API_URL
          value: {{IPINFO_API_URL}}
        - name: SENDGRID_API_KEY
          value: {{SENDGRID_API_KEY}}
        - name: SENDGRID_API_URL
          value: {{SENDGRID_API_URL}}
        - name: STRIPE_BLITZY_PRO_PRODUCT_ID
          value: {{STRIPE_BLITZY_PRO_PRODUCT_ID}}
        - name: ENVIRONMENT
          value: {{ENVIRONMENT}}
        - name: GITHUB_APP_ID
          value: '{{GITHUB_APP_ID}}'
        - name: GITHUB_PRIVATE_KEY
          value: {{GITHUB_PRIVATE_KEY}}
        - name: PLATFORM_URL
          value: {{PLATFORM_URL}}
        - name: SERVICE_URL_GITHUB
          value: {{SERVICE_URL_GITHUB}}
        - name: AZURE_CLIENT_ID
          value: {{AZURE_CLIENT_ID}}
        - name: AZURE_REDIRECT_URI
          value: {{AZURE_REDIRECT_URI}}
        - name: AZURE_STATE_SECRET
          value: {{AZURE_STATE_SECRET}}
        - name: AZURE_SUCCESS_AUTH_REDIRECT_URI
          value: {{AZURE_SUCCESS_AUTH_REDIRECT_URI}}
        - name: SERVICE_URL_ADMIN
          value: {{SERVICE_URL_ADMIN}}

        resources:
          limits:
            cpu: 1000m
            memory: 2Gi
        startupProbe:
          timeoutSeconds: 10
          periodSeconds: 10
          failureThreshold: 1
          tcpSocket:
            port: 8080
        volumeMounts:
        - mountPath: /sockets
          name: sockets-dir
        - name: service-account-key
          mountPath: /secrets
          readOnly: true
      - name: pgadapter
        image: gcr.io/cloud-spanner-pg-adapter/pgadapter
        volumeMounts:
        - mountPath: /sockets
          name: sockets-dir
        args:
        - -dir /sockets
        - -x
        resources:
          limits:
            memory: 2Gi  # Adjust this value as needed
            cpu: 1000m    # Optional: you can also set CPU limits
        startupProbe:
          initialDelaySeconds: 10
          timeoutSeconds: 10
          periodSeconds: 10
          failureThreshold: 3
          tcpSocket:
            port: 5432
  traffic:
  - percent: 100
    latestRevision: true
