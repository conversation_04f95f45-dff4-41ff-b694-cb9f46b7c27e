apiVersion: run.googleapis.com/v1
kind: Job
metadata:
  name: {{SERVICE_NAME}}
  labels:
    managed-by: github-actions
    cloud.googleapis.com/location: {{DEPLOYMENT_REGION}}
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/container-dependencies: '{"archie-db-migration":["pgadapter"]}'
    spec:
      taskCount: 1
      template:
        spec:
          timeoutSeconds: 3600
          serviceAccountName: {{SERVICE_ACCOUNT}}
          containers:
          - name: archie-db-migration
            image: {{ARTIFACTORY_REGION}}-docker.pkg.dev/{{PROJECT_ID}}/{{REPOSITORY}}/{{IMAGE_NAME}}:{{IMAGE_TAG}}
            env:
            - name: SPANNER_DATABASE_NAME
              value: {{SPANNER_DATABASE_NAME}}
            - name: PROJECT_ID
              value: {{PROJECT_ID}}
            resources:
              limits:
                cpu: 1000m
                memory: 512Mi
            volumeMounts:
            - mountPath: /sockets
              name: sockets-dir
          - name: pgadapter
            image: gcr.io/cloud-spanner-pg-adapter/pgadapter
            volumeMounts:
            - mountPath: /sockets
              name: sockets-dir
            args:
            - -dir /sockets
            - -x
            startupProbe:
              tcpSocket:
                port: 5432
              initialDelaySeconds: 10
              timeoutSeconds: 10
              periodSeconds: 10
              failureThreshold: 3
          volumes:
          - name: sockets-dir
            emptyDir:
              sizeLimit: 50Mi
              medium: Memory
