apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: {{SERVICE_NAME}}
  labels:
    managed-by: github-actions
    cloud.googleapis.com/location: {{DEPLOYMENT_REGION}}
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/container-dependencies: '{"archie-service-backend-1":["pgadapter"]}'
        run.googleapis.com/vpc-access-connector: {{CONNECTOR_NAME}}
        run.googleapis.com/vpc-access-egress: {{EGRESS_SETTING}}
    spec:
      containerConcurrency: 50
      timeoutSeconds: 3600
      serviceAccountName: {{SERVICE_ACCOUNT_NAME}}
      volumes:
      - name: sockets-dir
        emptyDir:
          sizeLimit: 50Mi
          medium: Memory
      containers:
      - name: archie-service-backend-1
        image: {{ARTIFACTORY_REGION}}-docker.pkg.dev/{{PROJECT_ID}}/{{REPOSITORY}}/{{IMAGE_NAME}}:{{IMAGE_TAG}}
        ports:
        - name: http1
          containerPort: {{PORT}}
        env:
        - name: SPANNER_DATABASE_NAME
          value: {{SPANNER_DATABASE_NAME}}
        - name: ALLOW_TABLE_CREATION
          value: '{{ALLOW_TABLE_CREATION}}'
        - name: SERVICE_URL_MARKDOWN
          value: {{SERVICE_URL_MARKDOWN}}
        - name: SERVICE_URL_BACKEND
          value: {{SERVICE_URL_BACKEND}}
        - name: GCS_BUCKET_NAME
          value: {{GCS_BUCKET_NAME}}
        - name: BLOB_NAME
          value: {{BLOB_NAME}}
        - name: TECH_SPECIFICATION_NAME
          value: {{TECH_SPECIFICATION_NAME}}
        - name: SENDGRID_VALIDATION_API_KEY
          value: {{SENDGRID_VALIDATION_API_KEY}}
        - name: SENDGRID_API_KEY
          value: {{SENDGRID_API_KEY}}
        - name: DEFAULT_FROM_EMAIL
          value: {{DEFAULT_FROM_EMAIL}}
        - name: DEFAULT_FROM_NAME
          value: {{DEFAULT_FROM_NAME}}
        - name: PLATFORM_URL
          value: {{PLATFORM_URL}}
        resources:
          limits:
            cpu: 1000m
            memory: 512Mi
        startupProbe:
          timeoutSeconds: 10
          periodSeconds: 10
          failureThreshold: 1
          tcpSocket:
            port: 8080
        volumeMounts:
        - mountPath: /sockets
          name: sockets-dir
      - name: pgadapter
        image: gcr.io/cloud-spanner-pg-adapter/pgadapter
        volumeMounts:
        - mountPath: /sockets
          name: sockets-dir
        args:
        - -dir /sockets
        - -x
        startupProbe:
          initialDelaySeconds: 10
          timeoutSeconds: 10
          periodSeconds: 10
          failureThreshold: 3
          tcpSocket:
            port: 5432
  traffic:
  - percent: 100
    latestRevision: true
