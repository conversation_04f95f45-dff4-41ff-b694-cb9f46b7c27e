from set_env import PROJECT_ID, GRAPH_CODE_TOPIC

from google.cloud import pubsub_v1

from blitzy_utils.common import publish_notification

publisher = pubsub_v1.PublisherClient()

batch_index = 0
excluded_indexes = [6]

notification_data = {"repo_name": "matplotlib", "repo_id": "1001674343", "branch_id": "a3d04b93-019f-4c8e-af25-235172650492", "branch_name": "matplotlib__matplotlib-20488", "company_id": "default", "user_id": "623eaeb5-f5c3-4e26-abdb-bdea6772da58", "team_id": "default",
                     "job_id": "f67a58a5-7b7a-424b-8a7f-1c78407d4af5", "project_id": "37b1fad4-df50-4f6b-be09-0c6b0aacb7df", "head_commit_hash": "b7ce415c15eb39b026a097a2865da73fbcf15c9c", "prev_head_commit_hash": "", "propagate": True, "batch_index": 6, "total_batches": 7, "tech_spec_id": "5c483c51-98e8-4810-9e2c-beacd07829ef"}

total_batches = notification_data["total_batches"]

while batch_index < total_batches:
    batch_index += 1
    if batch_index in excluded_indexes:
        continue
    notification_data["batch_index"] = batch_index
    publish_notification(
        publisher=publisher,
        notification_data=notification_data,
        project_id=PROJECT_ID,
        topic_id=GRAPH_CODE_TOPIC
    )
