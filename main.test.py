try:
    from test.test_azure import run_all_tests as azure_tests
    from test.test_github import run_all_tests as github_tests
except ImportError as e:
    print(f"Import error: {e}")
    exit(1)

SUPPRESS_LOGGING = True  # Set to False to see all logs during tests

try:
    azure_tests(suppress_logging=SUPPRESS_LOGGING)
    github_tests(suppress_logging=SUPPRESS_LOGGING)
except KeyboardInterrupt:
    print("\nTest execution interrupted by user.")
    exit(1)
