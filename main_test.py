from src.process_event import process_payload

payload = {'projectId': 'eace2154-3a9e-4ead-8d1d-6158870d4f81', 'jobId': '16005789-bbac-4595-ad46-8b4f5a22c39b', 'phase': 'CODE_GENERATION', 'status': 'DONE', 'user_id': 'e7be55e1-e659-47aa-b41e-7753d139d611', 'tech_spec_id': 'c166b8fd-840e-4528-aafe-6dde5b263fff', 'code_gen_id': '5d1ba0b0-6981-4621-ba2f-f11cddb56e13', 'git_project_repo_id': 'eee824eb-7abd-4f03-98a8-22dc8b24ec00', 'metadata': {'propagate': True, 'repo_name': '07-22-new-product', 'repo_url': 'https://dev.azure.com/blitzy-dev/11142fc3-fb99-42cf-9647-2db71eaa74ff/_apis/git/repositories/b94138b6-3eed-434c-a6a7-de4a67f109f8', 'user_id': 'e7be55e1-e659-47aa-b41e-7753d139d611', 'pr_data': {'id': 12, 'title': 'Autonomous product: 07-22-new-product created by Blitzy', 'description': 'Includes all files created by Blitzy Agents', 'url': 'https://dev.azure.com/blitzy-dev/11142fc3-fb99-42cf-9647-2db71eaa74ff/_apis/git/repositories/b94138b6-3eed-434c-a6a7-de4a67f109f8/pullRequests/12', 'status': 'active', 'source_ref': 'refs/heads/blitzy-5d1ba0b0-6981-4621-ba2f-f11cddb56e13', 'target_ref': 'refs/heads/main', 'created_date': '2025-07-24T18:44:12.178471+00:00'}}}

if __name__ == '__main__':
    process_payload(payload)
