#!/usr/bin/env python3
import argparse
import json
import os
import re
import subprocess
import sys


def check_gateway_exists(project_id, gateway_id, location):
    """Check if the API Gateway exists."""
    try:
        cmd = ["gcloud", "api-gateway", "gateways", "describe", gateway_id,
               f"--location={location}", f"--project={project_id}",
               "--format=json"]
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            return True
        else:
            error_msg = result.stderr.strip()
            if "NOT_FOUND" in error_msg:
                print(f"Gateway '{gateway_id}' not found in project '{project_id}' at location '{location}'")
            else:
                print(f"Error checking gateway: {error_msg}")
            return False
    except Exception as e:
        print(f"Error checking gateway existence: {str(e)}")
        return False


def create_gateway(project_id, gateway_id, location, api_id):
    """Create a new API Gateway."""
    try:
        print(f"Creating new API Gateway '{gateway_id}'...")
        cmd = ["gcloud", "api-gateway", "gateways", "create", gateway_id,
               f"--api={api_id}", f"--location={location}", f"--project={project_id}",
               "--format=json"]
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print(f"Successfully created API Gateway: {gateway_id}")
            return True
        else:
            error_msg = result.stderr.strip()
            if "ALREADY_EXISTS" in error_msg:
                print(f"Gateway '{gateway_id}' already exists")
                return True
            else:
                print(f"Failed to create gateway: {error_msg}")
                return False
    except Exception as e:
        print(f"Error creating gateway: {str(e)}")
        return False


def get_latest_config_version(project_id, api_id):
    """Get the latest config version from existing configs."""
    try:
        cmd = ["gcloud", "api-gateway", "api-configs", "list",
               f"--api={api_id}", f"--project={project_id}",
               "--format=json"]
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode != 0:
            print(f"Error fetching configs: {result.stderr.strip()}")
            return None

        try:
            configs = json.loads(result.stdout)
        except json.JSONDecodeError:
            print("Error parsing config list response")
            return None

        if not configs:
            print("No existing configs found")
            return 0

        latest_version = 0
        for config in configs:
            config_id = config.get('name', '').split('/')[-1]
            match = re.search(r'v0-0-(\d+)$', config_id)
            if match:
                version_num = int(match.group(1))
                latest_version = max(latest_version, version_num)

        return latest_version

    except Exception as e:
        print(f"Error getting config version: {str(e)}")
        return None


def get_last_deployed_commit(project_id, api_id):
    """Get the commit hash from the latest deployed config's description."""
    try:
        cmd = ["gcloud", "api-gateway", "api-configs", "list",
               f"--api={api_id}", f"--project={project_id}",
               "--format=json"]
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode != 0:
            print(f"Error fetching configs: {result.stderr.strip()}")
            return None

        try:
            configs = json.loads(result.stdout)
        except json.JSONDecodeError:
            print("Error parsing config list response")
            return None

        if not configs:
            return None

        # Find the latest version and its description
        latest_version = 0
        latest_description = None

        for config in configs:
            config_id = config.get('name', '').split('/')[-1]
            description = config.get('description', '')

            match = re.search(r'v0-0-(\d+)$', config_id)
            if match:
                version_num = int(match.group(1))
                if version_num > latest_version:
                    latest_version = version_num
                    latest_description = description

        if latest_description:
            match = re.search(r'Commit: ([a-f0-9]+)', latest_description)
            if match:
                return match.group(1)
        return None

    except Exception as e:
        print(f"Error getting last deployed commit: {str(e)}")
        return None


def get_file_changes(file_path, project_id, api_id):
    """Check if the file has changes in current diff or was changed in last commit."""
    print("\n=== Checking for Changes ===")
    try:
        # First check if we're in CI
        is_ci = os.getenv('CI') == 'true'
        print(f"Running in CI environment: {is_ci}")

        if is_ci:
            # In CI, check changes in the current commit
            try:
                changed_files = subprocess.check_output(
                    ['git', 'diff-tree', '--no-commit-id', '--name-only', '-r', 'HEAD'],
                    universal_newlines=True
                ).strip().split('\n')
                print(f"Files modified in current commit: {changed_files}")

                if file_path in changed_files:
                    print(f"✨ File {file_path} was changed in the current commit")
                    return True

            except subprocess.CalledProcessError as e:
                print(f"Error checking CI changes: {e}")
                return True
        else:
            # Local development checks
            # Check for uncommitted changes
            try:
                status_output = subprocess.check_output(
                    ['git', 'status', '--porcelain', file_path],
                    universal_newlines=True
                ).strip()

                if status_output:
                    print(f"✨ Found uncommitted changes in {file_path}")
                    return True

                # Check if file was changed in the last commit
                last_commit_files = subprocess.check_output(
                    ['git', 'diff-tree', '--no-commit-id', '--name-only', '-r', 'HEAD'],
                    universal_newlines=True
                ).strip().split('\n')

                print(f"Files modified in last commit: {last_commit_files}")

                if file_path in last_commit_files:
                    print(f"✨ File {file_path} was changed in the last commit")
                    return True

            except subprocess.CalledProcessError as e:
                print(f"Error checking local changes: {e}")
                return True

        print(f"ℹ️ No changes detected in {file_path}")
        return False

    except Exception as e:
        print(f"Error checking changes: {e}")
        return True


def deploy_api_gateway(project_id, api_id, swagger_path, gateway_id, location,
                       service_account=None, force=False, dry_run=False,
                       create_gateway_if_not_exists=False):
    """Deploy API Gateway configuration using gcloud commands."""
    print("\n=== Starting API Gateway Deployment ===")
    print(f"Project: {project_id}")
    print(f"Gateway: {gateway_id}")
    print(f"Location: {location}")

    if dry_run:
        print("🏃 DRY RUN MODE - No changes will be made")

    # Validate swagger file exists
    if not os.path.exists(swagger_path):
        print(f"❌ Error: Swagger file not found at path: {swagger_path}")
        sys.exit(1)

    # Check if we're in CI
    is_ci = os.getenv('CI') == 'true'
    if is_ci:
        print("CI environment detected - proceeding with deployment")
        force = True

    # Check if gateway exists
    print("\nChecking API Gateway status...")
    gateway_exists = check_gateway_exists(project_id, gateway_id, location)
    if not gateway_exists:
        if not create_gateway_if_not_exists:
            print(f"❌ Error: Gateway '{gateway_id}' does not exist.")
            print("Options:")
            print("1. Use --create-gateway-if-not-exists flag to create it automatically")
            print("2. Create the gateway manually using:")
            print(f"gcloud api-gateway gateways create {gateway_id} --api={api_id}"
                  f" --location={location} --project={project_id}")
            sys.exit(1)
        else:
            if not dry_run:
                print(f"🔨 Creating new gateway '{gateway_id}'...")
                if not create_gateway(project_id, gateway_id, location, api_id):
                    print("❌ Failed to create gateway. Please check error messages above.")
                    sys.exit(1)
            else:
                print(f"🔨 Would create new gateway: {gateway_id}")

    # Check if swagger file has changes
    print("\nChecking for changes in swagger file...")
    has_changes = get_file_changes(swagger_path, project_id, api_id)

    if not has_changes and not force:
        print("\n🛑 No changes detected and --force not used. Skipping deployment.")
        return

    if not has_changes and force:
        print("\n⚠️ No changes detected, but proceeding with deployment due to --force flag")

    if has_changes:
        print("\n✨ Changes detected, proceeding with deployment")

    # Get the latest version number
    latest_version = get_latest_config_version(project_id, api_id)
    if latest_version is None:
        print("❌ Error fetching config versions. Exiting.")
        sys.exit(1)

    new_version = latest_version + 1
    new_config_id = f"api-config-v0-0-{new_version}"

    # Get current commit for the config description
    try:
        current_commit = subprocess.check_output(
            ['git', 'rev-parse', 'HEAD'],
            universal_newlines=True
        ).strip()
    except subprocess.CalledProcessError:
        current_commit = "unknown"

    print("\n=== Deployment Plan ===")
    print(f"📋 Project ID: {project_id}")
    print(f"📋 API ID: {api_id}")
    print(f"📋 New Config ID: {new_config_id}")
    print(f"📋 Gateway ID: {gateway_id}")
    print(f"📋 Location: {location}")
    print(f"📋 Commit: {current_commit[:8]}")
    if service_account:
        print(f"📋 Service Account: {service_account}")
    else:
        print("📋 Using authenticated account for deployment")

    if dry_run:
        print("\n🏃 DRY RUN COMPLETE - No changes were made")
        return

    print("\n🚀 Proceeding with deployment...")

    # Create new API config
    create_config_cmd = [
        "gcloud", "api-gateway", "api-configs", "create", new_config_id,
        f"--api={api_id}",
        f"--openapi-spec={swagger_path}",
        f"--project={project_id}",
        "--format=json"
    ]

    if service_account:
        create_config_cmd.append(f"--backend-auth-service-account={service_account}")

    print("\nCreating new API config...")
    result = subprocess.run(create_config_cmd, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"❌ Failed to create API config: {result.stderr.strip()}")
        sys.exit(1)

    # Update gateway to use new config
    update_gateway_cmd = [
        "gcloud", "api-gateway", "gateways", "update", gateway_id,
        f"--api={api_id}",
        f"--api-config={new_config_id}",
        f"--location={location}",
        f"--project={project_id}",
        "--format=json"
    ]

    print("\nUpdating API Gateway...")
    result = subprocess.run(update_gateway_cmd, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"❌ Failed to update gateway: {result.stderr.strip()}")
        sys.exit(1)

    print(f"\n✅ Successfully deployed API Gateway config {new_config_id}")


def main():
    parser = argparse.ArgumentParser(description='Deploy Google API Gateway')
    parser.add_argument('--project-id', required=True, help='Google Cloud Project ID')
    parser.add_argument('--api-id', required=True, help='API ID')
    parser.add_argument('--swagger-path', required=True, help='Path to swagger.yaml file')
    parser.add_argument('--gateway-id', required=True, help='Gateway ID')
    parser.add_argument('--location', required=True, help='Gateway location (e.g., us-east1)')
    parser.add_argument('--service-account', help='Backend auth service account email (optional)')
    parser.add_argument('--force', action='store_true',
                        help='Force deployment even if no changes detected')
    parser.add_argument('--dry-run', action='store_true',
                        help='Show what would happen without making any changes')
    parser.add_argument('--create-gateway-if-not-exists', action='store_true',
                        help='Create the API Gateway if it does not exist')

    args = parser.parse_args()

    deploy_api_gateway(
        args.project_id,
        args.api_id,
        args.swagger_path,
        args.gateway_id,
        args.location,
        args.service_account,
        args.force,
        args.dry_run,
        args.create_gateway_if_not_exists
    )


if __name__ == "__main__":
    main()
