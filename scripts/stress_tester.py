import argparse
import asyncio
import json
import statistics
import sys
import textwrap
import time
from collections import defaultdict
from datetime import datetime

import aiohttp
from tabulate import tabulate


class PerformanceTest:
    def __init__(self):
        self.results = []
        self.status_codes = defaultdict(int)

    def print_result_header(self):
        print("\n" + tabulate([["Request ID", "Status Code", "Response Time (s)", "Status", "Details"]],
                              headers="firstrow", tablefmt="grid"))


async def dry_run(url, token, payload=None, verify_ssl=True, timeout=30):
    """
    Perform a single test request to verify API accessibility and authentication
    """
    print("\nPerforming dry run...")
    print(f"URL: {url}")
    print(f"SSL Verification: {'Enabled' if verify_ssl else 'Disabled'}")
    print(f"Timeout: {timeout} seconds")
    if payload:
        print(f"Payload: {json.dumps(payload, indent=2)}")

    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }

    try:
        connector = aiohttp.TCPConnector(force_close=True, ssl=verify_ssl)
        timeout_obj = aiohttp.ClientTimeout(total=timeout)

        async with aiohttp.ClientSession(connector=connector, timeout=timeout_obj) as session:
            start_time = time.perf_counter()
            async with session.post(url, headers=headers, json=payload) if payload else session.get(
                    url, headers=headers) as response:
                response_data = await response.json()
                end_time = time.perf_counter()

                print("\nDry Run Results:")
                print("-" * 50)
                print(f"Status Code: {response.status}")
                print(f"Response Time: {(end_time - start_time):.3f}s")
                print("Response Headers:")
                for header, value in response.headers.items():
                    print(f"  {header}: {value}")
                print("\nResponse Body:")
                print(json.dumps(response_data, indent=2))

                if response.status >= 200 and response.status < 300:
                    print("\n✅ Dry run successful! The API is accessible and responding correctly.")
                    return True
                else:
                    print(f"\n❌ Dry run failed! The API returned status code {response.status}")
                    return False

    except aiohttp.ClientError as e:
        print(f"\n❌ Dry run failed! Connection error: {str(e)}")
        return False
    except json.JSONDecodeError:
        print("\n⚠️ Warning: Response is not JSON format. Raw response:")
        print(await response.text())
        return False
    except asyncio.TimeoutError:
        print(f"\n❌ Dry run failed! Request timed out after {timeout} seconds")
        return False
    except Exception as e:
        print(f"\n❌ Dry run failed! Unexpected error: {str(e)}")
        return False


async def make_request(session, url, headers, request_id, perf_test, payload=None):
    try:
        start_time = time.perf_counter()
        method = session.post if payload else session.get
        async with method(url, headers=headers, json=payload) as response:
            await response.json()
            duration = time.perf_counter() - start_time

            result = {
                'request_id': request_id,
                'status_code': response.status,
                'response_time': duration,
                'status': 'Success' if str(response.status).startswith('2') else 'Failed',
                'details': 'OK'
            }
    except Exception as e:
        duration = time.perf_counter() - start_time
        result = {
            'request_id': request_id,
            'status_code': 'Error',
            'response_time': duration,
            'status': 'Failed',
            'details': str(e)
        }

    print(tabulate([[
        result['request_id'],
        result['status_code'],
        f"{duration:.3f}",
        result['status'],
        result['details']
    ]], tablefmt="grid"))

    perf_test.results.append(result)
    perf_test.status_codes[str(result['status_code'])] += 1

    return result


async def run_performance_test(url, token, num_requests=100, batch_size=None, dry_run_only=False,
                               verify_ssl=True, timeout=30, payload=None):
    if dry_run_only or (not dry_run_only and await dry_run(url, token, payload, verify_ssl, timeout)):
        if dry_run_only:
            return
    else:
        proceed = input("\nDry run failed. Do you want to proceed with the performance test anyway? (y/N): ")
        if proceed.lower() != 'y':
            print("Performance test aborted.")
            sys.exit(1)

    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }

    perf_test = PerformanceTest()

    print(f"\nStarting performance test at {datetime.now()}")
    print(f"URL: {url}")
    print(f"SSL Verification: {'Enabled' if verify_ssl else 'Disabled'}")
    print(f"Timeout: {timeout} seconds")

    # Calculate safe connection limits
    max_connections = min(batch_size or 100, 100)  # Default to 100 if no batch size

    if batch_size:
        print(f"Making {num_requests} requests in batches of {batch_size}")
    else:
        print(f"Making {num_requests} concurrent requests with max {max_connections} simultaneous connections")

    perf_test.print_result_header()

    # Configure connection pooling with limits
    connector = aiohttp.TCPConnector(
        limit=max_connections,
        limit_per_host=max_connections,
        force_close=True,
        ssl=verify_ssl
    )

    timeout_obj = aiohttp.ClientTimeout(total=timeout)

    test_start = time.perf_counter()

    async with aiohttp.ClientSession(connector=connector, timeout=timeout_obj) as session:
        try:
            if batch_size:
                # Process requests in batches
                for i in range(0, num_requests, batch_size):
                    batch_end = min(i + batch_size, num_requests)
                    batch_tasks = [
                        make_request(session, url, headers, j, perf_test, payload)
                        for j in range(i, batch_end)
                    ]
                    await asyncio.gather(*batch_tasks)
                    # Small delay between batches
                    await asyncio.sleep(0.1)
            else:
                # Create tasks for all requests but limit concurrent execution
                tasks = [
                    make_request(session, url, headers, i, perf_test, payload)
                    for i in range(num_requests)
                ]
                await asyncio.gather(*tasks)

        finally:
            # Ensure connections are closed
            await connector.close()

    test_end = time.perf_counter()

    # Calculate statistics based on actual request times
    response_times = [r['response_time'] for r in perf_test.results]
    successful_requests = len([r for r in perf_test.results if str(r['status_code']).startswith('2')])
    failed_requests = len([r for r in perf_test.results if not str(r['status_code']).startswith('2')])
    total_test_time = test_end - test_start
    actual_rps = len(perf_test.results) / total_test_time if total_test_time > 0 else 0

    sorted_times = sorted(response_times)

    summary_headers = ['Metric', 'Value']
    summary_data = [
        ['Total Requests', num_requests],
        ['Successful Requests', successful_requests],
        ['Failed Requests', failed_requests],
        ['Total Test Duration', f"{total_test_time:.2f}s"],
        ['Actual Requests/Second', f"{actual_rps:.2f}"],
        ['Average Response Time', f"{statistics.mean(response_times):.3f}s"],
        ['Median Response Time', f"{statistics.median(response_times):.3f}s"],
        ['Min Response Time', f"{min(response_times):.3f}s"],
        ['Max Response Time', f"{max(response_times):.3f}s"],
        ['Standard Deviation', f"{statistics.stdev(response_times):.3f}s"],
        ['90th Percentile', f"{sorted_times[int(len(sorted_times) * 0.9)]:.3f}s"],
        ['95th Percentile', f"{sorted_times[int(len(sorted_times) * 0.95)]:.3f}s"],
        ['99th Percentile', f"{sorted_times[int(len(sorted_times) * 0.99)]:.3f}s"]
    ]

    for code, count in sorted(perf_test.status_codes.items()):
        summary_data.append([f'Status {code}', count])

    print("\nFinal Summary:")
    print(tabulate(summary_data, headers=summary_headers, tablefmt='grid'))


def main():
    parser = argparse.ArgumentParser(
        description='API Performance Testing Tool',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=textwrap.dedent('''
            Example usage:
            --------------
              With JSON payload from command line:
                python script.py --url https://api.example.com/endpoint --token YOUR_JWT_TOKEN
                --payload '{"key": "value"}'

              With JSON payload from file:
                python script.py --url https://api.example.com/endpoint --token YOUR_JWT_TOKEN
                --payload-file payload.json

              Dry run only:
                python script.py --url https://api.example.com/endpoint --token YOUR_JWT_TOKEN --dry-run

              Full concurrent test:
                python script.py --url https://api.example.com/endpoint --token YOUR_JWT_TOKEN --num-requests 1000

              Batched test:
                python script.py --url https://api.example.com/endpoint --token YOUR_JWT_TOKEN --num-requests 1000
                --batch-size 50

              Disable SSL verification:
                python script.py --url https://api.example.com/endpoint --token YOUR_JWT_TOKEN --no-verify-ssl

              Set custom timeout:
                python script.py --url https://api.example.com/endpoint --token YOUR_JWT_TOKEN --timeout 60
            ''')
    )

    # Required arguments
    parser.add_argument('--url', '-u',
                        required=True,
                        help='The API endpoint URL to test')
    parser.add_argument('--token', '-t',
                        required=True,
                        help='JWT Bearer token for authentication')

    # Optional arguments
    parser.add_argument('--num-requests', '-n',
                        type=int,
                        default=100,
                        help='Number of requests to make (default: 100)')
    parser.add_argument('--batch-size', '-b',
                        type=int,
                        help='Optional: Number of concurrent requests per batch. If not specified, '
                             'all requests will be concurrent with max 100 connections.')
    parser.add_argument('--dry-run', '-d',
                        action='store_true',
                        help='Perform a single test request and show detailed response')
    parser.add_argument('--no-verify-ssl',
                        action='store_true',
                        help='Disable SSL certificate verification')
    parser.add_argument('--timeout',
                        type=float,
                        default=30,
                        help='Request timeout in seconds (default: 30)')

    # Add JSON payload arguments
    payload_group = parser.add_mutually_exclusive_group()
    payload_group.add_argument('--payload',
                               type=str,
                               help='JSON payload string for POST requests')
    payload_group.add_argument('--payload-file',
                               type=str,
                               help='Path to JSON file containing payload for POST requests')

    args = parser.parse_args()

    # Parse JSON payload
    payload = None
    if args.payload:
        try:
            payload = json.loads(args.payload)
        except json.JSONDecodeError:
            parser.error("Invalid JSON payload")
    elif args.payload_file:
        try:
            with open(args.payload_file) as f:
                payload = json.load(f)
        except (json.JSONDecodeError, FileNotFoundError) as e:
            parser.error(f"Error reading payload file: {str(e)}")

    # Validate URL format
    try:
        if not args.url.startswith(('http://', 'https://')):
            raise ValueError("URL must start with http:// or https://")
    except ValueError as e:
        parser.error(str(e))

    asyncio.run(run_performance_test(
        url=args.url,
        token=args.token,
        num_requests=args.num_requests,
        batch_size=args.batch_size,
        dry_run_only=args.dry_run,
        verify_ssl=not args.no_verify_ssl,
        timeout=args.timeout,
        payload=payload
    ))


if __name__ == "__main__":
    main()

if __name__ == "__main__":
    main()
