# Command to run stress testing script

First generate token from blitzy. Please use /auth API available in swagger. Contact Chaitanya or Sid for access.

Post generation export the generated token

```shell
export BLITZY_AUTH_TOKEN=<TOKEN>
```

Then run following command

```shell
python roughwork/stress_tester.py --url https://os.api.blitzy.dev/v1/user/subscription/status --token $BLITZY_AUTH_TOKEN --num-requests 1000 --batch-size=50
```

To send request with payload

```shell
python scripts/stress_tester.py --url https://os.api.blitzy.dev/v1/pdf/convert --token $BLITZY_AUTH_TOKEN --num-requests 20 --batch-size=50 --payload-file scripts/data.json
```
