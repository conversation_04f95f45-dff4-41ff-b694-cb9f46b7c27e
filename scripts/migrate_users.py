"""
<PERSON><PERSON><PERSON> to migrate users from github installations to github installation access.
"""

from common_models.db_client import get_db_session
from common_models.models import (AccessRole, AccessType, GithubInstallation,
                                  GitHubInstallationAccess,
                                  GithubInstallationStatus,
                                  VersionControlSystem)


def migrate_users():
    with get_db_session() as session:
        github_installation_list = session.query(GithubInstallation).filter(
            GithubInstallation.status == GithubInstallationStatus.ACTIVE,
            GithubInstallation.svc_type == VersionControlSystem.GITHUB
        ).all()

        for installation in github_installation_list:
            github_installation = session.query(GitHubInstallationAccess).filter(
                GitHubInstallationAccess.integration_id == installation.id).first()

            if github_installation:
                print(f"--Installation {installation.installation_id} already migrated.")
                continue
            print(f"--Migrating users for installation {installation.installation_id}...")
            if installation.user_id:
                github_installation_access = GitHubInstallationAccess(
                    integration_id=installation.id,
                    access_type=AccessType.USER,
                    entity_id=installation.user_id,
                    is_owner=True,
                    role=AccessRole.OWNER
                )
                session.add(github_installation_access)
                session.commit()
            print(f"--Migrated users for installation {installation.installation_id}.")


if __name__ == "__main__":
    migrate_users()
