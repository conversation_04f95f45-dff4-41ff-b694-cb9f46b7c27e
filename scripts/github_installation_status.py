"""
GitHub App Installation Status Checker
This script checks if a GitHub App installation is active based on installation ID.
"""

import time

import jwt
import requests
from common_models.db_client import get_db_session
from common_models.models import (GithubInstallation, GithubInstallationStatus,
                                  VersionControlSystem)

from src.consts import GITHUB_APP_ID, GITHUB_PRIVATE_KEY


def get_jwt_token(app_id, private_key):
    """
    Generate a JWT (JSON Web Token) for authentication.

    This function creates a signed JWT token using the provided
    application ID and private key. The token is set to expire
    10 minutes after its creation.

    :param app_id: Application ID to be used as the issuer in the JWT.
    :type app_id: str
    :param private_key: The private RSA key used to sign the JWT.
    :type private_key: str
    :return: Encoded JWT token generated using RS256 algorithm.
    :rtype: str
    """
    now = int(time.time())
    payload = {
        'iat': now,
        'exp': now + 600,
        'iss': app_id
    }

    encoded_jwt = jwt.encode(payload, private_key, algorithm='RS256')
    return encoded_jwt


def check_installation_status(installation_id):
    """
    Checks the status of a GitHub App installation.

    :param installation_id: The ID of the GitHub App installation to check.
    :type installation_id: int
    :return: A dictionary containing the installation status details.
    :rtype: dict
    """
    jwt_token = get_jwt_token(GITHUB_APP_ID, GITHUB_PRIVATE_KEY)

    headers = {
        'Authorization': f'Bearer {jwt_token}',
        'Accept': 'application/vnd.github.v3+json'
    }

    installation_url = f'https://api.github.com/app/installations/{installation_id}'
    response = requests.get(installation_url, headers=headers)

    if response.status_code == 404:
        return {
            'exists': False,
            'active': False,
            'message': 'Installation not found'
        }

    if response.status_code != 200:
        return {
            'exists': False,
            'active': False,
            'message': f'Error: {response.status_code} - {response.text}'
        }

    installation = response.json()

    is_active = installation.get('suspended_at') is None

    target_type = installation.get('target_type')
    target_name = installation.get('account', {}).get('login')

    return {
        'exists': True,
        'active': is_active,
        'suspended_at': installation.get('suspended_at'),
        'target_type': target_type,
        'target_name': target_name
    }


def get_active_installations():
    with get_db_session() as session:
        github_installations = session.query(GithubInstallation).filter(
            GithubInstallation.status == GithubInstallationStatus.ACTIVE,
            GithubInstallation.svc_type == VersionControlSystem.GITHUB
        ).all()

        for installation in github_installations:
            result = check_installation_status(installation.installation_id)
            if not result['exists']:
                if not result['active']:
                    print(f"--Installation {installation.installation_id} does not exist.")
                    installation.status = GithubInstallationStatus.UNINSTALLED
                    session.commit()
                    print(f"--Installation {installation.installation_id} marked as UNINSTALLED.")


def main():
    get_active_installations()


if __name__ == "__main__":
    main()
