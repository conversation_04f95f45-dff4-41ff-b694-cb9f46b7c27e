"""
Script Usage

To replicate user secrets to target secrets:
python3 secret_replication.py --project <project_id> replicate --target-prefix <target_prefix>

To delete secrets with a given prefix:
python3 secret_replication.py --project <project_id> delete --prefix <prefix>

To list secrets with given prefix:
python3 secret_replication.py --project <project_id> list --prefix <prefix>
"""

# !/usr/bin/env python3

import argparse
import json
import sys

from common_models.db_client import get_db_session
from common_models.models import (GithubInstallation, GithubInstallationStatus,
                                  VersionControlSystem)
from google.api_core import exceptions
from google.cloud import secretmanager


class SecretManager:
    def __init__(self, project_id):
        """Initialize the Secret Manager client with project ID."""
        self.project_id = project_id
        self.client = secretmanager.SecretManagerServiceClient()
        self.project_path = f"projects/{self.project_id}"

    def list_secrets(self, prefix=None):
        """List all secrets, optionally filtering by prefix."""
        try:
            response = self.client.list_secrets(request={"parent": self.project_path})
            secrets = []

            for secret in response:
                secret_name = secret.name.split('/')[-1]
                if prefix is None or secret_name.startswith(prefix):
                    secrets.append(secret_name)

            return secrets
        except exceptions.GoogleAPIError as e:
            print(f"Error listing secrets: {e}")
            return []

    def get_secret(self, secret_id, version_id="latest"):
        """Get the secret value for the given secret ID and version."""
        try:
            name = f"{self.project_path}/secrets/{secret_id}/versions/{version_id}"
            response = self.client.access_secret_version(request={"name": name})
            payload = response.payload.data.decode("UTF-8")
            return payload
        except exceptions.GoogleAPIError as e:
            print(f"Error accessing secret {secret_id}: {e}")
            return None

    def create_secret(self, secret_id, payload):
        """Create a new secret with the given ID and payload."""
        try:
            # Check if secret already exists
            try:
                self.client.get_secret(request={"name": f"{self.project_path}/secrets/{secret_id}"})
                print(f"Secret {secret_id} already exists.")
                return False
            except exceptions.NotFound:
                pass  # Secret doesn't exist, we'll create it

            # Create secret
            self.client.create_secret(
                request={
                    "parent": self.project_path,
                    "secret_id": secret_id,
                    "secret": {"replication": {"automatic": {}}},
                }
            )

            # Add secret version with payload
            parent = f"{self.project_path}/secrets/{secret_id}"
            payload_bytes = payload.encode("UTF-8")

            self.client.add_secret_version(
                request={"parent": parent, "payload": {"data": payload_bytes}}
            )

            print(f"Created secret: {secret_id}")
            return True
        except exceptions.GoogleAPIError as e:
            print(f"Error creating secret {secret_id}: {e}")
            return False

    def delete_secret(self, secret_id):
        """Delete a secret with the given ID."""
        try:
            name = f"{self.project_path}/secrets/{secret_id}"
            self.client.delete_secret(request={"name": name})
            print(f"Deleted secret: {secret_id}")
            return True
        except exceptions.GoogleAPIError as e:
            print(f"Error deleting secret {secret_id}: {e}")
            return False

    def replicate_user_secret(self, user_id, target_prefix="github-secret"):
        """
        Fetch user-secret-<user_id> and create <target_prefix>-<installation_id>
        using the installation_id from the original secret.
        """
        user_secret_id = f"user-secret-{user_id}"
        secret_content = self.get_secret(user_secret_id)

        if not secret_content:
            print(f"Error: Could not retrieve secret for user {user_id}")
            return False

        try:
            secret_data = json.loads(secret_content)

            if "installation_id" not in secret_data:
                print(f"Error: installation_id not found in secret for user {user_id}")
                return False

            installation_id = secret_data["installation_id"]
            target_secret_id = f"{target_prefix}-{installation_id}"

            # Create the new secret with the same content
            result = self.create_secret(target_secret_id, secret_content)

            if result:
                print(f"Successfully replicated {user_secret_id} to {target_secret_id}")

            return result
        except json.JSONDecodeError:
            print(f"Error: Secret content for user {user_id} is not valid JSON")
            return False

    def delete_secrets_with_prefix(self, prefix):
        """Delete all secrets that start with the given prefix."""
        secrets = self.list_secrets(prefix)

        if not secrets:
            print(f"No secrets found with prefix '{prefix}'")
            return 0

        count = 0
        for secret_id in secrets:
            if self.delete_secret(secret_id):
                count += 1

        print(f"Deleted {count} secrets with prefix '{prefix}'")
        return count


def get_and_replicate_secrets(project_id, target_prefix):
    manager = SecretManager(project_id)
    with get_db_session() as session:
        github_installations = session.query(GithubInstallation).filter(
            GithubInstallation.status == GithubInstallationStatus.ACTIVE,
            GithubInstallation.svc_type == VersionControlSystem.GITHUB
        ).all()

        for github_installation in github_installations:
            user_id = github_installation.user_id
            if user_id:
                manager.replicate_user_secret(user_id, target_prefix)


def main():
    parser = argparse.ArgumentParser(description="Google Secret Manager operations")
    parser.add_argument("--project", required=True, help="Google Cloud project ID")

    subparsers = parser.add_subparsers(dest="command", help="Command to execute")

    # Replicate command
    replicate_parser = subparsers.add_parser("replicate", help="Replicate user secret to target secret")
    replicate_parser.add_argument("--target-prefix", default="github-secret",
                                  help="Prefix for target secret (default: github-secret)")

    # Delete command
    delete_parser = subparsers.add_parser("delete", help="Delete secrets with prefix")
    delete_parser.add_argument("--prefix", required=True, help="Prefix of secrets to delete")

    # List command (additional utility)
    list_parser = subparsers.add_parser("list", help="List secrets")
    list_parser.add_argument("--prefix", help="Filter secrets by prefix")

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        sys.exit(1)

    manager = SecretManager(args.project)

    if args.command == "replicate":
        target_prefix = args.target_prefix
        get_and_replicate_secrets(args.project, target_prefix)

    elif args.command == "delete":
        count = manager.delete_secrets_with_prefix(args.prefix)
        sys.exit(0 if count > 0 else 1)

    elif args.command == "list":
        secrets = manager.list_secrets(args.prefix)
        for secret in secrets:
            print(secret)
        sys.exit(0)


if __name__ == "__main__":
    main()
