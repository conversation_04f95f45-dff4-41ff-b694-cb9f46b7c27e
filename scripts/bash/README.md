# Azure DevOps App Registration

This script creates an Azure AD app registration for OAuth authentication with Azure DevOps services.

## Prerequisites

- Azure CLI installed and accessible in PATH
- Sufficient permissions to create app registrations in Azure AD
- Internet connection for device code authentication

## Quick Start

1. **Run the script:**
   ```bash
   ./create_azure_app.sh
   ```

2. **Follow the device code authentication prompts**

3. **Copy the generated credentials and OAuth URL from the script output**

4. **Add granular permissions manually** (optional - see below)

## Optional: Add Granular Permissions

If you need more specific permissions instead of broad `user_impersonation`:

### Step 1: Access Your App Registration
1. Go to [Azure Portal → App registrations](https://portal.azure.com/#view/Microsoft_AAD_RegisteredApps)
2. Find your app (e.g., "Blitzy Dev") 
3. Click on **API permissions**

### Step 2: Add Specific Azure DevOps Permissions
1. Click **"Add a permission"** → **"APIs my organization uses"**
2. Search for **"Azure DevOps"** → Select **Azure DevOps** → **"Delegated permissions"**
3. **Select the permissions you need:**
#### **Code & Repository Access**
- ✅ **`vso.code`** - Code (read) - *Read source code and metadata*
- ✅ **`vso.code_write`** - Code (read and write) - *Read and write source code*
- ✅ **`vso.code_full`** - Code (full) - *Full access to source code, including delete*
- ⚠️ **`vso.code_status`** - Code (status) - *Read and write commit and pull request status*

#### **Project & Organization Access**
- ✅ **`vso.project`** - Project and team (read) - *Read projects and teams*
- ✅ **`vso.project_write`** - Project and team (read and write) - *Read and write projects and teams*
- ⚠️ **`vso.project_manage`** - Project and team (read, write and manage) - *Create, read, update and delete projects and teams*

#### **User Profile Access**
- ✅ **`vso.profile`** - User profile (read) - *Read user profile, accounts, collections, projects, teams*
- ⚠️ **`vso.profile_write`** - User profile (write) - *Write to user profile*

#### **Work Items & Boards**
- ✅ **`vso.work`** - Work items (read) - *Read work items, queries, boards, area and iterations*
- ✅ **`vso.work_write`** - Work items (read and write) - *Read and write work items, queries, boards, area and iterations*
- ⚠️ **`vso.work_full`** - Work items (full) - *Full access to work items, queries, boards, area and iterations*

#### **Build & Release**
- ✅ **`vso.build`** - Build (read) - *Read build artifacts, including builds, definitions and requests*
- ✅ **`vso.build_execute`** - Build (read and execute) - *Read and execute builds*
- ⚠️ **`vso.release`** - Release (read) - *Read release artifacts, including releases, definitions and requests*
- ⚠️ **`vso.release_execute`** - Release (read, write and execute) - *Read, write and execute releases*
- ⚠️ **`vso.release_manage`** - Release (read, write, execute and manage) - *Manage releases and release definitions*

#### **Testing**
- ⚠️ **`vso.test`** - Test management (read) - *Read test plans, cases, results and other test management related artifacts*
- ⚠️ **`vso.test_write`** - Test management (read and write) - *Read and write test plans, cases, results and other test management related artifacts*

#### **Package Management**
- ⚠️ **`vso.packaging`** - Packaging (read) - *Read feeds and packages*
- ⚠️ **`vso.packaging_write`** - Packaging (read and write) - *Read and write feeds and packages*
- ⚠️ **`vso.packaging_manage`** - Packaging (read, write and manage) - *Read, write and manage feeds and packages*

#### **Advanced/Admin Permissions**
- ⚠️ **`vso.analytics`** - Analytics (read) - *Read analytics data*
- ⚠️ **`vso.auditlog`** - Audit Log (read) - *Read audit log events*
- ⚠️ **`vso.extension`** - Extensions (read) - *Read installed extensions*
- ⚠️ **`vso.extension_manage`** - Extensions (read and manage) - *Read and manage installed extensions*
- ⚠️ **`vso.serviceendpoint`** - Service Connections (read) - *Read service endpoints*
- ⚠️ **`vso.serviceendpoint_manage`** - Service Connections (read and manage) - *Read and manage service endpoints*

4. Click **"Add permissions"**
5. Click **"Grant admin consent for [your tenant]"** → **"Yes"**

## That's it! 

Your app is ready to use for Azure DevOps OAuth authentication.