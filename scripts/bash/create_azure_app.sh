#!/bin/bash

#===============================================================================
# Azure DevOps App Registration Script
# Creates Azure AD app registration for OAuth authentication with Azure DevOps
#===============================================================================

set -e

# Configuration
APP_NAME="Blitzy Azure Devops"
REDIRECT_URIS=("https://platform.api.blitzy.com/v1/azure/auth/success")
AZURE_DEVOPS_API="499b84ac-1321-427f-aa17-267ca6975798"
USER_IMPERSONATION_GUID="07f90b3d-8b6b-4e8d-96b2-1b6fc8a6d3fc"

echo "=============================================="
echo "Azure DevOps App Registration"
echo "=============================================="

# Step 1: Authentication
echo "Authenticating to Azure..."
az login --use-device-code --allow-no-subscriptions

# Step 2: Create App
echo "Creating app registration '$APP_NAME'..."
echo "${REDIRECT_URIS[@]}"
APP_ID=$(az ad app create \
  --display-name "$APP_NAME" \
  --web-redirect-uris "${REDIRECT_URIS[@]}" \
  --query appId -o tsv)

echo "✓ App created: $APP_ID"

# Step 3: Configure Multi-tenant
echo "Configure multi-tenant '$APP_NAME'..."
OBJECT_ID=$(az ad app list --filter "appId eq '$APP_ID'" --query "[0].id" -o tsv)
az rest --method PATCH \
  --uri "https://graph.microsoft.com/v1.0/applications/$OBJECT_ID" \
  --headers "Content-Type=application/json" \
  --body '{"signInAudience":"AzureADMultipleOrgs"}'

echo "✓ Multi-tenant configured"

# Step 4: Generate Secret
CLIENT_SECRET=$(az ad app credential reset --id $APP_ID --append --years 2)

# Step 5: Add Azure DevOps Permission
echo "Adding Azure DevOps permission..."
az ad app permission add --id $APP_ID --api $AZURE_DEVOPS_API --api-permissions "${USER_IMPERSONATION_GUID}=Scope"

# Wait for propagation
echo "Waiting for Azure AD propagation..."
sleep 10

# Step 6: Grant Admin Consent for the app. This is only for our(Blitzy organization)
echo "Attempting to grant admin consent..."
if az ad app permission admin-consent --id $APP_ID 2>/dev/null; then
    echo "✓ Admin consent granted"
else
    echo "⚠️  Admin consent failed - grant manually via Azure Portal"
fi

# Extract credentials
tenant_id=$(echo "$CLIENT_SECRET" | awk -F'"' '/"tenant":/ {print $4}')
app_id=$(echo "$CLIENT_SECRET" | awk -F'"' '/"appId":/ {print $4}')
password=$(echo "$CLIENT_SECRET" | awk -F'"' '/"password":/ {print $4}')

# Results
echo ""
echo "==============================================================================="
echo "🎉 App Registration Complete!"
echo "==============================================================================="
echo "App Name:      $APP_NAME"
echo "Client ID:     $app_id"
echo "Tenant ID:     $tenant_id"
echo "Client Secret: $password"
echo "Redirect URIS:  $REDIRECT_URIS"
echo ""
echo "OAuth URL:"
echo "https://login.microsoftonline.com/common/oauth2/authorize?client_id=${app_id}&response_type=code&redirect_uri=${REDIRECT_URI}&scope=https://app.vssps.visualstudio.com/user_impersonation"
echo ""
echo "🔗 Manage permissions: https://portal.azure.com/#view/Microsoft_AAD_RegisteredApps/ApplicationMenuBlade/~/ApiPermissions/appId/$app_id"
echo "==============================================================================="
