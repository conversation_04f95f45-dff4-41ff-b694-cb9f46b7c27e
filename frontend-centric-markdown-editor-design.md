# Frontend-Centric Markdown Editor with TOC Design

## Overview

This document outlines a simplified, frontend-centric approach for editing large markdown documents (300+ pages) with table of contents navigation. The key principle is to leverage the frontend for editing and caching, only persisting the complete updated markdown to the backend when explicitly triggered.

## Design Philosophy

### Frontend-First Approach
- **Frontend handles**: TOC generation, block identification, editing, caching, and change management
- **Backend handles**: Document storage, retrieval, and persistence only
- **Simplicity**: Avoid complex block-based database schemas and APIs
- **Performance**: Leverage browser capabilities for fast interactions

### Core Workflow
1. Load complete markdown document from backend
2. Parse markdown to generate TOC and identify blocks on frontend
3. Enable click-to-edit functionality with frontend caching
4. Save complete updated markdown back to backend when triggered

## Current System Analysis

### Existing Frontend Components
- **TipTap Editor**: Already supports markdown with AST handling
- **Marked Library**: Used in `MarkdownViewer` for parsing
- **Mermaid Support**: Diagram rendering capability
- **Syntax Highlighting**: Code block highlighting with highlight.js

### Existing Backend Infrastructure
- **Tech Spec APIs**: Document CRUD operations in `src/api/routes/tech_spec.py`
- **GCS Storage**: File storage with presigned URLs
- **Document Models**: `TechnicalSpec` model for document management

## Data Models

### Frontend TypeScript Interfaces

```typescript
// Enhanced types for frontend-only block management
export interface MarkdownBlock {
    id: string;                    // Generated on frontend
    type: 'heading' | 'paragraph' | 'list' | 'code' | 'table' | 'blockquote' | 'image';
    level?: number;                // 1-6 for headings
    title?: string;                // For headings
    content: string;               // Raw markdown content
    startLine: number;             // Line number in original markdown
    endLine: number;               // End line number
    startOffset: number;           // Character offset in document
    endOffset: number;             // End character offset
    isModified: boolean;           // Track changes
    originalContent: string;       // For change detection
}

export interface TOCItem {
    id: string;
    blockId: string;
    title: string;
    level: number;                 // 1-6 for heading levels
    anchor: string;                // URL fragment
    children: TOCItem[];           // Nested structure
    lineNumber: number;            // For navigation
}

export interface DocumentState {
    id: string;
    title: string;
    originalMarkdown: string;      // Original content from backend
    currentMarkdown: string;       // Current state with edits
    blocks: MarkdownBlock[];       // Parsed blocks
    tableOfContents: TOCItem[];    // Generated TOC
    hasUnsavedChanges: boolean;    // Change tracking
    version: number;               // For conflict detection
    lastSaved: Date;
}

export interface EditSession {
    blockId: string;
    originalContent: string;
    currentContent: string;
    isActive: boolean;
    startTime: Date;
}
```

### Backend Models (Minimal Changes)

```python
# Reuse existing TechnicalSpec model or create simple MarkdownDocument
class MarkdownDocument(Base):
    __tablename__ = 'markdown_documents'
    
    id = Column(String, primary_key=True)
    project_id = Column(String, ForeignKey('projects.id'), nullable=False)
    title = Column(String, nullable=False)
    content = Column(Text, nullable=False)  # Full markdown content
    version = Column(Integer, default=1)
    checksum = Column(String, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
```

## Frontend Implementation (Using Proven Libraries)

### Required Dependencies

```json
{
  "dependencies": {
    "remark": "^15.0.1",
    "remark-parse": "^11.0.0",
    "remark-stringify": "^11.0.0",
    "mdast-util-toc": "^7.0.0",
    "mdast-util-to-markdown": "^2.1.0",
    "mdast-util-from-markdown": "^2.0.0",
    "unist-util-visit": "^5.0.0",
    "unist-util-map": "^4.0.0"
  }
}
```

### 1. Enhanced TOC Generator Service (Using mdast-util-toc)

```typescript
// src/services/TOCGeneratorService.ts
import { remark } from 'remark';
import { toc } from 'mdast-util-toc';
import { visit } from 'unist-util-visit';
import type { Root, Heading } from 'mdast';

export class TOCGeneratorService {
    generateTOC(markdown: string): TOCItem[] {
        const tree = remark().parse(markdown);
        const tocResult = toc(tree, {
            heading: 'Table of Contents',
            maxDepth: 6,
            tight: true
        });

        if (!tocResult.map) {
            return [];
        }

        return this.convertMdastToTOCItems(tocResult.map, tree);
    }

    private convertMdastToTOCItems(tocNode: any, originalTree: Root): TOCItem[] {
        const tocItems: TOCItem[] = [];
        const headingPositions = this.extractHeadingPositions(originalTree);

        visit(tocNode, 'list', (listNode) => {
            if (listNode.children) {
                listNode.children.forEach((listItem: any) => {
                    if (listItem.type === 'listItem') {
                        const tocItem = this.processListItem(listItem, headingPositions);
                        if (tocItem) {
                            tocItems.push(tocItem);
                        }
                    }
                });
            }
        });

        return tocItems;
    }

    private extractHeadingPositions(tree: Root): Map<string, { line: number; blockId: string }> {
        const positions = new Map();
        let lineNumber = 1;

        visit(tree, (node: any) => {
            if (node.type === 'heading') {
                const headingText = this.extractTextFromNode(node);
                const anchor = this.generateAnchor(headingText);
                positions.set(anchor, {
                    line: lineNumber,
                    blockId: this.generateBlockId(node)
                });
            }
            lineNumber += this.estimateNodeLines(node);
        });

        return positions;
    }

    private processListItem(listItem: any, headingPositions: Map<string, any>): TOCItem | null {
        const paragraph = listItem.children?.[0];
        if (!paragraph || paragraph.type !== 'paragraph') return null;

        const link = paragraph.children?.[0];
        if (!link || link.type !== 'link') return null;

        const title = this.extractTextFromNode(link);
        const anchor = link.url?.replace('#', '') || '';
        const position = headingPositions.get(anchor);

        const tocItem: TOCItem = {
            id: this.generateId(),
            blockId: position?.blockId || this.generateId(),
            title,
            level: this.inferLevelFromIndent(listItem),
            anchor,
            children: [],
            lineNumber: position?.line || 0
        };

        // Process nested children
        const nestedList = listItem.children?.[1];
        if (nestedList && nestedList.type === 'list') {
            nestedList.children.forEach((nestedItem: any) => {
                const childItem = this.processListItem(nestedItem, headingPositions);
                if (childItem) {
                    tocItem.children.push(childItem);
                }
            });
        }

        return tocItem;
    }

    private generateAnchor(text: string): string {
        return text.toLowerCase()
            .replace(/[^\w\s-]/g, '')
            .replace(/\s+/g, '-')
            .trim();
    }

    private extractTextFromNode(node: any): string {
        if (node.type === 'text') {
            return node.value;
        }
        if (node.children) {
            return node.children.map((child: any) => this.extractTextFromNode(child)).join('');
        }
        return '';
    }
}
```

### 2. Block Parser Service (Using Remark AST)

```typescript
// src/services/BlockParserService.ts
import { remark } from 'remark';
import { visit } from 'unist-util-visit';
import { toMarkdown } from 'mdast-util-to-markdown';
import type { Root, Node } from 'mdast';

export class BlockParserService {
    parseMarkdownToBlocks(markdown: string): MarkdownBlock[] {
        const tree = remark().parse(markdown);
        const blocks: MarkdownBlock[] = [];
        let currentLine = 1;
        let currentOffset = 0;

        visit(tree, (node: Node, index: number, parent: any) => {
            // Only process top-level nodes
            if (parent !== tree) return;

            const block = this.nodeToBlock(node, currentLine, currentOffset, index);
            blocks.push(block);

            // Update position tracking
            const nodeLines = this.estimateNodeLines(node);
            currentLine += nodeLines;
            currentOffset += block.content.length + 2; // +2 for \n\n separator
        });

        return blocks;
    }

    private nodeToBlock(node: Node, startLine: number, startOffset: number, index: number): MarkdownBlock {
        const content = toMarkdown(node).trim();
        const nodeLines = this.estimateNodeLines(node);

        return {
            id: this.generateBlockId(node, index),
            type: this.mapNodeType(node.type),
            level: node.type === 'heading' ? (node as any).depth : undefined,
            title: node.type === 'heading' ? this.extractTextFromNode(node) : undefined,
            content,
            startLine,
            endLine: startLine + nodeLines - 1,
            startOffset,
            endOffset: startOffset + content.length,
            isModified: false,
            originalContent: content
        };
    }

    private mapNodeType(nodeType: string): MarkdownBlock['type'] {
        const typeMap: Record<string, MarkdownBlock['type']> = {
            'heading': 'heading',
            'paragraph': 'paragraph',
            'list': 'list',
            'code': 'code',
            'table': 'table',
            'blockquote': 'blockquote',
            'image': 'image'
        };
        return typeMap[nodeType] || 'paragraph';
    }

    private estimateNodeLines(node: Node): number {
        const content = toMarkdown(node);
        return content.split('\n').length;
    }

    private extractTextFromNode(node: any): string {
        if (node.type === 'text') {
            return node.value;
        }
        if (node.children) {
            return node.children.map((child: any) => this.extractTextFromNode(child)).join('');
        }
        return '';
    }

    private generateBlockId(node: Node, index: number): string {
        return `block-${node.type}-${index}-${Date.now()}`;
    }
}
```

### 3. Markdown Rebuilder Service (Using mdast-util-to-markdown)

```typescript
// src/services/MarkdownRebuilderService.ts
import { remark } from 'remark';
import { toMarkdown } from 'mdast-util-to-markdown';
import { fromMarkdown } from 'mdast-util-from-markdown';
import { map } from 'unist-util-map';
import type { Root, Node } from 'mdast';

export class MarkdownRebuilderService {
    rebuildMarkdownFromBlocks(blocks: MarkdownBlock[]): string {
        // Convert each block back to markdown and join
        const markdownParts = blocks.map(block => block.content.trim());
        return markdownParts.join('\n\n');
    }

    updateBlockInMarkdown(originalMarkdown: string, blockId: string, newContent: string, blocks: MarkdownBlock[]): string {
        // Find the block to update
        const blockIndex = blocks.findIndex(b => b.id === blockId);
        if (blockIndex === -1) {
            throw new Error(`Block ${blockId} not found`);
        }

        // Parse original markdown to AST
        const tree = remark().parse(originalMarkdown);
        let currentIndex = 0;

        // Map through AST and replace the target node
        const updatedTree = map(tree, (node: Node, index: number, parent: any) => {
            // Only process top-level nodes
            if (parent !== tree) return node;

            if (currentIndex === blockIndex) {
                // Parse the new content and return its AST node
                const newContentTree = remark().parse(newContent.trim());
                const newNode = newContentTree.children[0]; // Get first child (the actual content node)
                currentIndex++;
                return newNode || node;
            }

            currentIndex++;
            return node;
        });

        // Convert back to markdown
        return toMarkdown(updatedTree as Root);
    }

    validateMarkdownStructure(markdown: string): { isValid: boolean; errors: string[] } {
        try {
            const tree = remark().parse(markdown);
            const errors: string[] = [];

            // Basic validation - check for common issues
            if (!tree.children || tree.children.length === 0) {
                errors.push('Document appears to be empty');
            }

            return {
                isValid: errors.length === 0,
                errors
            };
        } catch (error) {
            return {
                isValid: false,
                errors: [`Parse error: ${error.message}`]
            };
        }
    }
}
```

### 4. Enhanced Document State Manager

```typescript
// src/hooks/useMarkdownDocument.ts
import { MarkdownRebuilderService } from '../services/MarkdownRebuilderService';

export const useMarkdownDocument = (documentId: string) => {
    const [documentState, setDocumentState] = useState<DocumentState | null>(null);
    const [editSession, setEditSession] = useState<EditSession | null>(null);
    const [isLoading, setIsLoading] = useState(false);

    const tocGenerator = new TOCGeneratorService();
    const blockParser = new BlockParserService();
    const markdownRebuilder = new MarkdownRebuilderService();

    const loadDocument = async () => {
        setIsLoading(true);
        try {
            const response = await fetch(`/api/documents/${documentId}`);
            const data = await response.json();

            const blocks = blockParser.parseMarkdownToBlocks(data.content);
            const toc = tocGenerator.generateTOC(data.content);

            setDocumentState({
                id: data.id,
                title: data.title,
                originalMarkdown: data.content,
                currentMarkdown: data.content,
                blocks,
                tableOfContents: toc,
                hasUnsavedChanges: false,
                version: data.version,
                lastSaved: new Date(data.updated_at)
            });
        } catch (error) {
            console.error('Failed to load document:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const updateBlockContent = (blockId: string, newContent: string) => {
        if (!documentState) return;

        const updatedBlocks = documentState.blocks.map(block => {
            if (block.id === blockId) {
                return {
                    ...block,
                    content: newContent,
                    isModified: newContent !== block.originalContent
                };
            }
            return block;
        });

        // Use the rebuilder service to create new markdown
        const newMarkdown = markdownRebuilder.rebuildMarkdownFromBlocks(updatedBlocks);

        // Validate the new markdown structure
        const validation = markdownRebuilder.validateMarkdownStructure(newMarkdown);
        if (!validation.isValid) {
            console.warn('Markdown validation issues:', validation.errors);
        }

        // Regenerate TOC from updated markdown
        const updatedTOC = tocGenerator.generateTOC(newMarkdown);

        setDocumentState({
            ...documentState,
            blocks: updatedBlocks,
            currentMarkdown: newMarkdown,
            tableOfContents: updatedTOC,
            hasUnsavedChanges: newMarkdown !== documentState.originalMarkdown
        });

        // Update edit session
        if (editSession?.blockId === blockId) {
            setEditSession({
                ...editSession,
                currentContent: newContent
            });
        }
    };

    const saveDocument = async () => {
        if (!documentState?.hasUnsavedChanges) return;

        try {
            const response = await fetch(`/api/documents/${documentId}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    content: documentState.currentMarkdown,
                    version: documentState.version
                })
            });

            if (response.ok) {
                const updatedDoc = await response.json();

                // Update all blocks to mark them as saved
                const savedBlocks = documentState.blocks.map(block => ({
                    ...block,
                    originalContent: block.content,
                    isModified: false
                }));

                setDocumentState({
                    ...documentState,
                    blocks: savedBlocks,
                    originalMarkdown: documentState.currentMarkdown,
                    hasUnsavedChanges: false,
                    version: updatedDoc.version,
                    lastSaved: new Date()
                });
            }
        } catch (error) {
            console.error('Failed to save document:', error);
            throw error;
        }
    };

    return {
        documentState,
        editSession,
        isLoading,
        loadDocument,
        startEditingBlock: (blockId: string) => {
            const block = documentState?.blocks.find(b => b.id === blockId);
            if (!block) return;

            setEditSession({
                blockId,
                originalContent: block.content,
                currentContent: block.content,
                isActive: true,
                startTime: new Date()
            });
        },
        updateBlockContent,
        saveDocument,
        cancelEdit: () => setEditSession(null)
    };
};
```

## Architecture Diagrams

### System Architecture

```mermaid
graph TB
    subgraph "Frontend (React/TypeScript)"
        A[Document Loader] --> B[Markdown Parser]
        B --> C[TOC Generator]
        B --> D[Block Parser]
        C --> E[TOC Navigation]
        D --> F[Block Editor]
        F --> G[Change Cache]
        G --> H[Save Manager]
        E --> I[Scroll Navigation]
        F --> I
    end

    subgraph "Backend (Python/Flask)"
        J[Document API]
        K[File Storage]
        L[Version Control]
    end

    A --> |GET /documents/{id}| J
    H --> |PUT /documents/{id}| J
    J --> K
    J --> L

    style A fill:#e3f2fd
    style F fill:#fce4ec
    style G fill:#f1f8e9
    style H fill:#fff3e0
```

### Data Flow Sequence

```mermaid
sequenceDiagram
    participant U as User
    participant TOC as TOC Panel
    participant E as Block Editor
    participant C as Change Cache
    participant API as Backend API

    Note over U,API: Document Loading
    U->>TOC: Open document
    TOC->>API: GET /documents/{id}
    API-->>TOC: Full markdown content
    TOC->>TOC: Parse markdown to blocks & TOC
    TOC->>U: Display TOC + document

    Note over U,API: Block Editing
    U->>TOC: Click TOC item
    TOC->>E: Navigate to block
    U->>E: Click edit
    E->>E: Open inline editor
    U->>E: Edit content
    E->>C: Cache changes (debounced)
    
    Note over U,API: Saving
    U->>E: Trigger save
    E->>C: Rebuild full markdown
    C->>API: PUT /documents/{id} (full content)
    API-->>C: Success + new version
    C->>E: Update state
```

## API Design (Simplified)

### REST Endpoints

```python
# Minimal API endpoints in existing tech_spec.py or new markdown.py

@app.route("/api/documents/<document_id>", methods=["GET"])
def get_document(document_id: str):
    """Get full markdown document"""
    return {
        "id": document_id,
        "title": "Document Title",
        "content": "# Full markdown content...",
        "version": 1,
        "updated_at": "2024-01-01T00:00:00Z"
    }

@app.route("/api/documents/<document_id>", methods=["PUT"])
def update_document(document_id: str):
    """Update full markdown document"""
    data = request.json
    # Save full content, increment version
    return {
        "id": document_id,
        "version": 2,
        "updated_at": "2024-01-01T00:00:00Z"
    }
```

## Implementation Plan

### Phase 1: Core Frontend Components (Week 1-2)
- [ ] TOC generator service using marked.js
- [ ] Block parser service for content identification
- [ ] Document state management hook
- [ ] Basic TOC navigation component

### Phase 2: Editing Interface (Week 2-3)
- [ ] Inline block editor with TipTap
- [ ] Click-to-edit functionality
- [ ] Change caching and state management
- [ ] Auto-save with debouncing

### Phase 3: Backend Integration (Week 3-4)
- [ ] Simple document API endpoints
- [ ] Version conflict detection
- [ ] Save/load functionality
- [ ] Error handling and user feedback

### Phase 4: Polish & Optimization (Week 4-5)
- [ ] Performance optimization for large documents
- [ ] Virtual scrolling for 300+ pages
- [ ] Accessibility improvements
- [ ] Comprehensive testing

## Library Integration Example

Here's a complete example showing how the libraries work together:

```typescript
// Example usage demonstrating the full workflow
import { TOCGeneratorService } from './services/TOCGeneratorService';
import { BlockParserService } from './services/BlockParserService';
import { MarkdownRebuilderService } from './services/MarkdownRebuilderService';

const exampleMarkdown = `
# Chapter 1: Introduction

This is the introduction paragraph.

## 1.1 Overview

Some overview content here.

### 1.1.1 Details

Detailed information.

## 1.2 Getting Started

Getting started content.

# Chapter 2: Advanced Topics

Advanced content here.
`;

// Initialize services
const tocGenerator = new TOCGeneratorService();
const blockParser = new BlockParserService();
const markdownRebuilder = new MarkdownRebuilderService();

// Parse markdown into blocks
const blocks = blockParser.parseMarkdownToBlocks(exampleMarkdown);
console.log('Parsed blocks:', blocks.length);

// Generate TOC
const toc = tocGenerator.generateTOC(exampleMarkdown);
console.log('TOC structure:', toc);

// Simulate editing a block
const updatedBlocks = blocks.map(block => {
    if (block.type === 'heading' && block.level === 1) {
        return {
            ...block,
            content: '# Chapter 1: Updated Introduction',
            isModified: true
        };
    }
    return block;
});

// Rebuild markdown from updated blocks
const newMarkdown = markdownRebuilder.rebuildMarkdownFromBlocks(updatedBlocks);
console.log('Rebuilt markdown:', newMarkdown);

// Validate the result
const validation = markdownRebuilder.validateMarkdownStructure(newMarkdown);
console.log('Validation result:', validation);
```

## Benefits of Using Proven Libraries

1. **Reliability**: Battle-tested libraries used by thousands of projects
2. **Standards Compliance**: Proper CommonMark and GFM support
3. **Performance**: Optimized AST parsing and manipulation
4. **Maintainability**: Regular updates and community support
5. **Extensibility**: Rich plugin ecosystem for additional features
6. **Consistency**: Standardized AST format (mdast) across tools

## Technical Considerations

### Performance for Large Documents
- Use virtual scrolling for 300+ page documents
- Implement lazy loading for TOC sections
- Debounce parsing operations
- Cache parsed results in browser storage

### Change Management
- Track modifications at block level
- Implement undo/redo functionality
- Auto-save with configurable intervals
- Conflict detection on save

### Browser Compatibility
- Leverage modern browser APIs for performance
- Fallback strategies for older browsers
- Progressive enhancement approach

## Component Implementation Details

### 1. TOC Navigation Component

```typescript
// src/components/markdown/TOCNavigationPanel.tsx
interface TOCNavigationProps {
    tableOfContents: TOCItem[];
    activeBlockId: string | null;
    onNavigateToBlock: (blockId: string) => void;
    searchQuery?: string;
}

export const TOCNavigationPanel: React.FC<TOCNavigationProps> = ({
    tableOfContents,
    activeBlockId,
    onNavigateToBlock,
    searchQuery = ''
}) => {
    const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
    const [filteredTOC, setFilteredTOC] = useState(tableOfContents);

    // Filter TOC based on search query
    useEffect(() => {
        if (!searchQuery) {
            setFilteredTOC(tableOfContents);
            return;
        }

        const filterItems = (items: TOCItem[]): TOCItem[] => {
            return items.reduce((acc, item) => {
                const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase());
                const filteredChildren = filterItems(item.children);

                if (matchesSearch || filteredChildren.length > 0) {
                    acc.push({
                        ...item,
                        children: filteredChildren
                    });
                }
                return acc;
            }, [] as TOCItem[]);
        };

        setFilteredTOC(filterItems(tableOfContents));
    }, [tableOfContents, searchQuery]);

    const toggleExpanded = (itemId: string) => {
        setExpandedItems(prev => {
            const newSet = new Set(prev);
            if (newSet.has(itemId)) {
                newSet.delete(itemId);
            } else {
                newSet.add(itemId);
            }
            return newSet;
        });
    };

    return (
        <div className="toc-navigation-panel">
            <div className="toc-header">
                <h3>Table of Contents</h3>
                <input
                    type="text"
                    placeholder="Search sections..."
                    className="toc-search"
                    defaultValue={searchQuery}
                />
            </div>

            <div className="toc-tree">
                {filteredTOC.map(item => (
                    <TOCTreeItem
                        key={item.id}
                        item={item}
                        activeBlockId={activeBlockId}
                        expandedItems={expandedItems}
                        onToggleExpanded={toggleExpanded}
                        onNavigate={onNavigateToBlock}
                        level={0}
                    />
                ))}
            </div>
        </div>
    );
};

const TOCTreeItem: React.FC<{
    item: TOCItem;
    activeBlockId: string | null;
    expandedItems: Set<string>;
    onToggleExpanded: (id: string) => void;
    onNavigate: (blockId: string) => void;
    level: number;
}> = ({ item, activeBlockId, expandedItems, onToggleExpanded, onNavigate, level }) => {
    const isActive = activeBlockId === item.blockId;
    const isExpanded = expandedItems.has(item.id);
    const hasChildren = item.children.length > 0;

    return (
        <div className={`toc-item level-${level}`}>
            <div
                className={`toc-item-content ${isActive ? 'active' : ''}`}
                onClick={() => onNavigate(item.blockId)}
            >
                {hasChildren && (
                    <button
                        className={`expand-button ${isExpanded ? 'expanded' : ''}`}
                        onClick={(e) => {
                            e.stopPropagation();
                            onToggleExpanded(item.id);
                        }}
                    >
                        ▶
                    </button>
                )}
                <span className={`heading-level-${item.level}`}>
                    {item.title}
                </span>
            </div>

            {hasChildren && isExpanded && (
                <div className="toc-children">
                    {item.children.map(child => (
                        <TOCTreeItem
                            key={child.id}
                            item={child}
                            activeBlockId={activeBlockId}
                            expandedItems={expandedItems}
                            onToggleExpanded={onToggleExpanded}
                            onNavigate={onNavigate}
                            level={level + 1}
                        />
                    ))}
                </div>
            )}
        </div>
    );
};
```

### 2. Document Viewer with Click-to-Edit

```typescript
// src/components/markdown/MarkdownDocumentViewer.tsx
interface DocumentViewerProps {
    documentState: DocumentState;
    editingBlockId: string | null;
    onStartEdit: (blockId: string) => void;
    onUpdateBlock: (blockId: string, content: string) => void;
    onCancelEdit: () => void;
}

export const MarkdownDocumentViewer: React.FC<DocumentViewerProps> = ({
    documentState,
    editingBlockId,
    onStartEdit,
    onUpdateBlock,
    onCancelEdit
}) => {
    const containerRef = useRef<HTMLDivElement>(null);

    const scrollToBlock = (blockId: string) => {
        const blockElement = containerRef.current?.querySelector(`[data-block-id="${blockId}"]`);
        if (blockElement) {
            blockElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        }
    };

    return (
        <div ref={containerRef} className="document-viewer">
            {documentState.blocks.map(block => (
                <DocumentBlock
                    key={block.id}
                    block={block}
                    isEditing={editingBlockId === block.id}
                    onStartEdit={() => onStartEdit(block.id)}
                    onUpdateContent={(content) => onUpdateBlock(block.id, content)}
                    onCancelEdit={onCancelEdit}
                />
            ))}
        </div>
    );
};

const DocumentBlock: React.FC<{
    block: MarkdownBlock;
    isEditing: boolean;
    onStartEdit: () => void;
    onUpdateContent: (content: string) => void;
    onCancelEdit: () => void;
}> = ({ block, isEditing, onStartEdit, onUpdateContent, onCancelEdit }) => {
    const [editContent, setEditContent] = useState(block.content);

    useEffect(() => {
        setEditContent(block.content);
    }, [block.content]);

    const handleSave = () => {
        onUpdateContent(editContent);
    };

    const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
            onCancelEdit();
        } else if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
            handleSave();
        }
    };

    if (isEditing) {
        return (
            <div
                className="document-block editing"
                data-block-id={block.id}
                onKeyDown={handleKeyDown}
            >
                <div className="edit-toolbar">
                    <span className="block-type">{block.type}</span>
                    <div className="edit-actions">
                        <button onClick={handleSave} className="save-btn">
                            Save (Ctrl+Enter)
                        </button>
                        <button onClick={onCancelEdit} className="cancel-btn">
                            Cancel (Esc)
                        </button>
                    </div>
                </div>

                <TiptapEditor
                    markdown={editContent}
                    onChange={setEditContent}
                    editable={true}
                    className="block-editor"
                />
            </div>
        );
    }

    return (
        <div
            className={`document-block ${block.isModified ? 'modified' : ''}`}
            data-block-id={block.id}
            onClick={onStartEdit}
        >
            <div className="block-content">
                <MarkdownViewer markdown={block.content} />
            </div>
            <div className="edit-overlay">
                <button className="edit-button">
                    ✏️ Edit
                </button>
            </div>
        </div>
    );
};
```

### 3. Main Document Container

```typescript
// src/components/markdown/MarkdownDocumentContainer.tsx
export const MarkdownDocumentContainer: React.FC<{
    documentId: string;
}> = ({ documentId }) => {
    const {
        documentState,
        editSession,
        isLoading,
        loadDocument,
        startEditingBlock,
        updateBlockContent,
        saveDocument,
        cancelEdit
    } = useMarkdownDocument(documentId);

    const [selectedBlockId, setSelectedBlockId] = useState<string | null>(null);
    const [searchQuery, setSearchQuery] = useState('');

    useEffect(() => {
        loadDocument();
    }, [documentId]);

    const handleTOCNavigation = (blockId: string) => {
        setSelectedBlockId(blockId);
        // Scroll to block will be handled by the DocumentViewer
    };

    const handleSaveDocument = async () => {
        await saveDocument();
        // Show success notification
    };

    if (isLoading || !documentState) {
        return (
            <div className="loading-container">
                <div className="loading-spinner" />
                <p>Loading document...</p>
            </div>
        );
    }

    return (
        <div className="markdown-document-container">
            {/* Header with save controls */}
            <div className="document-header">
                <h1>{documentState.title}</h1>
                <div className="document-controls">
                    {documentState.hasUnsavedChanges && (
                        <span className="unsaved-indicator">
                            Unsaved changes
                        </span>
                    )}
                    <button
                        onClick={handleSaveDocument}
                        disabled={!documentState.hasUnsavedChanges}
                        className="save-document-btn"
                    >
                        Save Document
                    </button>
                </div>
            </div>

            <div className="document-layout">
                {/* TOC Sidebar */}
                <div className="toc-sidebar">
                    <TOCNavigationPanel
                        tableOfContents={documentState.tableOfContents}
                        activeBlockId={selectedBlockId}
                        onNavigateToBlock={handleTOCNavigation}
                        searchQuery={searchQuery}
                    />
                </div>

                {/* Main Content Area */}
                <div className="document-content">
                    <MarkdownDocumentViewer
                        documentState={documentState}
                        editingBlockId={editSession?.blockId || null}
                        onStartEdit={startEditingBlock}
                        onUpdateBlock={updateBlockContent}
                        onCancelEdit={cancelEdit}
                    />
                </div>
            </div>
        </div>
    );
};
```

## Styling and UX Considerations

### CSS Structure

```css
/* src/components/markdown/MarkdownEditor.css */
.markdown-document-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: #fafafa;
}

.document-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: white;
    border-bottom: 1px solid #e0e0e0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.document-layout {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.toc-sidebar {
    width: 300px;
    background: white;
    border-right: 1px solid #e0e0e0;
    overflow-y: auto;
    padding: 1rem;
}

.document-content {
    flex: 1;
    overflow-y: auto;
    padding: 2rem;
    background: white;
    margin: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* TOC Styles */
.toc-navigation-panel {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.toc-search {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 1rem;
}

.toc-item {
    margin: 0.25rem 0;
}

.toc-item-content {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.toc-item-content:hover {
    background-color: #f5f5f5;
}

.toc-item-content.active {
    background-color: #e3f2fd;
    color: #1976d2;
    font-weight: 500;
}

/* Block Editor Styles */
.document-block {
    position: relative;
    margin: 1rem 0;
    padding: 1rem;
    border: 2px solid transparent;
    border-radius: 4px;
    transition: border-color 0.2s;
}

.document-block:hover {
    border-color: #e0e0e0;
}

.document-block.editing {
    border-color: #1976d2;
    background-color: #fafafa;
}

.document-block.modified {
    border-left: 4px solid #ff9800;
}

.edit-overlay {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    opacity: 0;
    transition: opacity 0.2s;
}

.document-block:hover .edit-overlay {
    opacity: 1;
}

.edit-button {
    background: #1976d2;
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .document-layout {
        flex-direction: column;
    }

    .toc-sidebar {
        width: 100%;
        height: 200px;
        border-right: none;
        border-bottom: 1px solid #e0e0e0;
    }
}
```

## Performance Optimizations

### Virtual Scrolling for Large Documents

```typescript
// src/components/markdown/VirtualizedDocumentViewer.tsx
import { FixedSizeList as List } from 'react-window';

export const VirtualizedDocumentViewer: React.FC<{
    blocks: MarkdownBlock[];
    itemHeight: number;
}> = ({ blocks, itemHeight = 200 }) => {
    const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => (
        <div style={style}>
            <DocumentBlock
                block={blocks[index]}
                // ... other props
            />
        </div>
    );

    return (
        <List
            height={600}
            itemCount={blocks.length}
            itemSize={itemHeight}
            overscanCount={5}
        >
            {Row}
        </List>
    );
};
```

### Debounced Auto-save

```typescript
// src/hooks/useAutoSave.ts
export const useAutoSave = (
    documentState: DocumentState,
    saveFunction: () => Promise<void>,
    delay: number = 30000 // 30 seconds
) => {
    const debouncedSave = useMemo(
        () => debounce(saveFunction, delay),
        [saveFunction, delay]
    );

    useEffect(() => {
        if (documentState.hasUnsavedChanges) {
            debouncedSave();
        }
    }, [documentState.hasUnsavedChanges, debouncedSave]);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            debouncedSave.cancel();
        };
    }, [debouncedSave]);
};
```

## Testing Strategy

### Unit Tests

```typescript
// src/components/markdown/__tests__/TOCGenerator.test.ts
describe('TOCGeneratorService', () => {
    const service = new TOCGeneratorService();

    test('generates correct TOC structure', () => {
        const markdown = `
# Chapter 1
## Section 1.1
### Subsection 1.1.1
## Section 1.2
# Chapter 2
        `;

        const toc = service.generateTOC(markdown);

        expect(toc).toHaveLength(2);
        expect(toc[0].title).toBe('Chapter 1');
        expect(toc[0].children).toHaveLength(2);
        expect(toc[0].children[0].children).toHaveLength(1);
    });

    test('handles empty markdown', () => {
        const toc = service.generateTOC('');
        expect(toc).toHaveLength(0);
    });
});
```

### Integration Tests

```typescript
// src/components/markdown/__tests__/MarkdownEditor.integration.test.tsx
describe('Markdown Editor Integration', () => {
    test('full editing workflow', async () => {
        const { getByText, getByRole } = render(
            <MarkdownDocumentContainer documentId="test-doc" />
        );

        // Wait for document to load
        await waitFor(() => {
            expect(getByText('Chapter 1')).toBeInTheDocument();
        });

        // Click TOC item
        fireEvent.click(getByText('Chapter 1'));

        // Start editing
        fireEvent.click(getByRole('button', { name: /edit/i }));

        // Edit content
        const editor = getByRole('textbox');
        fireEvent.change(editor, { target: { value: '# Updated Chapter 1' } });

        // Save
        fireEvent.click(getByRole('button', { name: /save/i }));

        // Verify changes
        expect(getByText('Updated Chapter 1')).toBeInTheDocument();
    });
});
```

## Conclusion

This frontend-centric approach provides a simpler, more maintainable solution for editing large markdown documents with TOC navigation. By leveraging the frontend for parsing, caching, and change management, we can deliver a responsive user experience while keeping the backend simple and focused on document persistence.

The design emphasizes:
- **Simplicity**: Minimal backend changes, leveraging existing infrastructure
- **Performance**: Frontend parsing and virtual scrolling for large documents
- **User Experience**: Intuitive TOC navigation and inline editing
- **Maintainability**: Clean separation of concerns and testable components
- **Scalability**: Efficient handling of 300+ page documents
```
