from typing import Dict

from pydantic import BaseModel, Field
from langchain_core.tools import tool
from langchain_core.runnables import RunnableConfig

from blitzy_utils.logger import logger


class TechSpecSectionsInput(BaseModel):
    section_heading: str = Field(description="Listed Heading of the technical specification section to retrieve")
    config: RunnableConfig


@tool(args_schema=TechSpecSectionsInput)
def get_tech_spec_section(
    section_heading: str,
    config: RunnableConfig
) -> str:
    """
    Retrieves the full content of the specified section heading from the technical specification.
    """
    logger.info(f'received request to retrieve contents for heading: {section_heading}')
    tech_spec_parsed: Dict[str, str] = config["configurable"]["tech_spec_parsed"]
    potential_headings = list(tech_spec_parsed.keys())
    if section_heading not in potential_headings:
        return "Invalid section_heading, the contents of this section are already provided to you."

    ret = {
        "section_heading": section_heading,
        "section_contents": tech_spec_parsed.get(section_heading, "")
    }

    return ret
