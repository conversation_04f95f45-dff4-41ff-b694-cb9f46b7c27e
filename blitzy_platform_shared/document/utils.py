import re
from typing import Dict, Optional

from blitzy_utils.logger import logger
from blitzy_utils.enums import <PERSON>propCommand

from .consts import DOCUMENT_REPLACEMENTS, POTENTIAL_DOCUMENT_TITLES
from .prompts import TECHNICAL_SECTION_PROMPTS, DEFAULT_SUMMARY_PROMPT, BUG_FIX_SUMMARY_PROMPT, \
    SECURITY_VULNERABILITY_FIX_PROMPT, TESTING_SUMMARY_PROMPT, DOCUMENTATION_SUMMARY_PROMPT


def parse_major_tech_spec_sections(text: str) -> Dict[str, str]:
    """
    Parse markdown text and create a dictionary of major sections.
    Preserves code blocks in content but prevents them from being interpreted as headers.

    Args:
        text: Markdown formatted text string

    Returns:
        Dictionary mapping section headings to their content
    """
    heading_level = determine_heading_level(text)
    return parse_sections_at_heading_level(text, heading_level)


def determine_heading_level(text: str) -> Optional[int]:
    """
    Determine the heading level to use for major sections by finding the first heading 
    after "# Technical Specification".

    Args:
        text: Markdown formatted text string

    Returns:
        Integer representing the heading level (number of # characters)
        Returns None if no appropriate heading level is found
    """
    lines = text.split('\n')
    in_code_block = False

    # Find the first heading after "# Technical Specification"
    for line in lines:
        # Track code blocks to avoid interpreting their content as headers
        if '```' in line.strip():
            in_code_block = not in_code_block
            continue

        if in_code_block:
            continue

        if line.strip() == '# Technical Specification':
            continue

        if line.strip().startswith('#') and line.strip() != '# Technical Specification':
            # Count the number of # to determine heading level
            heading_level = len(line.split()[0])
            return heading_level

    return None


def parse_sections_at_heading_level(text: str, heading_level: Optional[int]) -> Dict[str, str]:
    """
    Parse markdown text and create a dictionary of sections at the specified heading level.
    Preserves code blocks in content but prevents them from being interpreted as headers.

    Args:
        text: Markdown formatted text string
        heading_level: Level of headings to use as section markers

    Returns:
        Dictionary mapping section headings to their content
    """
    if heading_level is None:
        return {}

    lines = text.split('\n')
    sections = {}
    current_section = None
    current_content = []
    in_code_block = False

    for line in lines:
        if line.strip() == '# Technical Specification':
            continue

        # Track code blocks but only for header detection
        if '```' in line.strip():
            in_code_block = not in_code_block
            if current_section:
                current_content.append(line)
            continue

        # Check if line is a heading at the specified level, but only if not in code block
        if not in_code_block and line.strip().startswith('#' * heading_level + ' '):
            # If we have a previous section, save it
            if current_section:
                sections[current_section] = '\n'.join(current_content).strip()

            # Start new section
            current_section = line.strip()[heading_level:].strip()
            current_content = []
        else:
            if current_section:
                current_content.append(line)

    # Add the last section
    if current_section:
        sections[current_section] = '\n'.join(current_content).strip()

    return sections


def get_tech_spec_addendum(job_type=BackpropCommand.CUSTOM.value) -> Dict[str, str]:
    summary_prompt = DEFAULT_SUMMARY_PROMPT
    if job_type == BackpropCommand.FIX_BUGS.value:
        summary_prompt = BUG_FIX_SUMMARY_PROMPT
    elif job_type == BackpropCommand.FIX_CVES.value:
        summary_prompt = SECURITY_VULNERABILITY_FIX_PROMPT
    elif job_type == BackpropCommand.ADD_TESTING.value:
        summary_prompt = TESTING_SUMMARY_PROMPT
    elif job_type == BackpropCommand.DOCUMENT_CODE.value:
        summary_prompt = DOCUMENTATION_SUMMARY_PROMPT
    return {
        "0. SUMMARY OF CHANGES": summary_prompt
    }


def format_document_heading(sub_heading: str) -> str:
    """
    Format headings based on numbering pattern:

    Args:
        sub_heading (str): The heading text to format

    Returns:
        str: Formatted heading with appropriate markdown
    """
    # Split the heading to check if it starts with a number pattern
    parts = sub_heading.split(maxsplit=1)
    if not parts:
        return "#### "

    first_part = parts[0]

    # Check if the first part is a numbering pattern
    if first_part and (first_part.count('.') > 0 or "appendices" in first_part.lower()):
        # Count the dots to determine heading level
        dot_count = first_part.count('.')

        if first_part[-1] != '.':
            return f"{'#' * (dot_count + 1)} {sub_heading}"
        else:
            valid_headings = list(TECHNICAL_SECTION_PROMPTS.keys())
            valid_headings.extend(list(get_tech_spec_addendum().keys()))
            valid_headings.extend(POTENTIAL_DOCUMENT_TITLES)
            is_valid_l1_heading = False
            for heading in valid_headings:
                if sub_heading.lower() == heading.lower():
                    is_valid_l1_heading = True
                    break

            if is_valid_l1_heading:
                return f"# {sub_heading}"
            else:
                return f"##### {sub_heading}"

    # For all other cases (no numbering), use quadruple hash
    return f"#### {sub_heading}"


def get_first_n_headings(text: str, n: int, heading_level=None, zero_indexed=False) -> tuple[str, str]:
    """
    Creates a formatted document with the first n headings and their content,
    and returns the rest of the document separately.

    Args:
        text (str): The markdown document text
        n (int): Number of headings to include

    Returns:
        tuple[str, str]: A tuple containing:
            - First element: Formatted document with the first n sections
            - Second element: The remaining text with sections after the first n
    """
    if not heading_level:
        # First, determine the heading level
        heading_level = determine_heading_level(text)
        if heading_level is None:
            return "", text

    if zero_indexed and n >= 1:
        n = n-1

    # Parse all the sections
    sections = parse_sections_at_heading_level(text, heading_level)

    # Get the first n sections and the rest
    section_items = list(sections.items())
    first_n_sections = dict(section_items[:min(n, len(section_items))])
    rest_sections = dict(section_items[min(n, len(section_items)):])

    # Format the first n sections as a document
    first_n_result = ""
    for heading, content in first_n_sections.items():
        # Add back the heading markers
        formatted_heading = '#' * heading_level + ' ' + heading
        # Format as "heading\n\ncontent\n\n"
        first_n_result += f"{formatted_heading}\n\n{content}\n\n"

    # Format the rest of the sections as a document
    rest_result = ""
    for heading, content in rest_sections.items():
        # Add back the heading markers
        formatted_heading = '#' * heading_level + ' ' + heading
        # Format as "heading\n\ncontent\n\n"
        rest_result += f"{formatted_heading}\n\n{content}\n\n"

    # Remove trailing newlines if any
    return first_n_result.rstrip("\n"), rest_result.rstrip("\n")


def get_last_n_headings(text: str, n: int, heading_level=None) -> tuple[str, str]:
    """
    Creates a formatted document with the last n headings and their content,
    and returns the rest of the document separately.

    Args:
        text (str): The markdown document text
        n (int): Number of headings to include from the end

    Returns:
        tuple[str, str]: A tuple containing:
            - First element: Formatted document with sections before the last n
            - Second element: Formatted document with the last n sections
    """
    if not heading_level:
        # First, determine the heading level
        heading_level = determine_heading_level(text)
        if heading_level is None:
            return "", text

    # Parse all the sections
    sections = parse_sections_at_heading_level(text, heading_level)

    # Get all sections except the last n, and the last n sections
    section_items = list(sections.items())
    first_sections = dict(section_items[:-min(n, len(section_items))] if n < len(section_items) else {})
    last_n_sections = dict(section_items[-min(n, len(section_items)):])

    # Format the first sections as a document
    first_result = ""
    for heading, content in first_sections.items():
        # Add back the heading markers
        formatted_heading = '#' * heading_level + ' ' + heading
        # Format as "heading\n\ncontent\n\n"
        first_result += f"{formatted_heading}\n\n{content}\n\n"

    # Format the last n sections as a document
    last_n_result = ""
    for heading, content in last_n_sections.items():
        # Add back the heading markers
        formatted_heading = '#' * heading_level + ' ' + heading
        # Format as "heading\n\ncontent\n\n"
        last_n_result += f"{formatted_heading}\n\n{content}\n\n"

    # Remove trailing newlines if any
    return first_result.rstrip("\n"), last_n_result.rstrip("\n")


def clean_document(text: str) -> str:
    """
    Removes duplicate headings that appear consecutively with only blank lines in between.
    Also validates and corrects heading levels to ensure they follow the rule:
    number of hashes = number of dots in the numbering pattern + 1.

    Also removes highlighting around code snippets.
    Fixes unterminated code blocks that are followed by headings.

    Args:
        text (str): The markdown document text

    Returns:
        str: Document with duplicate headings removed and heading levels validated
    """
    # First, fix unterminated code blocks followed by headings
    # text = fix_unterminated_code_blocks(text)

    lines = text.split('\n\n')
    result = []
    i = 0

    while i < len(lines):
        current_line = lines[i]

        # Check if the current line is a heading
        if current_line.strip() and current_line.lstrip().startswith('#') and not current_line.lstrip().startswith('# Technical Specification'):
            split_current_lines = current_line.split('\n')
            j = 0
            while j < len(split_current_lines):
                split_line = split_current_lines[j]
                # Validate and fix heading level
                split_line = clean_line(line=split_line)
                split_line = validate_heading_format(split_line)
                split_current_lines[j] = split_line
                j += 1
            current_line = '\n'.join(split_current_lines)

            # Add this heading to the result
            result.append(current_line)

            # Check ahead for blank lines followed by the same heading
            j = i + 1
            blank_lines = []

            # Collect blank lines
            while j < len(lines) and not lines[j].strip():
                blank_lines.append(lines[j])
                j += 1

            # Is the next non-blank line the same heading?
            if j < len(lines) and (lines[j] == current_line or lines[j] == (current_line + " (updated)")):
                logger.info(f'Removing duplicate heading: {current_line}')
                i = j + 1
            else:
                # No duplicate. Add the blank lines and move i to j
                result.extend(blank_lines)
                i = j
        else:
            # Not a heading
            current_line = clean_line(line=current_line)
            result.append(current_line)
            i += 1

    return '\n\n'.join(result)


def clean_line(line: str):
    sublines = line.split('\n')
    cleaned_sublines = []

    for subline in sublines:
        for pattern, replacement in DOCUMENT_REPLACEMENTS.items():
            if re.search(pattern, subline):
                subline = re.sub(pattern, replacement, subline)
                logger.info(f'Applied regex replacement to get cleaned subline {subline}')

        cleaned_sublines.append(subline)

    return '\n'.join(cleaned_sublines)


def validate_heading_format(heading_line: str) -> str:
    """
    Validates and corrects a heading line to ensure the number of hash symbols
    is one more than the number of dots in the numbering pattern.

    Args:
        heading_line (str): The heading line to validate

    Returns:
        str: Corrected heading line
    """
    # Split the heading line into hash part and text part
    parts = heading_line.strip().split(' ', 1)

    # Extract the hash part
    hash_part = parts[0]

    # Ensure hash_part only contains hash symbols
    if not all(c == '#' for c in hash_part) or len(parts) != 2:
        # This is not a properly formatted heading
        return heading_line

    # Extract the heading text
    heading_text = parts[1] if len(parts) > 1 else ""

    # Use format_document_heading to get the correct format
    correct_format = format_document_heading(heading_text)

    # Extract just the hash part from the correct format
    correct_hash_parts = correct_format.split(' ', 1)
    correct_hash = correct_hash_parts[0]

    # If the heading has a different number of hashes than expected, log and fix it
    if hash_part != correct_hash:
        logger.info(f'Correcting heading level: "{heading_line}" -> "{correct_hash} {heading_text}"')
        return f"{correct_hash} {heading_text}"

    # If it's already correct, return as is
    return heading_line


def fix_unterminated_code_blocks(text: str) -> str:
    """
    Fixes unterminated code blocks that are followed by headings.
    Detects patterns where a code block starts with ```mermaid (or any language)
    but isn't closed before a heading appears.

    Args:
        text (str): The markdown text to fix

    Returns:
        str: Text with fixed code blocks
    """
    lines = text.splitlines()
    result = []
    in_code_block = False

    for i, line in enumerate(lines):
        # Track if we're in a code block
        if line.strip().startswith('```'):
            in_code_block = not in_code_block
            result.append(line)
            continue

        # Check if we have a heading while still in a code block
        if in_code_block:
            stripped_line = line.strip()
            # Terminate if line contains span with background-color style
            if '<span style="background-color: rgba(91, 57, 243, 0.2)">' in line:
                logger.info(f'Fixing unterminated code block before span style: {line}')
                result.append('```')  # Add terminator
                in_code_block = False
                if i > 0 and result[-1] != '':  # Add newline before the line if needed
                    result.append('')
                result.append(line)
            # For 1-3 hashes, heading must start with a number
            elif re.match(r'^#{1,3}\s+\d+', stripped_line):
                logger.info(f'Fixing unterminated code block before heading: {line}')
                result.append('```')  # Add terminator
                in_code_block = False
                if i > 0 and result[-1] != '':  # Add newline before heading if needed
                    result.append('')
                result.append(line)
            # For 4+ hashes, may or may not start with a number
            elif re.match(r'^#{4,}\s+\S', stripped_line):
                logger.info(f'Fixing unterminated code block before heading: {line}')
                result.append('```')  # Add terminator
                in_code_block = False
                if i > 0 and result[-1] != '':  # Add newline before heading if needed
                    result.append('')
                result.append(line)
            # This is likely a code comment, not a heading
            else:
                result.append(line)
        else:
            result.append(line)

    # Also handle case where the document ends with an unterminated code block
    if in_code_block:
        logger.info('Fixing unterminated code block at end of document')
        result.append('```')

    return '\n'.join(result)
