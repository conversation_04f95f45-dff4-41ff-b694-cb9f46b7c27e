DOCUMENT_REPLACEMENTS = {
    r'<span style="background-color: rgba\(91, 57, 243, 0\.2\)">(?:\s*\n\s*)?```': r'```',
    r'```(?:\s*\n\s*)?</span>': r'```',
    r'```\s*\n\s*```': r'```',
    r'^<span style="background-color: rgba\(91, 57, 243, 0\.2\)">(.*\|.*)': r'\1',
    r'(.*\|.*)</span>\s*$': r'\1',
    r'^(?!.*<span style="background-color: rgba\(91, 57, 243, 0\.2\)">)(\s*- .+)</span>\s*$': r'\1',
    r'<span style="background-color: rgba\(91, 57, 243, 0\.2\)">\s*-\s+': r'- ',
    r'^<span style="background-color: rgba\(91, 57, 243, 0.2\)">(#{2,6}\s+)': r'\1',  # Span starting a heading
    r'(^#{2,6}\s+)<span style="background-color: rgba\(91, 57, 243, 0\.2\)">': r'\1',  # Span starting after a heading
    r'(^#{2,6}\s+\d+\.\s*)<span style="background-color: rgba\(91, 57, 243, 0\.2\)">': r'\1',
    r'(^#{2,6}\s+.*?)</span>': r'\1',
    r'`+markdown': r'',
    r'(^#{1,6}\s+[^\\]*)\\([^\\]*)$': r'\1\2',  # Single backslash in heading
    r'(^#{1,6}\s+[^\\]*)\\\\([^\\]*)$': r'\1\2',  # Double backslash in heading
    r'(^#{1,6}\s+.*?)\\(.*?)\\(.*)$': r'\1\2\3'  # Multiple backslashes
}

DIAGRAM_RETRIES = 5

POTENTIAL_DOCUMENT_TITLES = ["TECHNICAL SPECIFICATION", "TECHNICAL SPECIFICATIONS"]
