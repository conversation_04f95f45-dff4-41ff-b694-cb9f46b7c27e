DEFAULT_TECHNOLOGIES = """
    Default Technology Stack:

    Core Infrastructure:
    - Cloud Platform: AWS
    - Containerization: Docker
    - Infrastructure as Code: Terraform
    - CI/CD: GitHub Actions

    Backend:
    - Primary Language: Python
    - Framework: Flask
    - Authentication: Auth0
    - Database: MongoDB
    - AI Framework: Langchain

    Frontend:
    - Web: React with TypeScript
    - CSS Framework: TailwindCSS
    - Mobile/Cross-platform: React-Native with TypeScript

    Native Applications:
    - iOS: Swift
    - Android: Kotlin
    - MacOS: Objective-C
    - Desktop: ElectronJS
"""

TECHNICAL_SECTION_PROMPTS = {
    "1. INTRODUCTION": """
        Generate an INTRODUCTION section for the Technical Specification document that provides stakeholders with a clear understanding of the system's purpose, scope, and context.

        EXECUTIVE SUMMARY
            - Brief overview of the project
            - Core business problem being solved
            - Key stakeholders and users
            - Expected business impact and value proposition

        SYSTEM OVERVIEW
            - Project Context
            * Business context and market positioning
            * Current system limitations (if replacing/upgrading existing system)
            * Integration with existing enterprise landscape

            - High-Level Description
            * Primary system capabilities
            * Major system components
            * Core technical approach

            - Success Criteria
            * Measurable objectives
            * Critical success factors
            * Key performance indicators (KPIs)

        SCOPE
            Clearly define both in-scope and out-of-scope elements:

            In-Scope:
            - Core Features and Functionalities
            * Must-have capabilities
            * Primary user workflows
            * Essential integrations
            * Key technical requirements

            - Implementation Boundaries
            * System boundaries
            * User groups covered
            * Geographic/market coverage
            * Data domains included

            Out-of-Scope:
            - Explicitly list excluded features/capabilities
            - Future phase considerations
            - Integration points not covered
            - Unsupported use cases

        Output Format Requirements:
        - Use Markdown tables for structured data, text for unstructured data.
        - Tables should never have more than four columns.
    """,
    "2. PRODUCT REQUIREMENTS": """
        Generate a detailed PRODUCT REQUIREMENTS section that breaks down the product into discrete, testable features:

        Only include sections and items that are actually relevant to this system, based on your analysis of its requirements.
        Don't add any features of your own, or any items that aren't clearly applicable.

        FEATURE CATALOG
            For each feature, document:
            
            - Feature Metadata
                * Unique ID (format: F-XXX)
                * Feature Name
                * Feature Category
                * Priority Level (Critical/High/Medium/Low)
                * Status (Proposed/Approved/In Development/Completed)
                
            - Description
                * Overview
                * Business Value
                * User Benefits
                * Technical Context
                
            - Dependencies
                * Prerequisite Features
                * System Dependencies
                * External Dependencies
                * Integration Requirements

        FUNCTIONAL REQUIREMENTS TABLE
            For each feature, create a requirements table with:
            
            - Requirement Details
                * Requirement ID (format: F-XXX-RQ-YYY)
                * Description
                * Acceptance Criteria
                * Priority (Must-Have/Should-Have/Could-Have)
                * Complexity (High/Medium/Low)
                
            - Technical Specifications
                * Input Parameters
                * Output/Response
                * Performance Criteria
                * Data Requirements
                
            - Validation Rules
                * Business Rules
                * Data Validation
                * Security Requirements
                * Compliance Requirements

        FEATURE RELATIONSHIPS
            Document:
            - Feature dependencies map
            - Integration points
            - Shared components
            - Common services

            Only document feature relationships that are clearly evident in the requirements or source code.
            Don't imagine any feature relationships of your own.

        IMPLEMENTATION CONSIDERATIONS
            For each feature:
            - Technical constraints
            - Performance requirements
            - Scalability considerations
            - Security implications
            - Maintenance requirements

        Output Format:
        - Use markdown tables for structured data with not more than 4 columns per table
        - Include traceability matrix
        - Reference related process flowcharts
        - Link to relevant technical specifications

        Notes:
        - Maintain consistent ID formatting
        - Ensure requirements are testable
        - Include acceptance criteria
        - Reference related documents
        - Track requirement versions
        - Document assumptions and constraints
    """,
    "3. TECHNOLOGY STACK": f"""
        Generate the TECHNOLOGY STACK section adhering to existing architectural decisions and technical constraints based on existing sections of the document.
        Include and justify choices for each applicable subsection:

        Only include sections and items that are actually relevant to this system, based on your analysis of its requirements.
        Don't add any items that aren't clearly applicable.

        PROGRAMMING LANGUAGES
            - List languages by platform/component
            - Justify selection criteria
            - Note any constraints or dependencies

        FRAMEWORKS & LIBRARIES
            - Core frameworks with versions
            - Supporting libraries
            - Compatibility requirements
            - Justification for each major choice

        OPEN SOURCE DEPENDENCIES
            - Third-party / open-source libraries identified
            - Package dependencies, registries and versions

        THIRD-PARTY SERVICES
            - External APIs and integrations
            - Authentication services
            - Monitoring tools
            - Cloud services

        DATABASES & STORAGE
            - Primary and secondary databases
            - Data persistence strategies
            - Caching solutions
            - Storage services

        DEVELOPMENT & DEPLOYMENT
            - Development tools
            - Build system
            - Containerization
            - CI/CD requirements

        Default Technology Stack: {DEFAULT_TECHNOLOGIES}

        Notes:
        - Include version numbers for all components
        - Consider security implications of choices
        - Document integration requirements between components
        - Validate your diagrams to ensure that they compile correctly.
    """,
    "4. PROCESS FLOWCHART": """
        Generate a comprehensive PROCESS FLOWCHART section for the Technical Specification document that includes:

        SYSTEM WORKFLOWS
            - Core Business Processes
                * End-to-end user journeys
                * System interactions
                * Decision points
                * Error handling paths
                
            - Integration Workflows
                * Data flow between systems
                * API interactions
                * Event processing flows
                * Batch processing sequences

        FLOWCHART REQUIREMENTS
            - For each major workflow, include:
                * Start and end points
                * Process steps
                * Decision diamonds
                * System boundaries
                * User touchpoints
                * Error states and recovery paths
                * Timing and SLA considerations
                
            - Validation Rules
                * Business rules at each step
                * Data validation requirements
                * Authorization checkpoints
                * Regulatory compliance checks

        TECHNICAL IMPLEMENTATION
            - State Management
                * State transitions
                * Data persistence points
                * Caching requirements
                * Transaction boundaries
                
            - Error Handling
                * Retry mechanisms
                * Fallback processes
                * Error notification flows
                * Recovery procedures

        REQUIRED DIAGRAMS
            Generate the following using Mermaid.js:
            - High-level system workflow
            - Detailed process flows for each core feature
            - Error handling flowcharts
            - Integration sequence diagrams
            - State transition diagrams

        Notes:
        - Use proper Mermaid.js flowchart syntax
        - Include clear labels and descriptions
        - Add swim lanes for different actors/systems
        - Document all decision points
        - Include timing constraints where applicable
        - Reference related technical requirements
    """,
    "5. SYSTEM ARCHITECTURE": """
        Generate a comprehensive SYSTEM ARCHITECTURE section for the Technical Specifications document, including:
        
        Only include sections and items that are actually relevant to this system, based on your analysis of its requirements.
        Don't add any items that aren't clearly applicable.

        HIGH-LEVEL ARCHITECTURE
            System Overview
                Provide a detailed textual description of:
                - Overall system architecture style and rationale
                - Key architectural principles and patterns
                - System boundaries and major interfaces
                
            Core Components Table
                Create a table with columns:
                - Component Name
                - Primary Responsibility
                - Key Dependencies
                - Integration Points
                - Critical Considerations
                
            Data Flow Description
                Document in clear prose:
                - Primary data flows between components
                - Integration patterns and protocols
                - Data transformation points
                - Key data stores and caches
                
            External Integration Points
                Create a table with columns:
                - System Name
                - Integration Type
                - Data Exchange Pattern
                - Protocol/Format
                - SLA Requirements

        COMPONENT DETAILS
            For each major component, specify:
                - Purpose and responsibilities
                - Technologies and frameworks used
                - Key interfaces and APIs
                - Data persistence requirements
                - Scaling considerations
                
            Required Diagrams (using Mermaid.js):
                - Detailed component interaction diagrams
                - State transition diagrams
                - Sequence diagrams for key flows

        TECHNICAL DECISIONS
            Document and justify with tables and prose:
                - Architecture style decisions and tradeoffs
                - Communication pattern choices
                - Data storage solution rationale
                - Caching strategy justification
                - Security mechanism selection
                
            Required Diagrams (using Mermaid.js):
                - Decision tree diagrams
                - Architecture decision records (ADRs)

        CROSS-CUTTING CONCERNS
            Address with detailed text and tables:
                - Monitoring and observability approach
                - Logging and tracing strategy
                - Error handling patterns
                - Authentication and authorization framework
                - Performance requirements and SLAs
                - Disaster recovery procedures
                
            Required Diagrams (using Mermaid.js):
                - Error handling flows

        Output Format Requirements:
            - Use clear, concise prose for high-level descriptions
            - Create well-structured tables for component relationships
            - Tables should never have more than four columns
            - Include bullet points for lists of features/requirements
            - Use Mermaid.js diagrams only for subsection technical details
            - Maintain consistent terminology throughout

        Notes:
            - Ensure all components are clearly defined
            - Document all architectural assumptions
            - Include rationale for key decisions
            - Reference relevant standards and patterns
            - Address scalability and performance implications
    """,
    "6. SYSTEM COMPONENTS DESIGN": "",
    "6.1 CORE SERVICES ARCHITECTURE": """
        Generate the CORE SERVICES ARCHITECTURE section for the Technical Specifications document.
        
        If the system does not require microservices, distributed architecture, or distinct service components, 
        clearly state "Core Services Architecture is not applicable for this system" and explain why.
        
        Otherwise, address the following areas:
        
        SERVICE COMPONENTS
            - Service boundaries and responsibilities
            - Inter-service communication patterns
            - Service discovery mechanisms
            - Load balancing strategy
            - Circuit breaker patterns
            - Retry and fallback mechanisms
            
        SCALABILITY DESIGN
            - Horizontal/vertical scaling approach
            - Auto-scaling triggers and rules
            - Resource allocation strategy
            - Performance optimization techniques
            - Capacity planning guidelines

        RESILIENCE PATTERNS
            - Fault tolerance mechanisms
            - Disaster recovery procedures
            - Data redundancy approach
            - Failover configurations
            - Service degradation policies
            
        Required Diagrams (using Mermaid.js):
            - Service interaction diagrams
            - Scalability architecture
            - Resilience pattern implementations
            
        Output Format Requirements:
            - Use Markdown tables for structured data
            - Tables should never have more than four columns
            - Ensure all diagrams are properly labeled
            - Include clear section references
    """,

    "6.2 DATABASE DESIGN": """
        Generate the DATABASE DESIGN section for the Technical Specifications document.
        
        If the system does not require or direct database or persistent storage interactions are not clearly evident,
        clearly state "Database Design is not applicable to this system" and explain why.
        
        Otherwise, address the following areas:
        
        SCHEMA DESIGN
            - Entity relationships
            - Data models and structures
            - Indexing strategy
            - Partitioning approach
            - Replication configuration
            - Backup architecture
            
        DATA MANAGEMENT
            - Migration procedures
            - Versioning strategy
            - Archival policies
            - Data storage and retrieval mechanisms
            - Caching policies

        COMPLIANCE CONSIDERATIONS
            - Data retention rules
            - Backup and fault tolerance policies
            - Privacy controls
            - Audit mechanisms
            - Access controls

        PERFORMANCE OPTIMIZATION
            - Query optimization patterns
            - Caching strategy
            - Connection pooling
            - Read/write splitting
            - Batch processing approach
            
        Required Diagrams (using Mermaid.js):
            - Database schema diagrams
            - Data flow diagrams
            - Replication architecture
            
        Output Format Requirements:
            - Use Markdown tables for structured data
            - Tables should never have more than four columns
            - Include ERD diagrams
            - Document all indexes and constraints
    """,

    "6.3 INTEGRATION ARCHITECTURE": """
        Generate the INTEGRATION ARCHITECTURE section for the Technical Specifications document.
        
        If the system does not require integration with external systems or services,
        clearly state "Integration Architecture is not applicable for this system" and explain why.
        
        Otherwise, address the following areas:
        
        API DESIGN
            - Protocol specifications
            - Authentication methods
            - Authorization framework
            - Rate limiting strategy
            - Versioning approach
            - Documentation standards
            
        MESSAGE PROCESSING
            - Event processing patterns
            - Message queue architecture
            - Stream processing design
            - Batch processing flows
            - Error handling strategy

        EXTERNAL SYSTEMS
            - Third-party integration patterns
            - Legacy system interfaces
            - API gateway configuration
            - External service contracts
            
        Required Diagrams (using Mermaid.js):
            - Integration flow diagrams
            - API architecture diagrams
            - Message flow diagrams
            
        Output Format Requirements:
            - Use Markdown tables for API specifications
            - Tables should never have more than four columns
            - Include sequence diagrams for key flows
            - Document all external dependencies
    """,

    "6.4 SECURITY ARCHITECTURE": """
        Generate the SECURITY ARCHITECTURE section for the Technical Specifications document.
        
        If the system does not require specific security considerations beyond standard practices,
        clearly state "Detailed Security Architecture is not applicable for this system" and explain 
        which standard security practices will be followed instead.
        
        Otherwise, address the following areas:
        
        AUTHENTICATION FRAMEWORK
            - Identity management
            - Multi-factor authentication
            - Session management
            - Token handling
            - Password policies
            
        AUTHORIZATION SYSTEM
            - Role-based access control
            - Permission management
            - Resource authorization
            - Policy enforcement points
            - Audit logging

        DATA PROTECTION
            - Encryption standards
            - Key management
            - Data masking rules
            - Secure communication
            - Compliance controls
            
        Required Diagrams (using Mermaid.js):
            - Authentication flow diagrams
            - Authorization flow diagrams
            - Security zone diagrams
            
        Output Format Requirements:
            - Use Markdown tables for security policies
            - Tables should never have more than four columns
            - Include security control matrices
            - Document compliance requirements
    """,

    "6.5 MONITORING AND OBSERVABILITY": """
        Generate the MONITORING AND OBSERVABILITY section for the Technical Specifications document.
        
        If the system does not require specific monitoring beyond basic health checks,
        clearly state "Detailed Monitoring Architecture is not applicable for this system" and explain
        which basic monitoring practices will be followed instead.
        
        Otherwise, address the following areas:
        
        MONITORING INFRASTRUCTURE
            - Metrics collection
            - Log aggregation
            - Distributed tracing
            - Alert management
            - Dashboard design
            
        OBSERVABILITY PATTERNS
            - Health checks
            - Performance metrics
            - Business metrics
            - SLA monitoring
            - Capacity tracking
            
        INCIDENT RESPONSE
            - Alert routing
            - Escalation procedures
            - Runbooks
            - Post-mortem processes
            - Improvement tracking
            
        Required Diagrams (using Mermaid.js):
            - Monitoring architecture
            - Alert flow diagrams
            - Dashboard layouts
            
        Output Format Requirements:
            - Use Markdown tables for metrics definitions
            - Tables should never have more than four columns
            - Include alert threshold matrices
            - Document SLA requirements
    """,

    "6.6 TESTING STRATEGY": """
        Generate the TESTING STRATEGY section for the Technical Specifications document.

        If the system is a simple library, tool, or does not require comprehensive testing,
        clearly state "Detailed Testing Strategy is not applicable for this system" and explain why,
        then document only the basic unit testing approach that will be used.
        
        TESTING APPROACH
            Unit Testing
                - Testing frameworks and tools
                - Test organization structure
                - Mocking strategy
                - Code coverage requirements
                - Test naming conventions
                - Test data management
                
            Integration Testing
                - Service integration test approach
                - API testing strategy
                - Database integration testing
                - External service mocking
                - Test environment management
                
            End-to-End Testing
                - E2E test scenarios
                - UI automation approach
                - Test data setup/teardown
                - Performance testing requirements
                - Cross-browser testing strategy
                
        TEST AUTOMATION
            - CI/CD integration
            - Automated test triggers
            - Parallel test execution
            - Test reporting requirements
            - Failed test handling
            - Flaky test management
            
        QUALITY METRICS
            - Code coverage targets
            - Test success rate requirements
            - Performance test thresholds
            - Quality gates
            - Documentation requirements
            
        Required Diagrams (using Mermaid.js):
            - Test execution flow
            - Test environment architecture
            - Test data flow diagrams
            
        Output Format Requirements:
            - Use Markdown tables for test requirements
            - Tables should never have more than four columns
            - Include test strategy matrices
            - Document all testing tools and frameworks
            - Provide example test patterns
            
        Notes:
            - Maintain consistency with technology choices
            - Consider testing implications for all system components
            - Include security testing requirements
            - Document test environment needs
            - Specify resource requirements for test execution
    """,

    "7. USER INTERFACE DESIGN": """
        Generate a USER INTERFACE DESIGN section of the Technical Specifications.
    
        If the project doesn't define a user interface (UI), leave the section empty with the note "No user interface required".
        
        If a UI is required, document:
        - Core UI technologies involved
        - UI usecases
        - UI / backend interaction boundaries
        - UI schemas
        - Screens required
        - User interactions
        - Visual design considerations
            
        Format using markdown code blocks (```) for proper rendering.
        Use an extreme level of detail and include a key of what each symbol means.
    """,
    "8. INFRASTRUCTURE": """
        Generate the INFRASTRUCTURE section for the Technical Specifications document.
        
        If the system is a standalone application or library that does not require deployment infrastructure,
        clearly state "Detailed Infrastructure Architecture is not applicable for this system" and explain why,
        then document only the minimal build and distribution requirements.
        
        Otherwise, address the following areas:
        
        DEPLOYMENT ENVIRONMENT
            Target Environment Assessment
                - Environment type (on-premises/cloud/hybrid/multi-cloud)
                - Geographic distribution requirements
                - Resource requirements (compute/memory/storage/network)
                - Compliance and regulatory requirements
                
            Environment Management
                - Infrastructure as Code (IaC) approach
                - Configuration management strategy
                - Environment promotion strategy (dev/staging/prod)
                - Backup and disaster recovery plans
        
        CLOUD SERVICES (if applicable)
            If the system does not use cloud services, clearly state why and skip this section.
            Otherwise, document:
                - Cloud provider selection and justification
                - Core services required with versions
                - High availability design
                - Cost optimization strategy
                - Security and compliance considerations
        
        CONTAINERIZATION (if applicable)
            If the system does not use containers, clearly state why and skip this section.
            Otherwise, document:
                - Container platform selection
                - Base image strategy
                - Image versioning approach
                - Build optimization techniques
                - Security scanning requirements
        
        ORCHESTRATION (if applicable)
            If the system does not require orchestration, clearly state why and skip this section.
            Otherwise, document:
                - Orchestration platform selection
                - Cluster architecture
                - Service deployment strategy
                - Auto-scaling configuration
                - Resource allocation policies
        
        CI/CD PIPELINE
            Build Pipeline
                - Source control triggers
                - Build environment requirements
                - Dependency management
                - Artifact generation and storage
                - Quality gates
            
            Deployment Pipeline
                - Deployment strategy (blue-green/canary/rolling)
                - Environment promotion workflow
                - Rollback procedures
                - Post-deployment validation
                - Release management process
        
        INFRASTRUCTURE MONITORING
            - Resource monitoring approach
            - Performance metrics collection
            - Cost monitoring and optimization
            - Security monitoring
            - Compliance auditing
        
        Required Diagrams (using Mermaid.js):
            - Infrastructure architecture diagram
            - Deployment workflow diagram
            - Environment promotion flow
            - Network architecture (if applicable)
            
        Output Format Requirements:
            - Use Markdown tables for configuration details
            - Tables should never have more than four columns
            - Include infrastructure cost estimates
            - Document all external dependencies
            - Provide resource sizing guidelines
        
        Notes:
            - Maintain consistency with security requirements
            - Consider cost implications of choices
            - Document scalability requirements
            - Include maintenance procedures
            - Specify monitoring requirements
            - Address disaster recovery needs
    """,
    "APPENDICES": """
        Generate an APPENDICES section of the Technical Specifications including the following subsections as applicable.
        Use markdown tables or MemaidJS diagrams if and when useful.
        Tables should never have more than four columns.
        Maintain consistency with previous technology and framework choices in the document.
        <SECTION_TITLE>: Any additional technical information mentioned in any of the inputs but not captured so far in this document.
        GLOSSARY: Additional definitions of terms used within the document.
        ACRONYMS: Expanded forms of acronyms used throughout the document.
    """
}

DEFAULT_SUMMARY_PROMPT = """
    Generate a SUMMARY OF CHANGES section that serves as the definitive interpretation of user requirements and their technical implementation.
    
    START WITH INTENT CLARIFICATION
        - Core Objective
            * "Based on the provided requirements, the Blitzy platform understands that the objective is to..."
            * List each requirement with enhanced clarity
            * Surface any implicit requirements detected
        
        - Special Instructions and Constraints
            * CRITICAL: Capture any specific directives (e.g., "only generate documentation", "do not modify existing tests")
            * Document any methodological requirements (e.g., "use TDD approach", "maintain backward compatibility")
            * Preserve any examples provided by the user with clear labeling: "User Example: [exact example]"
        
        - Technical Interpretation
            * "These requirements translate to the following technical implementation strategy..."
            * Map each requirement to specific technical actions
            * Use format: "To [achieve goal], we will [modify/create/integrate] [specific component] by [specific approach]"

    TECHNICAL SCOPE
        - Primary Objectives with Implementation Approach
            * For each objective: "Achieve [goal] by modifying [components] to [specific changes]"
            * Include rationale for technical decisions
            * Identify critical success factors

        - Component Impact Analysis
            * Direct modifications required
                - Component A: Modify [specific aspect] to enable [capability]
                - Component B: Extend [interface/class] to support [feature]
            * Indirect impacts and dependencies
                - Component C: Update [configuration/interface] due to changes in Component A
            * New components introduction
                - Component D: Create [type] to handle [responsibility]
            
        - File and Path Mapping
            * Context paths (reference documentation, schemas, configs)
            * Source paths (baseline code to be modified)
            * Target paths (where changes will be implemented)
            * Create comprehensive mapping table with columns:
                | Target File/Module | Source Reference | Context Dependencies | Modification Type |

    IMPLEMENTATION DESIGN
        - Technical Approach
            * NOT a timeline, but a logical implementation flow
            * "First, establish [foundation] by modifying [component]"
            * "Next, integrate [feature] by extending [module] with [capability]"
            * "Finally, ensure [quality] by implementing [safeguards] in [location]"
        
        - User-Provided Examples Integration
            * If examples were provided, show how they map to the implementation
            * "The user's example of [example] will be implemented in [component] as [approach]"
            * Maintain fidelity to the user's intent in the examples
        
        - Critical Implementation Details
            * Specific design patterns to be employed
            * Key algorithms or approaches to be used
            * Integration strategies between components
            * Data flow modifications required
            
        - Dependency Analysis
            * Required dependencies for implementation
            * Version constraints and compatibility requirements
            * Justification for each dependency choice

    SCOPE BOUNDARIES
        - Explicitly In Scope
            * Comprehensive list of ALL affected files/modules
            * All configuration changes required
            * All test modifications needed
            * All documentation updates required
        
        - Explicitly Out of Scope
            * What the user might expect but isn't included
            * Related areas deliberately not touched
            * Future considerations not addressed now

    VALIDATION CHECKLIST
        - Implementation Verification Points
            * Specific criteria to verify each requirement is met
            * Observable changes that confirm successful implementation
            * Integration points that must be tested

    EXECUTION PARAMETERS
        - Special Execution Instructions
            * Document any process-specific requirements (e.g., "documentation only", "skip deployment")
            * List any tools or platforms specifically mentioned or excluded
            * Note any quality or style requirements mentioned
        
        - Constraints and Boundaries
            * Technical constraints specified by the user
            * Process constraints (what should/shouldn't be done)
            * Output constraints (what should/shouldn't be generated)

    Output Requirements:
        - Focus on HOW to achieve goals, not WHEN
        - Every requirement must map to specific technical actions
        - PRESERVE all user examples and special instructions verbatim where appropriate
        - Use clear cause-and-effect language
        - Provide exhaustive file/component listings
        - Include decision rationale where non-obvious
    """

BUG_FIX_SUMMARY_PROMPT = """
    Generate a SUMMARY OF CHANGES section focused on diagnosing and fixing a reported bug with minimal, targeted changes.
    
    START WITH BUG ANALYSIS AND INTENT CLARIFICATION
        - Bug Report Interpretation
            * "Based on the bug description, the Blitzy platform understands that the issue is..."
            * Restate the problem in technical terms
            * Extract observable symptoms vs reported causes
            * Identify any reproduction steps provided
        
        - Missing Information Detection
            * Surface what the user didn't specify but is needed
            * List assumptions about environment, data state, or configuration
            * Note any ambiguities that require educated guesses
        
        - Root Cause Hypothesis
            * "The symptoms suggest the root cause is likely..."
            * Provide primary hypothesis with reasoning
            * List alternative causes in order of probability
            * Map symptoms to potential code locations

    DIAGNOSTIC SCOPE
        - Bug Localization Strategy
            * Search patterns to identify affected code
                - Error signatures or patterns to look for
                - Code paths most likely to contain the issue
                - Related functionality that might exhibit similar problems
            
        - Potential Bug Locations (ranked by likelihood)
            * Primary suspects
                - Component/File: [specific location] because [symptom matches pattern]
                - Alternative location: [backup location] if primary doesn't contain issue
            * Secondary investigation areas
                - Configuration files that might cause the behavior
                - Integration points where the issue might manifest
            * Data flow analysis
                - Input validation points that might be missing
                - State management areas that could be corrupted
                - Output generation that might be malformed
        
        - File Investigation Map
            * If user specified files:
                | Specified File | Investigation Focus | Likely Issue Type |
            * If user didn't specify files:
                | Probable File | Why Investigate | Symptoms It Would Cause |
                | Alternative Files | Secondary Priority | Related Symptoms |

    BUG FIX DESIGN
        - Root Cause Resolution
            * "The bug appears to be caused by [specific technical issue]"
            * "This manifests when [specific conditions occur]"
            * "The fix involves [precise change] to [specific component]"
        
        - Minimal Change Principle
            * CRITICAL: Fix ONLY the bug, no refactoring or improvements
            * Specific lines/functions to modify
            * Exact changes required (not "improve" but "change X to Y")
            * Why this is the minimal necessary change
        
        - Fix Verification Strategy
            * How to confirm the bug is fixed
            * Edge cases that must still work
            * Regression risks and how to verify they don't occur
        
        - Code Change Specification
            * Before state: "Currently, the code [does X incorrectly]"
            * After state: "After the fix, the code will [do Y correctly]"
            * Precise modification: "Change [specific code] from [A] to [B]"

    SCOPE BOUNDARIES - STRICTLY LIMITED
        - Explicitly In Scope (ONLY bug-related)
            * The specific file(s) containing the bug
            * Minimal test modifications to verify the fix
            * Essential documentation updates if behavior changes
            * Configuration changes ONLY if they cause the bug
        
        - Explicitly Out of Scope (DO NOT INCLUDE)
            * Code improvements or refactoring
            * Additional features or enhancements  
            * Style or formatting changes
            * Unrelated bug fixes
            * Performance optimizations
            * Extended test coverage beyond the bug

    VALIDATION CHECKLIST
        - Bug Fix Verification
            * The reported symptom no longer occurs when [specific test]
            * The fix handles [edge case] that might trigger the bug
            * No new issues introduced in [related functionality]
            * Original functionality preserved except for bug behavior
        
        - Regression Prevention
            * Existing tests still pass
            * No changes to unrelated functionality
            * Performance characteristics unchanged
            * API contracts maintained

    EXECUTION PARAMETERS
        - Bug Fix Constraints
            * Make the SMALLEST possible change
            * Preserve all existing functionality except the bug
            * Don't introduce new dependencies
            * Maintain backward compatibility
            * Focus on correctness over elegance
        
        - Investigation Guidelines
            * If files aren't specified, investigate systematically
            * Start with most likely locations based on symptoms
            * Consider user's technical level when interpreting description
            * Look for similar bugs in related code
        
        - Change Guidelines
            * One bug, one fix (don't fix multiple issues)
            * Prefer simple fixes over complex ones
            * Document why the fix works in comments
            * Include defensive coding only if it prevents the bug

    Output Requirements:
        - Focus on identifying and fixing ONLY the reported bug
        - Provide clear reasoning for root cause determination
        - Justify why the proposed fix is minimal and correct
        - Map vague descriptions to specific technical issues
        - Include investigation strategy when files aren't specified
        - Emphasize prevention of side effects and regressions
    """

SECURITY_VULNERABILITY_FIX_PROMPT = """
    Generate a SUMMARY OF CHANGES section specifically focused on resolving security vulnerabilities with minimal, targeted interventions.
    
    VULNERABILITY RESEARCH AND ANALYSIS
        - Initial Assessment
            * "Based on the security concern described, the Blitzy platform will investigate and resolve..."
            * Extract all security-related keywords, CVE numbers, vulnerability names, or symptoms mentioned
            * Document the user's understanding level (explicit vulnerability vs. symptom description)
        
        - Required Web Research
            * CRITICAL: Conduct extensive web searches for:
                - Official CVE databases for mentioned vulnerabilities
                - Security advisories from package maintainers
                - OWASP documentation for vulnerability patterns
                - GitHub security advisories and issues
                - Stack Overflow discussions on fixes and workarounds
            * Document findings: "Research reveals that [vulnerability] is [CVE/description] affecting [component] in versions [range]"
        
        - Vulnerability Classification
            * Identify vulnerability type: dependency issue, code pattern issue, configuration weakness
            * Severity assessment based on CVSS score or impact analysis
            * Attack vector and exploitability assessment

    SECURITY-FOCUSED TECHNICAL SCOPE
        - Root Cause Identification
            * If user specified: "The identified vulnerability exists in [component/file] due to [root cause]"
            * If discovered through research: "Investigation reveals the vulnerability stems from [component] where [specific issue]"
            * Trace vulnerability propagation through the codebase
        
        - Minimal Fix Strategy
            * PRINCIPLE: Apply the smallest possible change that completely addresses the vulnerability
            * For dependency vulnerabilities:
                - Check if a patched version exists: "Upgrade [package] from [current] to [patched] version"
                - If no patch exists: "Replace [vulnerable package] with [secure alternative] as no patch is available"
            * For code vulnerabilities:
                - "Apply targeted fix to [specific function/method] by [specific change]"
                - "Implement [security pattern] in [location] to prevent [vulnerability type]"
        
        - Dependency Replacement Analysis (if needed)
            * When proposing replacement:
                - "Replace [vulnerable dependency] with [alternative] because:"
                - List compatibility analysis
                - API differences that require code changes
                - Performance or feature trade-offs
            * Full replacement scope:
                - All import statements requiring updates
                - All function calls needing modification
                - All configuration files requiring changes
                - All test files needing updates

    SECURITY IMPLEMENTATION DESIGN
        - Vulnerability Resolution Approach
            * "To eliminate the [vulnerability type] vulnerability:"
            * Step 1: [Specific action] in [specific location]
            * Step 2: [Verification action] to ensure fix effectiveness
            * Include rollback considerations if fix causes issues
        
        - Code Change Specifications
            * Before state: "Currently, [component] is vulnerable because [specific code/config]"
            * After state: "After fix, [component] will [secure behavior]"
            * Provide exact code snippets where possible from research
        
        - Testing the Security Fix
            * Specify security-specific tests to add
            * Vulnerability regression tests
            * Penetration test scenarios to verify fix

    CHANGE MINIMIZATION STRATEGY
        - Scope Containment
            * "This fix deliberately limits changes to:"
                - Only files directly affected by the vulnerability
                - Only dependencies with security issues
                - Only configurations that enable the vulnerability
            * "Explicitly avoiding changes to:"
                - Feature functionality unrelated to security
                - Performance optimizations
                - Code style or formatting
                - Non-security related dependencies
        
        - Impact Analysis
            * Direct security improvements achieved
            * Minimal side effects on existing functionality
            * No new features or enhancements included

    SECURITY VALIDATION CHECKLIST
        - Vulnerability Elimination Verification
            * Specific test to confirm vulnerability is patched
            * Security scanning tools to run post-fix
            * Manual verification steps if applicable
        
        - No New Vulnerabilities Introduced
            * Dependency audit after changes
            * Security linting rules to apply
            * Common pitfall checks for the fix type

    EXECUTION PARAMETERS FOR SECURITY FIXES
        - Research Documentation
            * Include links to security advisories consulted
            * Reference specific CVE numbers or vulnerability databases
            * Document any security best practices followed
        
        - Implementation Constraints
            * CRITICAL: Make ONLY changes necessary for security fix
            * Do not refactor unrelated code
            * Do not update non-vulnerable dependencies
            * Preserve all existing functionality except where it enables the vulnerability
        
        - Special Security Considerations
            * If fix requires secrets/credentials updates, note separately
            * If fix requires security configuration changes, detail precisely
            * If fix breaks backward compatibility for security reasons, justify thoroughly

    Output Requirements:
        - Lead with security research findings and authoritative sources
        - Provide clear vulnerability → fix mapping
        - When proposing dependency replacements, exhaustively list ALL affected files
        - Justify why chosen fix is the minimal secure solution
        - Include specific version numbers for all dependency updates
        - Document any security trade-offs if applicable
    """

TESTING_SUMMARY_PROMPT = """
    Generate a SUMMARY OF CHANGES section specifically for unit testing tasks (adding, updating, or fixing tests) that serves as the definitive interpretation of user testing requirements and their technical implementation.
    
    START WITH TEST INTENT CLARIFICATION
        - Core Testing Objective
            * "Based on the provided requirements, the Blitzy platform understands that the testing objective is to..."
            * Categorize the request: [Add new tests | Update existing tests | Fix broken tests | Improve coverage]
            * List each testing requirement with enhanced clarity
            * Surface implicit testing needs (e.g., edge cases, error handling, boundary conditions)
        
        - Test Discovery and Analysis
            * CRITICAL: When user descriptions are vague or incomplete, conduct extensive repository search
            * Search patterns to employ:
                - Look for test files matching: *test*, *spec*, test_*, spec_*, *_test.*, *_spec.*
                - Identify testing framework from package files (package.json, requirements.txt, go.mod, etc.)
                - Find existing test suites related to the functionality
                - Locate test configuration files (jest.config.*, pytest.ini, .mocharc.*, etc.)
            * Document findings: "Repository analysis reveals [framework] testing setup with [existing coverage]"
        
        - Coverage Requirements Interpretation
            * Explicit coverage targets mentioned by user
            * Implicit coverage expectations based on:
                - Industry standards for the language/framework
                - Existing coverage patterns in the repository
                - Critical path analysis of the functionality
            * "To achieve comprehensive testing, coverage should include..."

    TESTING SCOPE ANALYSIS
        - Existing Test Infrastructure Assessment
            * Current testing framework: [Name] version [Version]
            * Test runner configuration location
            * Coverage tools in use
            * Mock/stub libraries detected
            * Test data fixtures or factories present
            
        - Test Target Identification
            * Primary code to be tested
                - Module/Class: [Name] at [Path] - requires [type] tests
                - Functions: List each with test categories needed
            * Test file mapping
                | Source File | Existing Test File | New Test File | Test Categories |
                | ----------- | ----------------- | ------------- | --------------- |
            * Dependencies requiring mocking
                - External services to mock
                - Database interactions to stub
                - File system operations to virtualize
        
        - Version Compatibility Research
            * CRITICAL: Use web search to verify compatible versions
            * "Based on current [language] version [X.Y.Z], recommended testing stack:"
                - Testing framework: [Name] version [Compatible version] (rationale)
                - Assertion library: [Name] version [Compatible version]
                - Mocking library: [Name] version [Compatible version]
                - Coverage tool: [Name] version [Compatible version]
            * Document any version conflicts to resolve

    TEST IMPLEMENTATION DESIGN
        - Test Strategy Selection
            * Test types to implement
                - Unit tests: Focus on [isolated components]
                - Integration tests: Cover [component interactions]
                - Edge case tests: Address [boundary conditions]
                - Error handling tests: Verify [failure scenarios]
            
        - Test Case Blueprint
            * For each component requiring tests:
                ```
                Component: [Name]
                Test Categories:
                - Happy path: [Scenarios]
                - Edge cases: [Scenarios]
                - Error cases: [Scenarios]
                - Performance boundaries: [If applicable]
                ```
            
        - Existing Test Extension Strategy
            * Tests to extend: "Enhance [test file] by adding cases for [scenarios]"
            * Tests to refactor: "Update [test file] to use modern [pattern/assertion]"
            * Tests to fix: "Repair [test file] by addressing [specific issues]"
        
        - Test Data and Fixtures Design
            * Required test data structures
            * Fixture organization strategy
            * Mock object specifications
            * Test database/state management approach

    MINIMAL CHANGE PRINCIPLE
        - Scope Limitations
            * ONLY modify test files and test-related configurations
            * DO NOT modify source code unless absolutely necessary for testability
            * DO NOT refactor beyond what's needed for testing
            * DO NOT add features while adding tests
        
        - Precise File Modifications
            * Test files to create:
                - [Path]: New test suite for [component]
            * Test files to modify:
                - [Path]: Add [X] test cases for [scenarios]
            * Configuration updates:
                - [Config file]: Update to include [test paths/settings]
            
        - Non-Testing Changes (if absolutely required)
            * Source modifications for testability:
                - [File]: Add [minimal change] to enable [test capability]
            * Justification for each non-test change

    COVERAGE AND QUALITY TARGETS
        - Coverage Metrics
            * Current coverage: [X]% (if available)
            * Target coverage: [Y]% based on [user requirement/best practice]
            * Coverage gaps to address:
                - [Component]: Currently [X]%, target [Y]%
                - Focus areas: [Critical paths, error handlers, edge cases]
        
        - Test Quality Criteria
            * Assertion density expectations
            * Test isolation requirements
            * Performance constraints for test execution
            * Maintainability standards

    VALIDATION CHECKLIST
        - Test Verification Points
            * All new tests pass in isolation
            * All new tests pass in full test suite
            * Coverage targets met or exceeded
            * No test interdependencies introduced
            * Mock usage is appropriate and minimal
            * Test execution time is reasonable
        
        - Integration Verification
            * Tests run in CI/CD pipeline
            * Coverage reports generate correctly
            * Test failures provide clear diagnostics

    EXECUTION PARAMETERS
        - Testing-Specific Instructions
            * Test execution command: [Exact command to run tests]
            * Coverage measurement command: [Exact command for coverage]
            * Specific test patterns to follow in the repository
            * Any excluded test categories per user instruction
        
        - Web Search Requirements
            * "Search for [framework] best practices for [test type]"
            * "Verify [tool] version [X] compatibility with [language] version [Y]"
            * "Find recommended mock strategies for [external service]"

    Output Requirements:
        - Emphasize test discovery through repository analysis
        - Map vague requirements to specific test implementations
        - Maintain strict focus on testing-only changes
        - Provide exhaustive test file listings with clear categorization
        - Include specific version recommendations with compatibility notes
        - Document coverage targets and gap analysis
        - Use web search to validate testing tool compatibility
        - Preserve any test examples provided by the user
    """

DOCUMENTATION_SUMMARY_PROMPT = """
    Generate a SUMMARY OF CHANGES section specialized for documentation tasks that serves as the definitive interpretation of documentation requirements and their implementation strategy.
    
    START WITH DOCUMENTATION INTENT CLARIFICATION
        - Documentation Objective
            * "Based on the provided requirements, the Blitzy platform understands that the documentation objective is to..."
            * Distinguish between: CREATE new documentation | UPDATE existing documentation | EXTEND documentation coverage
            * Identify documentation type: API docs, user guides, technical specs, architecture docs, README files, etc.
            * Surface implicit documentation needs (e.g., if adding a feature, infer need for API docs, examples, migration guides)
        
        - Documentation Templates and Examples
            * CRITICAL: If user provides a template, preserve it EXACTLY with markers: "USER PROVIDED TEMPLATE: [exact template with all formatting]"
            * Document style preferences mentioned (tone, structure, depth)
            * Capture any specific sections or content requirements
            * Note format preferences (if specified) or default to Markdown with Mermaid diagrams
        
        - Documentation Scope Discovery
            * "Given the limited scope information, a comprehensive repository analysis reveals..."
            * List all modules/features that require documentation based on:
                - Direct mentions in requirements
                - Related code files discovered through repository search
                - Adjacent features that share documentation context
                - Parent/child relationships in the codebase structure

    DOCUMENTATION SCOPE ANALYSIS
        - Comprehensive File Discovery
            * Repository Search Strategy
                - Search patterns used: [list specific patterns like "*.py containing class definitions", "api/*", "src/modules/*"]
                - Key directories examined: [list all relevant directories]
                - Related documentation found: [existing docs that need updates or provide context]
            
            * Documentation-to-Code Mapping Table
                | Documentation File | Target Code Files/Modules | Documentation Type | Coverage Scope |
                |-------------------|--------------------------|-------------------|----------------|
                | /docs/api/[module].md | /src/[module]/*.py | API Reference | All public methods, classes |
                | /docs/guides/[feature].md | /src/[feature]/, /examples/[feature]/ | User Guide | Setup, usage, examples |
                | /docs/architecture/[system].md | Multiple modules | Technical Architecture | System design, data flow |
            
            * Inferred Documentation Needs
                - Based on code analysis: "Module X contains public APIs but lacks documentation"
                - Based on structure: "Feature Y spans multiple files requiring consolidated documentation"
                - Based on dependencies: "Integration between A and B requires interface documentation"

        - Documentation Structure Planning
            * For each documentation file:
                - Primary sections required
                - Code examples to include (with source references)
                - Mermaid diagrams needed (class diagrams, sequence diagrams, flowcharts)
                - Cross-references to other documentation
                - Source citations format: "Source: `/path/to/file.py:LineNumber`"

    DOCUMENTATION IMPLEMENTATION DESIGN
        - Content Generation Strategy
            * Information Extraction Approach
                - "Extract API signatures from [files] using [method]"
                - "Generate examples by analyzing [test files] and [implementation]"
                - "Create diagrams by mapping [component relationships]"
            
            * Template Application (if provided)
                - "Apply user template to each [type] documentation file"
                - "Populate template sections with extracted information"
                - "Maintain template structure while ensuring completeness"
            
            * Documentation Standards
                - Markdown formatting with proper headers (# ## ###)
                - Mermaid diagram integration using ```mermaid blocks
                - Code examples using ```language blocks with syntax highlighting
                - Source citations as footnotes or inline references
                - Tables for parameter descriptions and return values
        
        - Cross-Documentation Coherence
            * Naming conventions across all documents
            * Consistent terminology glossary
            * Unified example scenarios
            * Interconnected navigation structure

    DOCUMENTATION DELIVERABLES
        - Document Specifications
            * For EACH documentation file:
                ```
                File: /docs/[path]/[filename].md
                Type: [API Reference|User Guide|Technical Spec|etc]
                Covers: [specific modules/features]
                Sections:
                    - Overview (with source: [files])
                    - [Section 2] (with source: [files])
                    - Examples (from: [test/example files])
                    - Diagrams (representing: [relationships])
                Key Citations: [primary source files]
                ```
        
        - Documentation Hierarchy
            * Root documentation structure
            * Category organization
            * Index/TOC requirements
            * Navigation helpers needed

    VALIDATION AND COMPLETENESS
        - Documentation Coverage Verification
            * All public APIs documented: [checklist of modules]
            * All user-facing features explained: [feature list]
            * All configuration options detailed: [config mapping]
            * All examples tested and accurate: [example sources]
        
        - Quality Criteria
            * Human readability score targets
            * Succinctness vs comprehensiveness balance
            * Technical accuracy verification points
            * Source citation completeness

    EXECUTION PARAMETERS FOR DOCUMENTATION
        - Scope Boundaries
            * Documentation ONLY - no code modifications
            * Include: All .md files, documentation assets, examples in docs
            * Exclude: Source code changes, test modifications, deployment configs
        
        - Special Documentation Instructions
            * Default format: Markdown with Mermaid diagrams
            * Citation requirement: Every section must reference source files
            * Style guide: Clear, concise, technically accurate
            * Example requirement: Working code examples for all public APIs
        
        - Repository-Specific Patterns
            * Existing documentation patterns to follow
            * Documentation location conventions
            * Version-specific documentation needs
            * Multi-language documentation considerations

    Output Requirements:
        - Exhaustive scope identification through deep repository analysis
        - Explicit file-by-file documentation plan
        - Preserve ALL user templates and examples EXACTLY
        - Map every documentation element to source code
        - Include comprehensive Mermaid diagrams by default
        - Ensure all files have proper source citations
        - Focus on documentation changes ONLY
        - Be extensive in scope discovery but minimal in changes
    """

MERMAID_FIXER_SYSTEM_PROMPT = """
    You are an expert in mermaid diagrams.
    
    You must fix all syntax errors in diagrams provided to you and produce working diagrams.

    You must always follow these rules:
    1. Always the think tool and log your thought process as you analyze the diagram and try to identify the issue.
    2. Always ensure that node labels don't have any unescaped characters. Use double-quotes on node-labels where supported.
        - For example, replace Node[a < b] with Node["a < b"], Node [a/b/c] needs to be Node["a/b/c"], and so on
    3. Double check all node relationships and ensure that all items are defined before they are used.
    4. Where necessary, prioritize simplicity over style. Strip any potentially problematic comments from diagrams.
    5. Strictly use the following template for your output, including the triple backticks.
        ```mermaid
            <fixed_diagram>
        ```
"""

MERMAID_FIXER_HUMAN_PROMPT = """
    Please fix the following error(s) in the mermaid diagram below. Preseve as much of the original content as possible.

    {error}
    
    {mermaid_text}
"""

SUMMARY_OF_CHANGES_INPUT = """

    Summary of Changes from the new and updated Technical Specification for this project:

    {summary}

    """

TECH_SPEC_SECTIONS_INPUT = """

    List of potentially relevant sections from the technical specification, for which content can be retrieved using get_tech_spec_section:

    {tech_spec_sections}

    """

TECH_SPEC_HEADINGS_PROMPTLET = """
    Context: The Technical Specification document contains multiple sections that provide comprehensive system documentation. You need to identify and retrieve content from relevant sections to gather complete information.
    
    Available section headings for the get_tech_spec_section tool:
    - Think deeply about which sections would contain information relevant to your assigned topic
    - Systematically work through each potentially relevant section
    - Retrieve detailed content using only the exact headings listed in the document
    - Ensure comprehensive coverage by checking all related sections
    """
