from enum import Enum


class CodeGraphFileType(str, Enum):
    SOURCE = "source"
    SOURCE_ADJACENT = "source_adjacent"
    OTHER = "other"


# some files may not have an extension, others may have a split extension
CODE_GRAPH_SOURCE_ADJACENT_FILES = [
    # 1. Scripting
    "sh", "bash", "zsh", "ksh",
    "bat", "cmd",
    "ps1", "psm1", "psd1",
    "fish",  # other shells
    "csh", "tcsh",  # optionally

    # 2. IaC / DevOps
    "tf", "tfvars", "hcl", "nomad",
    "Dockerfile",
    "DockerFile",
    "dockerfile",
    "dockerignore",
    "helmignore",
    "gitignore", "gitattributes", "gitmodules",
    "editorconfig",
    "prettierrc", "prettierignore",
    "eslintignore", "stylelintrc", "stylelintignore",
    "flake8", "pylintrc",
    "sls",  # Serverless
    "multi",

    # 3. Config / Data
    "json", "jsonl", "ndjson", "cson", "rison", "geojson", "topojson",
    "yaml", "yml",
    "toml",
    "ini", "cfg", "conf", "env", "config", "Config",
    "properties",
    "plist",
    "csv", "tsv", "psv", "dsv", "ssv", "proto", "fdf",
    "xml",
    "Gemfile",
    "Procfile",
    "Makefile",
    "Rakefile",
    "gradlew",
    "Pipfile",
    "ru",
    "iml",
    "ruby-gemset",
    "ruby-version",
    "http",
    "runsettings",
    "DotSettings",
    "props",
    "jmx",
    "mvnw",
    "protoc",
    "resx",
    "job",
    "vbp",

    # 4. Markup / Doc / Plain Text
    "md", "MD", "markdown", "mkdn", "mdown",
    "rst", "adoc", "asciidoc", "org", "textile", "wiki",
    "html", "htm", "xhtml", "cshtml",
    "tex", "latex",
    "txt",
    "TXT",
    "Rmd",
    "ipynb",
    "keep",
    "rake",
    "css",
    "scss",
    "xaml",

    # 5. Templates
    "hbs", "handlebars", "mustache",
    "twig", "jinja", "j2", "njk",
    "liquid", "erb", "rhtml", "ejs",
    "pug", "haml", "slim", "example", "tt",
    "mdj",

    # 6. Specialized / Hybrid
    "sql",
    "graphql", "gql",
    "vue", "svelte", "astro",

    # 7. Environments
    "dev", "local", "staging",
    "prod", "production",

    # 8. Metadata
    "LICENSE", "CODEOWNERS",

    # 9. Project
    "sln", "csproj", "proj", "vbproj", "targets",

    # 10. Models
    "xsd"
]

CODE_GRAPH_SOURCE_FILES = [
    # -----------------------------------
    # WebAssembly Text
    # -----------------------------------
    "wat",  # (Text-based WebAssembly)

    # -----------------------------------
    # C / C++ / Objective-C / Objective-C++
    # -----------------------------------
    "c", "cc", "cpp", "cxx",
    "h", "hh", "hpp", "hxx",  # Header files
    "inl",                    # Inline functions
    "m",  # Objective-C (.m) - also MATLAB / Mercury conflict
    "mm",  # Objective-C++

    # -----------------------------------
    # C#
    # -----------------------------------
    "cs",

    # -----------------------------------
    # Java & JVM Languages
    # -----------------------------------
    "java",
    "scala", "sc",          # Scala
    "kt", "kts",            # Kotlin
    "groovy", "gvy", "gy", "gsh",  # Groovy
    "gradle",               # Groovy-based Gradle build scripts
    "jsp",

    # -----------------------------------
    # JavaScript / TypeScript
    # -----------------------------------
    "js", "mjs", "cjs",     # JavaScript variants
    "ts", "tsx",            # TypeScript (incl. React/JSX form)

    # -----------------------------------
    # Python
    # -----------------------------------
    "py",   # Standard
    "pyi",  # Type stubs
    "pyx",  # Cython

    # -----------------------------------
    # Ruby
    # -----------------------------------
    "rb",

    # -----------------------------------
    # PHP
    # -----------------------------------
    "php", "phtml",
    "php3", "php4", "php5",
    "phps",

    # -----------------------------------
    # Go
    # -----------------------------------
    "go",

    # -----------------------------------
    # Rust
    # -----------------------------------
    "rs",

    # -----------------------------------
    # Swift
    # -----------------------------------
    "swift",

    # -----------------------------------
    # D
    # -----------------------------------
    "d",

    # -----------------------------------
    # Fortran (various dialects)
    # -----------------------------------
    "f", "for", "f77",
    "f90", "f95", "f03", "f08",

    # -----------------------------------
    # Pascal / Delphi
    # -----------------------------------
    "pas", "pp",

    # -----------------------------------
    # Ada
    # -----------------------------------
    "ada", "adb", "ads",

    # -----------------------------------
    # Assembly (common)
    # -----------------------------------
    "s", "asm",

    # -----------------------------------
    # Hardware Description Languages
    # -----------------------------------
    "vhd", "vhdl",    # VHDL
    "v", "vh",        # Verilog
    "sv", "svh",      # SystemVerilog

    # -----------------------------------
    # COBOL
    # -----------------------------------
    "cbl", "cob", "cpy", "bms",

    # -----------------------------------
    # Smalltalk
    # -----------------------------------
    "st",

    # -----------------------------------
    # Lisp / Scheme / Racket
    # -----------------------------------
    "lisp", "lsp", "cl", "el",  # Common Lisp / Emacs Lisp
    "scm", "ss",                # Scheme
    "rkt",                      # Racket

    # -----------------------------------
    # Clojure (JVM-based but often listed separately)
    # -----------------------------------
    "clj", "cljs", "cljc",

    # -----------------------------------
    # Dart
    # -----------------------------------
    "dart",

    # -----------------------------------
    # Elm
    # -----------------------------------
    "elm",

    # -----------------------------------
    # PureScript
    # -----------------------------------
    "purs",

    # -----------------------------------
    # Reason / ReScript
    # -----------------------------------
    "re", "rei",
    "res", "resi",

    # -----------------------------------
    # Nim
    # -----------------------------------
    "nim",

    # -----------------------------------
    # Crystal
    # -----------------------------------
    "cr",

    # -----------------------------------
    # Zig
    # -----------------------------------
    "zig",

    # -----------------------------------
    # V Language (conflict with Verilog ".v")
    # -----------------------------------
    "v",

    # -----------------------------------
    # Odin
    # -----------------------------------
    "odin",

    # -----------------------------------
    # Idris
    # -----------------------------------
    "idr",

    # -----------------------------------
    # Standard ML
    # -----------------------------------
    "sml", "sig", "fun",

    # -----------------------------------
    # Mercury
    # -----------------------------------
    "m",  # Collides with Objective-C, MATLAB

    # -----------------------------------
    # ANTLR Grammar
    # -----------------------------------
    "g4",

    # -----------------------------------
    # Yacc / Bison Grammar
    # -----------------------------------
    "y", "yy",

    # -----------------------------------
    # Lex / Flex
    # -----------------------------------
    "l", "ll",

    # -----------------------------------
    # SQL (domain-specific but often treated as code)
    # -----------------------------------
    "sql",

    # -----------------------------------
    # R
    # -----------------------------------
    "r",

    # -----------------------------------
    # Julia
    # -----------------------------------
    "jl",

    # -----------------------------------
    # Erlang
    # -----------------------------------
    "erl", "hrl",

    # -----------------------------------
    # Elixir
    # -----------------------------------
    "ex", "exs",

    # -----------------------------------
    # OCaml / F#
    # -----------------------------------
    "ml", "mli", "mll", "mly",  # OCaml
    "fs", "fsi",                # F# (excluding .fsx/.fsscript as “scripts”)

    # -----------------------------------
    # Haskell
    # -----------------------------------
    "hs", "lhs", "hsc",

    # -----------------------------------
    # Prolog
    # -----------------------------------
    "pl",  # Also used by Perl
    "pro",
    "P",
    # Some variants use ".P"

    # -----------------------------------
    # MATLAB
    # -----------------------------------
    "m",   # Collides with Objective-C, Mercury

    # -----------------------------------
    # GPU / Shader Languages
    # -----------------------------------
    "glsl", "vert", "frag", "geom", "tesc", "tese", "comp",
    "shader",  # Unity ShaderLab

    # -----------------------------------
    # Modern Web Frameworks
    # -----------------------------------
    "astro",     # Astro

    # -----------------------------------
    # Game Development
    # -----------------------------------
    "uc",        # UnrealScript
    "gd",        # GDScript (Godot)

    # -----------------------------------
    # Arduino
    # -----------------------------------
    "ino",

    # -----------------------------------
    # Visual Basic
    # -----------------------------------
    "vb", "frm", "cls", "bas",

    # -----------------------------------
    # Pascal
    # -----------------------------------
    "dpr"
]


# needs further improvement to nail down a broad enough list that can be used consistently
LITERALS = [
    # ========================
    "high_availability",
    "scalable",
    "fault_tolerant",
    "performance_critical",

    # ========================
    # Development Phases
    # ========================
    "prototype",
    "production",
    "deprecated",
    "experimental",

    # ========================
    # Programming Languages
    # ========================
    "python",
    "java",
    "javascript",
    "typescript",
    "go",
    "csharp",
    "cplusplus",
    "rust",

    # ========================
    # Frameworks
    # ========================
    "react",
    "angular",
    "vue",
    "spring_boot",
    "django",
    "flask",
    "expressjs",
    "laravel",

    # ========================
    # Databases (Types)
    # ========================
    "sql",
    "nosql",
    "relational",
    "document_based",
    "key_value_store",
    "graph_db",

    # ========================
    # Testing
    # ========================
    "unit_testing",
    "integration_testing",
    "end_to_end_testing",
    "load_testing",
    "stress_testing",
    "performance_testing",

    # ========================
    # Deployment & DevOps
    # ========================
    "ci_cd",
    "docker",
    "kubernetes",
    "cloud",
    "on_premises",

    # ========================
    # Cloud Providers
    # ========================
    "aws",
    "azure",
    "gcp",

    # ========================
    # Design Patterns
    # ========================
    "singleton",
    "factory",
    "builder",
    "observer",
    "strategy",
    "decorator",

    # ========================
    # Additional Security
    # ========================
    "encryption",
    "token_based_auth",
    "oauth2",
    "openid_connect",

    # ========================
    # Network & Infrastructure
    # ========================
    "load_balancer",
    "proxy",
    "firewall",
    "in_memory",
    "persistent",

    # ========================
    # Observability
    # ========================
    "alerting",
    "tracing",
    "metrics",
    "log_aggregation",
    "distributed_tracing",

    # ========================
    # File/Storage Systems
    # ========================
    "object_storage",
    "block_storage",
    "file_storage",

    # ========================
    # AI/ML
    # ========================
    "machine_learning",
    "deep_learning",
    "nlp",
    "computer_vision",
    "recommendation_system",

    # ========================
    # UI/UX
    # ========================
    "ux_design",
    "wireframes",
    "prototyping",

    # ========================
    # Software Lifecycle
    # ========================
    "requirements",
    "design",
    "implementation",
    "testing",
    "deployment",
    "maintenance",

    # ========================
    # Operating Systems
    # ========================
    "linux",
    "windows",
    "macos"
]
