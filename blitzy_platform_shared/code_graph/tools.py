import os
from typing import List, Dict, Any

from pydantic import BaseModel, Field
from langchain_core.documents import Document
from langchain_core.tools import tool
from langchain_core.runnables import RunnableConfig
from langchain_neo4j import Neo4jVector

from blitzy_utils.logger import logger

from ..common.embeddings import voyage_3_large
from ..common.utils import clean_path
from .consts import COMMON_VECTOR_INDEX_PROPERTIES, ALT_VECTOR_INDEX_PROPERTIES, \
    IMPORT_VECTOR_INDEX_PROPERTIES, STEP_VECTOR_INDEX_PROPERTIES, METHOD_VECTOR_INDEX_PROPERTIES, \
    VectorIndexName, EMBEDDING_PROPERTY, COMMON_RETRIEVAL_QUERY, GraphNodeName, STEP_RETRIEVAL_QUERY
from .builder import CodeGraphBuilder
from .config import HYPER_K, DEFAULT_K, DEFAULT_MIN_SCORE
from .utils import get_vector_index_name

NEO4J_SERVER = os.environ["NEO4J_SERVER"]
NEO4J_USERNAME = os.environ["NEO4J_USERNAME"]
NEO4J_PASSWORD = os.environ["NEO4J_PASSWORD"]


def query_vector_index(
    index_name: str,
    node_label: str,
    query: str,
    company_id: str,
    repo_id: str,
    branch_id: str,
    head_commit_hash: str,
    retrieval_query: str = COMMON_RETRIEVAL_QUERY
) -> List[str]:
    index_properties = get_index_properties(index_name=index_name)

    vector_index_name = get_vector_index_name(
        index_name=index_name,
        repo_id=repo_id,
        branch_id=branch_id,
        head_commit_hash=head_commit_hash
    )
    logger.info(f'Executing search query {query} on vector index {index_name}')
    index = Neo4jVector.from_existing_graph(
        embedding=voyage_3_large,
        index_name=vector_index_name,
        node_label=node_label,
        text_node_properties=index_properties,
        embedding_node_property=EMBEDDING_PROPERTY,
        url=NEO4J_SERVER,
        username=NEO4J_USERNAME,
        password=NEO4J_PASSWORD,
        retrieval_query=retrieval_query
    )

    # Based on how this actually works, for the scoring logic to work correctly, we need to seach with a higher k first
    results: List[Document] = index.similarity_search(
        query=query,
        k=HYPER_K,
        params={
            "properties": index_properties,
            "min_score": DEFAULT_MIN_SCORE,
            "company_id": company_id,
            "repo_id": repo_id,
            "branch_id": branch_id
        }
    )
    search_results = []
    for result in results:
        search_results.append(result.page_content)
    logger.info(f'Retrieved {len(search_results)} results from {vector_index_name}')
    return search_results[:DEFAULT_K]


def get_index_properties(index_name: str):
    if index_name in [VectorIndexName.TYPE_DEFINITIONS.value, VectorIndexName.DECLARATIONS.value,
                      VectorIndexName.FUNCTIONS.value, VectorIndexName.CLASSES.value, VectorIndexName.EXPORTS.value,
                      VectorIndexName.SECTIONS.value]:
        return ALT_VECTOR_INDEX_PROPERTIES
    elif index_name in [VectorIndexName.EXTERNAL_IMPORTS.value, VectorIndexName.INTERNAL_IMPORTS.value]:
        return IMPORT_VECTOR_INDEX_PROPERTIES
    elif index_name == VectorIndexName.STEPS.value:
        return STEP_VECTOR_INDEX_PROPERTIES
    elif index_name == VectorIndexName.METHODS.value:
        return METHOD_VECTOR_INDEX_PROPERTIES
    else:
        return COMMON_VECTOR_INDEX_PROPERTIES


class SearchInput(BaseModel):
    query: str = Field(description="The search term to look up in a vector index "
                       "that was created using natural language names, descriptions, and summaries")
    config: RunnableConfig


class GraphFolderInput(BaseModel):
    folder_path: str = Field(description="Absolute path of the folder being retrieved")
    include_pending_changes: bool = Field(
        description=(
            "If True, this will include any file additions, deletions or updates recommended by other agents so far. "
            "If False, will only fetch the unmodified folder contents from the base branch. "
            "Use True when exploring the destination branch and False for the source branch."
        )
    )
    config: RunnableConfig


class GraphSourceFolderInput(BaseModel):
    folder_path: str = Field(description="Absolute path of the folder being retrieved")
    config: RunnableConfig


class GraphFileSummaryInput(BaseModel):
    file_path: str = Field(
        description="Absolute path of the file from the repository for which the summary is being retrieved")
    config: RunnableConfig


@tool(args_schema=SearchInput)
def search_files(
    query: str,
    config: RunnableConfig
) -> List[str]:
    """Searches through names, paths, and summaries of files across the entire repository."""
    company_id = config["configurable"]["company_id"]
    repo_id = config["configurable"]["repo_id"]
    branch_id = config["configurable"]["branch_id"]
    head_commit_hash = config["configurable"]["head_commit_hash"]
    return query_vector_index(
        index_name=VectorIndexName.FILES.value,
        node_label=GraphNodeName.FILE.value,
        company_id=company_id,
        repo_id=repo_id,
        branch_id=branch_id,
        head_commit_hash=head_commit_hash,
        query=query
    )


@tool(args_schema=SearchInput)
def search_folders(
    query: str,
    config: RunnableConfig
) -> List[str]:
    """Searches through names, paths, and summaries of folders across the entire repository.
    Each folder's summary is based on their first-order children."""
    company_id = config["configurable"]["company_id"]
    repo_id = config["configurable"]["repo_id"]
    branch_id = config["configurable"]["branch_id"]
    head_commit_hash = config["configurable"]["head_commit_hash"]
    return query_vector_index(
        index_name=VectorIndexName.FOLDERS.value,
        node_label=GraphNodeName.FOLDER.value,
        company_id=company_id,
        repo_id=repo_id,
        branch_id=branch_id,
        head_commit_hash=head_commit_hash,
        query=query
    )


@tool(args_schema=SearchInput)
def search_config_or_data_file_sections(
    query: str,
    config: RunnableConfig
) -> List[str]:
    """Searches through names, file paths, and summaries of logical sections within configuration, markup, and data files.
    Each section represents a cohesive block with a specific purpose (e.g., configuration block, template section)."""
    company_id = config["configurable"]["company_id"]
    repo_id = config["configurable"]["repo_id"]
    branch_id = config["configurable"]["branch_id"]
    head_commit_hash = config["configurable"]["head_commit_hash"]
    return query_vector_index(
        index_name=VectorIndexName.SECTIONS.value,
        node_label=GraphNodeName.SECTION.value,
        company_id=company_id,
        repo_id=repo_id,
        branch_id=branch_id,
        head_commit_hash=head_commit_hash,
        query=query
    )


@tool(args_schema=SearchInput)
def search_internal_imports(
    query: str,
    config: RunnableConfig
) -> List[str]:
    """Searches through names, file paths, and summaries of internal (same-repository) dependency relationships between files."""
    company_id = config["configurable"]["company_id"]
    repo_id = config["configurable"]["repo_id"]
    branch_id = config["configurable"]["branch_id"]
    head_commit_hash = config["configurable"]["head_commit_hash"]
    return query_vector_index(
        index_name=VectorIndexName.INTERNAL_IMPORTS.value,
        node_label=GraphNodeName.INTERNAL_IMPORT.value,
        company_id=company_id,
        repo_id=repo_id,
        branch_id=branch_id,
        head_commit_hash=head_commit_hash,
        query=query
    )


@tool(args_schema=SearchInput)
def search_external_imports(
    query: str,
    config: RunnableConfig
) -> List[str]:
    """Searches through names, file paths, and summaries of external (third-party) dependency usage across the repository."""
    company_id = config["configurable"]["company_id"]
    repo_id = config["configurable"]["repo_id"]
    branch_id = config["configurable"]["branch_id"]
    head_commit_hash = config["configurable"]["head_commit_hash"]
    return query_vector_index(
        index_name=VectorIndexName.EXTERNAL_IMPORTS.value,
        node_label=GraphNodeName.EXTERNAL_IMPORT.value,
        company_id=company_id,
        repo_id=repo_id,
        branch_id=branch_id,
        head_commit_hash=head_commit_hash,
        query=query
    )


@tool(args_schema=SearchInput)
def search_type_definitions(
    query: str,
    config: RunnableConfig
) -> List[str]:
    """Searches through names, file paths, and summaries of type definitions declared at file scope.
    Helps understand the data structures and type system used in the file. Excludes classes that are used as implementations.."""
    company_id = config["configurable"]["company_id"]
    repo_id = config["configurable"]["repo_id"]
    branch_id = config["configurable"]["branch_id"]
    head_commit_hash = config["configurable"]["head_commit_hash"]
    return query_vector_index(
        index_name=VectorIndexName.TYPE_DEFINITIONS.value,
        node_label=GraphNodeName.TYPE_DEFINITION.value,
        company_id=company_id,
        repo_id=repo_id,
        branch_id=branch_id,
        head_commit_hash=head_commit_hash,
        query=query
    )


@tool(args_schema=SearchInput)
def search_declarations(
    query: str,
    config: RunnableConfig
) -> List[str]:
    """Searches through names, file paths, and summaries of declarations at file scope, including variables,
    constants, and forward declarations. Excludes implementations and focuses on symbols that define the file's interface with other code."""
    company_id = config["configurable"]["company_id"]
    repo_id = config["configurable"]["repo_id"]
    branch_id = config["configurable"]["branch_id"]
    head_commit_hash = config["configurable"]["head_commit_hash"]
    return query_vector_index(
        index_name=VectorIndexName.DECLARATIONS.value,
        node_label=GraphNodeName.DECLARATION.value,
        company_id=company_id,
        repo_id=repo_id,
        branch_id=branch_id,
        head_commit_hash=head_commit_hash,
        query=query
    )


@tool(args_schema=SearchInput)
def search_classes(
    query: str,
    config: RunnableConfig
) -> List[str]:
    """Searches through names, file paths, and summaries of class definitions with their full context, 
    including inheritance hierarchies, properties, and classifications (e.g., abstract, final).
    Excludes class methods."""
    company_id = config["configurable"]["company_id"]
    repo_id = config["configurable"]["repo_id"]
    branch_id = config["configurable"]["branch_id"]
    head_commit_hash = config["configurable"]["head_commit_hash"]
    return query_vector_index(
        index_name=VectorIndexName.CLASSES.value,
        node_label=GraphNodeName.CLASS.value,
        company_id=company_id,
        repo_id=repo_id,
        branch_id=branch_id,
        head_commit_hash=head_commit_hash,
        query=query
    )


@tool(args_schema=SearchInput)
def search_class_methods(
    query: str,
    config: RunnableConfig
) -> List[str]:
    """Searches through names, file paths, and summaries of methods defined within classes.
    Helps understand class behaviors and how objects interact within the system. Excludes details about class definitions."""
    company_id = config["configurable"]["company_id"]
    repo_id = config["configurable"]["repo_id"]
    branch_id = config["configurable"]["branch_id"]
    head_commit_hash = config["configurable"]["head_commit_hash"]
    return query_vector_index(
        index_name=VectorIndexName.METHODS.value,
        node_label=GraphNodeName.METHOD.value,
        company_id=company_id,
        repo_id=repo_id,
        branch_id=branch_id,
        head_commit_hash=head_commit_hash,
        query=query
    )


@tool(args_schema=SearchInput)
def search_functions(
    query: str,
    config: RunnableConfig
) -> List[str]:
    """Searches through names, file paths, and summaries of function definitions at file scope.
    Excludes details about function steps."""
    company_id = config["configurable"]["company_id"]
    repo_id = config["configurable"]["repo_id"]
    branch_id = config["configurable"]["branch_id"]
    head_commit_hash = config["configurable"]["head_commit_hash"]
    return query_vector_index(
        index_name=VectorIndexName.FUNCTIONS.value,
        node_label=GraphNodeName.FUNCTION.value,
        company_id=company_id,
        repo_id=repo_id,
        branch_id=branch_id,
        head_commit_hash=head_commit_hash,
        query=query
    )


@tool(args_schema=SearchInput)
def search_function_steps(
    query: str,
    config: RunnableConfig
) -> List[str]:
    """Searches through names, file paths, and summaries of detailed implementation steps within functions.
    Helps understand specific algorithms and function behaviors at a granular level.
    Excludes details about function defintion."""
    company_id = config["configurable"]["company_id"]
    repo_id = config["configurable"]["repo_id"]
    branch_id = config["configurable"]["branch_id"]
    head_commit_hash = config["configurable"]["head_commit_hash"]
    return query_vector_index(
        index_name=VectorIndexName.STEPS.value,
        node_label=GraphNodeName.STEP.value,
        retrieval_query=STEP_RETRIEVAL_QUERY,
        company_id=company_id,
        repo_id=repo_id,
        branch_id=branch_id,
        head_commit_hash=head_commit_hash,
        query=query
    )


@tool(args_schema=SearchInput)
def search_exports(
    query: str,
    config: RunnableConfig
) -> List[str]:
    """Searches through names, file paths, and summaries of exported symbols that define each module's public interface,
    including both default and named exports. Helps understand how modules expose functionality for external consumption."""
    company_id = config["configurable"]["company_id"]
    repo_id = config["configurable"]["repo_id"]
    branch_id = config["configurable"]["branch_id"]
    head_commit_hash = config["configurable"]["head_commit_hash"]
    return query_vector_index(
        index_name=VectorIndexName.EXPORTS.value,
        node_label=GraphNodeName.EXPORT.value,
        company_id=company_id,
        repo_id=repo_id,
        branch_id=branch_id,
        head_commit_hash=head_commit_hash,
        query=query
    )


@tool(args_schema=GraphFolderInput)
def get_folder_contents(
    folder_path: str,
    include_pending_changes: bool,
    config: RunnableConfig
) -> Dict[str, Dict[str, str]]:
    """
    Returns a dictionary containing:
    - The specified folder's path and summary
    - A list of all its direct children (files and folders) with their summaries

    IMPORTANT - Repository Path Usage:
    - When include_pending_changes=False: Use SOURCE/OLD repository paths
      (explores the original repository structure before migration)
    - When include_pending_changes=True: Use DESTINATION/NEW repository paths
      (explores the new repository structure including pending changes)

    The returned structure is typically as follows:
    {
        'folder_path': <folder path>,
        'summary': <folder summary>,
        'children': [
            {
                'path': <child path>,
                'summary': <child summary>,
                'type': 'file' or 'folder' 
            },
            ...
        ]
    }

    Note the following:
    1.  Sub-folder children typically don't have summaries. You need to retrieve them separately using "get_folder_contents".
    2.  If the folder contains 'file' children that may or may not be "UPDATED", "CREATED", or "DELETED" yet,
        these items may have additional properties.
    3.  If you are retrieving a dest_folder in a new repository, the "summary" fields may not be available.
    """
    company_id: str = config["configurable"]["company_id"]
    repo_id: str = config["configurable"]["repo_id"]
    branch_id: str = config["configurable"]["branch_id"]
    graph_builder: CodeGraphBuilder = config["configurable"]["graph_builder"]
    is_new_dest_repo: bool = config["configurable"]["is_new_dest_repo"]
    file_mapping: Dict[str, List[Dict[str, Any]]] = config["configurable"]["file_mapping"]
    return _get_folder_contents(
        folder_path=folder_path,
        include_pending_changes=include_pending_changes,
        company_id=company_id,
        repo_id=repo_id,
        branch_id=branch_id,
        graph_builder=graph_builder,
        is_new_dest_repo=is_new_dest_repo,
        file_mapping=file_mapping
    )


def _get_folder_contents(
    folder_path: str,
    include_pending_changes: bool,
    company_id: str,
    repo_id: str,
    branch_id: str,
    graph_builder: CodeGraphBuilder,
    is_new_dest_repo: bool,
    file_mapping: Dict[str, List[Dict[str, Any]]]
) -> Dict[str, Dict[str, str]]:
    folder_path = clean_path(folder_path)
    logger.info(f'retrieving folder contents ({include_pending_changes}) from path: {folder_path}')
    if not include_pending_changes:
        # For source branch, return as-is
        return _get_folder_contents_from_graph(
            folder_path=folder_path,
            company_id=company_id,
            repo_id=repo_id,
            branch_id=branch_id,
            graph_builder=graph_builder
        )

    if is_new_dest_repo:
        # New repository: build from file_mapping only, no summary available
        result = {
            'folder_path': folder_path,
            'children': []
        }
    else:
        # Existing repository: start with source contents
        result = _get_folder_contents_from_graph(
            folder_path=folder_path,
            company_id=company_id,
            repo_id=repo_id,
            branch_id=branch_id,
            graph_builder=graph_builder
        )
        if not result:
            result = {
                'folder_path': folder_path,
                'children': []
            }
        # Make a copy to avoid modifying the original
        result = result.copy()
        result['children'] = result.get('children', []).copy()

    # Get files from file_mapping for this folder
    mapped_files = file_mapping.get(folder_path, [])

    # Create a dict of existing file children by path for quick lookup
    existing_children = {}
    existing_children_indices = {}
    for i, child in enumerate(result['children']):
        if child['type'] == 'file':
            existing_children[child['path']] = child
            existing_children_indices[child['path']] = i

        # Track which paths we've processed
    processed_paths = set()

    # Process mapped files
    for mapped_file in mapped_files:
        dest_path = clean_path(mapped_file['dest_path'])

        processed_paths.add(dest_path)

        # Create file entry with all properties except dest_path
        file_entry = {}
        for key, value in mapped_file.items():
            if key != 'dest_path':
                file_entry[key] = value
        file_entry['path'] = dest_path
        file_entry['type'] = 'file'

        if dest_path in existing_children:
            # Update existing child with mapped properties
            idx = existing_children_indices[dest_path]
            result['children'][idx].update(file_entry)
        else:
            # Add new file child
            result['children'].append(file_entry)

    # Find and add folder children
    # Collect all unique folder paths that are direct children of current folder
    child_folders = set()

    # Check all folders in file_mapping
    for folder_key in file_mapping.keys():
        # Check if this folder is a child of the current folder
        if folder_key.startswith(folder_path):
            # Remove the current folder path
            relative_path = clean_path(folder_key[len(folder_path):])
            # Get the first component (direct child folder)
            parts = relative_path.split('/', 1)
            if parts[0]:  # Ensure it's not empty
                child_folder_path = folder_path + '/' + parts[0]
                child_folders.add(child_folder_path)

    # Add folder children if they don't already exist
    existing_paths = {child['path'] for child in result['children']}
    for child_folder in child_folders:
        if child_folder not in existing_paths:
            folder_child = {
                'path': child_folder,
                'type': 'folder'
            }
            result['children'].append(folder_child)

    return result


@tool(args_schema=GraphSourceFolderInput)
def get_source_folder_contents(
    folder_path: str,
    config: RunnableConfig
) -> Dict[str, Dict[str, str]]:
    """
    Returns a dictionary containing:
    - The specified folder's path and summary
    - A list of all its direct children (files and folders) with their summaries

    The returned structure is typically as follows:
    {
        'folder_path': <folder path>,
        'summary': <folder summary>,
        'children': [
            {
                'path': <child path>,
                'summary': <child summary_if_type_file>,
                'type': 'file' or 'folder' 
            },
            ...
        ]
    }

    Note: Sub-folder children typically don't have summaries. You need to retrieve them separately using "get_folder_contents".
    """
    company_id = config["configurable"]["company_id"]
    repo_id = config["configurable"]["repo_id"]
    branch_id = config["configurable"]["branch_id"]
    graph_builder = config["configurable"]["graph_builder"]
    return _get_source_folder_contents(
        folder_path=folder_path,
        company_id=company_id,
        repo_id=repo_id,
        branch_id=branch_id,
        graph_builder=graph_builder
    )


def _get_source_folder_contents(
    folder_path: str,
    company_id: str,
    repo_id: str,
    branch_id: str,
    graph_builder: CodeGraphBuilder
) -> Dict[str, Dict[str, str]]:
    folder_path = clean_path(folder_path)
    logger.info(f'retrieving folder contents from path: {folder_path}')
    return _get_folder_contents_from_graph(
        folder_path=folder_path,
        company_id=company_id,
        repo_id=repo_id,
        branch_id=branch_id,
        graph_builder=graph_builder
    )


# This is a WAR till we have a graph service
_folder_contents_cache = {}


def _get_folder_contents_from_graph(
    folder_path: str,
    company_id: str,
    repo_id: str,
    branch_id: str,
    graph_builder: CodeGraphBuilder
):
    # Create a cache key from all parameters
    cache_key = (folder_path, company_id, repo_id, branch_id)

    # Check if result exists in cache
    if cache_key in _folder_contents_cache:
        logger.debug(f'Cache hit for folder contents: {folder_path}')
        return _folder_contents_cache[cache_key]

    # If not in cache, retrieve from graph builder
    logger.debug(f'Cache miss for folder contents: {folder_path}')
    result = graph_builder.get_folder_contents(
        folder_path=folder_path,
        company_id=company_id,
        repo_id=repo_id,
        branch_id=branch_id
    )

    # Store in cache
    _folder_contents_cache[cache_key] = result

    return result


@tool(args_schema=GraphFileSummaryInput)
def get_file_summary(
    file_path: str,
    config: RunnableConfig
) -> Dict[str, Dict[str, str]]:
    """
    Returns a dictionary containing:
    - The specified file's path and summary

    Useful to learn about a file's contents and purpose efficiently without retrieveving its full contents.

    The returned structure is:
    {
        'file_path': <file path>,
        'summary': <file summary>
    }
    """
    company_id = config["configurable"]["company_id"]
    repo_id = config["configurable"]["repo_id"]
    branch_id = config["configurable"]["branch_id"]
    graph_builder = config["configurable"]["graph_builder"]
    return _get_file_summary(
        file_path=file_path,
        company_id=company_id,
        repo_id=repo_id,
        branch_id=branch_id,
        graph_builder=graph_builder
    )


def _get_file_summary(
    file_path: str,
    company_id: str,
    repo_id: str,
    branch_id: str,
    graph_builder: CodeGraphBuilder
):
    file_path = clean_path(path=file_path)
    logger.info(f'retrieving file summary from path: {file_path}')
    return graph_builder.get_file_summary(
        file_path=file_path,
        company_id=company_id,
        repo_id=repo_id,
        branch_id=branch_id
    )
