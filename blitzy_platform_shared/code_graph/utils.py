from pydantic import BaseModel, create_model
from typing import Type, List

from .common import CODE_GRAPH_SOURCE_ADJACENT_FILES, CODE_GRAPH_SOURCE_FILES


def create_single_field_model(
    base_model: Type[BaseModel],
    new_model_name: str,
    field_to_keep: str
) -> Type[BaseModel]:
    """
    Creates a new Pydantic model with a single field from an existing model.

    Args:
        base_model: The original Pydantic model class
        new_model_name: Name for the new model
        field_to_keep: Name of the field to retain from the original model

    Returns:
        A new Pydantic model class with only the specified field

    Raises:
        KeyError: If field_to_keep doesn't exist in the base model
    """
    if field_to_keep not in base_model.model_fields:
        raise KeyError(f"Field '{field_to_keep}' not found in {base_model.__name__}")

    field_def = base_model.model_fields[field_to_keep]

    return create_model(
        new_model_name,
        **{
            field_to_keep: (
                field_def.annotation,
                field_def.default_factory
            )
        }
    )


def is_source_adjacent_file(file_path: str):
    return test_file_type(
        file_path=file_path,
        types_list=CODE_GRAPH_SOURCE_ADJACENT_FILES
    )


def is_source_file(file_path: str):
    return test_file_type(
        file_path=file_path,
        types_list=CODE_GRAPH_SOURCE_FILES
    )


def test_file_type(file_path: str, types_list: List[str]):
    file_path_parts = file_path.split('/')
    file_name_parts = file_path_parts[-1].split('.')

    # Handle hidden files that start with a dot
    if file_name_parts[0] == '':
        file_name_parts = file_name_parts[1:]

    return file_name_parts[-1].lower() in types_list


def get_vector_index_name(
    repo_id: str,
    branch_id: str,
    head_commit_hash: str,
    index_name: str
) -> str:
    return f"{index_name}_{repo_id}_{branch_id}_{head_commit_hash}".replace('-', '_')


def get_parent_folder_path(folder_path: str) -> str:
    """
    Get the parent path of a folder path

    Args:
        folder_path: Path of the folder

    Returns:
        Parent folder path, or empty string for root-level folders
    """
    if folder_path == "":
        return None

    if "/" not in folder_path:
        return ""  # The parent of the root or a top-level folder is the root

    path_parts = folder_path.split('/')
    return '/'.join(path_parts[:-1])
