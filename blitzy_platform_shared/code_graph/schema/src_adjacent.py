from pydantic import BaseModel, Field, ConfigDict
from typing import List, Dict, Optional
from enum import Enum

from blitzy_platform_shared.code_graph.schema.functions import References
from blitzy_platform_shared.code_graph.schema.summary import FileSummary


class SectionType(str, Enum):
    """Describes the primary type/purpose of a section"""
    METADATA = "metadata"           # File metadata, headers, frontmatter
    CONFIGURATION = "configuration"  # Config settings, environment vars
    CONTENT = "content"            # Primary content/text
    TEMPLATE = "template"          # Template logic/markup
    STATEMENT = "statement"        # SQL/GraphQL statements
    COMMENT = "comment"           # Documentation blocks
    DIRECTIVE = "directive"       # Special instructions (e.g., in .gitignore)


class ContentFormat(str, Enum):
    """Describes the format of the section content"""
    PLAIN = "plain"          # Plain text
    STRUCTURED = "structured"  # JSON/YAML-like
    MARKUP = "markup"        # Markdown/HTML/etc
    EXPRESSION = "expression"  # Template expressions
    QUERY = "query"          # Database queries
    PATTERN = "pattern"      # Patterns (e.g., glob patterns)


class Section(BaseModel):
    """
    Represents a logical section within a source-adjacent file, with enhanced 
    type information and formatting details.
    """
    model_config = ConfigDict(extra='forbid')

    name: str = Field(
        ...,
        description="Descriptive name identifying this section's purpose within the file"
    )

    section_type: SectionType = Field(
        ...,
        description="Categorizes the primary purpose/type of this section, helping understand its role in the file"
    )

    format: ContentFormat = Field(
        ...,
        description="Specifies the format/structure of the content, guiding how it should be parsed and interpreted"
    )

    content: List[str] = Field(
        ...,
        description="Raw content lines for this section, preserved in their original format for accurate reproduction"
    )

    summary: str = Field(
        ...,
        description="IMPORTANT: Must never be blank. Natural language summary of the section's purpose and contents, useful for LLM understanding"
    )


class CodeGraphSourceAdjacentSchema(BaseModel):
    """
    A comprehensive schema for analyzing and representing source-adjacent files such as 
    configuration, markup, template, and other non-primary source code files. This model 
    captures the complete static analysis of these files, breaking them down into logical 
    sections while preserving their structure and relationships.
    """

    model_config = ConfigDict(extra='forbid')

    path: str = Field(
        ...,
        description="Absolute filesystem path to the source-adjacent file, used for unique identification and resolving relative references"
    )

    references: References = Field(
        ...,
        description="Collection of external and internal references found within this file, including file imports, environment variables, external resources, and cross-file dependencies that this file relies on or defines"
    )

    sections: List[Section] = Field(
        ...,
        description="Ordered list of logical sections that comprise the file's content, where each section represents a cohesive block of content with a specific purpose (e.g., configuration block, template section, documentation). Sections preserve the original file's structure while providing additional semantic context"
    )

    summary: FileSummary = Field(
        ...,
        description="IMPORTANT: Must never be blank. Comprehensive natural language summary describing the file's primary purpose, its role within the project, key configurations or content it contains, and how it relates to other project files. This helps establish context for both humans and LLMs analyzing the codebase"
    )
