from typing import Dict, List, Any

from pydantic import BaseModel, Field
from langchain_core.tools import tool
from langchain_core.runnables import RunnableConfig

from blitzy_utils.common import DictFileHelper
from blitzy_utils.logger import logger
from blitzy_utils.github import download_single_file

from .utils import get_fuzzy_path, read_range
from .consts import GITHUB_FILE_RETRIEVAL_ERROR

dict_helper: DictFileHelper = DictFileHelper(files={})


def set_dict_helper_files(files: Dict[str, Any]):
    dict_helper.set_dict(files=files)


@tool("get_file_specification", parse_docstring=True)
def get_file_specification(imported_file_path: str, assigned_file_path: str):
    """
        Retrieves a JSON specification of a potential dependency file or import from a GitHub repository.
        Useful for understanding the schema, structure and imports of potential dependency files, when those file paths are queried using imported_file_path.

        Args:
            imported_file_path: A file path in the GitHub repo which might be imported inside the assigned_file_path file.
            assigned_file_path: The file path from the GitHub repo that you are currently investigating, which might import file_path file.
    """
    if imported_file_path == assigned_file_path:
        # print(f'tool get_file_specification invoked with the same arguments: {imported_file_path}')
        return ""
    files_dict = dict_helper.get_dict()
    file = files_dict.get(imported_file_path)
    if file:
        print(f'retrieved existing file specification: {imported_file_path}')
        # print(file)
        if imported_file_path in dict_helper.seen_pending_files:
            dict_helper.add_pending_file(file_path=assigned_file_path)
            # print(f'file {imported_file_path} is in pending files, will add {assigned_file_path} to pending files')
        return file
    dict_helper.add_pending_file(file_path=assigned_file_path)
    print(f'file specfication does not exist yet: {imported_file_path}')
    return ""


class ThinkInput(BaseModel):
    thought: str = Field(description="A thought to think about.")


@tool(args_schema=ThinkInput)
def think(thought: str):
    """
    Always use this tool before taking any action to think about something, when complex reasoning or brainstorming is needed. It will not obtain new information or make any changes to the repository, but just log the thought.
    For example,
     - If you need to decide which document section, folder or file contents to retrieve, use this tool to help identify which item is most relevant to your task at hand, and to ensure that you don't miss any relevant items.
     - If you explore the repo and retrieve a file, call this tool to brainstorm the content of the file, and decide if any of the functions, types and interfaces reflected are relevant, and how to use them.
     - Alternatively, if you retrieve a section of the document, call this tool to brainstorm the requirements and technical details in this section, and how to process it effectively to solve your assigned task.
    """
    logger.info(f'Using the think tool to log thoughts: {thought}')
    return "Thought captured successfully."


class MappedFilesInput(BaseModel):
    folder_path: str = Field(description="Absolute path of the mapped folder for which file paths are being retrieved")
    config: RunnableConfig


@tool(args_schema=MappedFilesInput)
def get_mapped_files_so_far(
    folder_path: str,
    config: RunnableConfig
) -> List[Dict[str, Any]]:
    """
    Retrieves a list of all files from the destination repository mapped so far for a given folder path.
    """
    logger.info(f'received request to list folders for path: {folder_path}')
    file_mapping = config["configurable"]["file_mapping"]
    potential_paths = list(file_mapping.keys())
    folder_path = get_fuzzy_path(
        path=folder_path,
        potential_paths=potential_paths
    )

    ret = {
        "folder_path": folder_path,
        "folder_contents": file_mapping.get(folder_path, [])
    }
    return ret


class GraphFileInput(BaseModel):
    file_path: str = Field(description="Absolute path of the file being retrieved "
                           "from the repository")
    view_range: List[int] = Field(description="An array of two integers specifying the start and end line numbers to view. "
                                  "Line numbers are 1-indexed, inclusive, and -1 for the end line means read to the end of the file. "
                                  "For example, use [1, -1] to read the entire file")
    config: RunnableConfig


@tool(args_schema=GraphFileInput)
def get_file_contents(
    file_path: str,
    config: RunnableConfig,
    view_range: List[int] = [1, -1]
) -> str:
    """
    Returns the source code contents of the file at the given absolute path from the repository
    """
    repo_name = config["configurable"]["repo_name"]
    repo_id = config["configurable"]["repo_id"]
    user_id = config["configurable"]["user_id"]
    head_commit_hash = config["configurable"]["head_commit_hash"]
    github_server = config["configurable"]["github_server"]
    git_project_repo_id = config["configurable"]["git_project_repo_id"]

    try:
        decoded_content = download_single_file(
            repo_name=repo_name,
            user_id=user_id,
            server=github_server,
            commit_hash=head_commit_hash,
            file_path=file_path,
            repo_id=repo_id,
            git_project_repo_id=git_project_repo_id
        )

        return read_range(
            view_range=view_range,
            file_text=decoded_content
        )
    except ValueError:
        return GITHUB_FILE_RETRIEVAL_ERROR


ANTHROPIC_WEB_SEARCH_TOOL_NAME = "web_search"

ANTHROPIC_WEB_SEARCH_TOOL_DEFINITION = {
    "type": "web_search_20250305",
    "name": ANTHROPIC_WEB_SEARCH_TOOL_NAME
}
