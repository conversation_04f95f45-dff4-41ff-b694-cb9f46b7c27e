import json
from typing import List, Literal

from google.cloud.storage import C<PERSON> as StorageClient
from pydantic import BaseModel, Field
from langchain_core.tools import tool
from langchain_core.runnables import RunnableConfig

from blitzy_utils.logger import logger
from blitzy_utils.common import download_from_gcs, upload_to_gcs

from ..common.utils import clean_path
from ..code_generation.models import CreatedFile, UpdatedFile

from .models import FileSchemaType, ThinkingInternalImport, ThinkingInternalImportUniqueKey, ThinkingExternalImport, \
    ThinkingExternalImportUniqueKey, ThinkingExport, ThinkingExportUniqueKey
from .utils import get_property, set_property, will_create_circular_dependency
from .consts import FILE_ALREADY_EXISTS, FILE_ERROR_RESPONSE, FILE_PATH_NOT_FOUND, CREATE_FILE_SUFFIX, \
    SOURCE_FILE_ERROR_RESPONSE, CIRCULAR_DEPENDENCY_ERROR, ITEM_ADDED_RESPONSE, ITEM_NOT_FOUND, ITEM_REMOVED_RESPONSE


UNCHANGED_FILE_TOOL_NAME = "mark_file_unchanged"
ANTHROPIC_TEXT_EDITOR_TOOL_NAME = "str_replace_based_edit_tool"
ANTHROPIC_BASH_TOOL_NAME = "bash"

ANTHROPIC_TEXT_EDITOR_TOOL_DEFINITION = {
    "type": "text_editor_20250429",
    "name": ANTHROPIC_TEXT_EDITOR_TOOL_NAME
}

ANTHROPIC_BASH_TOOL_DEFINITION = {
    "type": "bash_20250124",
    "name": ANTHROPIC_BASH_TOOL_NAME
}

FILE_COMPLETE_TOOL_NAME = "mark_file_complete"


class GetItemInput(BaseModel):
    file_path: str = Field(
        description="Absolute path of the target file"
    )
    config: RunnableConfig


def download_file_schemas(
    storage_client: StorageClient,
    bucket_name: str,
    blob_name: str,
    filename: str
) -> FileSchemaType:
    file_schemas: FileSchemaType = json.loads(download_from_gcs(
        storage_client=storage_client,
        bucket_name=bucket_name,
        blob_name=blob_name,
        filename=filename
    ))
    return file_schemas


def upload_file_schemas(
    storage_client,
    bucket_name: str,
    blob_name: str,
    filename: str,
    file_schemas: FileSchemaType
):
    upload_to_gcs(
        storage_client=storage_client,
        bucket_name=bucket_name,
        blob_name=blob_name,
        filename=filename,
        content_type='application/json',
        data=json.dumps(file_schemas)
    )


@tool(args_schema=GetItemInput)
def get_file_schema(
    file_path: str,
    config: RunnableConfig
) -> str:
    """
    Fetches the complete schema of a pending file including key_changes, internal_imports, external_imports, exports, and so on.
    Useful when you want to retrieve details about file changes that are not available from other more specific tools for each property.
    """
    file_path = clean_path(file_path)
    logger.info(f'Retrieving file schema for file: {file_path}')
    storage_client = config["configurable"]["storage_client"]
    bucket_name = config["configurable"]["bucket_name"]
    blob_name = config["configurable"]["blob_name"]
    file_schemas_filename = config["configurable"]["file_schemas_filename"]
    file_schemas: FileSchemaType = download_file_schemas(
        storage_client=storage_client,
        bucket_name=bucket_name,
        blob_name=blob_name,
        filename=file_schemas_filename
    )
    file_schema = file_schemas.get(file_path)
    if not file_schema:
        logger.warning(f'File schema not found: {file_path}')
        return FILE_ERROR_RESPONSE.format(file_path=file_path, message=FILE_PATH_NOT_FOUND)
    return json.dumps(file_schema)


@tool(args_schema=GetItemInput)
def get_internal_imports(
    file_path: str,
    config: RunnableConfig
) -> str:
    """
    Fetches the new internal imports for an UPDATED file and the full set of internal imports for a CREATED file.
    Useful for identifying repository level local dependencies for a given file.
    """
    file_path = clean_path(file_path)
    logger.info(f'Retrieving internal imports for file: {file_path}')
    storage_client = config["configurable"]["storage_client"]
    bucket_name = config["configurable"]["bucket_name"]
    blob_name = config["configurable"]["blob_name"]
    file_schemas_filename = config["configurable"]["file_schemas_filename"]
    file_schemas: FileSchemaType = download_file_schemas(
        storage_client=storage_client,
        bucket_name=bucket_name,
        blob_name=blob_name,
        filename=file_schemas_filename
    )
    return json.dumps(
        get_property(
            file_path=file_path,
            file_schemas=file_schemas,
            updated_file_property='new_internal_imports',
            default_property='internal_imports'
        )
    )


class AddUpdateInternalImportInput(GetItemInput, ThinkingInternalImport):
    pass


@tool(args_schema=AddUpdateInternalImportInput)
def add_or_update_internal_import(
    file_path: str,
    source_file_path: str,
    is_compiled: bool,
    name: str,
    purpose: str,
    category: str,
    members_accessed: List[str],
    config: RunnableConfig
):
    """
    Adds a new internal import or updates an existing one for a file.
    Internal imports are from modules within the codebase/repository. Will not add or update if it detects a circular dependency.
    """
    file_path = clean_path(file_path)
    source_file_path = clean_path(source_file_path)
    logger.info(f'Adding internal import for file: {file_path}, source file: {source_file_path}')
    storage_client = config["configurable"]["storage_client"]
    bucket_name = config["configurable"]["bucket_name"]
    blob_name = config["configurable"]["blob_name"]
    file_schemas_filename = config["configurable"]["file_schemas_filename"]
    file_schemas: FileSchemaType = download_file_schemas(
        storage_client=storage_client,
        bucket_name=bucket_name,
        blob_name=blob_name,
        filename=file_schemas_filename
    )
    internal_imports: List[ThinkingInternalImport] = get_property(
        file_path=file_path,
        file_schemas=file_schemas,
        updated_file_property='new_internal_imports',
        default_property='internal_imports'
    )
    if FILE_PATH_NOT_FOUND in internal_imports:
        logger.warning(
            f'Attemped to add an internal import {source_file_path} for a file that does not exist: {file_path}')
        return FILE_ERROR_RESPONSE.format(
            file_path=file_path,
            message=FILE_PATH_NOT_FOUND
        )

    source_file_imports: List[ThinkingInternalImport] = get_property(
        file_path=source_file_path,
        file_schemas=file_schemas,
        updated_file_property='new_internal_imports',
        default_property='internal_imports'
    )
    if FILE_PATH_NOT_FOUND in source_file_imports:
        logger.warning(
            f'Attemped to add an internal import {source_file_path} for {file_path}, but {source_file_path} does not exist')
        return SOURCE_FILE_ERROR_RESPONSE.format(
            source_file_path=source_file_path,
            file_path=file_path,
            suffix=CREATE_FILE_SUFFIX
        )

    # Check for circular dependencies using DFS
    if will_create_circular_dependency(file_path=file_path, source_file_path=source_file_path, file_schemas=file_schemas):
        logger.warning(
            f'Circular dependency detected: importing {source_file_path} into {file_path} would create a cycle')
        return FILE_ERROR_RESPONSE.format(
            file_path=file_path,
            message=CIRCULAR_DEPENDENCY_ERROR.format(
                first_file=file_path,
                second_file=source_file_path
            )
        )

    # Find existing import
    existing_import = None
    for imported_item in internal_imports:
        if imported_item["source_file_path"] == source_file_path:
            existing_import = imported_item
            break

    if existing_import == None:
        # Create new import instance and append
        new_import = ThinkingInternalImport(
            source_file_path=source_file_path,
            name=name,
            purpose=purpose,
            category=category,
            members_accessed=members_accessed,
            is_compiled=is_compiled
        )
        internal_imports.append(new_import.model_dump(mode='json'))
    else:
        # Update existing import through attributes
        existing_import["source_file_path"] = source_file_path
        existing_import["name"] = name
        existing_import["purpose"] = purpose
        existing_import["category"] = category
        existing_import["members_accessed"] = members_accessed
        existing_import["is_compiled"] = is_compiled

    file_schemas = set_property(
        file_path=file_path,
        file_schemas=file_schemas,
        updated_file_property='new_internal_imports',
        default_property='internal_imports',
        value=internal_imports
    )

    # correct depends_on_files
    file_schemas = rebuild_internal_dependencies(
        file_path=file_path,
        internal_imports=internal_imports,
        file_schemas=file_schemas
    )

    upload_file_schemas(
        storage_client=storage_client,
        bucket_name=bucket_name,
        blob_name=blob_name,
        filename=file_schemas_filename,
        file_schemas=file_schemas
    )

    return ITEM_ADDED_RESPONSE.format(item="Internal import", file_path=file_path)


def rebuild_internal_dependencies(
    file_path: str,
    internal_imports: List[ThinkingInternalImport],
    file_schemas: FileSchemaType
):
    depends_on_files = []
    for internal_import in internal_imports:
        depends_on_files.append(internal_import["source_file_path"])
    file_schemas = set_property(
        file_path=file_path,
        file_schemas=file_schemas,
        updated_file_property='depends_on_files',
        default_property='depends_on_files',
        value=depends_on_files
    )
    return file_schemas


class RemoveInternalImportInput(GetItemInput, ThinkingInternalImportUniqueKey):
    pass


@tool(args_schema=RemoveInternalImportInput)
def remove_internal_import(
    file_path: str,
    source_file_path: str,
    config: RunnableConfig
):
    """
    Removes an internal import from a file.
    Use this to clean up unused dependencies or resolve circular dependency issues.
    """
    file_path = clean_path(file_path)
    source_file_path = clean_path(source_file_path)
    logger.info(f'Removing internal import for file: {file_path}, source file: {source_file_path}')
    storage_client = config["configurable"]["storage_client"]
    bucket_name = config["configurable"]["bucket_name"]
    blob_name = config["configurable"]["blob_name"]
    file_schemas_filename = config["configurable"]["file_schemas_filename"]
    file_schemas: FileSchemaType = download_file_schemas(
        storage_client=storage_client,
        bucket_name=bucket_name,
        blob_name=blob_name,
        filename=file_schemas_filename
    )
    internal_imports: List[ThinkingInternalImport] = get_property(
        file_path=file_path,
        file_schemas=file_schemas,
        updated_file_property='new_internal_imports',
        default_property='internal_imports'
    )
    if FILE_PATH_NOT_FOUND in internal_imports:
        logger.warning(
            f'Attemped to remove an internal import {source_file_path} for a file that does not exist: {file_path}')
        return FILE_ERROR_RESPONSE.format(
            file_path=file_path,
            message=FILE_PATH_NOT_FOUND
        )

    # Find and remove by index
    for i, imported_item in enumerate(internal_imports):
        if imported_item["source_file_path"] == source_file_path:
            internal_imports.pop(i)

            file_schemas = set_property(
                file_path=file_path,
                file_schemas=file_schemas,
                updated_file_property='new_internal_imports',
                default_property='internal_imports',
                value=internal_imports
            )

            file_schemas = rebuild_internal_dependencies(
                file_path=file_path,
                internal_imports=internal_imports,
                file_schemas=file_schemas
            )

            upload_file_schemas(
                storage_client=storage_client,
                bucket_name=bucket_name,
                blob_name=blob_name,
                filename=file_schemas_filename,
                file_schemas=file_schemas
            )

            return ITEM_REMOVED_RESPONSE.format(
                item="Internal import",
                value=f"{source_file_path}",
                file_path=f"{file_path}"
            )
    logger.warning(f'Attemped to remove an internal import for {source_file_path} that does not exist in {file_path}')
    # Not found
    return FILE_ERROR_RESPONSE.format(
        file_path=file_path,
        message=ITEM_NOT_FOUND.format(
            item="Internal import",
            key="source_file_path",
            value=f"{source_file_path}",
            file_path=file_path
        )
    )


@tool(args_schema=GetItemInput)
def get_external_imports(
    file_path: str,
    config: RunnableConfig
) -> str:
    """
    Fetches the new external imports for an UPDATED file and the full set of external imports for a CREATED file.
    Useful for identifying third party, open-source, or built-in package dependencies for a given file.
    """
    file_path = clean_path(file_path)
    logger.info(f'Retrieving external imports for file: {file_path}')
    storage_client = config["configurable"]["storage_client"]
    bucket_name = config["configurable"]["bucket_name"]
    blob_name = config["configurable"]["blob_name"]
    file_schemas_filename = config["configurable"]["file_schemas_filename"]
    file_schemas: FileSchemaType = download_file_schemas(
        storage_client=storage_client,
        bucket_name=bucket_name,
        blob_name=blob_name,
        filename=file_schemas_filename
    )
    return json.dumps(
        get_property(
            file_path=file_path,
            file_schemas=file_schemas,
            updated_file_property='new_external_imports',
            default_property='external_imports'
        )
    )


class AddUpdateExternalImportInput(GetItemInput, ThinkingExternalImport):
    pass


@tool(args_schema=AddUpdateExternalImportInput)
def add_or_update_external_import(
    file_path: str,
    package_name: str,
    package_version: str,
    package_registry: str,
    is_dev_dependency: bool,
    name: str,
    purpose: str,
    category: str,
    members_accessed: List[str],
    config: RunnableConfig
):
    """
    Adds a new external import or updates an existing one for a file.
    External imports are third-party packages, standard library modules, or external dependencies.
    """
    file_path = clean_path(file_path)
    logger.info(f'Adding external import for file: {file_path}')
    storage_client = config["configurable"]["storage_client"]
    bucket_name = config["configurable"]["bucket_name"]
    blob_name = config["configurable"]["blob_name"]
    file_schemas_filename = config["configurable"]["file_schemas_filename"]
    file_schemas: FileSchemaType = download_file_schemas(
        storage_client=storage_client,
        bucket_name=bucket_name,
        blob_name=blob_name,
        filename=file_schemas_filename
    )
    external_imports: List[ThinkingExternalImport] = get_property(
        file_path=file_path,
        file_schemas=file_schemas,
        updated_file_property='new_external_imports',
        default_property='external_imports'
    )
    if FILE_PATH_NOT_FOUND in external_imports:
        logger.warning(f'Attemped to add an external import {package_name} for a file that does not exist: {file_path}')
        return FILE_ERROR_RESPONSE.format(
            file_path=file_path,
            message=FILE_PATH_NOT_FOUND
        )

    # Find existing import
    existing_import = None
    for imported_item in external_imports:
        if imported_item["package_name"] == package_name:
            existing_import = imported_item
            break

    if existing_import == None:
        # Create new import instance and append
        new_import = ThinkingExternalImport(
            package_name=package_name,
            package_version=package_version,
            package_registry=package_registry,
            is_dev_dependency=is_dev_dependency,
            name=name,
            purpose=purpose,
            category=category,
            members_accessed=members_accessed
        )
        external_imports.append(new_import.model_dump(mode='json'))
    else:
        # Update existing import through attributes
        existing_import["package_name"] = package_name
        existing_import["package_version"] = package_version
        existing_import["package_registry"] = package_registry
        existing_import["is_dev_dependency"] = is_dev_dependency
        existing_import["name"] = name
        existing_import["purpose"] = purpose
        existing_import["category"] = category
        existing_import["members_accessed"] = members_accessed

    file_schemas = set_property(
        file_path=file_path,
        file_schemas=file_schemas,
        updated_file_property='new_external_imports',
        default_property='external_imports',
        value=external_imports
    )

    upload_file_schemas(
        storage_client=storage_client,
        bucket_name=bucket_name,
        blob_name=blob_name,
        filename=file_schemas_filename,
        file_schemas=file_schemas
    )

    return ITEM_ADDED_RESPONSE.format(item="External import", file_path=file_path)


class RemoveExternalImportInput(GetItemInput, ThinkingExternalImportUniqueKey):
    pass


@tool(args_schema=RemoveExternalImportInput)
def remove_external_import(
    file_path: str,
    package_name: str,
    config: RunnableConfig
):
    """
    Removes an external import from a file.
    Use this to clean up unused third-party dependencies or standard library imports.
    """
    file_path = clean_path(file_path)
    logger.info(f'Removing external import for file: {file_path}')
    storage_client = config["configurable"]["storage_client"]
    bucket_name = config["configurable"]["bucket_name"]
    blob_name = config["configurable"]["blob_name"]
    file_schemas_filename = config["configurable"]["file_schemas_filename"]
    file_schemas: FileSchemaType = download_file_schemas(
        storage_client=storage_client,
        bucket_name=bucket_name,
        blob_name=blob_name,
        filename=file_schemas_filename
    )
    external_imports: List[ThinkingExternalImport] = get_property(
        file_path=file_path,
        file_schemas=file_schemas,
        updated_file_property='new_external_imports',
        default_property='external_imports'
    )
    if FILE_PATH_NOT_FOUND in external_imports:
        logger.warning(
            f'Attemped to remove an external import {package_name} for a file that does not exist: {file_path}')
        return FILE_ERROR_RESPONSE.format(
            file_path=file_path,
            message=FILE_PATH_NOT_FOUND
        )

    # Find and remove by index
    for i, imported_item in enumerate(external_imports):
        if imported_item["package_name"] == package_name:
            external_imports.pop(i)

            file_schemas = set_property(
                file_path=file_path,
                file_schemas=file_schemas,
                updated_file_property='new_external_imports',
                default_property='external_imports',
                value=external_imports
            )

            upload_file_schemas(
                storage_client=storage_client,
                bucket_name=bucket_name,
                blob_name=blob_name,
                filename=file_schemas_filename,
                file_schemas=file_schemas
            )

            return ITEM_REMOVED_RESPONSE.format(
                item="External import",
                value=f"{package_name}",
                file_path=f"{file_path}"
            )

    logger.warning(f'Attemped to remove an external import for {package_name} that does not exist in {file_path}')
    # Not found
    return FILE_ERROR_RESPONSE.format(
        file_path=file_path,
        message=ITEM_NOT_FOUND.format(
            item="External import",
            key="package_name",
            value=f"{package_name}",
            file_path=file_path
        )
    )


@tool(args_schema=GetItemInput)
def get_exports(
    file_path: str,
    config: RunnableConfig
) -> str:
    """
    Fetches the new exports for an UPDATED file and the full set of exports for a CREATED file.
    Useful for identifying items contained in and exported by a given file.
    """
    file_path = clean_path(file_path)
    logger.info(f'Retrieving exports for file: {file_path}')
    storage_client = config["configurable"]["storage_client"]
    bucket_name = config["configurable"]["bucket_name"]
    blob_name = config["configurable"]["blob_name"]
    file_schemas_filename = config["configurable"]["file_schemas_filename"]
    file_schemas: FileSchemaType = download_file_schemas(
        storage_client=storage_client,
        bucket_name=bucket_name,
        blob_name=blob_name,
        filename=file_schemas_filename
    )
    return json.dumps(
        get_property(
            file_path=file_path,
            file_schemas=file_schemas,
            updated_file_property='new_exports',
            default_property='exports'
        )
    )


class AddUpdateExportInput(GetItemInput, ThinkingExport):
    pass


@tool(args_schema=AddUpdateExportInput)
def add_or_update_export(
    file_path: str,
    name: str,
    kind: str,
    members_exposed: List[str],
    is_default: bool,
    config: RunnableConfig
):
    """
    Adds a new export or updates an existing one for a file.
    Exports define the module's public API that other modules can import.
    """
    file_path = clean_path(file_path)
    logger.info(f'Adding export for file: {file_path}')
    storage_client = config["configurable"]["storage_client"]
    bucket_name = config["configurable"]["bucket_name"]
    blob_name = config["configurable"]["blob_name"]
    file_schemas_filename = config["configurable"]["file_schemas_filename"]
    file_schemas: FileSchemaType = download_file_schemas(
        storage_client=storage_client,
        bucket_name=bucket_name,
        blob_name=blob_name,
        filename=file_schemas_filename
    )
    exports: List[ThinkingExport] = get_property(
        file_path=file_path,
        file_schemas=file_schemas,
        updated_file_property='new_exports',
        default_property='exports'
    )
    if FILE_PATH_NOT_FOUND in exports:
        logger.warning(f'Attemped to add an export {name} for a file that does not exist: {file_path}')
        return FILE_ERROR_RESPONSE.format(
            file_path=file_path,
            message=FILE_PATH_NOT_FOUND
        )

    # Find existing export
    existing_export = None
    for exported_item in exports:
        if exported_item["name"] == name:
            existing_export = exported_item
            break

    if existing_export == None:
        # Create new export instance and append
        new_export = ThinkingExport(
            name=name,
            kind=kind,
            members_exposed=members_exposed,
            is_default=is_default
        )
        exports.append(new_export.model_dump(mode='json'))
    else:
        # Update existing import through attributes
        existing_export["name"] = name
        existing_export["kind"] = kind
        existing_export["members_exposed"] = members_exposed
        existing_export["is_default"] = is_default

    file_schemas = set_property(
        file_path=file_path,
        file_schemas=file_schemas,
        updated_file_property='new_exports',
        default_property='exports',
        value=exports
    )

    upload_file_schemas(
        storage_client=storage_client,
        bucket_name=bucket_name,
        blob_name=blob_name,
        filename=file_schemas_filename,
        file_schemas=file_schemas
    )

    return ITEM_ADDED_RESPONSE.format(item="Export", file_path=file_path)


class RemoveExportInput(GetItemInput, ThinkingExportUniqueKey):
    pass


@tool(args_schema=RemoveExportInput)
def remove_export(
    file_path: str,
    name: str,
    config: RunnableConfig
):
    """
    Removes an export from a file.
    Use this to clean up the module's public API by removing unused or deprecated exports.
    """
    file_path = clean_path(file_path)
    logger.info(f'Removing export for file: {file_path}')
    storage_client = config["configurable"]["storage_client"]
    bucket_name = config["configurable"]["bucket_name"]
    blob_name = config["configurable"]["blob_name"]
    file_schemas_filename = config["configurable"]["file_schemas_filename"]
    file_schemas: FileSchemaType = download_file_schemas(
        storage_client=storage_client,
        bucket_name=bucket_name,
        blob_name=blob_name,
        filename=file_schemas_filename
    )
    exports: List[ThinkingExport] = get_property(
        file_path=file_path,
        file_schemas=file_schemas,
        updated_file_property='new_exports',
        default_property='exports'
    )
    if FILE_PATH_NOT_FOUND in exports:
        logger.warning(f'Attemped to remove an export {name} for a file that does not exist: {file_path}')
        return FILE_ERROR_RESPONSE.format(
            file_path=file_path,
            message=FILE_PATH_NOT_FOUND
        )

    # Find and remove by index
    for i, exported_item in enumerate(exports):
        if exported_item["name"] == name:
            exports.pop(i)

            file_schemas = set_property(
                file_path=file_path,
                file_schemas=file_schemas,
                updated_file_property='new_exports',
                default_property='exports',
                value=exports
            )

            upload_file_schemas(
                storage_client=storage_client,
                bucket_name=bucket_name,
                blob_name=blob_name,
                filename=file_schemas_filename,
                file_schemas=file_schemas
            )

            return ITEM_REMOVED_RESPONSE.format(
                item="Export",
                value=f"{name}",
                file_path=f"{file_path}"
            )

    logger.warning(f'Attemped to remove an export for {name} that does not exist in {file_path}')
    # Not found
    return FILE_ERROR_RESPONSE.format(
        file_path=file_path,
        message=ITEM_NOT_FOUND.format(
            item="Export",
            key="name",
            value=f"{name}",
            file_path=file_path
        )
    )


class AddCreatedFileInput(CreatedFile):
    status: Literal["CREATED"]
    config: RunnableConfig


@tool(args_schema=AddCreatedFileInput)
def add_created_file(
    dest_path: str,
    summary: str,
    requirements: List[str],
    source_files: List[str],
    key_changes: List[str],
    is_dependency_file: bool,
    depends_on_files: List[str],
    status: Literal["CREATED"],
    config: RunnableConfig
):
    """
    Adds a schema for a file that can then be extended with internal_imports, external_imports, and exports using the appropriate functions.
    Use this to add schemas for missing files after you have thoroughly searched for them.
    """
    file_path = clean_path(dest_path)
    logger.info(f'Add created file schema for file: {file_path}')
    storage_client = config["configurable"]["storage_client"]
    bucket_name = config["configurable"]["bucket_name"]
    blob_name = config["configurable"]["blob_name"]
    file_schemas_filename = config["configurable"]["file_schemas_filename"]
    file_schemas: FileSchemaType = download_file_schemas(
        storage_client=storage_client,
        bucket_name=bucket_name,
        blob_name=blob_name,
        filename=file_schemas_filename
    )
    file_schema = file_schemas.get(file_path)
    if file_schema:
        logger.warning(f"Attempted to add a schema for a file that already exists: {file_path}")
        return FILE_ERROR_RESPONSE.format(file_path=file_path, message=FILE_ALREADY_EXISTS)
    file_schema = {
        "dest_path": file_path,
        "summary": summary,
        "requirements": requirements,
        "source_files": source_files,
        "key_changes": key_changes,
        "is_dependency_file": is_dependency_file,
        "depends_on_files": [],
        "status": status,
        "internal_imports": [],
        "external_imports": [],
        "exports": []

    }
    file_schemas[file_path] = file_schema

    upload_file_schemas(
        storage_client=storage_client,
        bucket_name=bucket_name,
        blob_name=blob_name,
        filename=file_schemas_filename,
        file_schemas=file_schemas
    )

    return f"Schema for file {file_path} created successfully and is now available for adding internal_imports, external_imports, and exports"


class AddUpdatedFileInput(UpdatedFile):
    status: Literal["UPDATED"]
    config: RunnableConfig


@tool(args_schema=AddUpdatedFileInput)
def update_unchanged_file(
    dest_path: str,
    key_changes: List[str],
    is_dependency_file: bool,
    depends_on_files: List[str],
    status: Literal["UPDATED"],
    config: RunnableConfig
):
    """
    Marks an UNCHANGED file as UPDATED. This file then needs to be extended with new_internal_imports, new_external_imports, and new_exports using the respective tools.
    Useful for modifying files that exist in the source branch but are currently marked UNCHANGED and not CREATED or UPDATED.
    """
    file_path = clean_path(dest_path)
    logger.info(f'Updating unchanged file: {file_path}')
    storage_client = config["configurable"]["storage_client"]
    bucket_name = config["configurable"]["bucket_name"]
    blob_name = config["configurable"]["blob_name"]
    file_schemas_filename = config["configurable"]["file_schemas_filename"]
    file_schemas: FileSchemaType = download_file_schemas(
        storage_client=storage_client,
        bucket_name=bucket_name,
        blob_name=blob_name,
        filename=file_schemas_filename
    )
    file_schema = file_schemas.get(file_path)
    if file_schema:
        logger.warning(f"Attempted to update a schema for a file that is not UNCHANGED: {file_path}")
        return FILE_ERROR_RESPONSE.format(file_path=file_path, message=FILE_ALREADY_EXISTS)
    file_schema = {
        "dest_path": file_path,
        "key_changes": key_changes,
        "is_dependency_file": is_dependency_file,
        "depends_on_files": [],
        "status": status,
        "new_internal_imports": [],
        "new_external_imports": [],
        "new_exports": []

    }
    file_schemas[file_path] = file_schema

    upload_file_schemas(
        storage_client=storage_client,
        bucket_name=bucket_name,
        blob_name=blob_name,
        filename=file_schemas_filename,
        file_schemas=file_schemas
    )

    return f"Schema for file {file_path} updated successfully and is now ready for you to add new_internal_imports, new_external_imports, and new_exports"


@tool(args_schema=GetItemInput)
def mark_file_complete(
    file_path: str,
    config: RunnableConfig
):
    """
    Marks a file as complete in the processing workflow.
    Use this after all modifications to a file have been successfully applied.
    """
    file_path = clean_path(file_path)
    storage_client = config["configurable"]["storage_client"]
    bucket_name = config["configurable"]["bucket_name"]
    blob_name = config["configurable"]["blob_name"]
    file_schemas_filename = config["configurable"]["file_schemas_filename"]
    file_schemas: FileSchemaType = download_file_schemas(
        storage_client=storage_client,
        bucket_name=bucket_name,
        blob_name=blob_name,
        filename=file_schemas_filename
    )
    internal_imports: List[ThinkingInternalImport] = get_property(
        file_path=file_path,
        file_schemas=file_schemas,
        updated_file_property='new_internal_imports',
        default_property='internal_imports'
    )

    file_schemas = rebuild_internal_dependencies(
        file_path=file_path,
        internal_imports=internal_imports,
        file_schemas=file_schemas
    )

    upload_file_schemas(
        storage_client=storage_client,
        bucket_name=bucket_name,
        blob_name=blob_name,
        filename=file_schemas_filename,
        file_schemas=file_schemas
    )
    logger.info(f'File {file_path} marked completed')
    return f"File {file_path} marked completed successfully."
