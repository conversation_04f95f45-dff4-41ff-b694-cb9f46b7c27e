DEST_FILE_PATH_PREFIX = "dest_file:"
SRC_FILE_PATH_PREFIX = "src_file:"
DEST_FOLDER_PATH_PREFIX = "dest_folder:"

CREATE_FILE_SUFFIX = "If this file is supposed to exist, you need to create it using add_created_file."
FILE_PATH_NOT_FOUND = f"Error: File path does not exist, or not marked for an update, change or deletion. {CREATE_FILE_SUFFIX}"
FILE_RETRY_SUFFIX = "You may double check your path and try again with a different path that you haven't tried so far."
FILE_ALREADY_EXISTS = f"Error: File schema already exists and is marked for changes. {FILE_RETRY_SUFFIX}"
CIRCULAR_DEPENDENCY_ERROR = """
    Error: Circular depedency detected from {first_file} to {second_file}. Please execute one of the following options.
    1. Avoid adding this internal import dependency on {first_file}, OR
    2. Remove this internal import dependency from {second_file} using remove_internal_import.

    Review the schemas of {first_file} and {second_file} and make your decision.
    """
ITEM_NOT_FOUND = "Error: No {item} found for {key}: {value} in {file_path}. Check your {key} and try again if you haven't already."
ITEM_REMOVED_RESPONSE = "{item} of {value} removed successfully for {file_path}"
ITEM_ADDED_RESPONSE = "{item} added successfully for {file_path}"
SOURCE_FILE_ERROR_RESPONSE = """
    Error: You attempted to import {source_file_path} into {file_path}, but no schema exists for {source_file_path}.
    {suffix}
    """
FILE_ERROR_RESPONSE = """
    File: {file_path}.
    {message}
    """
