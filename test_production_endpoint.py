#!/usr/bin/env python3
"""
Test script to verify the production endpoint handles IP allowlist restrictions correctly.
This script tests the actual endpoint behavior.
"""

import requests
import json
import sys

def test_production_endpoint():
    """Test the production endpoint for IP allowlist handling."""
    print("🧪 Testing Production Endpoint IP Allowlist Handling")
    print("=" * 60)
    
    # Production endpoint URL
    base_url = "https://archie-github-handler-648803317587.us-central1.run.app"
    user_id = "7cc7a385-400d-4d92-a1e5-cf6f6cb4d476"
    endpoint = f"{base_url}/v1/github/users/{user_id}/organizations"
    
    print(f"Testing endpoint: {endpoint}")
    
    try:
        # Make request to the production endpoint
        response = requests.get(endpoint, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"Response Data: {json.dumps(data, indent=2)}")
                
                # Check if the response has the expected structure
                if isinstance(data, dict) and 'results' in data:
                    print(f"✅ Endpoint returned valid response structure")
                    print(f"✅ Number of organizations: {len(data.get('results', []))}")
                    
                    # If there are no results, this could be due to IP allowlist restrictions
                    # being handled gracefully (returning empty list instead of 500 error)
                    if len(data.get('results', [])) == 0:
                        print("ℹ️  No organizations returned - this could indicate IP allowlist restrictions are being handled gracefully")
                    
                    return True
                else:
                    print(f"❌ Unexpected response structure: {data}")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse JSON response: {e}")
                print(f"Raw response: {response.text}")
                return False
                
        elif response.status_code == 500:
            print(f"❌ Server returned 500 error - IP allowlist handling may not be working")
            print(f"Response: {response.text}")
            return False
            
        else:
            print(f"⚠️  Unexpected status code: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False


def test_endpoint_resilience():
    """Test that the endpoint is resilient to various scenarios."""
    print("\n🧪 Testing Endpoint Resilience")
    print("=" * 60)
    
    base_url = "https://archie-github-handler-648803317587.us-central1.run.app"
    
    # Test with different user IDs to see behavior
    test_cases = [
        "7cc7a385-400d-4d92-a1e5-cf6f6cb4d476",  # Original user
        "00000000-0000-0000-0000-000000000000",  # Non-existent user
    ]
    
    for user_id in test_cases:
        endpoint = f"{base_url}/v1/github/users/{user_id}/organizations"
        print(f"\nTesting with user_id: {user_id}")
        
        try:
            response = requests.get(endpoint, timeout=30)
            print(f"  Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    org_count = len(data.get('results', []))
                    print(f"  Organizations: {org_count}")
                    print("  ✅ Endpoint returned 200 (good resilience)")
                except json.JSONDecodeError:
                    print("  ❌ Invalid JSON response")
            elif response.status_code == 404:
                print("  ✅ User not found (expected for non-existent user)")
            elif response.status_code == 500:
                print("  ❌ Server error - may indicate unhandled exception")
            else:
                print(f"  ⚠️  Status: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"  ❌ Request failed: {e}")


def main():
    """Run all tests."""
    print("🚀 Starting Production Endpoint Tests")
    print("=" * 60)
    
    success = True
    
    # Test the main endpoint
    if not test_production_endpoint():
        success = False
    
    # Test endpoint resilience
    test_endpoint_resilience()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Production endpoint appears to be handling IP allowlist restrictions correctly!")
        print("✅ The endpoint returns 200 status instead of 500 errors")
        print("✅ This indicates the IP allowlist handling fix is working")
    else:
        print("⚠️  Production endpoint may still have issues with IP allowlist handling")
        print("❌ Consider checking logs for more details")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
