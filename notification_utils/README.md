# Notification Utils

A powerful, flexible, and easy-to-use Python library for sending templated email notifications. Built with best
practices and designed for scalability.

## Features

- **Zero Configuration** - Works out of the box with environment variables
- **Jinja2 Templating** - Support for both simple variables and complex logic (loops, conditionals)
- **Professional Templates** - Beautiful, responsive HTML email templates included
- **Pluggable Providers** - Easy to add new email providers (SendGrid supported)
- **Multiple Recipients** - Send to single or multiple email addresses
- **Type Safety** - Template and provider constants prevent typos
- **Attachments** - Support for file attachments
- **Preview Mode** - Test templates without sending emails
- **Flexible Configuration** - Environment variables, explicit config, or mixed approaches

## Quick Start

### 1. Install

```bash
pip install notification-utils
```

### 2. Set Environment Variables

```bash
export SENDGRID_API_KEY="your-sendgrid-api-key"
export DEFAULT_FROM_EMAIL="<EMAIL>"
export DEFAULT_FROM_NAME="Your Company"
```

### 3. Send Your First Email

```python
from notification_utils.notification import NotificationService, EmailTemplates

# Zero configuration needed!
service = NotificationService()

# Send welcome email
result = service.send(
    template_type=EmailTemplates.WELCOME_EMAIL,
    to="<EMAIL>",
    template_vars={
        "user_name": "John Doe",
        "company_name": "Acme Corp",
        "login_url": "https://app.acme.com/login"
    }
)

print(f"Email sent successfully! Message ID: {result['message_id']}")
```

That's it!

## Usage Examples

Note these are just examples, available templates are added in the table.

### Send to Multiple Recipients

```python
result = service.send(
    template_type=EmailTemplates.WELCOME_EMAIL,
    to=["<EMAIL>", "<EMAIL>", "<EMAIL>"],
    template_vars={
        "user_name": "Team",
        "company_name": "Acme Corp",
        "login_url": "https://app.acme.com/login"
    }
)
```

### Custom Subject

```python
result = service.send(
    template_type=EmailTemplates.WELCOME_EMAIL,
    to="<EMAIL>",
    subject="Welcome to Our Amazing Platform!",  # Custom subject
    template_vars={
        "user_name": "John Doe",
        "company_name": "Acme Corp"
    }
)
```

### Override Sender for Specific Email

```python
result = service.send(
    template_type=EmailTemplates.INVOICE,
    to="<EMAIL>",
    from_email="<EMAIL>",  # Override default
    from_name="Billing Department",
    template_vars={
        "customer_name": "John Doe",
        "invoice_number": "INV-001",
        "total_amount": 250.00,
        "company_name": "Acme Corp"
    }
)
```

### Preview Template Before Sending

```python
preview = service.preview_template(
    template_type=EmailTemplates.PASSWORD_RESET,
    template_vars={
        "user_name": "John Doe",
        "company_name": "Acme Corp",
        "reset_url": "https://app.acme.com/reset/token123"
    }
)

print(f"Subject: {preview['subject']}")
print(f"HTML Content: {preview['html_content']}")
```

### With Attachments

```python
result = service.send(
    template_type=EmailTemplates.INVOICE,
    to="<EMAIL>",
    template_vars={"customer_name": "John", "invoice_number": "INV-001"},
    attachments=[
        {
            "file_path": "/path/to/invoice.pdf",
            "filename": "invoice_INV-001.pdf",
            "mime_type": "application/pdf"
        }
    ]
)
```

## Available Templates

The library comes with professional, responsive email templates:

| Template                      | Constant                         | Use Case                      |
|-------------------------------|----------------------------------|-------------------------------|
| Technical Specification Ready | `EmailTemplates.TECH_SPEC_READY` | Technical Specification Ready |

### Template Variables

#### Technical Spec Ready

```python
template_vars = {
    "project_name": "This is new project",
    "username": "Chaitanya",
    "project_url": "https://google.com"
}
```

## Configuration

### Environment Variables (Recommended)

```bash
# Required
export SENDGRID_API_KEY="your-sendgrid-api-key"

# Optional (but recommended)
export DEFAULT_FROM_EMAIL="<EMAIL>"
export DEFAULT_FROM_NAME="Blitzy Auto"
```

### Explicit Configuration

```python
from notification_utils.notification import NotificationService, EmailProviders

config = {
    EmailProviders.SENDGRID: {
        'api_key': 'your-sendgrid-api-key',
        'default_from_email': '<EMAIL>',
        'default_from_name': 'Your Company'
    }
}

service = NotificationService(provider_configs=config)
```

### Configuration Priority

The system follows this priority order:

1. **Method parameters** (highest priority)
2. **Explicit config** (if provided)
3. **Environment variables** (fallback)

## Supported Providers

- **SendGrid** - Production ready
- Support to add more providers is there (AWS SES, Mailgun, etc.)

🫡