import sendgrid
from sendgrid.helpers.mail import Mail, Attachment, FileContent, FileName, FileType, Disposition
from typing import Dict, List, Optional, Any, Union
import base64
import os

from .base import EmailProvider
from ..exceptions import ProviderError, ConfigurationError


class SendGridProvider(EmailProvider):
    """SendGrid email provider implementation."""

    def __init__(self, config: Dict[str, Any]):
        # Auto-populate config from environment variables if not provided
        self._populate_config_from_env(config)
        super().__init__(config)
        self.client = sendgrid.SendGridAPIClient(api_key=self.config['api_key'])

    def _populate_config_from_env(self, config: Dict[str, Any]) -> None:
        """Populate configuration from environment variables if not provided."""
        if 'api_key' not in config:
            config['api_key'] = os.getenv('SENDGRID_API_KEY')

        if 'default_from_email' not in config:
            config['default_from_email'] = os.getenv('DEFAULT_FROM_EMAIL')

        if 'default_from_name' not in config:
            config['default_from_name'] = os.getenv('DEFAULT_FROM_NAME')

    def _validate_config(self) -> None:
        """Validate SendGrid configuration."""
        if not self.config.get('api_key'):
            raise ConfigurationError(
                "SendGrid API key is required. Provide it in config or set SENDGRID_API_KEY environment variable."
            )

    def send_email(
            self,
            to: Union[str, List[str]],
            subject: str,
            html_content: str,
            from_email: Optional[str] = None,
            from_name: Optional[str] = None,
            attachments: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """Send email via SendGrid."""
        try:
            # Use provided from_email or fall back to config or env
            sender_email = (
                    from_email or
                    self.config.get('default_from_email') or
                    os.getenv('DEFAULT_FROM_EMAIL')
            )
            sender_name = (
                    from_name or
                    self.config.get('default_from_name') or
                    os.getenv('DEFAULT_FROM_NAME')
            )

            if not sender_email:
                raise ConfigurationError(
                    "Sender email is required. Provide it in the send() call, config, or set DEFAULT_FROM_EMAIL environment variable."
                )

            # Create mail object
            mail = Mail(
                from_email=(sender_email, sender_name) if sender_name else sender_email,
                to_emails=to,
                subject=subject,
                html_content=html_content
            )

            # Add attachments if provided
            if attachments:
                for attachment_info in attachments:
                    attachment = self._create_attachment(attachment_info)
                    mail.attachment = attachment

            # Send email
            response = self.client.send(mail)

            return {
                'success': True,
                'status_code': response.status_code,
                'message_id': response.headers.get('X-Message-Id'),
                'provider': 'sendgrid'
            }

        except Exception as e:
            raise ProviderError(f"SendGrid send failed: {str(e)}")

    def _create_attachment(self, attachment_info: Dict[str, Any]) -> Attachment:
        """Create SendGrid attachment from attachment info."""
        if 'file_path' in attachment_info:
            # File path provided
            with open(attachment_info['file_path'], 'rb') as f:
                data = f.read()
            encoded_file = base64.b64encode(data).decode()
            file_name = attachment_info.get('filename') or os.path.basename(attachment_info['file_path'])
        elif 'content' in attachment_info:
            # Raw content provided
            encoded_file = base64.b64encode(attachment_info['content']).decode()
            file_name = attachment_info.get('filename', 'attachment')
        else:
            raise ConfigurationError("Attachment must have either 'file_path' or 'content'")

        attachment = Attachment()
        attachment.file_content = FileContent(encoded_file)
        attachment.file_name = FileName(file_name)
        attachment.file_type = FileType(attachment_info.get('mime_type', 'application/octet-stream'))
        attachment.disposition = Disposition('attachment')

        return attachment