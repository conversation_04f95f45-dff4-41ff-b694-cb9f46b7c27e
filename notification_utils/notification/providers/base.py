from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union


class EmailProvider(ABC):
    """Abstract base class for email providers."""

    def __init__(self, config: Dict[str, Any]):
        """
        Represents an instance for initializing configuration and validating it.

        :param config: A dictionary containing configuration settings.
        """
        self.config = config
        self._validate_config()

    @abstractmethod
    def _validate_config(self) -> None:
        """Validate the provider configuration."""
        pass

    @abstractmethod
    def send_email(
            self,
            to: Union[str, List[str]],
            subject: str,
            html_content: str,
            from_email: Optional[str] = None,
            from_name: Optional[str] = None,
            attachments: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """
        Send an email with the given content, subject, and attachments.

        :param to: Recipient email address or list of email addresses.
        :param subject: Subject of the email.
        :param html_content: HTML content of the email body.
        :param from_email: Sender's email address (optional).
        :param from_name: Sender's name (optional).
        :param attachments: List of attachments for the email (optional).
        :return: A dictionary containing details of the sent email.
        """
        pass