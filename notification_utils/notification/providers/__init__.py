from .base import EmailProvider
from .sendgrid import Send<PERSON>ridProvider
from .factory import EmailProviderFactory

__all__ = [
    'EmailProvider',
    'SendGridProvider',
    'EmailProviderFactory'
]

"""
# Simple usage with environment variables

# Set environment variables:
# export SENDGRID_API_KEY="your-sendgrid-api-key"
# export DEFAULT_FROM_EMAIL="<EMAIL>"
# export DEFAULT_FROM_NAME="Your Company"

from notification_utils.notification import NotificationService, EmailTemplates

# Super simple initialization - uses environment variables by default
notification_service = NotificationService()

# Send welcome email - that's it!
result = notification_service.send(
    template_type=EmailTemplates.WELCOME_EMAIL,
    to="<EMAIL>",
    template_vars={
        "user_name": "<PERSON>",
        "company_name": "Acme Corp",
        "login_url": "https://app.acme.com/login"
    }
)

print(f"Email sent: {result}")

# =========================================
# Alternative: Explicit configuration
# =========================================

from notification_utils.notification import NotificationService, EmailTemplates, EmailProviders

# If you prefer explicit configuration (optional)
config = {
    EmailProviders.SENDGRID: {
        'api_key': 'your-sendgrid-api-key',
        'default_from_email': '<EMAIL>',
        'default_from_name': 'Your Company'
    }
}

notification_service = NotificationService(provider_configs=config)

# Send email - same as above
result = notification_service.send(
    template_type=EmailTemplates.WELCOME_EMAIL,
    to="<EMAIL>",
    template_vars={
        "user_name": "John Doe",
        "company_name": "Acme Corp",
        "login_url": "https://app.acme.com/login"
    }
)

# =========================================
# Override sender for specific email
# =========================================

result = notification_service.send(
    template_type=EmailTemplates.INVOICE,
    to="<EMAIL>",
    template_vars={
        "customer_name": "John Doe",
        "invoice_number": "INV-001",
        "invoice_date": "2025-01-15",
        "due_date": "2025-02-15",
        "total_amount": 250.00,
        "line_items": [
            {"description": "Web Development", "quantity": 10, "unit_price": 25.00, "total": 250.00}
        ],
        "company_name": "Acme Corp"
    },
    from_email="<EMAIL>",  # Override default
    from_name="Billing Department"
)

# =========================================
# Preview template before sending
# =========================================

preview = notification_service.preview_template(
    template_type=EmailTemplates.PASSWORD_RESET,
    template_vars={
        "user_name": "John Doe",
        "company_name": "Acme Corp",
        "reset_url": "https://app.acme.com/reset/token123",
        "expiry_hours": 24
    }
)

print(f"Subject: {preview['subject']}")
print(f"HTML Preview: {preview['html_content'][:200]}...")
"""
