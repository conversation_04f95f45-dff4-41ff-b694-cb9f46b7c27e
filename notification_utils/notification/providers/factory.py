from typing import Dict, Any, List

from .base import <PERSON>ailProvider
from .sendgrid import SendGridProvider
from ..constants import EmailProviders
from ..exceptions import ConfigurationError


class EmailProviderFactory:
    """Factory for creating email provider instances."""

    _providers = {
        EmailProviders.SENDGRID: SendGridProvider,
        # Add more providers here as they're implemented
        # EmailProviders.AWS_SES: AWSESProvider,
        # EmailProviders.MAILGUN: MailgunProvider,
    }

    @classmethod
    def create_provider(cls, provider_type: str, config: Dict[str, Any]) -> EmailProvider:
        """
        Creates and returns an email provider instance based on the specified
        provider type and configuration.

        :param provider_type: The type of the email provider to create.
        :param config: The configuration required to instantiate the email
            provider.
        :return: An instance of the requested email provider.
        :raises ConfigurationError: If the provided provider_type is not
            registered or recognized among available providers.
        """
        if provider_type not in cls._providers:
            available_providers = list(cls._providers.keys())
            raise ConfigurationError(
                f"Unknown provider type: {provider_type}. "
                f"Available providers: {available_providers}"
            )

        provider_class = cls._providers[provider_type]
        return provider_class(config)

    @classmethod
    def get_available_providers(cls) -> List[str]:
        """Get list of available provider types."""
        return list(cls._providers.keys())