class NotificationError(Exception):
    """Base exception for notification-related errors."""
    pass


class TemplateNotFoundError(NotificationError):
    """Raised when a template file is not found."""
    pass


class ProviderError(NotificationError):
    """Raised when there's an error with the email provider."""
    pass


class ConfigurationError(NotificationError):
    """Raised when configuration is invalid."""
    pass
