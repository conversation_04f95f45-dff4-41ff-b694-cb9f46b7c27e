import os
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
import jinja2

from .providers.factory import EmailProviderFactory
from .providers.base import EmailProvider
from .constants import EmailProviders
from .exceptions import TemplateNotFoundError, ConfigurationError


class NotificationService:
    """Main notification service for sending templated emails."""

    def __init__(
            self,
            provider_configs: Optional[Dict[str, Dict[str, Any]]] = None,
            default_provider: str = EmailProviders.SENDGRID
    ):
        """Initialize notification service.

        Args:
            provider_configs: Optional dictionary mapping provider names to their configs.
                            If not provided, defaults to SendGrid with env variables.
            default_provider: Default provider to use (defaults to SendGrid)
        """
        # If no config provided, create default SendGrid config from env
        if provider_configs is None:
            provider_configs = {
                EmailProviders.SENDGRID: {}  # Will be populated from env variables
            }

        self.provider_configs = provider_configs
        self.default_provider = default_provider
        self._providers: Dict[str, EmailProvider] = {}

        # Initialize Jinja2 environment
        template_dir = Path(__file__).parent / "templates"
        self.jinja_env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(template_dir),
            autoescape=jinja2.select_autoescape(['html', 'xml'])
        )

        # Initialize default provider
        self._get_provider(default_provider)

    def _get_provider(self, provider_type: str) -> EmailProvider:
        """Get or create a provider instance."""
        if provider_type not in self._providers:
            # Create default config if provider not in configs
            if provider_type not in self.provider_configs:
                if provider_type == EmailProviders.SENDGRID:
                    self.provider_configs[provider_type] = {}  # Will use env variables
                else:
                    raise ConfigurationError(f"No configuration found for provider: {provider_type}")

            config = self.provider_configs[provider_type]
            self._providers[provider_type] = EmailProviderFactory.create_provider(provider_type, config)

        return self._providers[provider_type]

    def send(
            self,
            template_type: str,
            to: Union[str, List[str]],
            template_vars: Dict[str, Any],
            subject: Optional[str] = None,
            provider: Optional[str] = None,
            from_email: Optional[str] = None,
            from_name: Optional[str] = None,
            attachments: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """Send a templated email notification.

        Args:
            template_type: Template type (use EmailTemplates constants)
            to: Recipient email address or list of email addresses
            template_vars: Variables to substitute in template
            subject: Email subject (if not provided, uses template default)
            provider: Email provider to use (defaults to default_provider)
            from_email: Sender email address (falls back to config or env variable)
            from_name: Sender name (falls back to config or env variable)
            attachments: List of attachment dictionaries

        Returns:
            Dict containing send result information
        """
        provider = provider or self.default_provider
        email_provider = self._get_provider(provider)
        html_content, template_subject = self._render_template(template_type, template_vars)

        final_subject = subject or template_subject or f"Notification: {template_type}"

        result = email_provider.send_email(
            to=to,
            subject=final_subject,
            html_content=html_content,
            from_email=from_email,
            from_name=from_name,
            attachments=attachments
        )

        result['template_type'] = template_type
        return result

    def _render_template(self, template_type: str, template_vars: Dict[str, Any]) -> tuple[str, Optional[str]]:
        """Render a template with provided variables.

        Returns:
            Tuple of (html_content, subject)
        """
        template_filename = f"{template_type}.html"

        try:
            template = self.jinja_env.get_template(template_filename)
        except jinja2.TemplateNotFound:
            raise TemplateNotFoundError(f"Template not found: {template_filename}")

        # Render template
        rendered = template.render(**template_vars)

        # Extract subject if it exists in template (look for <!-- SUBJECT: ... --> comment)
        subject = None
        if "<!-- SUBJECT:" in rendered:
            start = rendered.find("<!-- SUBJECT:") + len("<!-- SUBJECT:")
            end = rendered.find("-->", start)
            if end != -1:
                subject = rendered[start:end].strip()
                # Remove subject comment from rendered content
                rendered = rendered.replace(f"<!-- SUBJECT:{subject} -->", "").strip()

        return rendered, subject

    def preview_template(self, template_type: str, template_vars: Dict[str, Any]) -> Dict[str, str]:
        """Preview a template without sending.

        Args:
            template_type: Template type
            template_vars: Variables to substitute

        Returns:
            Dict with 'html_content' and 'subject' keys
        """
        html_content, subject = self._render_template(template_type, template_vars)
        return {
            'html_content': html_content,
            'subject': subject or f"Notification: {template_type}"
        }

    def get_available_templates(self) -> List[str]:
        """Get list of available template types."""
        template_dir = Path(__file__).parent / "templates"
        if not template_dir.exists():
            return []

        templates = []
        for file_path in template_dir.glob("*.html"):
            template_name = file_path.stem
            templates.append(template_name)

        return templates