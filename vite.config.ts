import path from 'path';
import react from '@vitejs/plugin-react-swc';
// import { defineConfig } from "vite";
import {defineConfig} from 'vitest/config';
import {loadEnv} from 'vite';

export default defineConfig(({mode}) => {
    // Load environment variables based on the mode (e.g., 'development' or 'production')
    const env = loadEnv(mode, process.cwd(), '');

    return {
        plugins: [react()],
        test: {
            // 👋 add the line below to add jsdom to vite
            environment: 'jsdom',
        },
        server: {
            host: env.VITE_HOST || 'localhost', // set VITE_HOST to '127.0.0.1' in your .env.local file if running on a linux box
            port: parseInt(env.VITE_PORT || '5173', 10), // Set the port from the environment variable, default to 3000
            // Uncomment and configure the proxy as needed
            // proxy: {
            //   '/v1/job': {
            //     target: 'https://os.api.blitzy.dev',
            //     changeOrigin: true,
            //     ws: true,
            //     configure: (proxy, _options) => {
            //       proxy.on('error', (err, _req, _res) => {
            //         console.log('proxy error', err);
            //       });
            //       proxy.on('proxyReq', (proxyReq, req, _res) => {
            //         console.log('Sending Request:', req.method, req.url);
            //         console.log('Sending Request to the Target:', proxyReq.method, proxyReq.path);
            //       });
            //       proxy.on('proxyRes', (proxyRes, req, _res) => {
            //         console.log(JSON.stringify(req.headers));
            //         console.log('Received Response from the Target:', proxyRes.statusCode);
            //         console.log('Received Response from the Target:', proxyRes.statusMessage);
            //       });
            //     },
            //     rewrite: (path) => path.replace(/^\/api/, ''),
            //   }
            // },
        },
        resolve: {
            alias: {
                '@': path.resolve(__dirname, './src'),
            },
        },
    };
});
