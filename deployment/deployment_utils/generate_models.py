import argparse
import json
import subprocess
import sys
from pathlib import Path
from typing import Dict, Any

import requests
import yaml


def install_requirements():
    """Install required packages if not already installed"""
    required_packages = ["datamodel-code-generator"]
    subprocess.check_call([sys.executable, "-m", "pip", "install"] + required_packages)


def detect_spec_version(spec_dict: Dict[Any, Any]) -> str:
    """
    Detect whether the specification is Swagger 2.0 or OpenAPI 3.0
    Returns: "2.0" or "3.0"
    """
    if "swagger" in spec_dict and spec_dict["swagger"].startswith("2"):
        return "2.0"
    elif "openapi" in spec_dict and spec_dict["openapi"].startswith("3"):
        return "3.0"
    else:
        raise ValueError("Unable to determine specification version. Must be Swagger 2.0 or OpenAPI 3.0")


def convert_to_openapi3(swagger_dict: Dict[Any, Any]) -> Dict[Any, Any]:
    """Convert Swagger 2.0 to OpenAPI 3.0 using Swagger.io API"""
    print("Converting Swagger 2.0 to OpenAPI 3.0...")
    response = requests.post(
        "https://converter.swagger.io/api/convert",
        data=json.dumps(swagger_dict),
        headers={"Content-Type": "application/json"}
    )
    response.raise_for_status()
    return response.json()


def generate_models(spec_file: str, output_path: str, base_class: str | None = None):
    """Generate Pydantic models from OpenAPI 3.0 specification"""
    print(f"Generating models from {spec_file}...")
    output_dir = Path(output_path).parent
    output_dir.mkdir(parents=True, exist_ok=True)

    cmd = [
        "datamodel-codegen",
        "--input", spec_file,
        "--input-file-type", "openapi",
        "--output", output_path,
        "--target-python-version", "3.12"
    ]

    if base_class:
        cmd.extend(["--base-class", base_class])

    subprocess.run(cmd, check=True)


def convert_and_generate_models(input_path: str, output_path: str, base_class: str | None = None):
    print("Starting process...")
    print(f"Input file: {input_path}")
    print(f"Output models file: {output_path}")
    if base_class:
        print(f"Using base class: {base_class}")

    temp_dir = Path("temp")
    temp_file = temp_dir / "openapi3.yaml"

    try:
        # Read and parse input file
        with open(input_path, "r") as file:
            spec_content = file.read()
            spec_dict = yaml.safe_load(spec_content)

        # Detect specification version
        spec_version = detect_spec_version(spec_dict)
        print(f"Detected specification version: {spec_version}")

        if spec_version == "2.0":
            # Convert Swagger 2.0 to OpenAPI 3.0
            temp_dir.mkdir(exist_ok=True)
            openapi3_dict = convert_to_openapi3(spec_dict)

            # Save converted spec temporarily
            with open(temp_file, "w") as file:
                yaml.dump(openapi3_dict, file)

            # Generate models using temporary file
            generate_models(str(temp_file), output_path, base_class)

            # Clean up
            temp_file.unlink()
            temp_dir.rmdir()
        else:
            # Already OpenAPI 3.0, generate models directly
            generate_models(input_path, output_path, base_class)

        print(f"\nSuccess! Models have been generated in {output_path}")

    except FileNotFoundError:
        print(f"Error: {input_path} not found")
    except yaml.YAMLError as e:
        print(f"Error parsing {input_path}: {e}")
    except requests.exceptions.RequestException as e:
        print(f"Error during conversion: {e}")
    except subprocess.CalledProcessError as e:
        print(f"Error generating models: {e}")
    except ValueError as e:
        print(f"Error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")
        if temp_file.exists():
            temp_file.unlink()
        if temp_dir.exists():
            temp_dir.rmdir()


def main():
    parser = argparse.ArgumentParser(
        description="Convert Swagger 2.0 to OpenAPI 3.0 and generate Pydantic models, or generate models directly from OpenAPI 3.0"
    )
    parser.add_argument("--input", "-i", type=str, required=True, help="Input Swagger 2.0 or OpenAPI 3.0 file path")
    parser.add_argument("--output", "-o", type=str, required=True, help="Output Python models file path")
    parser.add_argument("--base-class", "-b", type=str,
                        help="Base class for generated models (e.g., 'src.models_config.base_class.BaseModel')")

    args = parser.parse_args()

    print("Checking and installing requirements...")
    install_requirements()
    convert_and_generate_models(args.input, args.output, args.base_class)


if __name__ == "__main__":
    main()