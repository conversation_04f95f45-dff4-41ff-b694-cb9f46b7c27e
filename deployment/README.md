# Deployment Utils
This repo contains code which is required for deploying various kinds of Blitzy services.

## Deploy to cloud run
This utility helps deploy cloud run function with env file to the cloud run.

### How to use this Utility
You have to create a yaml file which will contain all the env variables. You have to pass this file using
`--vars-file=<FILE_NAME>`. 
Sample template in `service.yaml`
```yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: {{SERVICE_NAME}}
```

Following command is the sample to be used for deploying the cloud run function.
```shell
deploy-to-cloud-run \
  --image-tag=latest \
  --yaml-file=service.yaml \
  --vars-file=vars.yaml \
  --show-rendered
```

Please follow this example: https://github.com/blitzy-ai/archie-service-backend/blob/main/service.yaml
