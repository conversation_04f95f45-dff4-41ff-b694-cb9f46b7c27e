#!/usr/bin/env python3
"""
Test script to verify IP allowlist error handling in GitHub API calls.
This test simulates the scenario where GitHub returns 403 errors due to IP allowlist restrictions.
"""

import sys
import os
from unittest.mock import Mock, patch, MagicMock
from github import GithubException

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_ip_allowlist_handling_in_get_installation_org():
    """Test that get_installation_org handles IP allowlist 403 errors gracefully."""
    print("🧪 Testing IP Allowlist Handling in get_installation_org")
    print("=" * 60)
    
    from src.api.utils.github_utils import get_installation_org
    from src.consts import VersionControlSystem
    
    # Mock GitHub integration and installation
    with patch('src.api.utils.github_utils.GithubIntegration') as mock_integration_class:
        mock_integration = Mock()
        mock_integration_class.return_value = mock_integration
        
        # Create a mock GithubException with IP allowlist message
        ip_allowlist_error = GithubException(
            status=403,
            data={
                "message": "Although you appear to have the correct authorization credentials, the `stanbridge` organization has an IP allow list enabled, and your IP address is not permitted to access this resource.",
                "documentation_url": "https://docs.github.com/rest/apps/apps#get-an-installation-for-the-authenticated-app"
            }
        )
        
        # Configure the mock to raise the IP allowlist error
        mock_integration.get_app_installation.side_effect = ip_allowlist_error
        
        # Test parameters
        installation_id = "12345"
        svc_type = VersionControlSystem.GITHUB
        retry_state = {"retries": 0, "max": 3}
        
        # Call the function
        result = get_installation_org(installation_id, svc_type, retry_state)
        
        # Verify that the function returns None instead of raising an exception
        assert result is None, f"Expected None, but got: {result}"
        print("✅ get_installation_org correctly returns None for IP allowlist errors")
        
        # Verify that the GitHub integration was called
        mock_integration.get_app_installation.assert_called_once_with(installation_id)
        print("✅ GitHub API was called as expected")


def test_ip_allowlist_handling_in_get_repos_by_installation_id():
    """Test that get_repos_by_installation_id handles IP allowlist 403 errors gracefully."""
    print("\n🧪 Testing IP Allowlist Handling in get_repos_by_installation_id")
    print("=" * 60)
    
    from src.api.utils.github_utils import get_repos_by_installation_id
    
    # Mock the GitHub integration and requests
    with patch('src.api.utils.github_utils.GithubIntegration') as mock_integration_class, \
         patch('src.api.utils.github_utils.requests') as mock_requests:
        
        mock_integration = Mock()
        mock_integration_class.return_value = mock_integration
        
        # Mock access token
        mock_access_token = Mock()
        mock_access_token.token = "test_token"
        mock_integration.get_access_token.return_value = mock_access_token
        
        # Mock response with IP allowlist error
        mock_response = Mock()
        mock_response.status_code = 403
        mock_response.text = "Although you appear to have the correct authorization credentials, the `stanbridge` organization has an IP allow list enabled, and your IP address is not permitted to access this resource."
        mock_requests.get.return_value = mock_response
        
        # Test parameters
        installation_id = "12345"
        
        # Call the function
        result = get_repos_by_installation_id(installation_id)
        
        # Verify that the function returns empty repositories instead of raising an exception
        assert result == {"repositories": {}}, f"Expected empty repositories dict, but got: {result}"
        print("✅ get_repos_by_installation_id correctly returns empty repositories for IP allowlist errors")
        
        # Verify that the requests.get was called
        mock_requests.get.assert_called()
        print("✅ GitHub API was called as expected")


def test_ip_allowlist_handling_in_get_installation_id_for_account():
    """Test that get_installation_id_for_account handles IP allowlist errors gracefully."""
    print("\n🧪 Testing IP Allowlist Handling in get_installation_id_for_account")
    print("=" * 60)
    
    from src.api.utils.github_utils import get_installation_id_for_account
    
    # Mock GitHub integration
    with patch('src.api.utils.github_utils.GithubIntegration') as mock_integration_class:
        mock_integration = Mock()
        mock_integration_class.return_value = mock_integration
        
        # Create a mock exception with IP allowlist message
        ip_allowlist_error = Exception(
            "Although you appear to have the correct authorization credentials, the `stanbridge` organization has an IP allow list enabled, and your IP address is not permitted to access this resource."
        )
        
        # Configure the mock to raise the IP allowlist error for first installation,
        # but succeed for the second one
        mock_account = Mock()
        mock_account.login = "test_account"
        mock_installation = Mock()
        mock_installation.account = mock_account
        
        mock_integration.get_app_installation.side_effect = [
            ip_allowlist_error,  # First call fails with IP allowlist
            mock_installation    # Second call succeeds
        ]
        
        # Test parameters
        installation_ids = ["12345", "67890"]
        account_name = "test_account"
        
        # Call the function
        result = get_installation_id_for_account(installation_ids, account_name)
        
        # Verify that the function continues and finds the account in the second installation
        assert result == "67890", f"Expected '67890', but got: {result}"
        print("✅ get_installation_id_for_account correctly skips IP allowlist errors and continues")
        
        # Verify that both installations were checked
        assert mock_integration.get_app_installation.call_count == 2
        print("✅ Function correctly tried both installations")


def test_organizations_endpoint_with_ip_allowlist():
    """Test the full organizations endpoint flow with IP allowlist errors."""
    print("\n🧪 Testing Organizations Endpoint with IP Allowlist Errors")
    print("=" * 60)
    
    from src.api.routes.github_users import get_github_user_organizations
    from src.api.models import GithubOrgBasicList
    
    # Mock all the dependencies
    with patch('src.api.routes.github_users.get_user_by_id') as mock_get_user, \
         patch('src.api.routes.github_users.get_only_github_installations_by_user') as mock_get_installations, \
         patch('src.api.routes.github_users.get_orgs_by_installations') as mock_get_orgs, \
         patch('src.api.routes.github_users.map_org_list_to_pydantic') as mock_map_orgs:
        
        # Mock user exists
        mock_get_user.return_value = {"id": "test_user"}
        
        # Mock installations exist
        mock_installation = Mock()
        mock_installation.installation_id = "12345"
        mock_installation.svc_type = "github"
        mock_get_installations.return_value = [mock_installation]
        
        # Mock get_orgs_by_installations returns empty list (simulating IP allowlist handling)
        mock_get_orgs.return_value = []
        
        # Mock the pydantic mapping
        mock_response = GithubOrgBasicList(results=[])
        mock_map_orgs.return_value = mock_response
        
        # Call the endpoint
        response, status_code = get_github_user_organizations("test_user")
        
        # Verify successful response with empty organizations
        assert status_code == 200, f"Expected status 200, but got: {status_code}"
        assert isinstance(response, GithubOrgBasicList), f"Expected GithubOrgBasicList, but got: {type(response)}"
        assert len(response.results) == 0, f"Expected empty results, but got: {response.results}"
        print("✅ Organizations endpoint correctly returns 200 with empty results for IP allowlist scenarios")


def run_all_tests():
    """Run all IP allowlist handling tests."""
    print("🚀 Starting IP Allowlist Error Handling Tests")
    print("=" * 60)
    
    tests = [
        test_ip_allowlist_handling_in_get_installation_org,
        test_ip_allowlist_handling_in_get_repos_by_installation_id,
        test_ip_allowlist_handling_in_get_installation_id_for_account,
        test_organizations_endpoint_with_ip_allowlist
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            test()
            passed += 1
        except Exception as e:
            print(f"❌ {test.__name__} FAILED: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! IP allowlist handling is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
