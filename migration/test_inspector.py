from common_models.db_client import SpannerClientSingleton
from migration.differ import SimpleSchemaInspector, print_schema_info


def test_schema_inspection():
    # Initialize SpannerClient
    client = SpannerClientSingleton()

    # Initialize our inspector
    inspector = SimpleSchemaInspector(client.engine)

    try:
        print("Starting schema inspection...")
        print("=" * 50)

        # Get all tables and their schema
        tables = inspector.get_all_tables()

        # Print information for each table
        for table_name, table_info in tables.items():
            print_schema_info(table_name, table_info)

    except Exception as e:
        import traceback
        print(f"Error during inspection: {str(e)}")
        print("Traceback:")
        traceback.print_exc()


if __name__ == "__main__":
    test_schema_inspection()
