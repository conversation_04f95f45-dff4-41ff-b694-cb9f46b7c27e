"""
Auto-generated migration: github_current_commit_hash
Version: 20250313_195950_1d877a2a
Created At: 2025-03-13T19:59:50.739574
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        "ALTER TABLE github_project_repo ADD COLUMN github_current_commit_hash VARCHAR;"
    ]


def down() -> List[str]:
    return [
        "ALTER TABLE github_project_repo DROP COLUMN github_current_commit_hash;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
