"""
Auto-generated migration: previous_commit_hash
Version: 20250310_200443_2b27d8af
Created At: 2025-03-10T20:04:43.991470
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        "ALTER TABLE github_project_repo ADD COLUMN previous_commit_hash VARCHAR;"
    ]


def down() -> List[str]:
    return [
        "ALTER TABLE github_project_repo DROP COLUMN previous_commit_hash;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
