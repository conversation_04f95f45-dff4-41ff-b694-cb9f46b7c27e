"""
Auto-generated migration: repo_id
Version: 20250310_194634_23a42018
Created At: 2025-03-10T19:46:34.359185
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        "ALTER TABLE github_branch_patterns ADD COLUMN repo_id VARCHAR;"
    ]


def down() -> List[str]:
    return [
        "ALTER TABLE github_branch_patterns DROP COLUMN repo_id;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
