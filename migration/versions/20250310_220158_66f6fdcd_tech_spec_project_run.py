"""
Auto-generated migration: tech_spec_project_run
Version: 20250310_220158_66f6fdcd
Created At: 2025-03-10T22:01:58.580430
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        "ALTER TABLE technical_specs ADD COLUMN project_run_id VARCHAR;",
        "ALTER TABLE technical_specs ADD CONSTRAINT fk_technical_specs_project_run_id FOREIGN KEY (project_run_id) REFERENCES project_runs(id);"
    ]


def down() -> List[str]:
    return [
        "ALTER TABLE technical_specs DROP COLUMN project_run_id;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
