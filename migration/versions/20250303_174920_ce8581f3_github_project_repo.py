"""
Auto-generated migration: github_project_repo
Version: 20250303_174920_ce8581f3
Created At: 2025-03-03T17:49:20.028690
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        """
        create table if not exists github_branch_patterns (
        id varchar primary key not null,
        org_name varchar,
        repo_name varchar,
        branch_name varchar,
        status varchar(9),
        is_deleted boolean  ,
        deleted_at timestamptz,
        created_at timestamptz,
        updated_at timestamptz
        );
        """,
        """
        create table if not exists github_project_repo (
        id varchar primary key not null,
        project_id varchar,
        org_id varchar,
        org_name varchar,
        installation_id varchar,
        repo_name varchar,
        repo_id varchar,
        branch_name varchar,
        usage_type varchar(6),
        onboarding_commit_hash varchar,
        current_commit_hash varchar,
        last_scan_at timestamptz,
        needs_scan boolean,
        create_repo boolean,
        repo_metadata jsonb,
        is_deleted boolean,
        deleted_at timestamptz,
        created_at timestamptz,
        updated_at timestamptz
        );
        """,
        """
        create table if not exists github_branch_pattern_projects (
        id varchar primary key not null,
        pattern_id varchar,
        project_id varchar,
        usage_type varchar(6),
        is_deleted boolean,
        deleted_at timestamptz,
        created_at timestamptz,
        updated_at timestamptz
        );
        """,
        """alter table github_project_repo add constraint fk_github_project_repo_project_id foreign key (project_id) references projects(id);""",
        """alter table github_branch_pattern_projects add constraint fk_github_branch_pattern_projects_project_id foreign key (project_id) references projects(id);""",
        """alter table github_branch_pattern_projects add constraint fk_github_branch_pattern_projects_pattern_id foreign key (pattern_id) references github_branch_patterns(id);""",
        """alter table projects add column project_valid boolean default false;""",
        """alter table projects add column project_invalid_reason text;"""
    ]


def down() -> List[str]:
    return [
        "DROP TABLE github_branch_pattern_projects;",
        "DROP TABLE github_project_repo;",
        "DROP TABLE github_branch_patterns;",
        "ALTER TABLE projects DROP COLUMN project_valid;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
