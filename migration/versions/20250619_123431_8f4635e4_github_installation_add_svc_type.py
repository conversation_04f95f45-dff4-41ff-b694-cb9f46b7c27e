"""
Auto-generated migration: github_installation_add_svc_type
Version: 20250619_123431_8f4635e4
Created At: 2025-06-19T12:34:31.503633
Author: anlorn
"""

from typing import List, Optional
from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        "ALTER TABLE github_installations ADD COLUMN svc_type VARCHAR(50) NOT NULL DEFAULT 'GITHUB';"
    ]


def down() -> List[str]:
    return [
        "ALTER TABLE github_installations DROP COLUMN svc_type;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
