"""
Auto-generated migration: project_run
Version: 20250307_161048_8e790f06
Created At: 2025-03-07T16:10:48.677796
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        """
        create table if not exists project_runs
        (
        id varchar primary key not null,
        project_id varchar not null,
        code_gen_id varchar,
        tech_spec_id varchar,
        stage varchar(16),
        run_type varchar(16),
        status varchar(11),
        start_at timestamptz,
        completed_at timestamptz,
        job_metadata jsonb,
        is_deleted boolean,
        deleted_at timestamptz,
        created_at timestamptz,
        updated_at timestamptz
        );
        """,
        "ALTER TABLE project_runs ADD CONSTRAINT fk_project_runs_code_gen_id FOREIGN KEY (code_gen_id) REFERENCES code_generations(id);",
        "ALTER TABLE project_runs ADD CONSTRAINT fk_project_runs_tech_spec_id FOREIGN KEY (tech_spec_id) REFERENCES technical_specs(id);",
        "ALTER TABLE project_runs ADD CONSTRAINT fk_project_runs_project_id FOREIGN KEY (project_id) REFERENCES projects(id);"
    ]


def down() -> List[str]:
    return [
        "DROP TABLE project_runs;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
