"""
Auto-generated migration: tech_spec_document_context_repo_id
Version: 20250320_172612_6bc991f6
Created At: 2025-03-20T17:26:12.339807
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        "ALTER TABLE tech_spec_document_contexts ADD COLUMN repo_id VARCHAR;"
    ]


def down() -> List[str]:
    return [
        "ALTER TABLE tech_spec_document_contexts DROP COLUMN repo_id;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
