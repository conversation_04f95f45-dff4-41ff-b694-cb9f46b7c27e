"""
Auto-generated migration: company_info
Version: 20250307_203134_3ed7baf3
Created At: 2025-03-07T20:31:34.268612
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        "ALTER TABLE users ADD COLUMN company_id VARCHAR;",
        "ALTER TABLE users ADD CONSTRAINT fk_users_company_id FOREIGN KEY (company_id) REFERENCES companies(id);"
    ]


def down() -> List[str]:
    return [
        "ALTER TABLE users DROP COLUMN company_id;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
