"""
Manual migration: github_installation
Version: 20250116_165058_822129e6_github_installation
Created At: 2025-01-16T16:50:58.807455
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:  # noqa

    return [
        """
        create table github_repositories
        (
            id                     varchar      not null primary key,
            repo_id                varchar(100),
            repo_name              varchar(255) not null,
            repo_full_name         varchar(255),
            status                 varchar(50),
            project_id             varchar,
            github_installation_id varchar,
            user_id                varchar,
            is_archived            bool,
            onboarded_at           timestamptz,
            previous_names         jsonb,
            repository_metadata    jsonb,
            created_at             timestamptz  not null,
            updated_at             timestamptz  not null,
            deleted_at             timestamptz,
            is_deleted             bool,
            constraint fk_github_repositories_projects foreign key (project_id) references projects (id) on delete cascade,
            constraint fk_github_repositories_github_installations foreign key (github_installation_id) references github_installations (id) on delete cascade,
            constraint fk_github_repositories_user foreign key (user_id) references users (id) on delete cascade
        );
        """,
        """
        create index idx_github_repositories_repo_name on github_repositories (repo_name);
        """
    ]


def down() -> List[str]:
    return [
        "drop index idx_github_repositories_repo_name;",
        "drop table github_repositories;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
