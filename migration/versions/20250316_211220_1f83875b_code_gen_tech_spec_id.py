"""
Auto-generated migration: code_gen_tech_spec_id
Version: 20250316_211220_1f83875b
Created At: 2025-03-16T21:12:20.738126
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        "ALTER TABLE code_generations ADD COLUMN tech_spec_id VARCHAR;",
        "ALTER TABLE code_generations ADD CONSTRAINT fk_code_generations_tech_spec_id FOREIGN KEY (tech_spec_id) REFERENCES technical_specs(id);"
    ]


def down() -> List[str]:
    return [
        "ALTER TABLE code_generations DROP COLUMN tech_spec_id;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
