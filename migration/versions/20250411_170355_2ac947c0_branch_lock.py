"""
Auto-generated migration: branch_lock
Version: 20250411_170355_2ac947c0
Created At: 2025-04-11T17:03:55.205105
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        """
        create table branch_locks
        (
            id                varchar primary key not null,
            branch_pattern_id varchar,
            project_repo_id   varchar,
            project_run_id    varchar,
            project_id        varchar,
            lock_reason       varchar,
            is_active         boolean default true,
            locked_at         timestamptz,
            released_at       timestamptz,
            is_deleted        boolean,
            deleted_at        timestamptz,
            created_at        timestamptz,
            updated_at        timestamptz
        );
        """,
        "ALTER TABLE branch_locks ADD CONSTRAINT fk_branch_locks_project_repo_id FOREIGN KEY (project_repo_id) REFERENCES github_project_repo(id);",
        "ALTER TABLE branch_locks ADD CONSTRAINT fk_branch_locks_project_run_id FOREIGN KEY (project_run_id) REFERENCES project_runs(id);",
        "ALTER TABLE branch_locks ADD CONSTRAINT fk_branch_locks_branch_pattern_id FOREIGN KEY (branch_pattern_id) REFERENCES github_branch_patterns(id);",
        "ALTER TABLE branch_locks ADD CONSTRAINT fk_branch_locks_project_id FOREIGN KEY (project_id) REFERENCES projects (id);"
    ]


def down() -> List[str]:
    return [
        "DROP TABLE branch_locks;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
