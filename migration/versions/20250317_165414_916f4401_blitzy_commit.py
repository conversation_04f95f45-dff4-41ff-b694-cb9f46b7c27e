"""
Manual migration: blitzy_commit
Version: 20250317_165414_916f4401_blitzy_commit
Created At: 2025-03-17T16:54:14.680667
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
       """
        create table blitzy_commits (
            id varchar not null primary key,
            project_run_id varchar not null,
            code_gen_id varchar not null,
            org_name varchar,
            repo_name varchar,
            repo_id varchar,
            branch_name varchar,
            version_control_system varchar,
            blitzy_commit_hash varchar,
            blitzy_commit_url varchar,
            status varchar not null,
            original_head_commit_hash varchar,
            pr_number integer,
            pr_link varchar,
            pr_action varchar,
            commit_metadata jsonb,
            resolved_at timestamptz,
            created_at timestamptz not null,
            updated_at timestamptz not null,
            is_deleted boolean not null default false,
            deleted_at timestamptz,
            constraint fk_blitzy_commits_project_run_id foreign key (project_run_id) references project_runs(id),
            constraint fk_blitzy_commits_code_gen_id foreign key (code_gen_id) references code_generations(id)
        );
       """
    ]


def down() -> List[str]:
    return [
        "drop table blitzy_commits"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
