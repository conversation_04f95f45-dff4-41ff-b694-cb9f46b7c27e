"""
Manual migration: update_azure_columns
Version: 20250624_164202_18ad1d48_update_azure_columns
Created At: 2025-06-24T16:42:02.752690
Author: christian
"""

from typing import List, Optional
from sqlalchemy.orm import Session

def up() -> List[str]:
    return [
        "ALTER TABLE github_project_repo ADD COLUMN azure_project_id VARCHAR;",
        "ALTER TABLE github_project_repo ADD COLUMN azure_org_id VARCHAR;"
    ]


def down() -> List[str]:
    return [
        "ALTER TABLE github_project_repo drop COLUMN azure_project_id;",
        "ALTER TABLE github_project_repo drop COLUMN azure_org_id;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
