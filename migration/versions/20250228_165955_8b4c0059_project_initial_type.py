"""
Auto-generated migration: project_initial_type
Version: 20250228_165955_8b4c0059
Created At: 2025-02-28T16:59:55.064065
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        "ALTER TABLE technical_specs ADD COLUMN version INTEGER DEFAULT 0;",
        "ALTER TABLE technical_specs ADD COLUMN job_type VARCHAR(20);",
        "ALTER TABLE projects ADD COLUMN initial_type VARCHAR(16);",
        "ALTER TABLE TECHNICAL_SPECS ADD COLUMN PROMPT TEXT;",
    ]


def down() -> List[str]:
    return [
        "ALTER TABLE technical_specs DROP COLUMN version;",
        "ALTER TABLE technical_specs DROP COLUMN job_type;",
        "ALTER TABLE projects DROP COLUMN initial_type;",
        "ALTER TABLE TECHNICAL_SPECS DROP COLUMN PROMPT TEXT;",

    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
