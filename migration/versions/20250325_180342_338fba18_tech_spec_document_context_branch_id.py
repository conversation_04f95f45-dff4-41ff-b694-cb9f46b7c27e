"""
Auto-generated migration: tech_spec_document_context_branch_id
Version: 20250325_180342_338fba18
Created At: 2025-03-25T18:03:42.423456
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        "ALTER TABLE tech_spec_document_contexts ADD COLUMN branch_id VARCHAR;"
    ]


def down() -> List[str]:
    return [
        "ALTER TABLE tech_spec_document_contexts DROP COLUMN branch_id;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
