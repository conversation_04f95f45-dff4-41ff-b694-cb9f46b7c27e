"""
Auto-generated migration: project_disable
Version: 20250402_202841_8db0f675
Created At: 2025-04-02T20:28:41.090763
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        "ALTER TABLE projects ADD COLUMN is_disabled BOOLEAN DEFAULT False;",
        "ALTER TABLE projects ADD COLUMN disable_reason TEXT;"
    ]


def down() -> List[str]:
    return [
        "ALTER TABLE projects DROP COLUMN is_disabled;",
        "ALTER TABLE projects DROP COLUMN disable_reason;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
