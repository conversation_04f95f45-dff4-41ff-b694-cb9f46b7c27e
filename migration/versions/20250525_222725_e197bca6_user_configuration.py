"""
Auto-generated migration: user_configuration
Version: 20250525_222725_e197bca6
Created At: 2025-05-25T22:27:25.678624
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        """
        create table if not exists user_configs
        (
            user_id                        varchar,
            tech_spec_notification_enabled boolean,
            code_gen_notification_enabled  boolean,
            platform_config                jsonb,
            is_deleted                     boolean,
            deleted_at                     timestamptz,
            id                             varchar primary key not null,
            created_at                     timestamptz,
            updated_at                     timestamptz
        );
        """,
        "ALTER TABLE user_configs ADD CONSTRAINT fk_user_configs_user_id FOREIGN KEY (user_id) REFERENCES users(id);"
    ]


def down() -> List[str]:
    return [
        "DROP TABLE user_configs;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    pass
