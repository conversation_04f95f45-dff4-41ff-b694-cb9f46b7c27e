"""
Auto-generated migration: pending_installation_user_id
Version: 20250126_214357_a52ea487
Created At: 2025-01-26T21:43:57.149926
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        "ALTER TABLE pending_installation_requests ADD COLUMN user_id VARCHAR;",
        "ALTER TABLE pending_installation_requests ADD CONSTRAINT fk_pending_installation_requests_user_id FOREIGN KEY (user_id) REFERENCES users(id);"
    ]


def down() -> List[str]:
    return [
        "ALTER TABLE pending_installation_requests DROP COLUMN user_id;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
