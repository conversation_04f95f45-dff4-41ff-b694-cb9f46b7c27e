"""
Auto-generated migration: blitzy_commit_repo_url
Version: 20250319_181622_5c039f3d
Created At: 2025-03-19T18:16:22.810259
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        "ALTER TABLE blitzy_commits ADD COLUMN repo_url VARCHAR;"
    ]


def down() -> List[str]:
    return [
        "ALTER TABLE blitzy_commits DROP COLUMN repo_url;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
