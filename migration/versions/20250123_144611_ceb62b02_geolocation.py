"""
Manual migration: geolocation
Version: 20250123_144611_ceb62b02_geolocation
Created At: 2025-01-23T14:46:11.192344
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        """
         create table if not exists geolocations
         (
             id           varchar     not null primary key,
             user_id      varchar     not null,
             ip_address   varchar(45),
             country_code varchar(2),
             city         varchar(100),
             region       varchar(100),
             latitude     float8,
             longitude    float8,
             timezone     varchar(50),
             is_deleted   bool,
             deleted_at   timestamptz,
             created_at   timestamptz not null,
             updated_at   timestamptz not null,
             constraint fk_geolocations_users foreign key (user_id) references users (id) on delete cascade
         );
        """,
        "create index if not exists idx_country_code on geolocations (country_code);"
    ]


def down() -> List[str]:
    return [
        # Add your DOWN migration SQL statements here
        # Example:
        # "ALTER TABLE users DROP COLUMN new_field"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
