"""
Auto-generated migration: github_access_share
Version: 20250430_142102_197fe515
Created At: 2025-04-30T14:21:02.520163
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        """
        create table github_installation_access
        (
            id                 varchar primary key not null,
            integration_id     varchar,
            access_type        varchar,
            entity_id          varchar,
            role               varchar,
            is_owner           boolean default false,
            granted_by_user_id varchar,
            granted_at         timestamptz,
            is_deleted         boolean,
            deleted_at         timestamptz,
            created_at         timestamptz,
            updated_at         timestamptz
        );
        """,
        "alter table github_installation_access add constraint fk_github_installation_access_granted_by_user_id foreign key (granted_by_user_id) references users(id);",
        "alter table github_installation_access add constraint fk_github_installation_access_integration_id foreign key (integration_id) references github_installations(id);",
        "alter table github_installations add column is_shared boolean default false;"
    ]


def down() -> List[str]:
    return [
        "DROP TABLE github_installation_access;",
        "ALTER TABLE github_installations DROP COLUMN is_shared;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
