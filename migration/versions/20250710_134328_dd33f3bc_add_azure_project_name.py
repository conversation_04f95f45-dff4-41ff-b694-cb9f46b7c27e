"""
Manual migration: add_azure_project_name
Version: 20250710_134328_dd33f3bc_add_azure_project_name
Created At: 2025-07-10T13:43:28.475236
Author: christian
"""

from typing import List, Optional
from sqlalchemy.orm import Session


def up() -> List[str]:
    return [

        "ALTER TABLE github_project_repo ADD COLUMN azure_project_name varchar"
    ]


def down() -> List[str]:
    return [

        "ALTER TABLE github_project_repo DROP COLUMN azure_project_name"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
