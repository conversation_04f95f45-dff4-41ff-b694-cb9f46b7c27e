"""
Auto-generated migration: user_registration_status
Version: 20250521_160442_dd01f70a
Created At: 2025-05-21T16:04:42.330619
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        "ALTER TABLE users ADD COLUMN registration_completed BOOLEAN DEFAULT False;"
    ]


def down() -> List[str]:
    return [
        "ALTER TABLE users DROP COLUMN registration_completed;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
