"""
Auto-generated migration: blitzy_commit_branch_url
Version: 20250329_171618_de3b2be0
Created At: 2025-03-29T17:16:18.582926
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        "ALTER TABLE blitzy_commits ADD COLUMN blitzy_branch_url VARCHAR;"
    ]


def down() -> List[str]:
    return [
        "ALTER TABLE blitzy_commits DROP COLUMN blitzy_branch_url;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
