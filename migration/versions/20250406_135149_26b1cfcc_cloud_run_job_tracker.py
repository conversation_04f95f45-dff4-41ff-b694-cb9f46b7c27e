"""
Manual migration: cloud_run_job_tracker
Version: 20250406_135149_26b1cfcc_cloud_run_job_tracker
Created At: 2025-04-06T13:51:49.488585
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        """
        create table cloud_run_job_trackers
        (
            id                      varchar     not null primary key,
            project_id              varchar,
            tech_spec_id            varchar,
            code_gen_id             varchar,
            job_id                  varchar,
            job_phase               varchar(50),
            job_status              varchar(50),
            job_name                varchar,
            event_data              jsonb,
            job_submission_metadata jsonb,
            user_id                 varchar,
            is_triggered            boolean default false,
            trigger_topic           varchar,
            is_deleted              boolean,
            deleted_at              timestamptz,
            created_at              timestamptz,
            updated_at              timestamptz,
            constraint fk_cloud_run_job_trackers_project foreign key (project_id) references projects (id),
            constraint fk_cloud_run_job_trackers_user foreign key (user_id) references users (id)
        );
        """
    ]


def down() -> List[str]:
    return [
        "drop table cloud_run_job_trackers;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
