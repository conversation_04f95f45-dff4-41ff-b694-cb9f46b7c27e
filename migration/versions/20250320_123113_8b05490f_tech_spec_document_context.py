"""
Manual migration: tech_spec_document_context
Version: 20250320_123113_8b05490f_tech_spec_document_context
Created At: 2025-03-20T12:31:13.373156
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        """
        create table tech_spec_document_contexts
        (
            id               varchar     not null primary key,
            company_id       varchar,
            project_id       varchar,
            team_id          varchar,
            tech_spec_id     varchar,
            org_name         varchar,
            repo_name        varchar,
            branch_name      varchar,
            head_commit_hash varchar,
            context_metadata jsonb,
            created_at       timestamptz not null,
            updated_at       timestamptz not null,
            is_deleted       boolean     not null default false,
            deleted_at       timestamptz
        );
        """
    ]


def down() -> List[str]:
    return [
        """
        drop table tech_spec_document_contexts;
        """
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
