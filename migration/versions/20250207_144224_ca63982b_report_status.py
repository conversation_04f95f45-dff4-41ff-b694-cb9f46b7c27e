"""
Auto-generated migration: report_status
Version: 20250207_144224_ca63982b
Created At: 2025-02-07T14:42:24.805272
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        "ALTER TABLE technical_specs ADD COLUMN pdf_report_status VARCHAR(50);"
    ]


def down() -> List[str]:
    return [
        "ALTER TABLE technical_specs DROP COLUMN pdf_report_status;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
