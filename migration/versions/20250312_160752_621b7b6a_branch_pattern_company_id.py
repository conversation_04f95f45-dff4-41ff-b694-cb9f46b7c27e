"""
Auto-generated migration: branch_pattern_company_id
Version: 20250312_160752_621b7b6a
Created At: 2025-03-12T16:07:52.929408
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        "ALTER TABLE github_branch_patterns ADD COLUMN company_id VARCHAR;"
    ]


def down() -> List[str]:
    return [
        "ALTER TABLE github_branch_patterns DROP COLUMN company_id;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
