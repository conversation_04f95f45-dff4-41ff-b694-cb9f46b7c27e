"""
Manual migration: user_configs_migration
Version: 20250627_161036_2a9e545c_user_configs_migration
Created At: 2025-06-27T16:10:36.106893
Author: christian
"""

from typing import List, Optional
from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        """
        INSERT INTO user_configs (
            id,
            user_id,
            tech_spec_notification_enabled,
            code_gen_notification_enabled,
            platform_config,
            is_deleted,
            deleted_at,
            created_at,
            updated_at
        )
        SELECT 
            CAST(spanner.generate_uuid() AS varchar) as id,
            CAST(u.id AS varchar) as user_id,
            CAST(true AS boolean) as tech_spec_notification_enabled,
            CAST(true AS boolean) as code_gen_notification_enabled,
            CAST('{}' AS jsonb) as platform_config,
            CAST(false AS boolean) as is_deleted,
            CAST(NULL AS timestamptz) as deleted_at,
            CAST(CURRENT_TIMESTAMP AS timestamptz) as created_at,
            CAST(CURRENT_TIMESTAMP AS timestamptz) as updated_at
        FROM users u
        LEFT JOIN user_configs uc ON u.id = uc.user_id
        WHERE uc.user_id IS NULL
        AND u.is_deleted = false;
        """
    ]


def down() -> List[str]:
    return [
        # Add your DOWN migration SQL statements here
        # Example:
        # "ALTER TABLE users DROP COLUMN new_field"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
