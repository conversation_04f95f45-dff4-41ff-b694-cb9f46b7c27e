"""
Auto-generated migration: add_teams_companies_and_teammembers_tables
Version: 20250227_115419_755d962e
Created At: 2025-02-27T11:54:19.911965
Author: adars<PERSON><PERSON><PERSON><PERSON>har
"""

from typing import List, Optional
from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        """
        CREATE TABLE companies (
            id VARCHAR PRIMARY KEY NOT NULL,
            name VARCHAR(100),
            domain VARCHAR(100),
            status VARCHAR(9),
            is_default BOOLEAN DEFAULT True,
            settings JSONB,
            is_deleted BOOLEAN NOT NULL DEFAULT False,
            deleted_at TIMESTAMPTZ,
            created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
        );
        """,
        """
        CREATE TABLE teams (
            id VARCHAR PRIMARY KEY NOT NULL,
            company_id VARCHAR NOT NULL,
            name <PERSON><PERSON><PERSON><PERSON>(100),
            is_default BOOLEAN DEFAULT True,
            is_admin_team BOOLEAN DEFAULT True,
            settings JSONB,
            is_deleted BOOLEAN NOT NULL DEFAULT False,
            deleted_at TIMESTAMPTZ,
            created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT fk_teams_company_id FOREIGN KEY (company_id) REFERENCES companies(id)
        );
        """,
        """
        CREATE TABLE teammembers (
            id VARCHAR PRIMARY KEY NOT NULL,
            team_id VARCHAR NOT NULL,
            user_id VARCHAR NOT NULL,
            company_id VARCHAR NOT NULL,
            role VARCHAR(6),
            role_settings JSONB,
            joined_at TIMESTAMPTZ,
            is_deleted BOOLEAN NOT NULL DEFAULT False,
            deleted_at TIMESTAMPTZ,
            created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT fk_teammembers_company_id FOREIGN KEY (company_id) REFERENCES companies(id),
            CONSTRAINT fk_teammembers_team_id FOREIGN KEY (team_id) REFERENCES teams(id),
            CONSTRAINT fk_teammembers_user_id FOREIGN KEY (user_id) REFERENCES users(id)
        );
        """
    ]


def down() -> List[str]:
    return [
        "DROP TABLE teammembers;",
        "DROP TABLE teams;",
        "DROP TABLE companies;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass