"""
Manual migration: update_registration_status
Version: 20250609_213351_8d55c96f_update_registration_status
Created At: 2025-06-09T21:33:51.210928
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        "update users set registration_completed = true"
    ]


def down() -> List[str]:
    return [
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
