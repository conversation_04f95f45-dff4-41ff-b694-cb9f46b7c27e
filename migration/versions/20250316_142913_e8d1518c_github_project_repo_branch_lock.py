"""
Auto-generated migration: github_project_repo_branch_lock
Version: 20250316_142913_e8d1518c
Created At: 2025-03-16T14:29:13.359208
Author: axe
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        "ALTER TABLE github_project_repo ADD COLUMN branch_lock BOOLEAN;"
    ]


def down() -> List[str]:
    return [
        "ALTER TABLE github_project_repo DROP COLUMN branch_lock;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
