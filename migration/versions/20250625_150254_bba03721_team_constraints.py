"""
Manual migration: team_constraints
Version: 20250625_150254_bba03721_team_constraints
Created At: 2025-06-25T15:02:54.989776
Author: christian
"""

from typing import List, Optional
from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        "alter table teams drop CONSTRAINT fk_teams_company_id;",
        """ALTER TABLE teams 
        ALTER COLUMN company_id TYPE varchar,
        ALTER COLUMN company_id DROP NOT NULL;"""
    ]


def down() -> List[str]:
    return [
       """ALTER TABLE teams 
        ALTER COLUMN company_id TYPE varchar,
        ALTER COLUMN company_id SET NOT NULL;""",

        "alter table teams add CONSTRAINT fk_teams_company_id FOREIGN KEY (company_id) REFERENCES companies(id);"

    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
