import os
import re
import uuid
from datetime import datetime
from pathlib import Path
from typing import Optional, Set

from common_models.db_client import SpannerClientSingleton
from migration.differ import SimpleSchemaInspector


class MigrationGenerator:
    def __init__(self):
        self.migration_dir = Path(__file__).parent
        self.versions_dir = self.migration_dir / "versions"
        self.templates_dir = self.migration_dir / "templates"

        # Ensure directories exist
        self.versions_dir.mkdir(exist_ok=True)
        self.templates_dir.mkdir(exist_ok=True)

    def _generate_version_id(self) -> str:
        """Generate a unique version ID using timestamp and uuid"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        return f"{timestamp}_{unique_id}"

    def _sanitize_name(self, name: str) -> str:
        """Convert name to snake case and remove special characters"""
        name = re.sub(r'[^a-zA-Z0-9\s_]', '', name)
        name = name.lower().replace(' ', '_')
        return name

    def _read_template(self, template_type: str) -> str:
        """Read template content from file"""
        template_path = self.templates_dir / f"{template_type}.py.tmpl"
        if not template_path.exists():
            raise FileNotFoundError(f"Template {template_type} not found")

        with open(template_path, 'r') as f:
            return f.read()

    def _write_migration_file(self, version_id: str, content: str) -> Path:
        """Write migration content to file"""
        file_path = self.versions_dir / f"{version_id}.py"
        with open(file_path, 'w') as f:
            f.write(content)
        return file_path

    def generate_manual(self, name: str, description: Optional[str] = None) -> Path:
        """Generate a manual migration file for both schema and data changes"""
        version_id = self._generate_version_id()
        sanitized_name = self._sanitize_name(name)
        full_version_id = f"{version_id}_{sanitized_name}"

        template = self._read_template('manual')
        content = template.format(
            version_id=full_version_id,
            description=description or name,
            created_at=datetime.now().isoformat(),
            author=os.getenv('USER', 'unknown')
        )

        return self._write_migration_file(full_version_id, content)

    def generate_auto(self, name: str, description: Optional[str] = None) -> Path:
        """Generate migration file from schema differences"""
        client = SpannerClientSingleton()
        differ = SimpleSchemaInspector(client.engine)

        # Get current schema from database
        current_schema = differ.get_all_tables()

        # Get schema from models
        from common_models.models import Base
        target_schema = {}

        for table in Base.metadata.tables.values():
            columns = {}

            # Add all columns
            for column in table.columns:
                type_str = str(column.type)
                columns[column.name] = {
                    'type': type_str,
                    'normalized_type': self._format_column_type(type_str),
                    'nullable': column.nullable,
                    'primary_key': column.primary_key,
                    'foreign_key': None,
                    'default': self._format_default(column.default),
                    'unique': column.unique
                }

                # Handle foreign keys
                if column.foreign_keys:
                    fk = list(column.foreign_keys)[0]
                    columns[column.name]['foreign_key'] = {
                        'table': fk.column.table.name,
                        'column': fk.column.name
                    }

            target_schema[table.name] = {
                'columns': columns,
                'foreign_keys': [
                    {
                        'column': fk.parent.name,
                        'references_table': fk.column.table.name,
                        'references_column': fk.column.name
                    }
                    for fk in table.foreign_keys
                ]
            }

        # Calculate dependencies with cycle detection
        dependency_map = {}

        def get_dependencies(table_name: str, visited=None) -> Set[str]:
            """Get all tables this table depends on with cycle detection"""
            if table_name in dependency_map:
                return dependency_map[table_name]

            if visited is None:
                visited = set()

            # Skip if we're in a cycle
            if table_name in visited:
                return set()

            visited.add(table_name)

            if table_name not in target_schema:
                dependency_map[table_name] = set()
                return set()

            deps = set()
            for fk in target_schema[table_name].get('foreign_keys', []):
                ref_table = fk['references_table']
                if ref_table != table_name:  # Avoid self-references
                    deps.add(ref_table)
                    deps.update(get_dependencies(ref_table, visited.copy()))

            dependency_map[table_name] = deps
            return deps

        # Generate DDL statements
        up_commands = []
        down_commands = []

        # Handle new tables in dependency order
        new_tables = list(set(target_schema.keys()) - set(current_schema.keys()))

        # Calculate dependencies for all tables first to avoid recursion issues
        for table in new_tables:
            get_dependencies(table)

        # Sort tables by dependency count
        new_tables.sort(key=lambda x: len(dependency_map.get(x, set())))

        for table_name in new_tables:
            table_info = target_schema[table_name]

            # Generate column definitions
            column_defs = []
            for col_name, col_info in table_info['columns'].items():
                col_def = f"{col_name} {col_info['type']}"

                if col_info.get('primary_key'):
                    col_def += " PRIMARY KEY"
                if not col_info.get('nullable', True):
                    col_def += " NOT NULL"
                if col_info.get('unique'):
                    col_def += " UNIQUE"
                if col_info.get('default'):
                    col_def += f" DEFAULT {col_info['default']}"

                column_defs.append(col_def)

            # Create table
            up_commands.append(
                f"CREATE TABLE {table_name} ({', '.join(column_defs)})"
            )

            # Add foreign key constraints
            for fk in table_info.get('foreign_keys', []):
                fk_name = f"fk_{table_name}_{fk['column']}"
                up_commands.append(
                    f"ALTER TABLE {table_name} ADD CONSTRAINT {fk_name} "
                    f"FOREIGN KEY ({fk['column']}) REFERENCES {fk['references_table']}({fk['references_column']})"
                )

        # Down commands in reverse order
        down_commands = [f"DROP TABLE {table_name}" for table_name in reversed(new_tables)]

        # Handle column changes in existing tables
        for table_name in set(target_schema.keys()) & set(current_schema.keys()):
            target_cols = target_schema[table_name]['columns']
            current_cols = current_schema[table_name]['columns']

            # Find new columns
            for col_name, col_info in target_cols.items():
                if col_name not in current_cols:
                    # Add column
                    col_def = f"{col_name} {col_info['type']}"
                    if not col_info.get('nullable', True):
                        col_def += " NOT NULL"
                    if col_info.get('unique'):
                        col_def += " UNIQUE"
                    if col_info.get('default'):
                        col_def += f" DEFAULT {col_info['default']}"

                    up_commands.append(
                        f"ALTER TABLE {table_name} ADD COLUMN {col_def}"
                    )

                    # Add foreign key constraint if it's a FK
                    if col_info.get('foreign_key'):
                        fk = col_info['foreign_key']
                        fk_name = f"fk_{table_name}_{col_name}"
                        up_commands.append(
                            f"ALTER TABLE {table_name} ADD CONSTRAINT {fk_name} "
                            f"FOREIGN KEY ({col_name}) REFERENCES {fk['table']}({fk['column']})"
                        )

                    # Generate down commands
                    down_commands.append(f"ALTER TABLE {table_name} DROP COLUMN {col_name}")

        if not up_commands:
            print("No schema changes detected")
            return None

        # Generate migration file
        version_id = self._generate_version_id()
        template = self._read_template('auto')

        # Format SQL commands without extra indentation
        formatted_up_commands = [f'"{cmd};"' for cmd in up_commands]
        formatted_down_commands = [f'"{cmd};"' for cmd in down_commands]

        content = template.format(
            version_id=version_id,
            description=description or f"Auto-generated migration: {name}",
            created_at=datetime.now().isoformat(),
            author=os.getenv('USER', 'unknown'),
            up_commands=',\n        '.join(formatted_up_commands),
            down_commands=',\n        '.join(formatted_down_commands)
        )

        return self._write_migration_file(f"{version_id}_{name}", content)

    def generate_baseline(self) -> Path:
        """Generate baseline migration file"""
        version_id = "000_baseline"

        template = self._read_template('baseline')
        content = template.format(
            created_at=datetime.now().isoformat(),
            author=os.getenv('USER', 'unknown')
        )

        return self._write_migration_file(version_id, content)

    def list_pending_migrations(self) -> list[str]:
        """List all migration files that haven't been applied yet"""
        # TODO: Compare with schema_migrations table
        return [f.stem for f in self.versions_dir.glob("*.py")
                if f.stem != '__init__']

    def validate_migration_file(self, file_path: Path) -> bool:
        """
        Validate migration file format and content
        Returns True if valid, raises ValueError if invalid
        """
        required_functions = ['up', 'down']

        with open(file_path, 'r') as f:
            content = f.read()

        # Basic validation - check if required functions exist
        for func in required_functions:
            if f"def {func}(" not in content:
                raise ValueError(f"Missing required function: {func}")

        return True

    def _format_default(self, default) -> Optional[str]:
        """Format default values properly"""
        if default is None:
            return None

        # Convert the default to string for inspection
        default_str = str(default)

        # Handle SQLAlchemy func.now()
        if 'functions.now' in default_str:
            return "CURRENT_TIMESTAMP"

        # Handle UUID default
        if 'uuid' in default_str.lower():
            return "gen_random_uuid()"

        # Handle boolean defaults
        if isinstance(default, bool):
            return str(default).upper()

        # Handle scalar defaults
        if hasattr(default, 'arg'):
            return str(default.arg)

        return str(default)

    def _format_column_type(self, col_type: str) -> str:
        """Normalize column types for Spanner"""
        type_mapping = {
            'DATETIME': 'timestamptz',
            'TIMESTAMP': 'timestamptz',
            'VARCHAR': 'varchar',
            'INTEGER': 'integer',
            'FLOAT': 'float8',
            'BOOLEAN': 'boolean'
        }
        base_type = col_type.split('(')[0].upper()
        if base_type in type_mapping:
            if '(' in col_type:
                return f"{type_mapping[base_type]}{col_type[col_type.index('('):]}"
            return type_mapping[base_type]
        return col_type
