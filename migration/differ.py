import logging
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional

from sqlalchemy import text
from sqlalchemy.engine import Engine


class ChangeType(Enum):
    """Enum defining all possible schema change types"""
    CREATE_TABLE = "create_table"
    DROP_TABLE = "drop_table"
    ADD_COLUMN = "add_column"
    DROP_COLUMN = "drop_column"
    RENAME_COLUMN = "rename_column"
    MODIFY_COLUMN = "modify_column"
    ADD_INDEX = "add_index"
    DROP_INDEX = "drop_index"
    ADD_FOREIGN_KEY = "add_foreign_key"
    DROP_FOREIGN_KEY = "drop_foreign_key"
    MODIFY_FOREIGN_KEY = "modify_foreign_key"


@dataclass
class SchemaChange:
    """Data class representing a single schema change"""
    type: ChangeType
    table_name: str
    column_name: Optional[str] = None
    old_value: Optional[dict] = None
    new_value: Optional[dict] = None
    index_name: Optional[str] = None
    constraint_name: Optional[str] = None


class SpannerLimitations:
    """Constants defining Spanner's limitations"""
    MAX_STRING_LENGTH = 2621440  # ~2.5MB
    MAX_COLUMNS_PER_TABLE = 1024
    MAX_INDEXES_PER_TABLE = 40

    # Updated mapping based on actual schema
    TYPE_MAPPING = {
        'character varying': 'STRING',
        'boolean': 'BOOL',
        'timestamp with time zone': 'TIMESTAMP',
        'bigint': 'INT64',
        'double precision': 'FLOAT64',
        'jsonb': 'JSON'
    }

    # Common length patterns seen in schema
    COMMON_LENGTHS = {
        'character varying': {
            'id': None,
            'email': 255,
            'name': 255,
            'url': 1000,
            'status': 50,
            'code': 50
        }
    }

    CONSTRAINTS = {
        'PRIMARY_KEY': 'PRIMARY KEY',
        'FOREIGN_KEY': 'FOREIGN KEY',
        'CHECK': 'CHECK',
        'NOT_NULL': 'NOT NULL'
    }


class ValidationError(Exception):
    """Custom exception for schema validation errors"""
    pass


class SpannerValidator:
    """Validates schema changes against Spanner limitations"""

    @staticmethod
    def validate_column(column_info: dict) -> List[str]:
        """Validate column definition against Spanner limitations"""
        errors = []

        # Validate column type and length
        col_type = column_info['type'].lower()

        # Handle varchar length
        if 'varchar' in col_type and '(' in col_type:
            try:
                length = int(col_type.split('(')[1].split(')')[0])
                if length > SpannerLimitations.MAX_STRING_LENGTH:
                    errors.append(f"varchar length {length} exceeds maximum of {SpannerLimitations.MAX_STRING_LENGTH}")
            except (IndexError, ValueError):
                errors.append("Invalid varchar length specification")

        # Validate timestamp
        if 'timestamp' in col_type:
            if 'timezone' not in col_type and 'timestamptz' not in col_type:
                errors.append("Use timestamp with time zone for Spanner compatibility")

        # Validate default values
        default = column_info.get('default')
        if default:
            if isinstance(default, str) and 'now()' in default.lower():
                errors.append("Use CURRENT_TIMESTAMP instead of NOW()")

        return errors

    @staticmethod
    def validate_table(table_info: dict) -> List[str]:
        """Validate table definition against Spanner limitations"""
        errors = []

        # Check column count
        if len(table_info['columns']) > SpannerLimitations.MAX_COLUMNS_PER_TABLE:
            errors.append(f"Table exceeds maximum column count of {SpannerLimitations.MAX_COLUMNS_PER_TABLE}")

        # Check index count
        if len(table_info.get('indexes', {})) > SpannerLimitations.MAX_INDEXES_PER_TABLE:
            errors.append(f"Table exceeds maximum index count of {SpannerLimitations.MAX_INDEXES_PER_TABLE}")

        return errors


# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class SimpleSchemaInspector:
    def __init__(self, engine: Engine):
        self.engine = engine
        logger.info("Initialized SchemaInspector with engine")

    def get_all_tables(self) -> Dict[str, dict]:
        """Get all user tables and their details"""
        logger.info("Starting schema inspection for user tables...")

        with self.engine.connect() as connection:
            logger.info("Connected to database")

            # Get only user tables (excluding system tables)
            table_query = text("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
            """)

            logger.info("Executing table query...")
            result = connection.execute(table_query)
            table_names = [row[0] for row in result]
            logger.info(f"Found {len(table_names)} user tables: {', '.join(table_names)}")

            # Get details for each table
            tables = {}
            for table_name in table_names:
                logger.info(f"\nInspecting table: {table_name}")
                tables[table_name] = self.get_table_schema(table_name, connection)

            return tables

    def get_table_schema(self, table_name: str, connection) -> dict:
        """Get schema information for a specific table"""
        logger.info(f"Getting complete schema for table: {table_name}")

        schema = {
            'columns': self._get_columns(table_name, connection),
            'indexes': self._get_indexes(table_name, connection),
            'constraints': self._get_constraints(table_name, connection)
        }

        logger.info(f"Found {len(schema['columns'])} columns, {len(schema['indexes'])} indexes, "
                    f"and {len(schema['constraints'])} constraints")
        return schema

    def _get_columns(self, table_name: str, connection) -> Dict[str, dict]:
        """Get column information"""
        logger.info(f"Fetching columns for table: {table_name}")

        column_query = text(f"""
            SELECT
                column_name,
                spanner_type,
                is_nullable,
                column_default
            FROM information_schema.columns
            WHERE table_name = '{table_name}'
            ORDER BY ordinal_position
        """)

        result = connection.execute(column_query)

        columns = {}
        for row in result:
            columns[row.column_name] = {
                'type': row.spanner_type,
                'nullable': row.is_nullable == 'YES',
                'default': row.column_default
            }
            logger.debug(f"Found column: {row.column_name} ({row.spanner_type})")

        logger.info(f"Found {len(columns)} columns in {table_name}")
        return columns

    def _get_indexes(self, table_name: str, connection) -> Dict[str, dict]:
        """Get index information"""
        logger.info(f"Fetching indexes for table: {table_name}")

        index_query = text(f"""
            SELECT
                index_name,
                index_type,
                is_unique,
                is_null_filtered
            FROM information_schema.indexes
            WHERE table_name = '{table_name}'
        """)

        try:
            result = connection.execute(index_query)
            indexes = {}
            for row in result:
                indexes[row.index_name] = {
                    'type': row.index_type,
                    'unique': row.is_unique == 'YES',
                    'null_filtered': row.is_null_filtered == 'YES'
                }
                logger.debug(f"Found index: {row.index_name} ({row.index_type})")

            logger.info(f"Found {len(indexes)} indexes in {table_name}")
            return indexes

        except Exception as e:
            logger.error(f"Error fetching indexes for {table_name}: {str(e)}")
            return {}

    def _get_constraints(self, table_name: str, connection) -> Dict[str, dict]:
        """Get constraint information"""
        logger.info(f"Fetching constraints for table: {table_name}")

        constraint_query = text(f"""
            SELECT
                constraint_name,
                constraint_type
            FROM information_schema.table_constraints
            WHERE table_name = '{table_name}'
        """)

        try:
            result = connection.execute(constraint_query)
            constraints = {}
            for row in result:
                constraints[row.constraint_name] = {
                    'type': row.constraint_type
                }
                logger.debug(f"Found constraint: {row.constraint_name} ({row.constraint_type})")

            logger.info(f"Found {len(constraints)} constraints in {table_name}")
            return constraints

        except Exception as e:
            logger.error(f"Error fetching constraints for {table_name}: {str(e)}")
            return {}


def print_schema_info(table_name: str, table_info: dict):
    """Helper function to print schema information in a readable format"""
    print("\n" + "=" * 50)
    print(f"TABLE: {table_name}")
    print("=" * 50)

    print("\nCOLUMNS:")
    print("-" * 30)
    for col_name, col_info in table_info['columns'].items():
        nullable = "NULL" if col_info['nullable'] else "NOT NULL"
        default = f"DEFAULT {col_info['default']}" if col_info['default'] else ""
        print(f"  {col_name}: {col_info['type']} {nullable} {default}")

    if table_info['indexes']:
        print("\nINDEXES:")
        print("-" * 30)
        for idx_name, idx_info in table_info['indexes'].items():
            unique = "UNIQUE " if idx_info['unique'] else ""
            print(f"  {unique}INDEX {idx_name} ({idx_info['type']})")

    if table_info['constraints']:
        print("\nCONSTRAINTS:")
        print("-" * 30)
        for const_name, const_info in table_info['constraints'].items():
            print(f"  {const_name}: {const_info['type']}")

    print("\n")


class DiffGenerator:
    """Generates schema differences between current and target schemas"""

    def __init__(self, inspector: SimpleSchemaInspector, validator: SpannerValidator):
        self.inspector = inspector
        self.validator = validator

    def generate_diff(self, current_schema: Dict, target_schema: Dict) -> List[SchemaChange]:
        """Generate list of schema changes between current and target schemas"""
        changes = []

        # Get all table names from both schemas
        all_tables = set(current_schema.keys()) | set(target_schema.keys())

        for table_name in all_tables:
            # Handle table addition
            if table_name not in current_schema:
                changes.append(SchemaChange(
                    type=ChangeType.CREATE_TABLE,
                    table_name=table_name,
                    new_value=target_schema[table_name]
                ))
                continue

            # Handle table removal
            if table_name not in target_schema:
                changes.append(SchemaChange(
                    type=ChangeType.DROP_TABLE,
                    table_name=table_name,
                    old_value=current_schema[table_name]
                ))
                continue

            # For existing tables, compare structure
            changes.extend(self._diff_table(
                table_name,
                current_schema[table_name],
                target_schema[table_name]
            ))

        return self._sort_changes(changes)

    def _diff_table(self, table_name: str, current: Dict, target: Dict) -> List[SchemaChange]:
        """Generate changes for a single table"""
        changes = []

        # Compare columns
        changes.extend(self._diff_columns(table_name, current['columns'], target['columns']))

        # Compare indexes
        changes.extend(self._diff_indexes(table_name, current['indexes'], target['indexes']))

        # Compare foreign keys
        changes.extend(self._diff_foreign_keys(table_name, current['foreign_keys'], target['foreign_keys']))

        return changes

    def _diff_columns(self, table_name: str, current_cols: Dict, target_cols: Dict) -> List[SchemaChange]:
        """Compare columns and detect changes including renames"""
        changes = []

        # First, try to detect renamed columns
        removed_cols = set(current_cols.keys()) - set(target_cols.keys())
        added_cols = set(target_cols.keys()) - set(current_cols.keys())

        rename_pairs = self._detect_renames(
            {name: current_cols[name] for name in removed_cols},
            {name: target_cols[name] for name in added_cols}
        )

        # Handle renames
        for old_name, new_name in rename_pairs.items():
            changes.append(SchemaChange(
                type=ChangeType.RENAME_COLUMN,
                table_name=table_name,
                column_name=new_name,
                old_value={'name': old_name, **current_cols[old_name]},
                new_value={'name': new_name, **target_cols[new_name]}
            ))
            removed_cols.remove(old_name)
            added_cols.remove(new_name)

        # Handle remaining column additions
        for col_name in added_cols:
            col_info = target_cols[col_name]
            # Validate new column
            errors = self.validator.validate_column(col_info)
            if errors:
                raise ValidationError(f"Invalid column {col_name}: {', '.join(errors)}")

            changes.append(SchemaChange(
                type=ChangeType.ADD_COLUMN,
                table_name=table_name,
                column_name=col_name,
                new_value=col_info
            ))

        # Handle remaining column removals
        for col_name in removed_cols:
            changes.append(SchemaChange(
                type=ChangeType.DROP_COLUMN,
                table_name=table_name,
                column_name=col_name,
                old_value=current_cols[col_name]
            ))

        # Handle modifications to existing columns
        common_cols = set(current_cols.keys()) & set(target_cols.keys())
        for col_name in common_cols:
            if self._is_column_modified(current_cols[col_name], target_cols[col_name]):
                changes.append(SchemaChange(
                    type=ChangeType.MODIFY_COLUMN,
                    table_name=table_name,
                    column_name=col_name,
                    old_value=current_cols[col_name],
                    new_value=target_cols[col_name]
                ))

        return changes

    def _detect_renames(self, removed: Dict, added: Dict) -> Dict[str, str]:
        """Detect potential column renames based on similarity"""
        renames = {}

        # Simple case: exactly one removal and one addition
        if len(removed) == 1 and len(added) == 1:
            old_name = list(removed.keys())[0]
            new_name = list(added.keys())[0]
            old_col = removed[old_name]
            new_col = added[new_name]

            if self._columns_are_similar(old_col, new_col):
                renames[old_name] = new_name
                return renames

        # For multiple changes, use similarity matching
        for old_name, old_col in removed.items():
            best_match = None
            highest_similarity = 0.7  # Minimum similarity threshold

            for new_name, new_col in added.items():
                if new_name not in renames.values():
                    similarity = self._calculate_column_similarity(old_col, new_col)
                    if similarity > highest_similarity:
                        highest_similarity = similarity
                        best_match = new_name

            if best_match:
                renames[old_name] = best_match

        return renames

    def _columns_are_similar(self, col1: Dict, col2: Dict) -> bool:
        """Check if two columns are similar enough to be considered a rename"""
        return (
                col1['type'] == col2['type'] and
                col1['nullable'] == col2['nullable'] and
                col1['primary_key'] == col2['primary_key']
        )

    def _calculate_column_similarity(self, col1: Dict, col2: Dict) -> float:
        """Calculate similarity score between two columns"""
        score = 0.0
        total_checks = 4

        # Type match is most important
        if col1['type'] == col2['type']:
            score += 2.0

        # Other properties contribute less
        if col1['nullable'] == col2['nullable']:
            score += 0.5
        if col1['primary_key'] == col2['primary_key']:
            score += 0.5

        return score / total_checks

    def _sort_changes(self, changes: List[SchemaChange]) -> List[SchemaChange]:
        """Sort changes in the correct execution order"""
        change_priority = {
            ChangeType.DROP_FOREIGN_KEY: 1,
            ChangeType.DROP_INDEX: 2,
            ChangeType.DROP_COLUMN: 3,
            ChangeType.DROP_TABLE: 4,
            ChangeType.CREATE_TABLE: 5,
            ChangeType.ADD_COLUMN: 6,
            ChangeType.MODIFY_COLUMN: 7,
            ChangeType.RENAME_COLUMN: 8,
            ChangeType.ADD_INDEX: 9,
            ChangeType.ADD_FOREIGN_KEY: 10
        }

        return sorted(changes, key=lambda x: change_priority[x.type])
