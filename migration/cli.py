import logging
from pathlib import Path
from typing import Optional

import click
from sqlalchemy import text

from common_models.db_client import SpannerClientSingleton
from migration.executor import MigrationExecutor
from migration.generator import MigrationGenerator

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Initialize components
client = SpannerClientSingleton()
migration_dir = Path(__file__).parent
generator = MigrationGenerator()
executor = MigrationExecutor(client.engine, migration_dir / "versions")


@click.group()
def cli():
    """Database migration tool"""
    pass


@cli.command()
@click.option('--name', required=True, help='Name of the migration')
@click.option('--auto/--manual', default=False, help='Auto generate from schema changes or create manual migration')
@click.option('--description', help='Migration description')
def generate(name: str, auto: bool, description: Optional[str] = None):
    """Generate a new migration file"""
    try:
        if auto:
            logger.info("Generating automatic migration...")
            migration_file = generator.generate_auto(
                name=name,
                description=description or f"Auto-generated migration: {name}"
            )
        else:
            logger.info("Generating manual migration template...")
            migration_file = generator.generate_manual(
                name=name,
                description=description or f"Manual migration: {name}"
            )

        logger.info(f"Generated migration file: {migration_file}")

    except Exception as e:
        logger.error(f"Error generating migration: {str(e)}")
        raise click.ClickException(str(e))


@cli.command()
@click.option('--dry-run', is_flag=True, help='Show what would be executed without actually running')
@click.option('--target', help='Target migration version to run up to')
def migrate(dry_run: bool, target: Optional[str] = None):
    """Run pending migrations"""
    try:
        if dry_run:
            logger.info("Performing dry run...")

        results = executor.run_migrations(target_version=target, dry_run=dry_run)

        if dry_run:
            return

        logger.info("Migration Results:")
        for result in results:
            status = "✅" if result.success else "❌"
            logger.info(f"{status} {result.version} ({result.elapsed_ms}ms)")

    except Exception as e:
        logger.error(f"Error running migrations: {str(e)}")
        raise click.ClickException(str(e))


@cli.command()
def status():
    """Show migration status"""
    try:
        status = executor.get_detailed_status()

        click.echo("\nMigration Status Summary:")
        click.echo(f"Total Migrations: {status['total_migrations']}")
        click.echo(f"Pending: {status['pending_count']}")
        click.echo(f"Successful: {status['success_count']}")
        click.echo(f"Failed: {status['failed_count']}")
        click.echo(f"Rolled Back: {status['rolled_back_count']}")

        click.echo("\nDetailed Status:")
        for version, info in status['migrations'].items():
            status_symbol = {
                'pending': '⏳',
                'success': '✅',
                'failed': '❌',
                'rolled_back': '⬅️'
            }.get(info['status'].value, '❓')

            click.echo(f"\n{status_symbol} {version}")
            if info.get('applied_at'):
                click.echo(f"  Applied: {info['applied_at']}")
                click.echo(f"  Duration: {info.get('elapsed_ms', 0)}ms")
            if info.get('error_message'):
                click.echo(f"  Error: {info['error_message']}")

    except Exception as e:
        logger.error(f"Error getting status: {str(e)}")
        raise click.ClickException(str(e))


@cli.command()
@click.option('--steps', default=1, help='Number of migrations to roll back')
@click.option('--dry-run', is_flag=True, help='Show what would be rolled back without executing')
def rollback(steps: int, dry_run: bool):
    """Rollback migrations"""
    try:
        if dry_run:
            logger.info(f"Would rollback {steps} migration(s)")
            # Show what would be rolled back
            return

        results = executor.rollback(steps)

        logger.info("Rollback Results:")
        for result in results:
            status = "✅" if result.success else "❌"
            logger.info(f"{status} Rolled back {result.version} ({result.elapsed_ms}ms)")

    except Exception as e:
        logger.error(f"Error rolling back migrations: {str(e)}")
        raise click.ClickException(str(e))


@cli.command()
def failed():
    """List failed migrations with details"""
    try:
        status = executor.get_detailed_status()
        failed_migrations = {
            version: info for version, info in status['migrations'].items()
            if info['status'].value == 'failed'
        }

        if not failed_migrations:
            click.echo("No failed migrations found.")
            return

        click.echo("\nFailed Migrations:")
        for version, info in failed_migrations.items():
            click.echo(f"\n❌ {version}")
            click.echo(f"  Failed at: {info['applied_at']}")
            click.echo(f"  Error: {info['error_message']}")
            if info.get('details'):
                click.echo(f"  Details: {info['details']}")

    except Exception as e:
        logger.error(f"Error listing failed migrations: {str(e)}")
        raise click.ClickException(str(e))


@cli.command()
@click.option('--limit', default=10, help='Number of migrations to show')
def history(limit: int):
    """Show migration execution history"""
    try:
        with executor.engine.connect() as conn:
            query = text("""
                SELECT
                    version,
                    applied_at,
                    status,
                    is_success,
                    elapsed_time_ms,
                    error_message,
                    details
                FROM schema_migrations
                ORDER BY applied_at DESC
                LIMIT :limit
            """)

            result = conn.execute(query, {"limit": limit})

            click.echo("\nMigration History:")
            for row in result:
                status_symbol = {
                    'success': '✅',
                    'failed': '❌',
                    'rolled_back': '⬅️'
                }.get(row.status, '❓')

                click.echo(f"\n{status_symbol} {row.version}")
                click.echo(f"  Applied: {row.applied_at}")
                click.echo(f"  Status: {row.status}")
                click.echo(f"  Duration: {row.elapsed_time_ms}ms")

                if row.error_message:
                    click.echo(f"  Error: {row.error_message}")
                if row.details:
                    click.echo(f"  Details: {row.details}")

    except Exception as e:
        logger.error(f"Error fetching migration history: {str(e)}")
        raise click.ClickException(str(e))


@cli.command()
def verify():
    """Verify migration files integrity and dependencies"""
    try:
        issues = []
        migrations = sorted([
            f for f in (migration_dir / "versions").glob("*.py")
            if not f.stem.startswith('_') and f.stem != '__init__'
        ])

        click.echo("\nVerifying migration files...")

        for migration_file in migrations:
            click.echo(f"\nChecking {migration_file.name}...")

            try:
                # Check if file can be loaded
                module = executor._load_migration_module(migration_file.stem)

                # Verify required functions
                for func in ['up', 'down']:
                    if not hasattr(module, func):
                        issues.append(f"{migration_file.name}: Missing required function '{func}'")

                # Verify function signatures
                if hasattr(module, 'up'):
                    if not isinstance(module.up(), list):
                        issues.append(f"{migration_file.name}: 'up' function must return a list of statements")

                if hasattr(module, 'down'):
                    if not isinstance(module.down(), list):
                        issues.append(f"{migration_file.name}: 'down' function must return a list of statements")

                # Check SQL syntax in up() and down()
                for stmt in module.up():
                    try:
                        text(stmt)
                    except Exception as e:
                        issues.append(f"{migration_file.name}: Invalid SQL in up(): {str(e)}")

                for stmt in module.down():
                    try:
                        text(stmt)
                    except Exception as e:
                        issues.append(f"{migration_file.name}: Invalid SQL in down(): {str(e)}")

                click.echo("  ✅ File structure valid")
                click.echo("  ✅ Required functions present")
                click.echo("  ✅ SQL syntax valid")

            except Exception as e:
                issues.append(f"{migration_file.name}: Failed to load - {str(e)}")
                click.echo(f"  ❌ Error: {str(e)}")

        if issues:
            click.echo("\n❌ Verification failed! Issues found:")
            for issue in issues:
                click.echo(f"  • {issue}")
        else:
            click.echo("\n✅ All migration files verified successfully!")

    except Exception as e:
        logger.error(f"Error verifying migrations: {str(e)}")
        raise click.ClickException(str(e))


@cli.command()
def cleanup():
    """Remove failed migration records"""
    try:
        client = SpannerClientSingleton()
        with client.engine.connect().execution_options(isolation_level="AUTOCOMMIT") as conn:
            result = conn.execute(
                text("DELETE FROM schema_migrations WHERE is_success = false")
            )
            print(f"Cleaned up {result.rowcount} failed migration records")
    except Exception as e:
        print(f"Error cleaning up failed migrations: {str(e)}")


if __name__ == '__main__':
    cli()
