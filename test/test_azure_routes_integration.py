"""
Integration tests for Azure routes with caching functionality.
Tests the actual Azure routes implementation including the caching logic.
"""

import hashlib
import json
import os
import unittest
from unittest.mock import MagicMock, Mo<PERSON>, patch
from urllib.parse import urljoin

import pytest


class TestAzureRoutesIntegration(unittest.TestCase):
    """Integration test cases for Azure routes with caching."""

    def setUp(self):
        """Set up test fixtures."""
        # Mock environment variables
        self.env_patcher = patch.dict(os.environ, {
            'AZURE_CLIENT_ID': 'test_client_id',
            'AZURE_REDIRECT_URI': '/auth/azure/callback',
            'AZURE_STATE_SECRET': 'test_secret_key_for_hmac_signing',
            'AZURE_SUCCESS_AUTH_REDIRECT_URI': '/dashboard',
            'JWT_AUDIENCE': 'https://api.example.com',
            'PLATFORM_URL': 'https://platform.example.com'
        })
        self.env_patcher.start()

        # Mock the Flask app and request context
        self.app_patcher = patch('src.api.routes.azure.request')
        self.mock_request = self.app_patcher.start()

        # Mock ServiceClient
        self.service_client_patcher = patch('src.api.routes.azure.ServiceClient')
        self.mock_service_client_class = self.service_client_patcher.start()
        self.mock_service_client = MagicMock()
        self.mock_service_client_class.return_value.__enter__.return_value = self.mock_service_client

        # Clear the cache before each test
        from src.api.routes.azure import auth_code_cache
        auth_code_cache.clear()

    def tearDown(self):
        """Clean up test fixtures."""
        self.env_patcher.stop()
        self.app_patcher.stop()
        self.service_client_patcher.stop()

    @patch('src.api.routes.azure.get_user_info')
    def test_get_auth_url_generates_correct_url(self, mock_get_user_info):
        """Test that get_auth_url generates the correct Azure OAuth URL."""
        # Mock user info
        mock_user_info = {'id': 'test_user_123'}
        mock_get_user_info.return_value = mock_user_info

        from src.api.routes.azure import get_auth_url

        # Call the function
        result, status_code = get_auth_url(mock_user_info)

        # Verify status code
        self.assertEqual(status_code, 200)

        # Verify the auth URL contains expected components
        auth_url = result.authUrl
        self.assertIn('login.microsoftonline.com', auth_url)
        self.assertIn('client_id=test_client_id', auth_url)
        self.assertIn('response_type=code', auth_url)
        self.assertIn('scope=499b84ac-1321-427f-aa17-267ca6975798', auth_url)

    def test_token_exchange_caching_prevents_duplicate_calls(self):
        """Test that token exchange caching prevents duplicate API calls."""
        from src.api.routes.azure import perform_azure_token_exchange

        auth_code = "test_auth_code_123"
        redirect_uri = "https://api.example.com/auth/azure/callback"

        # Mock successful token exchange response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'access_token': 'test_access_token',
            'refresh_token': 'test_refresh_token'
        }
        self.mock_service_client.post.return_value = mock_response

        # First call should hit the API
        access_token1, refresh_token1 = perform_azure_token_exchange(auth_code, redirect_uri)

        # Verify API was called
        self.mock_service_client.post.assert_called_once()
        self.assertEqual(access_token1, 'test_access_token')
        self.assertEqual(refresh_token1, 'test_refresh_token')

        # Reset mock to track subsequent calls
        self.mock_service_client.reset_mock()

        # Second call with same parameters should use cache
        access_token2, refresh_token2 = perform_azure_token_exchange(auth_code, redirect_uri)

        # Verify API was NOT called again
        self.mock_service_client.post.assert_not_called()

        # Verify same tokens returned
        self.assertEqual(access_token1, access_token2)
        self.assertEqual(refresh_token1, refresh_token2)

    def test_different_auth_codes_get_separate_cache_entries(self):
        """Test that different auth codes get separate cache entries."""
        from src.api.routes.azure import perform_azure_token_exchange

        auth_code1 = "auth_code_1"
        auth_code2 = "auth_code_2"
        redirect_uri = "https://api.example.com/auth/azure/callback"

        # Mock different responses for different calls
        mock_responses = [
            MagicMock(status_code=200, **{'json.return_value': {'access_token': 'token1', 'refresh_token': 'refresh1'}}),
            MagicMock(status_code=200, **{'json.return_value': {'access_token': 'token2', 'refresh_token': 'refresh2'}})
        ]
        self.mock_service_client.post.side_effect = mock_responses

        # Call with first auth code
        access1, refresh1 = perform_azure_token_exchange(auth_code1, redirect_uri)

        # Call with second auth code
        access2, refresh2 = perform_azure_token_exchange(auth_code2, redirect_uri)

        # Verify both API calls were made
        self.assertEqual(self.mock_service_client.post.call_count, 2)

        # Verify different tokens returned
        self.assertEqual(access1, 'token1')
        self.assertEqual(refresh1, 'refresh1')
        self.assertEqual(access2, 'token2')
        self.assertEqual(refresh2, 'refresh2')

    def test_git_client_install_azure_uses_cached_tokens(self):
        """Test that git_client_install_azure uses cached token exchange."""
        from src.api.routes.azure import (cache_token_exchange,
                                          git_client_install_azure)

        auth_code = "test_auth_code"
        user_id = "test_user_123"
        redirect_uri = "https://api.example.com/auth/azure/callback"

        # Pre-populate cache
        cache_token_exchange(auth_code, redirect_uri, "cached_access", "cached_refresh")

        # Mock successful install response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'success': True}
        self.mock_service_client.post.return_value = mock_response

        # Call the function
        git_client_install_azure(auth_code, user_id)

        # Verify install API was called with cached tokens
        self.mock_service_client.post.assert_called_once_with(
            "github", "/v1/azure/install",
            json={
                'access_token': 'cached_access',
                'refresh_token': 'cached_refresh',
                'user_id': user_id,
                'redirect_uri': redirect_uri,
                'requested_scope': '499b84ac-1321-427f-aa17-267ca6975798/.default offline_access'
            }
        )

    def test_cache_key_generation_matches_expected_format(self):
        """Test that cache keys are generated in the expected format."""
        from src.api.routes.azure import (cache_token_exchange,
                                          get_cached_token_exchange)

        auth_code = "test_code"
        redirect_uri = "test_uri"

        # Cache a token
        cache_token_exchange(auth_code, redirect_uri, "test_access", "test_refresh")

        # Retrieve it
        result = get_cached_token_exchange(auth_code, redirect_uri)

        # Verify it was cached and retrieved correctly
        self.assertIsNotNone(result)
        access_token, refresh_token = result
        self.assertEqual(access_token, "test_access")
        self.assertEqual(refresh_token, "test_refresh")

        # Verify cache key format by checking the cache directly
        from src.api.routes.azure import auth_code_cache
        expected_key = f"auth_code_{hashlib.sha256(f'{auth_code}_{redirect_uri}'.encode()).hexdigest()}"
        self.assertIn(expected_key, auth_code_cache)


def run_azure_routes_integration_tests():
    """Run all Azure routes integration tests."""
    print("🧪 Running Azure Routes Integration Tests")
    print("=" * 60)

    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestAzureRoutesIntegration)

    # Run tests with detailed output
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # Print summary
    print("\n" + "=" * 60)
    print("INTEGRATION TEST SUMMARY")
    print("=" * 60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")

    if result.failures:
        print("\nFAILURES:")
        for test, traceback in result.failures:
            print(f"  • {test}: {traceback}")

    if result.errors:
        print("\nERRORS:")
        for test, traceback in result.errors:
            print(f"  • {test}: {traceback}")

    success = len(result.failures) == 0 and len(result.errors) == 0

    if success:
        print("\n🎉 All integration tests passed!")
        print("\n📋 What this validates:")
        print("   • Azure OAuth URL generation works correctly")
        print("   • Token exchange caching prevents duplicate API calls")
        print("   • Different auth codes get separate cache entries")
        print("   • git_client_install_azure uses cached tokens")
        print("   • Cache key generation matches expected format")
        print("   • Integration between routes and caching logic works")
    else:
        print("\n❌ Some integration tests failed!")

    return success


if __name__ == "__main__":
    run_azure_routes_integration_tests()
