"""
GitHub Service Test Suite

Basic tests for GitHub operations including repository management,
branch operations, and pull request handling.
"""

import logging
import os
import sys
from contextlib import contextmanager
from pathlib import Path
from typing import Callable, List

try:
    from main import app  # Import your Flask app
except ImportError:
    raise AssertionError("Could not import Flask app from main.py.")
from src.github.github_app_service import GithubAppService

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Test Configuration
TEST_CONFIG = {
    'owner': 'montanaromi',
    'repo': 'hello-world',
    'repo_id': '1009121430',
    'project_repo_id': '3abfe990-623f-4a32-9344-3975fc9e55f2',
    'installation_id': '72672779',
    'user_id': '04815901-b9dd-4fc6-ada2-92c1f7f8f376',
    'branch_name': 'main',
    'path': 'README.md',
    'token': os.getenv('GITHUB_TOKEN')  # Set this environment variable
}


class TestRunner:
    """Test runner with pass/fail tracking and logging control."""

    def __init__(self, suppress_logging: bool = True):
        self.suppress_logging = suppress_logging
        self.results = {
            'passed': 0,
            'failed': 0,
            'total': 0,
            'details': []
        }
        self.app_service = GithubAppService()

    @contextmanager
    def logging_suppressed(self):
        """Context manager to suppress logging during tests."""
        if self.suppress_logging:
            root_logger = logging.getLogger()
            original_level = root_logger.level
            root_logger.setLevel(logging.CRITICAL + 1)

            try:
                yield
            finally:
                root_logger.setLevel(original_level)
        else:
            yield

    def run_test(self, test_func: Callable, test_name: str | None = None) -> bool:
        """Run a single test and track results."""
        if test_name is None:
            test_name = test_func.__name__

        self.results['total'] += 1

        try:
            with self.logging_suppressed():
                test_func()

            self.results['passed'] += 1
            self.results['details'].append({
                'name': test_name,
                'status': 'PASSED',
                'error': None
            })
            print(f"✅ {test_name}: PASSED")
            return True

        except Exception as e:
            self.results['failed'] += 1
            self.results['details'].append({
                'name': test_name,
                'status': 'FAILED',
                'error': str(e)
            })
            print(f"❌ {test_name}: FAILED - {str(e)}")
            return False

    def print_summary(self):
        """Print test execution summary."""
        print("\n" + "="*60)
        print("TEST EXECUTION SUMMARY")
        print("="*60)
        print(f"Total Tests: {self.results['total']}")
        print(f"Passed: {self.results['passed']} ✅")
        print(f"Failed: {self.results['failed']} ❌")

        if self.results['total'] > 0:
            pass_rate = (self.results['passed'] / self.results['total']) * 100
            print(f"Pass Rate: {pass_rate:.1f}%")

        if self.results['failed'] > 0:
            print("\nFAILED TESTS:")
            for detail in self.results['details']:
                if detail['status'] == 'FAILED':
                    print(f"  • {detail['name']}: {detail['error']}")

        print("="*60)


def test_get_file_content_path():
    """Test get_file_content_by_path with and without ref, using asserts."""
    app_service = GithubAppService()
    installation_id = TEST_CONFIG.get('installation_id') or ""
    path = TEST_CONFIG.get('path') or ""
    repo_id = TEST_CONFIG.get('repo_id') or ""

    # With ref
    branch = TEST_CONFIG.get('branch_name') or ""
    response_with_ref = app_service.get_file_content_by_path(installation_id, repo_id, path, branch)
    assert response_with_ref, "No content found or error fetching file (with ref)."

    # Without ref
    response_without_ref = app_service.get_file_content_by_path(installation_id, repo_id, path, None)
    assert response_without_ref, "No content found or error fetching file (without ref)."


def test_e2e_get_file_content_flask():
    """
    E2E test for the /v1/github/repositories/<repo_id>/content/<file_path> endpoint using Flask test client,
    both with and without the 'ref' query parameter. Uses asserts for validation.
    """
    project_repo_id = TEST_CONFIG.get('project_repo_id') or ""
    file_path = TEST_CONFIG.get('path') or ""

    # Construct the new endpoint URL with file_path as a URL parameter
    url = f"/v1/github/repositories/{project_repo_id}/content/{file_path}"

    # --- Test with 'ref' query parameter ---
    query_with_ref = {
        "ref": TEST_CONFIG.get('branch_name') or ""
    }
    with app.test_client() as client:
        response = client.get(url, query_string=query_with_ref)
        # Assert status code is 200 OK
        assert response.status_code == 200, (
            f"Status code (with ref): {response.status_code}, data: {response.data.decode()}"
        )
        # Assert JSON response is returned
        assert response.json is not None, "No JSON returned with ref"
        # Assert 'content' key exists or response is non-empty
        assert "content" in response.json or len(response.json) > 0, "No content in response with ref"

    # --- Test without 'ref' query parameter ---
    with app.test_client() as client:
        response = client.get(url)
        # Assert status code is 200 OK
        assert response.status_code == 200, (
            f"Status code (without ref): {response.status_code}, data: {response.data.decode()}"
        )
        # Assert JSON response is returned
        assert response.json is not None, "No JSON returned without ref"
        # Assert 'content' key exists or response is non-empty
        assert "content" in response.json or len(response.json) > 0, "No content in response without ref"


def run_all_tests(suppress_logging: bool = True, run_specific: List[str] | None = None):
    """
    Run all test functions with progress tracking.
    Args:
        suppress_logging: Whether to suppress logging during test execution
        run_specific: List of specific test names to run (None = run all)
    """
    runner = TestRunner(suppress_logging=suppress_logging)
    all_tests = [
        (test_get_file_content_path, "Get File Content by Path with and without Ref"),
        (test_e2e_get_file_content_flask, "E2E Get File Content by Path with and without Ref"),
    ]
    if run_specific:
        all_tests = [
            (func, name) for func, name in all_tests
            if func.__name__ in run_specific or name in run_specific
        ]
    print(f"Running {len(all_tests)} tests...")
    print("-" * 60)
    for test_func, test_name in all_tests:
        runner.run_test(test_func, test_name)
    runner.print_summary()
    return runner.results


# To run:
# test_e2e_get_file_content_flask()
if __name__ == "__main__":
    # Configuration options
    SUPPRESS_LOGGING = True  # Set to False to see all logs during tests

    # Check if GitHub token is available
    if not TEST_CONFIG['token']:
        print("⚠️  Warning: GITHUB_TOKEN environment variable not set")
        print("   Some tests will be skipped or simulated")
        print("   Set GITHUB_TOKEN to run full integration tests")
        print()

    # Run all tests
    run_all_tests(suppress_logging=SUPPRESS_LOGGING)

    # Or run specific tests:
    # run_all_tests(suppress_logging=SUPPRESS_LOGGING,
    #               run_specific=['test_github_connection', 'test_rate_limit_handling'])
