"""
Test cases for tech spec idempotency functionality.
"""
import threading
import time
import unittest
from unittest.mock import Mock, patch

from common_models.models import TechSpecJobType

from src.api.routes.tech_spec import (get_project_lock,
                                      is_tech_spec_job_type_in_progress)
from src.error.errors import JobAlreadyRunningError


class TestTechSpecIdempotency(unittest.TestCase):
    """Test cases for tech spec idempotency functionality."""

    def setUp(self):
        """Set up test fixtures."""
        self.mock_project = Mock()
        self.mock_project.id = "test-project-123"

    @patch('src.api.routes.tech_spec.get_in_progress_tech_spec_by_project_id_and_job_type')
    def test_no_job_in_progress_allows_new_job(self, mock_get_in_progress):
        """Test that when no job is in progress, new job is allowed."""
        # Arrange
        mock_get_in_progress.return_value = None

        # Act & Assert - should not raise exception
        try:
            is_tech_spec_job_type_in_progress(self.mock_project, TechSpecJobType.ADD_FEATURE)
        except JobAlreadyRunningError:
            self.fail("JobAlreadyRunningError was raised when no job was in progress")

    @patch('src.api.routes.tech_spec.get_in_progress_tech_spec_by_project_id_and_job_type')
    def test_job_in_progress_blocks_new_job(self, mock_get_in_progress):
        """Test that when a job is in progress, new job of same type is blocked."""
        # Arrange
        mock_tech_spec = Mock()
        mock_tech_spec.id = "existing-job-123"
        mock_get_in_progress.return_value = mock_tech_spec

        # Act & Assert
        with self.assertRaises(JobAlreadyRunningError) as context:
            is_tech_spec_job_type_in_progress(self.mock_project, TechSpecJobType.ADD_FEATURE)

        self.assertIn("ADD_FEATURE", str(context.exception))
        self.assertIn("in progress", str(context.exception))

    def test_project_specific_locks_are_different(self):
        """Test that different projects get different locks."""
        # Arrange
        project1_id = "project-1"
        project2_id = "project-2"
        job_type = "add_feature"

        # Act
        lock1 = get_project_lock(project1_id, job_type)
        lock2 = get_project_lock(project2_id, job_type)

        # Assert
        self.assertTrue(hasattr(lock1, "acquire") and hasattr(lock1, "release"))
        self.assertTrue(hasattr(lock2, "acquire") and hasattr(lock2, "release"))
        self.assertNotEqual(lock1, lock2, "Different projects should have different locks")

    def test_same_project_same_job_type_gets_same_lock(self):
        """Test that the same project and job type always gets the same lock."""
        # Arrange
        project_id = "project-1"
        job_type = "add_feature"

        # Act
        lock1 = get_project_lock(project_id, job_type)
        lock2 = get_project_lock(project_id, job_type)

        # Assert
        self.assertEqual(lock1, lock2, "Same project and job type should get the same lock")

    def test_same_project_different_job_types_get_different_locks(self):
        """Test that the same project with different job types gets different locks."""
        # Arrange
        project_id = "project-1"

        # Act
        lock_add_feature = get_project_lock(project_id, "add_feature")
        lock_refactor = get_project_lock(project_id, "refactor_code")

        # Assert
        self.assertNotEqual(lock_add_feature, lock_refactor,
                            "Same project with different job types should have different locks")

    @patch('src.api.routes.tech_spec.get_in_progress_tech_spec_by_project_id_and_job_type')
    def test_concurrent_requests_race_condition_protection(self, mock_get_in_progress):
        """Test that concurrent requests are properly serialized by locks."""
        # Arrange
        project_id = "test-project-race"
        mock_project = Mock()
        mock_project.id = project_id

        # First call returns None (no job in progress), subsequent calls return a job
        mock_tech_spec = Mock()
        mock_tech_spec.id = "job-123"

        call_count = 0

        def side_effect(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                # Simulate some processing time
                time.sleep(0.1)
                return None
            else:
                return mock_tech_spec

        mock_get_in_progress.side_effect = side_effect

        results = []
        exceptions = []

        def make_request():
            try:
                is_tech_spec_job_type_in_progress(mock_project, TechSpecJobType.ADD_FEATURE)
                results.append("success")
            except JobAlreadyRunningError as e:
                exceptions.append(str(e))

        # Act - simulate two concurrent requests
        thread1 = threading.Thread(target=make_request)
        thread2 = threading.Thread(target=make_request)

        thread1.start()
        thread2.start()

        thread1.join()
        thread2.join()

        # Assert - one should succeed, one should fail
        self.assertEqual(len(results) + len(exceptions), 2, "Both requests should complete")
        # Due to the lock, the requests should be serialized, so the behavior depends on timing
        # At minimum, we verify that the lock mechanism doesn't cause deadlocks


if __name__ == '__main__':
    unittest.main()
